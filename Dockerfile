# Node.js 22をベースイメージとして使用する
FROM node:22-alpine AS base

# インストール依存関係の段階
FROM base AS deps
WORKDIR /app

# libc6-compat と git をインストール (git は lefthook をサポートするため)
RUN apk add --no-cache libc6-compat git

# 依存関係のみをコピーしてキャッシュを最適化
COPY package.json yarn.lock* ./

# lefthook のインストールを無視して依存関係をインストール
RUN yarn install --frozen-lockfile --ignore-scripts

# ビルド段階
FROM base AS builder
WORKDIR /app

# deps 段階から node_modules をコピー
COPY --from=deps /app/node_modules ./node_modules

# 先に設定ファイルをコピー
COPY next.config.js tsconfig.json ./
# 再コピー源コード
COPY public ./public
COPY src ./src
# 残りのファイルをコピー
COPY . .
# copy .env.example to .env
COPY .env.example ./.env

# 環境変数を生産環境に設定
ENV NODE_ENV=production

# ビルド時に環境変数を注入
ARG NEXT_PUBLIC_APP_ENV
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
ARG NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID


# 環境変数を設定（条件付き）
RUN if [ ! -z "$NEXT_PUBLIC_APP_ENV" ]; then \
    echo "NEXT_PUBLIC_APP_ENV=$NEXT_PUBLIC_APP_ENV" >> .env; \
    fi

RUN if [ ! -z "$NEXT_PUBLIC_API_URL" ]; then \
    echo "NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL" >> .env; \
    fi

RUN if [ ! -z "$NEXT_PUBLIC_GOOGLE_MAPS_API_KEY" ]; then \
    echo "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=$NEXT_PUBLIC_GOOGLE_MAPS_API_KEY" >> .env; \
    fi

RUN if [ ! -z "$NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID" ]; then \
    echo "NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID=$NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID" >> .env; \
    fi

# アプリケーションをビルド
RUN yarn build

# 生産運行段階
FROM base AS runner
WORKDIR /app

# 環境変数を設定
ENV NODE_ENV=production

# 非 root ユーザーを作成
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# ビルド出力と必要なファイルをコピー
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 非 root ユーザーに切り替え
USER nextjs

# 暴露端口
EXPOSE 3000

# 環境変数を設定，指定アプリケーションが監視するホスト
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康チェックを追加
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1

# ラベルを追加
LABEL org.opencontainers.image.description="Next.js application with standalone output"

# 起動コマンド
CMD ["node", "server.js"]
