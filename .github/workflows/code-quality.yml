name: Code Quality

on:
  push:
    branches: [master, develop, staging]
  pull_request:
    branches: [master, develop, staging]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "yarn"

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run Biome check
        run: npx @biomejs/biome check .
