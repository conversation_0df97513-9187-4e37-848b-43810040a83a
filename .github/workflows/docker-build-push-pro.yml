name: Pro Build and Push Docker Image on Tag

on:
  push:
    tags:
      - 'v*'  # match all v prefix tag

env:
  REGISTRY_NAME: ${{ secrets.REGISTRY }}
  IMAGE_NAME: web-app
  REPOSITORY_NAME: mileage-dev
  DOCKERFILE_PATH: "./Dockerfile"  # Dockerfile path demo: ./Dockerfile dir
  BUILD_CONTEXT: "./"  # build context app dir
  NEXT_PUBLIC_APP_ENV: 'pro'
  NEXT_PUBLIC_API_URL: 'https://app-api.kmileage-pro.sdpf4hc-ntt.com/api/v1/app'
  NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY_PRO || 'pro_maps_key' }}
  NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID: ${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID_PRO || 'pro_maps_mapid' }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    # main branch and tag startwith v 
    if: startsWith(github.ref, 'refs/tags/v')
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}  # checkout trigger tag

      - name: Extract Docker Tag
        id: extract-tag
        run: |
          # get tag name（cut refs/tags/ prefix）
          TAG_NAME=${GITHUB_REF#refs/tags/}
          echo "DOCKER_TAG=${TAG_NAME}" >> $GITHUB_OUTPUT
          echo "Extracted tag: ${GITHUB_OUTPUT} -- ${TAG_NAME}"

      - name: Determine Branch
        id: detect
        run: |
          # obtain tag commit
          TAG_COMMIT=$(git rev-parse ${{ github.ref }}^{commit})
          
          # get main branch commit
          git fetch origin main:main
          MAIN_COMMIT=$(git rev-parse main)
          
          # check tag commit in main
          if git merge-base --is-ancestor $TAG_COMMIT $MAIN_COMMIT; then
            echo "BRANCH_NAME=main" >> $GITHUB_OUTPUT
            echo "IMAGE_NAME=web-app" >> $GITHUB_OUTPUT
            echo "run_tests=true" >> $GITHUB_OUTPUT
          else
            echo "::warning::Tag ${{ github.ref }} is not in main branch history"
            echo "Tag commit: $TAG_COMMIT"
            echo "Main branch head: $MAIN_COMMIT"
            echo "run_tests=false" >> $GITHUB_OUTPUT
          fi

      - name: Login to Azure Container Registry
        if: steps.detect.outputs.run_tests == 'true'
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.REGISTRY }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and push Docker image
        if: steps.detect.outputs.run_tests == 'true'
        uses: docker/build-push-action@v5
        with:
          context: ${{ env.BUILD_CONTEXT }}  # config build context
          file: ${{ env.DOCKERFILE_PATH }}    # finger out Dockerfile path
          push: true
          build-args: |
            NEXT_PUBLIC_APP_ENV=${{ env.NEXT_PUBLIC_APP_ENV }}
            NEXT_PUBLIC_API_URL=${{ env.NEXT_PUBLIC_API_URL }}
            NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${{ env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}
            NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID=${{ env.NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID }}
          tags: |
            ${{ secrets.REGISTRY }}/${{ env.REPOSITORY_NAME }}/${{ env.IMAGE_NAME }}:${{ steps.extract-tag.outputs.DOCKER_TAG }}
            ${{ secrets.REGISTRY }}/${{ env.REPOSITORY_NAME }}/${{ env.IMAGE_NAME }}:latest
          labels: |
            org.opencontainers.image.source=${{ github.repositoryUrl }}
            org.opencontainers.image.version=${{ steps.extract-tag.outputs.DOCKER_TAG }}
      
      - name: Checkout INFRA repository
        if: steps.detect.outputs.run_tests == 'true'
        uses: actions/checkout@v4
        with:
          repository: BBX-Unified-Repo/shKENKOMILEAGE_INFRA
          token: ${{ secrets.INFRA_GITHUB_TOKEN }}  # need token for INFRA repo commit
          path: infra-repo
          ref: main 

      - name: Update values.yaml
        if: steps.detect.outputs.run_tests == 'true'
        run: |
          # enter INFRA dir
          cd infra-repo
          
          # modify values.yaml tag value
          sed -i "s/tag: \".*\"/tag: \"${{ steps.extract-tag.outputs.DOCKER_TAG }}\"/g" charts/web-app/values.yaml
          
          # check 
          git diff
          
          # config git account info
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Action"

      - name: Commit and push changes
        if: steps.detect.outputs.run_tests == 'true'
        run: |
          cd infra-repo
          
          # check change file
          if git diff --quiet; then
            echo "No changes to commit"
          else
            git add charts/web-app/values.yaml
            git commit -m "Update web-app image tag to ${{ steps.extract-tag.outputs.DOCKER_TAG }}"
            git push origin HEAD:main  # push to target branch 
          fi
          