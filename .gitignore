# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.env
.env.local
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
.history
.prompt

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
# .env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Lefthook
.lefthook-local

*storybook.log
storybook-static
*/stories-bak/*
# cursor
.cursor
