{"name": "mileage", "version": "v20250806.1749", "private": true, "browserslist": [">0.2%, Chrome >= 74, last 2 versions, not dead"], "scripts": {"dev": "yarn check:env && next dev --port 4399", "check:env": "node check-env.js", "build": "yarn check:env && next build", "lint": "biome check .", "lint:error": "biome check --diagnostic-level=error .", "lint:fix": "biome check --write .", "format": "biome format --write .", "check": "biome check ---write --organize-imports .", "prepare": "lefthook install", "test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@googlemaps/markerclusterer": "^2.5.3", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.30.0", "@tanstack/react-query": "^5.82.0", "@tanstack/react-table": "^8.21.2", "@types/core-js": "^2.5.8", "@uidotdev/usehooks": "^2.4.1", "@vis.gl/react-google-maps": "^1.5.2", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "core-js": "^3.45.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "motion": "^12.4.0", "next": "14.2.28", "next-themes": "^0.4.4", "nuqs": "^2.3.2", "prism-react-renderer": "^2.4.1", "react": "18.3.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "sonner": "^1.7.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@chromatic-com/storybook": "^4", "@iconify/react": "^5.2.0", "@storybook/addon-a11y": "^9.0.4", "@storybook/addon-docs": "^9.0.4", "@storybook/addon-onboarding": "^9.0.4", "@storybook/addon-vitest": "^9.0.4", "@storybook/nextjs-vite": "9.0.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "3.1.3", "@vitest/coverage-v8": "3.1.3", "jsdom": "^26.1.0", "lefthook": "^1.11.3", "playwright": "^1.52.0", "postcss": "^8", "storybook": "^9.0.4", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^3.1.3"}}