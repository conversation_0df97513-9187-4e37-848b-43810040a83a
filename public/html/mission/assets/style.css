@charset"utf-8";

html,body,div,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,p,blockquote,fieldset,input,abbr,article,aside,command,details,figcaption,figure,footer,header,mark,meter,nav,output,progress,section,summary,time {
  margin: 0;
  padding: 0;
  vertical-align: baseline;
	box-sizing: border-box;
}

li,h5,h6,span,pre,code,address,caption,cite,code,th,figcaption {
  font-size: 1em;
  font-weight: normal;
  font-style: normal;
}

em,strong,b,h1,h2,h3,h4 {
  font-size: 1em;
  font-weight: bold;
  font-style: normal;
}

fieldset,iframe {
  border: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

article,aside,footer,header,nav,section,figure,figcaption,main {
  display: block;
}

img,abbr,acronym,fieldset {
  border: 0;
  font-size: 0;
  line-height: 0;
  vertical-align: bottom;
}

li {
  list-style: none;
}

body {
  line-height: 1;
  font-family:Arial;
  color: #151515;
  font-family: 'Noto Sans JP', "Helvetica Neue",<PERSON><PERSON>,"Hiragino Kaku Gothic ProN","Hiragino Sans",Meiryo, sans-serif;
  -webkit-text-size-adjust: 100%;
  width: 100%;
  position: relative;
  font-size: 16px;
  overflow-wrap: break-word;
}

header, footer {
  width: 100%;
  z-index: 1;
}

dl,menu,ol,ul,dt,dd {
  margin: 0;
}

img {
	max-width: 100%;
}

a {
	text-decoration: none;
	color: #151515;
	transition: color 150ms, background 150ms, border 150ms, border-radius 150ms;
}

input,
button,
select,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: transparent;
  border: none;
  border-radius: 0;
  font: inherit;
  outline: none;
}

input:focus,
textarea:focus,
select:focus {
	outline: none;
}

input[type="submit"],
input[type="button"],
input[type="reset"] {
	border-radius: 0;
	-webkit-box-sizing: content-box;
	-webkit-appearance: button;
	appearance: button;
	border: none;
	box-sizing: border-box;
	cursor: pointer;
}
input[type="submit"]::-webkit-search-decoration,
input[type="button"]::-webkit-search-decoration,
input[type="reset"]::-webkit-search-decoration {
	display: none;
}
input[type="submit"]::focus,
input[type="button"]::focus {
	outline-offset: -2px;
}

input[type="checkbox"] {
	border-radius: 0;
}

* {
	box-sizing: border-box;
}

@media (min-width: 768px) {
	.sp {
		display: none !important;
	}
}


@media screen and (max-width: 767px) {
	.pc {
		display: none !important;
	}
}

.common-inner {
	max-width: 1100px;
	margin: 0 auto;
}

.hide{
       display: none !important;
}

/* ------------------------------------------------------- */
header {
	height: 90px;
	background: #fff;
	z-index: 1000;
	position: fixed;
}

header .common-inner {
	position: relative;
	padding-top: 18px;
}

header h1.logo {
	width: 262px;
	margin-left: 22px;
	display: inline-block;
	vertical-align: middle;
}

header .download-link {
	height: 56px;
	font-size: 20px;
	font-weight: bold;
	display: inline-block;
	vertical-align: middle;
	height: 56px;
	border: 4px solid #41C2D0;
	background: url(./tri02.png) no-repeat right 30px center #fff;
	background-size: auto 18px ;
	line-height: 48px;
	padding: 0 66px 0 33px;
	border-radius: 40px;
	margin-left: 45px;
}

.navi-bar {
	padding-top: 90px;
	background: #41C2D0;
}

.navi-bar ul {
	height: 56px;
	text-align: center;
}

.navi-bar li {
	display: inline-block;
	position: relative;
}

.navi-bar li a {
	color: #fff;
	line-height: 56px;
	font-weight: bold;
}

.navi-bar li + li {
	margin-left: 80px;
}

.navi-bar .active {
	color: #fff;
	line-height: 56px;
	font-weight: bold;
}

.navi-bar .active:after {
	content: "";
	display: block;
	width: 16px;
	height: 2px;
	border-radius: 1px / 50%;
	position: absolute;
	bottom: 12px;
	left: 50%;
	margin-left: -8px;
	background: #fff;
}

.column-index {
	padding-bottom: 80px;
	text-align: center;
}

.column-index h2 {
	font-size: 40px;
	line-height: 1.4;
	padding: 0 10px;
	border-bottom: 4px solid #41C2D0;
	display: inline-block;
	margin-top: 45px;
	margin-bottom: 20px;
}

.column-index .main-list {
	width: 980px;
	margin: 0 auto;
	text-align: left;
}

.column-index .main-list li {
	position: relative;
	border-bottom: 4px solid #41C2D0;
	min-height: 230px;
}

.column-index .main-list li a {
	display: block;
	padding: 24px 0 20px 360px;
}

.column-index .main-list li:after {
	content: "";
	display: block;
	width: 24px;
	height: 22px;
	background: url(./line-end.png) no-repeat center top;
	background-size: 100% auto;
	position: absolute;
	bottom: 0;
	right: 0;
}

.column-index .main-list .thumb {
	width: 320px;
	height: 180px;
	position: absolute;
	left: 0;
	top: 20px;
}

.column-index .main-list time {
	font-size: 16px;
	color: #929292;
}

.column-index .main-list h3 {
	font-size: 24px;
	line-height: 1.6;
	padding-top: 10px;
	padding-bottom: 40px;
	background: url(./dot.png) repeat-x bottom 22px left;
	background-size: auto 4px;
	
}

.column-index .main-list .desc {
	font-size: 20px;
	line-height: 1.6;
	text-overflow: clip;
	overflow: hidden;
	height: 96px;
}

.column-index .main-list .more {
	font-size: 20px;
	line-height: 1.6;
	font-weight:bold;
	text-align:right;
}

.column-pager {
	max-width:980px;
	font-size: 24px;
	margin: 45px auto 0;
	text-align: center;
	display:flex;
	justify-content: center;
	gap:22px;
}

.column-pager img {
	width: 100%;
}

.column-pager .first {
	width: 18px;
	height: 16px;
	margin-right: 22px;
	display: inline-block;
}
.column-pager .back {
	margin-right: 22px;
}

.column-pager li span {
	color: #41C2D0;
	font-weight: bold;
}
.column-pager li a {
	font-weight: bold;
}

.column-pager .separater {
	padding: 0 7px;
	font-weight: bold;
}

.column-pager .last-page {
	font-weight: bold;
}

.column-pager .next {
}

.column-pager .next img,
.column-pager .back img {
	width: 18px;
}

.column-pager .last {
	width: 18px;
	height: 16px;
	display: inline-block;
}

/* --- single --------------------- */
.column-single {
	padding-bottom: 80px;
}

.column-single .bg-colored {
	background: #EAF9FA;
	padding: 40px 0 0;
	text-align: center;
}

.column-single .bg-colored h2 {
	max-width:800px;
	font-size: 40px;
	line-height: 1.4;
	padding: 0 10px;
	border-bottom: 4px solid #41C2D0;
	display: inline-block;
}

.column-single .movie {
	margin: 24px auto 0;
	width: 800px;
	height: 450px;
}

.column-single .movie iframe {
	width: 100%;
	height: 100%;
}

.column-single .column-kv {
	margin: 24px auto 0;
	width: 800px;
}

.column-single .txt-area {
	padding: 40px 0;
	width: 800px;
	margin: 0 auto;
}

.column-single h3 {
	padding-bottom: 40px;
	background: url(./dot.png) repeat-x bottom 22px left;
	background-size: auto 4px;
	font-size: 24px;
	font-weight: bold;
	display: inline-block;
}
.column-single h4 {
	margin-bottom:20px;
	padding-left:22px;
	background: url(./tri02.png) no-repeat left 4px;
	background-size: 16px;
	font-size: 22px;
	font-weight: bold;
}
.column-single .txt-area div p { margin-bottom:40px; }

.column-single .desc { padding:24px 0 40px; background: #EAF9FA; }
.column-single p {
	max-width:800px;
	margin:0 auto;
	line-height: 2;
	text-align:left;
	font-size: medium;
}

.column-single .to-column-index {
	width: 380px;
	height: 56px;
	line-height: 56px;
	border-radius: 28px / 50%;
	color: #fff;
	font-weight: bold;
	font-size: 20px;
	text-align: left;
	padding: 0 24px;
	background: url(./tri-white.png) no-repeat right 24px center #41C2D0;
	background-size: auto 18px;
	display: block;
	margin: 0 auto;
}

.column-single .to-column-index .icon {
	width: 56px;
	margin: 0 8px 4px 0;
	vertical-align: middle;
}

.appli-download {
	background: #41C2D0;
	padding: 50px 0 35px;
	text-align: center;
}

.appli-download h2 {
	font-size: 32px;
	margin-bottom: 25px;
	color: #fff;
}

.appli-download h2:before {
	content: "";
	background: url(./appli_dl_title_before.png) no-repeat center center;
	background-size: 100% auto;
	display: inline-block;
	vertical-align: bottom;
	width: 28px;
	height: 28px;
	margin-right: 30px;
}

.appli-download h2:after {
	content: "";
	background: url(./appli_dl_title_after.png) no-repeat center center;
	background-size: 100% auto;
	display: inline-block;
	vertical-align: bottom;
	width: 28px;
	height: 28px;
	margin-left: 25px;
}

.appli-download .detail {
	background: #EAF9FA;
	border-radius: 20px;
	text-align: center;
	width: 792px;
	padding: 15px 0;
	margin: 0 auto 38px;
}

.appli-download .detail p {
	margin-bottom: 10px;
}

.appli-download .detail .box {
	background: #41C2D0;
	color: #fff;
	border-radius: 8px;
	min-width: 168px;
	margin-right: 15px;
	display: inline-flex;
	vertical-align: middle;
	justify-content: center;
	align-items: center;
	font-size: 20px;
	height: 36px;
	padding-top: 2px;
}

.appli-download .detail .box img {
	height: 22px;
}

.appli-download .detail .code {
	font-size: 26px;
	letter-spacing: .2rem;
	display: inline-block;
	vertical-align: middle;
	font-weight:bold;
}

.appli-download .detail .code img {
	height: 19px;
}

.appli-download .detail .code + .box {
	margin-left: 50px;
}

.appli-download .device-link-wrap {
	font-size: 0;
}

.appli-download .device-link {
	border-radius: 20px;
	text-align: center;
	width: 380px;
	font-weight: bold;
	display: inline-block;
	vertical-align: top;
	box-shadow: 5px 5px 10px 0px rgba(0, 0, 0, 0.4);
	background: #fff;
	font-size: 20px;
	font-weight: bold;
}

.appli-download .device-link .btn {
	display: block;
	background: url(./tri.png) no-repeat right 30px center;
	background-size: 18px auto;
	cursor: pointer;
	height: 78px;
	line-height: 78px;
	padding-right: 30px;
	transition: background 150ms;
	font-weight: bold;
}

.appli-download .device-link.active .btn {
	background: url(./tri03.png) no-repeat right 30px center;
	background-size: 18px auto;
}

.appli-download .device-link + .device-link {
	margin-left: 30px;
}

.appli-download .accordion {
	display: none;
	text-align: center;
	padding-bottom: 12px;
	position: relative;
}

.appli-download .accordion:before {
	content: "";
	display: block;
	background: #41C2D0;
	height: 4px;
	width: 332px;
	margin: 0 auto 15px;
	background: #41C2D0;
	border-radius: 2px;
}

.appli-download .accordion .dl-btn {
	height: 52px;
	display: inline-block;
	vertical-align: middle;
	line-height: 1;
}

.appli-download .accordion .dl-btn img {
	height: 100%;
	width: auto;
}

.appli-download .accordion .qr-code {
	width: 120px;
	display: inline-block;
	vertical-align: middle;
	margin-left: 18px;
}
.appli-download .accordion .note {
	font-size: 12px;
	text-align: left;
	width: 332px;
	margin: 0 auto;
	line-height: 1.33;
	height: 84px;
}

.appli-download .accordion .note li {
	padding-left: 1em;
	text-indent: -1em;
}

footer {
        text-align: center;
        color: #fff;
        font-weight: bold;
        background: #41C2D0;
        padding: 25px 0;
}

.footer-menu {
        background-color: #fff;
        background: #41C2D0;
        text-align: center;
        padding-top: 10px;
}

.footer-menu-list {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        align-items: center;
        font-size: 10px;
        justify-content: center;
}

.footer-menu-list li {
        height: 15px;
        margin: 10px;
}

.footer-menu-list li a {
        text-decoration: none;
        color: #fff;
}

/* --- sp --------------------------------------- */
@media screen and (max-width: 767px) {
	body {
		font-size: 2.9vw;
	}
	header {
		height: 15.8vw;
	}

	header .common-inner {
		padding-top: 2vw;
	}

	header h1.logo {
		width: 33.55vw;
		margin-left: 2.6vw;
	}

	header .download-link {
		height: auto;
		font-size: 3.2vw;
		border: 0.79vw solid #41C2D0;
		background: url(./tri02.png) no-repeat right 1.8vw center #fff;
		background-size: auto 2.5vw;
		line-height: 1.25;
		padding: 1vw 5vw 1vw 2.4vw;
		border-radius: 5.5vw / 50%;
		margin-left: 2.2vw;
		text-align: center;
	}

	.navi-bar {
		padding-top: 15.8vw;
	}

	.navi-bar ul {
		height: 11.32vw;
	}

	.navi-bar li a {
		line-height: 11.32vw;
	}

	.navi-bar li + li {
		margin-left: 6.58vw;
	}

	.navi-bar .active {
		line-height: 11.32vw;
	}

	.navi-bar .active:after {
		content: "";
		display: block;
		width: 3.2vw;
		position: absolute;
		bottom: 2.6vw;
		margin-left: -1.6vw;
	}

	.column-index {
		padding-bottom: 15.79vw;
	}

	.column-index h2 {
		font-size: 6.31vw;
		padding: 0 2.24vw;
		border-bottom: 0.79vw solid #41C2D0;
		margin-top: 9.2vw;
		margin-bottom: 0;
	}

	.column-single .bg-colored .desc { width:90vw; margin: 0 auto; padding:24px 0 0; }

	.column-index .main-list {
		width: 90vw;
	}

	.column-index .main-list li {
		border-bottom: 0.79vw solid #41C2D0;
		min-height: auto;
	}

	.column-index .main-list li a {
		padding: 5.3vw 0 8.55vw;
	}

	.column-index .main-list li:after {
		content: "";
		width: 5.3vw;
		height: 4.737vw;
	}

	.column-index .main-list .thumb {
		width: 100%;
		height: auto;
		position: static;
		margin-bottom: 3.2vw;
	}

	.column-index .main-list time {
		font-size: 3.2vw;
	}

	.column-index .main-list h3 {
		font-size: 5.3vw;
		padding-top: 1.8vw;
		padding-bottom: 6.58vw;
		background: url(./dot.png) repeat-x bottom 2.6vw left;
		background-size: auto 0.79vw;
	}

	.column-index .main-list .desc {
		font-size: 4.2vw;
		height: 20.16vw;
	}

	.column-pager {
		font-size: 4.2vw;
		margin: 5.3vw auto 0;
	}

	.column-pager .back {
		width: 4.2vw;
		height: 3.9vw;
		margin-right: 2.89vw;
	}

	.column-pager .separater {
		padding: 0 0.79vw;
	}

	.column-pager .next {
		width: 4.2vw;
		height: 3.9vw;
		margin-left: 2.89vw;
	}

	/* --- single --------------------- */
	.column-single .bg-colored {
		padding: 8.42vw 0;
		text-align: left;
	}

	.column-single .bg-colored h2 {
		font-size: 6.32vw;
		padding: 0;
		display: block;
		border-bottom: 0.79vw solid #41C2D0;
		width: 90vw;
		margin: 0 auto;
	}

	.column-single .movie {
		margin: 4.2vw auto 0;
		width: 90vw;
		height: auto;
		position: relative;
		padding-top: 56.25%;
	}

	.column-single .movie iframe {
		position: absolute;
		top: 0;
		left: 0;
	}

	.column-single .column-kv {
		margin: 4.2vw auto 0;
		width: 90vw;
	}

	.column-single .txt-area {
		padding: 8.42vw 0 15.8vw;
		width: 90vw;
		text-align: left;
	}

	.column-single h3 {
		padding-bottom: 9.2vw;
		background: url(./dot.png) repeat-x bottom 4.6vw left;
		background-size: auto 0.79vw;
		font-size: 5.3vw;
		display: block;
	}

	.column-single .to-column-index {
		width: 90vw;
		height: 14vw;
		line-height: 14vw;
		border-radius: 7vw / 50%;
		font-size: 4.2vw;
		padding: 0 2.6vw;
		background: url(./tri-white.png) no-repeat right 4.2vw center #41C2D0;
		background-size: auto 2.6vw;
	}

	.column-single .to-column-index .icon {
		width: 11.84vw;
		margin: 0 6.58vw 0.8vw 0;
	}

	.appli-download {
		padding: 12vw 0 10vw;
	}

	.appli-download h2 {
		font-size: 5.3vw;
		margin-bottom: 4vw;
		position: relative;
		padding: 0 9.5vw;
		display: inline-block;
		line-height: 1.8;
	}

	.appli-download h2:before {
		content: "";
		background: url(./appli_dl_title_before_sp.png) no-repeat center center;
		background-size: 100% auto;
		display: inline-block;
		vertical-align: bottom;
		width: 5vw;
		height: 12vw;
		position: absolute;
		bottom: 0;
		left: 0;
	}

	.appli-download h2:after {
		content: "";
		background: url(./appli_dl_title_after_sp.png) no-repeat center center;
		background-size: 100% auto;
		display: inline-block;
		vertical-align: bottom;
		width: 5vw;
		height: 12vw;
		position: absolute;
		bottom: 0;
		right: 0;
	}

	.appli-download .detail {
		border-radius: 20px;
		width: 88vw;
		padding: 4vw 2vw 4vw 5.5vw;
		margin: 0 auto 5vw;
		text-align: left;
	}

	.appli-download .detail p {
		margin-bottom: 1.6vw;
		font-size: 2.89vw;
	}

	.appli-download .detail .box {
		border-radius: 12px;
		min-width: 43vw;
		font-size: 5.3vw;
		height: 8.8vw;
		line-height: 8.8vw;
		margin: 2.4vw 0 0 0;
		text-align: center;
		padding-top: 1.2vw;
	}

	.appli-download .detail .box img {
		height: 5.6vw;
	}

	.appli-download .detail .code {
		font-size: 5.3vw;
		margin-top: 2.4vw;
		padding: 0 0 0 5vw;
	}

	.appli-download .detail .code img {
		height: 4.96vw;
	}

	.appli-download .detail .code + .box {
		margin-left: 0;
	}

	.appli-download .device-link {
		font-size: 3.4vw;
		width: 44vw;
		box-shadow: 5px 5px 10px 0px #333333;
		height: auto;
		line-height: 1.2;
		padding: 6vw 3vw 5.5vw;
		background: #fff;
		cursor: initial;
	}

	.appli-download .device-link + .device-link {
		margin-left: 2vw;
	}

	.appli-download .device-link span {
		padding-bottom: 8vw;
		position: relative;
		display: block;
		font-weight: bold;
	}

	.appli-download .device-link span:after {
		content: "";
		position: absolute;
		bottom: calc(4vw - 2px);
		right: 0;
		display: block;
		background: #41C2D0;
		height: 4px;
		width: 100%;
		border-radius: 2px;
	}

	.appli-download .device-link img {
		height: 10.5vw;
	}

	.appli-download .device-link .note {
		font-size: 2.63vw;
		line-height: 1.33;
		text-align: left;
		margin: 3.16vw auto 0;
		height: 33.3vw;
		text-align: left;
	}

	.appli-download .device-link .note li {
		padding-left: 1em;
		text-indent: -1em;
	}

	footer {
                height: 15vw;
                font-size: 3.7vw
        }

        .footer-menu-list {
                list-style: none;
                padding-bottom: 15px;
                display: block;
                align-items: center;
                font-size: 10px;
                justify-content: center;
        }

}

@media screen and (min-width: 768px) and (max-width: 1140px) {
	body {
		font-size: 1.455vw;
	}

	header {
		height: 8.182vw;
	}

	header .common-inner {
		padding-top: 1.636vw;
	}

	header h1.logo {
		width: 23.818vw;
		margin-left: 2vw;
	}

	header .download-link {
		height: 5.091vw;
		font-size: 1.818vw;
		height: 5.091vw;
		border: 0.364vw solid #41C2D0;
		background: url(./tri02.png) no-repeat right 2.7vw center #fff;
		background-size: auto 1.63vw ;
		line-height: 4.364vw;
		padding: 0 6vw 0 3vw;
		border-radius: 3.636vw;
		margin-left: 4.091vw;
	}

	.navi-bar {
		padding-top: 8.108vw;
	}

	.navi-bar ul {
		height: 5.045vw;
	}

	.navi-bar li a {
		line-height: 5.045vw;
	}

	.navi-bar li + li {
		margin-left: 7.207vw;
	}

	.navi-bar .active {
		color: #fff;
		line-height: 5.045vw;
	}

	.navi-bar .active:after {
		content: "";
		display: block;
		width: 1.441vw;
		height: 0.18vw;
		position: absolute;
		bottom: 1.081vw;
		left: 50%;
		margin-left: -0.721vw;
	}

	.column-index {
		padding-bottom: 7.207vw;
	}

	.column-index h2 {
		font-size: 3.604vw;
		padding: 0 0.901vw;
		border-bottom: 0.36vw solid #41C2D0;
		margin-top: 4.054vw;
		margin-bottom: 1.802vw;
	}

	.column-index .main-list {
		width: 88.288vw;
	}

	.column-index .main-list li {
		border-bottom: 0.36vw solid #41C2D0;
		min-height: 20.721vw;
	}

	.column-index .main-list li a {
		padding: 2.162vw 0 1.802vw 32.432vw;
	}

	.column-index .main-list li:after {
		content: "";
		display: block;
		width: 2.162vw;
		height: 1.982vw;
		background: url(./line-end.png) no-repeat center top;
		background-size: 100% auto;
		position: absolute;
		bottom: 0;
		right: 0;
	}

	.column-index .main-list .thumb {
		width: 28.829vw;
		height: 16.216vw;
		position: absolute;
		left: 0;
		top: 1.802vw;
	}

	.column-index .main-list time {
		font-size: 1.441vw;
	}

	.column-index .main-list h3 {
		font-size: 2.162vw;
		padding-top: 0.901vw;
		padding-bottom: 3.604vw;
		background: url(./dot.png) repeat-x bottom 1.98vw left;
		background-size: auto 0.36vw;
	}

	.column-index .main-list .desc {
		font-size: 1.802vw;
		height: 8.6496vw;
	}

	.column-pager {
		font-size: 2.162vw;
		margin: 4.054vw auto 0;
	}

	.column-pager .back {
		width: 1.622vw;
		height: 1.441vw;
		margin-right: 1.982vw;
	}

	.column-pager .separater {
		padding: 0 0.631vw;
	}

	.column-pager .next {
		width: 1.622vw;
		height: 1.441vw;
		margin-left: 1.982vw;
	}


	/* --- single --------------------- */
	.column-single {
		padding-bottom: 7.207vw;
	}

	.column-single .bg-colored {
		padding: 3.604vw 0;
	}

	.column-single .bg-colored h2 {
		font-size: 3.604vw;
		padding: 0 0.901vw;
		border-bottom: 0.36vw solid #41C2D0;
	}

	.column-single .movie {
		margin: 24px auto 0;
		width: 72.072vw;
		height: 40.54vw;
	}

	.column-single .txt-area {
		padding: 3.604vw 0;
		width: 72.072vw;
	}

	.column-single h3 {
		padding-bottom: 3.604vw;
		background: url(./dot.png) repeat-x bottom 1.982vw left;
		background-size: auto 0.36vw;
		font-size: 2.162vw;
	}

	.column-single .to-column-index {
		width: 34.234vw;
		height: 5.045vw;
		line-height: 5.045vw;
		border-radius: 2.5225vw / 50%;
		font-size: 1.802vw;
		padding: 0 2.162vw;
		background: url(./tri-white.png) no-repeat right 2.16vw center #41C2D0;
		background-size: auto 1.62vw;
	}

	.column-single .to-column-index .icon {
		width: 5.045vw;
		margin: 0 0.721vw 0.36vw 0;
	}


	.appli-download {
		padding: 4.545vw 0 3.182vw;
	}

	.appli-download h2 {
		font-size: 2.909vw;
		margin-bottom: 2.273vw;
	}

	.appli-download h2:before {
		content: "";
		background: url(./appli_dl_title_before.png) no-repeat center center;
		background-size: 100% auto;
		width: 2.545vw;
		height: 2.545vw;
		margin-right: 2.727vw;
	}

	.appli-download h2:after {
		content: "";
		background: url(./appli_dl_title_after.png) no-repeat center center;
		background-size: 100% auto;
		width: 2.545vw;
		height: 2.545vw;
		margin-left: 2.252vw;
	}

	.appli-download .detail {
		border-radius: 1.818vw;
		width: 72vw;
		padding: 1.364vw 0;
		margin: 0 auto 3.45vw;
	}

	.appli-download .detail p {
		margin-bottom: 0.909vw;
	}

	.appli-download .detail .box {
		border-radius: 0.727vw;
		min-width: 15.273vw;
		margin-right: 1.364vw;
		font-size: 1.818vw;
		height: 3.273vw;
		padding-top: 0.18vw;
	}

	.appli-download .detail .box img {
		height: 1.98vw;
	}

	.appli-download .detail .code {
		font-size: 1.818vw;
	}

	.appli-download .detail .code img {
		height: 1.711vw;
	}

	.appli-download .detail .code + .box {
		margin-left: 4.545vw;
	}

	.appli-download .device-link {
		border-radius: 1.818vw;
		width: 34.545vw;
		box-shadow: 0.454vw 0.454vw 0.9vw 0 rgba(0, 0, 0, 0.4);
		font-size: 1.8vw;
	}

	.appli-download .device-link .btn {
		background: url(./tri.png) no-repeat right 2.7vw center;
		background-size: 1.63vw auto;
		height: 7.091vw;
		line-height: 7.091vw;
		padding-right: 2.727vw;
	}

	.appli-download .device-link.active .btn {
		background: url(./tri03.png) no-repeat right 2.7vw center;
		background-size: 1.63vw auto;
	}

	.appli-download .device-link + .device-link {
		margin-left: 2.727vw;
	}

	.appli-download .accordion {
		padding-bottom: 1.091vw;
	}

	.appli-download .accordion:before {
		content: "";
		display: block;
		background: #41C2D0;
		height: 0.364vw;
		width: 30.182vw;
		margin: 0 auto 1.36vw;
		border-radius: 0.182vw;
	}

	.appli-download .accordion .dl-btn {
		height: 4.727vw;
	}

	.appli-download .accordion .qr-code {
		width: 10.909vw;
		margin-left: 1.636vw;
	}

	footer {
                height: 6.909vw;
        }
        .footer-menu-list {
                list-style: none;
                padding-bottom: 15px;
                display: block;
                align-items: center;
                font-size: 10px;
        }
}

