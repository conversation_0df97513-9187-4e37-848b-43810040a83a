(function() {
  var supportsTouch;
  supportsTouch = 'ontouchstart' in window;
  return window.isSupportTouch = function() {
    return supportsTouch;
  };
})();


/*
    ヘッダーメニュー周り
 */

jQuery(function($) {
  var $header, $nav, pathname;
  $header = $('#header');
  $nav = $header.find('> .wrap nav');
  $header.find('.burger').on('click', function(e) {
    if ($nav.hasClass('open')) {
      return $nav.removeClass('open');
    } else {
      return $nav.addClass('open');
    }
  });
  pathname = location.pathname.replace('index.html', '');
  if (pathname === '/') {
    return $header.find('ul a[href="/"]').closest('div > ul > li').addClass('current-menu-item');
  } else {
    return $header.find('ul a').each(function(i, elem) {
      var $a, href;
      $a = $(elem);
      href = $a.attr('href').replace(location.origin, '').replace('index.html', '');
      if ((pathname.indexOf(href) !== -1) && href !== '/') {
        return $a.closest('div > ul > li').addClass('current-menu-item');
      }
    });
  }
});


/*
    フッターのメニュー周り
 */

jQuery(function($) {
  var $footer;
  $footer = $('#footer');
  return $footer.find('div  >ul >li >.sub-menu').each(function(i, elem) {
    var $a, $parent, $subMenu;
    $subMenu = $(elem);
    $parent = $subMenu.parent();
    $a = $subMenu.siblings('a');
    return $a.on('click', function(e) {
      if (isSupportTouch()) {
        e.preventDefault();
        if ($parent.hasClass('open')) {
          return $footer.find('div  >ul >li').removeClass('open');
        } else {
          $footer.find('div  >ul >li').removeClass('open');
          return $parent.addClass('open');
        }
      }
    });
  });
});


/*
    ちょっとスクロールしたら
    <header>と<footer>にクラスをつける
 */

jQuery(function($) {
  var $footer, $header, $target, $window, state;
  $window = $(window);
  $header = $('#header');
  $footer = $('#footer');
  state = '';
  $target = $header.add($footer);
  return $window.on('scroll', function(e) {
    var scrollTop;
    scrollTop = $window.scrollTop();
    if (60 < scrollTop) {
      if (state !== 'scrolled') {
        state = 'scrolled';
        $target.removeClass('no_scroll');
        return $target.addClass(state);
      }
    } else {
      if (state !== 'no_scroll') {
        state = 'no_scroll';
        $target.removeClass('scrolled');
        return $target.addClass(state);
      }
    }
  }).trigger('scroll');
});
