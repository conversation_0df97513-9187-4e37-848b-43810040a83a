@charset "utf-8";
@media screen and (max-width: 767px) {
  body header {
    zoom: 1;
    position: relative;
    z-index: 99;
    height: 48px;
    background-color: #fff;
    overflow: visible;
  }
  body header:before,
  body header:after {
    content: "";
    display: table;
  }
  body header:after {
    clear: both;
  }
  body header > .wrap {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #ededed;
    padding: 10px 0 10px 10px;
  }
  body header > .wrap h1 {

  }
  body header > .wrap h1 a {
    display: table-cell;
    height: 50px;
    vertical-align: middle;
    padding: 0 0 0 16px;
  }
  body header > .wrap h1 a svg {
    width: 82px;
    height: 20px;
  }
  body header.scrolled > nav {
    display: none;
  }
  body header.scrolled > .wrap nav {
    position: absolute;
    top: 50px;
    right: 0;
    background-color: #5acc87;
    width: 100%;
  }
  body header.scrolled > .wrap nav a {
    color: #fff;
    text-decoration: none;
  }
  body header.scrolled > .wrap nav > div {
    padding: 0 20px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    display: none;
  }
  body header.scrolled > .wrap nav > div > ul {
    padding-top: 50px;
    font-size: 0;
  }
  body header.scrolled > .wrap nav > div > ul > li {
    border-top: 1px solid #fff;
    background-position: left 9px;
    background-repeat: no-repeat;
    -webkit-background-size: 18px auto;
    -moz-background-size: 18px auto;
    background-size: 18px auto;
  }
  body header.scrolled > .wrap nav > div > ul > li:first-child {
    border-top: none;
  }
  body header.scrolled > .wrap nav > div > ul > li a {
    background-image: url("/content/themes/healthcare/images/common/arrow2.svg");
    background-position: right center;
    background-repeat: no-repeat;
    -webkit-background-size: 14px auto;
    -moz-background-size: 14px auto;
    background-size: 14px auto;
  }
  body header.scrolled > .wrap nav > div > ul > li > a {
    font-size: 14px;
    letter-spacing: 1px;
    display: block;
    padding: 11px 0 11px 35px;
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu {
    padding-left: 34px;
    font-size: 0;
    border-top: 1px solid rgba(255,255,255,0.4);
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu li {
    display: inline-block;
    width: 49.9%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu li:nth-child(even) {
    position: relative;
    padding-left: 10px;
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu li:nth-child(odd) {
    position: relative;
    padding-right: 10px;
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu li:nth-child(odd):after {
    position: absolute;
    top: 50%;
    right: 0;
    content: '';
    display: block;
    width: 1px;
    height: 13px;
    margin-top: -6px;
    background-color: rgba(255,255,255,0.4);
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu li:nth-child(odd):before {
    position: absolute;
    top: 0;
    left: 0;
    content: '';
    display: block;
    width: 200%;
    height: 1px;
    background-color: rgba(255,255,255,0.4);
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu li:first-child:before {
    display: none;
  }
  body header.scrolled > .wrap nav > div > ul > li > .sub-menu li a {
    display: block;
    font-size: 11px;
    padding: 13px 10px 13px 0;
  }
  body header.scrolled > .wrap nav > div > ul > li.home {
    background-image: url("/content/themes/healthcare/images/common/icon1-1.svg");
  }
  body header.scrolled > .wrap nav > div > ul > li.products {
    background-image: url("/content/themes/healthcare/images/common/icon2-1.svg");
  }
  body header.scrolled > .wrap nav > div > ul > li.column {
    background-image: url("/content/themes/healthcare/images/common/icon3-1.svg");
  }
  body header.scrolled > .wrap nav > div > ul > li.corporate {
    background-image: url("/content/themes/healthcare/images/common/icon4-1.svg");
  }
  body header.scrolled > .wrap nav > div > ul > li.business {
    background-image: url("/content/themes/healthcare/images/common/icon5-1.svg");
  }
  body header.scrolled > .wrap nav .contact {
    display: none;
    border: 1px solid #fff;
    border-radius: 5px;
    font-size: 11px;
    text-align: center;
    margin: 0 20px 50px;
  }
  body header.scrolled > .wrap nav .contact span {
    padding: 10px 0;
    display: inline-block;
  }
  body header.scrolled > .wrap nav .contact span:first-child {
    background-image: url("/content/themes/healthcare/images/common/icon6-1.svg");
    background-position: left center;
    background-repeat: no-repeat;
    -webkit-background-size: 20px auto;
    -moz-background-size: 20px auto;
    background-size: 20px auto;
    padding-left: 26px;
  }
  body header.scrolled > .wrap nav .contact span:first-child:after {
    content: '・';
  }
  body header.scrolled > .wrap nav .burger {
    position: fixed;
    top: 0;
    right: 0;
    height: 50px;
    width: 90px;
    border-left: 1px solid #ededed;
    font-size: 0;
    background-color: #fff;
    background-image: url("/content/themes/healthcare/images/common/icon7.svg");
    background-position: center center;
    background-repeat: no-repeat;
    -webkit-background-size: 18px auto;
    -moz-background-size: 18px auto;
    background-size: 18px auto;
  }
  body header.scrolled > .wrap nav.open > div {
    display: block;
  }
  body header.scrolled > .wrap nav.open .contact {
    display: block;
  }
  body header.scrolled > .wrap nav.open .burger {
    background-image: url("/content/themes/healthcare/images/common/icon8.svg");
    border-left: 1px solid #fff;
    background-color: #ddd;
  }
  body header.scrolled .searchWrap {
    display: none;
  }
  body header.no_scroll > .wrap nav div {
    display: none;
  }
  body header.no_scroll > .wrap nav .contact {
    position: fixed;
    top: 0;
    right: 90px;
    background-repeat: no-repeat;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    font-size: 10px;
    text-decoration: none;
    color: #222;
    font-weight: bold;
    display: block;
    letter-spacing: 1px;
    line-height: 12px;
    height: 50px;
    width: 90px;
    background-image: url("/content/themes/healthcare/images/common/icon6.svg");
    background-color: #5acc87;
    -webkit-background-size: 23px auto;
    -moz-background-size: 23px auto;
    background-size: 23px auto;
    background-position: center 4px;
    color: #fff;
    padding-top: 24px;
  }
  body header.no_scroll > .wrap nav .contact span {
    display: block;
  }
  body header.no_scroll > .wrap nav .burger {
    display: none;
  }
  body header.no_scroll > nav {
    position: absolute;
    top: 50px;
    left: 0;
    z-index: 1;
    background-color: #fff;
    border-bottom: 1px solid #ededed;
    width: 100%;
  }
  body header.no_scroll > nav > div {
    font-size: 0;
  }
  body header.no_scroll > nav > div > ul {
    display: table;
    table-layout: fixed;
    width: 100%;
  }
  body header.no_scroll > nav > div > ul > li {
    display: table-cell;
    border-left: 1px solid #ededed;
  }
  body header.no_scroll > nav > div > ul > li.current-menu-item > a,
  body header.no_scroll > nav > div > ul > li.current-menu-ancestor > a,
  body header.no_scroll > nav > div > ul > li.current-menu-parent > a {
    position: relative;
  }
  body header.no_scroll > nav > div > ul > li.current-menu-item > a:after,
  body header.no_scroll > nav > div > ul > li.current-menu-ancestor > a:after,
  body header.no_scroll > nav > div > ul > li.current-menu-parent > a:after {
    position: absolute;
    bottom: 0;
    left: 50%;
    content: '';
    display: block;
    width: 45%;
    height: 6px;
    background-color: #5acc87;
    border-radius: 4px 4px 0 0;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
  }
  body header.no_scroll > nav > div > ul > li > a {
    background-repeat: no-repeat;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    font-size: 10px;
    text-decoration: none;
    color: #222;
    font-weight: bold;
    display: block;
    letter-spacing: 1px;
    line-height: 12px;
    background-position: center 12px;
    -webkit-background-size: 27px auto;
    -moz-background-size: 27px auto;
    background-size: 27px auto;
    height: 72px;
    padding-top: 40px;
  }
  body header.no_scroll > nav > div > ul > li > a.open {
    background-color: #f2f2f0;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu {
    display: none;
    position: absolute;
    top: 72px;
    left: 0;
    width: 100%;
    background-color: #5acc87;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open {
    display: block;
    width: 100%;
    background-color: #5acc87;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner {
    padding: 23px 0 22px;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .categoryTitle {
    padding: 0 20px;
    font-size: 0;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .categoryTitle .en {
    padding-right: 12px;
    font-size: 15px;
    font-weight: bold;
    letter-spacing: 0.025em;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .categoryTitle .ja {
    font-size: 11px;
    font-weight: bold;
    letter-spacing: 0.025em;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList {
    margin: 14px 20px 0;
    padding: 0 0 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-top: 1px solid #fff;
    overflow: hidden;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul {
    font-size: 0;
    margin: 0 0;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li {
    display: inline-block;
    vertical-align: bottom;
    text-align: left;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 0;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a {
    position: relative;
    display: block;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 0;
    width: 14px;
    height: 12px;
    background-image: url("/content/themes/healthcare/images/common/arrow2.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 14px auto;
    -moz-background-size: 14px auto;
    background-size: 14px auto;
    margin-top: -6px;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .thumbnail {
    display: none;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock {
    border-bottom: 1px solid #bdebcf;
    font-size: 0;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock .contentsTitle {
    display: inline-block;
    padding: 13px 18px 13px 0;
    font-size: 10px;
    font-weight: bold;
    line-height: 1.2;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock > p {
    display: inline-block;
    padding: 13px 0 13px;
    text-align: left;
    vertical-align: top;
    font-size: 9px;
    font-weight: normal;
    line-height: 1.2;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory {
    margin: 17px 20px 0;
    padding: 14px 0 0;
    border-top: 1px dotted #d6f2e1;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn {
    position: relative;
    position: relative;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    border: 1px solid rgba(34,34,34,0.2);
    border-radius: 4px;
    display: block;
    display: block;
    padding: 11px 13px;
    text-align: center;
    color: #fff;
    font-size: 14px;
    border: 1px solid #fff;
    border-radius: 4px;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 14px;
    width: 13px;
    height: 11px;
    background-image: url("/content/themes/healthcare/images/common/arrow.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 13px 11px;
    -moz-background-size: 13px 11px;
    background-size: 13px 11px;
    margin-top: -6px;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 12px;
    width: 14px;
    height: 12px;
    background-image: url("/content/themes/healthcare/images/common/arrow2.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 14px auto;
    -moz-background-size: 14px auto;
    background-size: 14px auto;
    margin-top: -6px;
  }
  body header.no_scroll > nav > div > ul > li > .sub-menu.open .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    width: 32px;
    height: 32px;
    top: 0;
    right: 0;
    font-size: 0;
    background-color: #fff;
    background-image: url("/content/themes/healthcare/images/common/icon8.svg");
    background-position: center center;
    background-repeat: no-repeat;
    -webkit-background-size: 15px auto;
    -moz-background-size: 15px auto;
    background-size: 15px auto;
    cursor: pointer;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList {
    margin: 14px 20px 0;
    padding: 13px 0 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-top: 1px solid #fff;
    overflow: hidden;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul {
    font-size: 0;
    margin: -7px -7px;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul > li {
    display: inline-block;
    vertical-align: top;
    text-align: left;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 7px 7px;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul > li a {
    display: table;
    width: 100%;
    font-size: 0;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul > li a:after {
    content: '';
    display: none;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul > li a .thumbnail {
    display: table-cell;
    width: 100px;
    vertical-align: top;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul > li a .thumbnail img {
    width: 100%;
    height: auto;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul > li a .textBlock {
    display: table-cell;
    vertical-align: top;
    padding-left: 12px;
    border-bottom: 0;
  }
  body header.no_scroll > nav > div > ul > li.column > .sub-menu.open .sub-menuInner .subMenuList > ul > li a .textBlock > p {
    display: block;
    padding: 0 0 0 0;
    text-align: left;
    vertical-align: top;
    font-size: 10px;
    font-weight: normal;
    letter-spacing: 0.05em;
    line-height: 1.571;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList {
    margin: 14px 20px 0;
    padding: 0 0 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-top: 1px solid #fff;
    overflow: hidden;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul {
    margin: 0 0;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li {
    width: 100%;
    padding: 0 0;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock .contentsTitle {
    display: inline-block;
    padding: 13px 18px 13px 0;
    font-size: 10px;
    font-weight: bold;
    line-height: 1.2;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock .contentsTitle .small {
    font-size: 9px;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock > p {
    display: none;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner {
    margin: 0 auto 0;
    padding: 0 0 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul {
    font-size: 0;
    margin: 0 0;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li {
    display: inline-block;
    vertical-align: top;
    text-align: left;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 0;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a {
    position: relative;
    display: block;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 0;
    width: 14px;
    height: 12px;
    background-image: url("/content/themes/healthcare/images/common/arrow2.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 14px auto;
    -moz-background-size: 14px auto;
    background-size: 14px auto;
    margin-top: -6px;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .thumbnail {
    display: none;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .textBlock {
    border-bottom: 1px solid #bdebcf;
    font-size: 0;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .textBlock .contentsTitle {
    display: inline-block;
    padding: 13px 10px 13px 0;
    font-size: 10px;
    font-weight: bold;
    line-height: 1.2;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li.business > .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .textBlock > p {
    display: inline-block;
    padding: 13px 18px 13px 0;
    text-align: left;
    vertical-align: top;
    font-size: 9px;
    font-weight: normal;
    line-height: 1.2;
    color: #fff;
  }
  body header.no_scroll > nav > div > ul > li.corporate > .sub-menu.open .sub-menuInner .subMenuList {
    margin: 14px 20px 0;
  }
  body header.no_scroll > nav > div > ul > li.corporate > .sub-menu.open .sub-menuInner .subMenuList > ul {
    margin: 0 -5px;
  }
  body header.no_scroll > nav > div > ul > li.corporate > .sub-menu.open .sub-menuInner .subMenuList > ul > li {
    width: 49.9%;
    padding: 0 5px;
  }
  body header.no_scroll > nav > div > ul > li.products > a {
    font-size: 9px;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList {
    margin: 14px 20px 0;
    padding: 0 0 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-top: 1px solid #fff;
    overflow: hidden;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul {
    font-size: 0;
    margin: 0 -5px;
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: box;
    display: flex;
    box-lines: multiple;
    -webkit-box-lines: multiple;
    -moz-box-lines: multiple;
    -o-box-lines: multiple;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li {
    display: inline-block;
    vertical-align: bottom;
    text-align: left;
    width: 49.9%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 5px;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a {
    position: relative;
    display: block;
    height: 100%;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 0;
    width: 14px;
    height: 12px;
    background-image: url("/content/themes/healthcare/images/common/arrow2.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 14px auto;
    -moz-background-size: 14px auto;
    background-size: 14px auto;
    margin-top: -6px;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .textBlock {
    border-bottom: 1px solid #bdebcf;
    font-size: 0;
    width: 100%;
    height: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    display: table;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .textBlock .contentsTitle {
    display: table-cell;
    padding: 13px 18px 13px 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 10px;
    font-weight: bold;
    line-height: 1.2;
    color: #fff;
    vertical-align: middle;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .textBlock > p {
    display: none;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts {
    margin-top: 25px;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList {
    margin: 14px 20px 0;
    padding: 0 0 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-top: 1px solid #fff;
    overflow: hidden;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul {
    font-size: 0;
    margin: 0 0;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li {
    display: inline-block;
    vertical-align: bottom;
    text-align: left;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 0;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li > a {
    position: relative;
    display: block;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li > a:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 0;
    width: 14px;
    height: 12px;
    background-image: url("/content/themes/healthcare/images/common/arrow2.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 14px auto;
    -moz-background-size: 14px auto;
    background-size: 14px auto;
    margin-top: -6px;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li > a .textBlock {
    border-bottom: 1px solid #bdebcf;
    font-size: 0;
    display: table;
    width: 100%;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li > a .textBlock .contentsTitle {
    display: table-cell;
    width: 8em;
    padding: 13px 0 13px;
    font-size: 10px;
    font-weight: bold;
    padding-right: 8px;
    line-height: 1.2;
    vertical-align: middle;
    color: #fff;
    white-space: nowrap;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body header.no_scroll > nav > div > ul > li.products > .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li > a .textBlock > p {
    display: table-cell;
    padding: 13px 18px 13px 0;
    text-align: left;
    vertical-align: middle;
    font-size: 9px;
    font-weight: normal;
    line-height: 1.2;
    color: #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body header.no_scroll > nav > div > ul > li.home > a {
    background-image: url("/content/themes/healthcare/images/common/icon1.svg");
  }
  body header.no_scroll > nav > div > ul > li.products > a {
    background-image: url("/content/themes/healthcare/images/common/icon2.svg");
  }
  body header.no_scroll > nav > div > ul > li.column > a {
    background-image: url("/content/themes/healthcare/images/common/icon3.svg");
  }
  body header.no_scroll > nav > div > ul > li.corporate > a {
    background-image: url("/content/themes/healthcare/images/common/icon4.svg");
  }
  body header.no_scroll > nav > div > ul > li.business > a {
    background-image: url("/content/themes/healthcare/images/common/icon5.svg");
  }
  body header.no_scroll > nav > .contact {
    display: none;
  }
  body header .searchWrap a {
    position: fixed;
    top: 0;
    right: 0;
    background-repeat: no-repeat;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    font-size: 10px;
    text-decoration: none;
    color: #222;
    font-weight: bold;
    display: block;
    letter-spacing: 1px;
    line-height: 12px;
    width: 90px;
    height: 50px;
    background-color: #5acc87;
    background-image: url("/content/themes/healthcare/images/common/icon11.svg");
    -webkit-background-size: 23px auto;
    -moz-background-size: 23px auto;
    background-size: 23px auto;
    background-position: center 4px;
    padding-top: 28px;
    color: #fff;
    z-index: 15;
    border-left: 1px solid #8ddbac;
  }
  body header .searchWrap a:hover,
  body header .searchWrap a:link,
  body header .searchWrap a:visited,
  body header .searchWrap a:active {
    color: #fff;
  }
  body header .searchWrap a.open {
    background-color: #8cdbab;
  }
  body header .searchWrap > .searchForm {
    position: fixed;
    top: 51px;
    right: 0;
    display: none;
    width: 100%;
    height: 57px;
    margin: 0;
    background-color: #f2f2f0;
    overflow: hidden;
    padding: 0 10px 0 45px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background-image: url("/content/themes/healthcare/images/common/icon12.svg");
    background-position: 15px center;
    background-repeat: no-repeat;
    z-index: 9;
  }
  body header .searchWrap > .searchForm.open {
    display: block;
  }
  body header .searchWrap > .searchForm form > table {
    width: 100%;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr {
    height: 57px;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr .input-box {
    height: 21px;
    border: 0;
    padding: 5px;
    font-size: 16px;
    border: solid 1px #ccc;
    width: 90%;
    border-radius: 3px;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr .search-button {
    position: relative;
    padding: 0 5px;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr .search-button input {
    -webkit-appearance: none;
    display: block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 31px;
    background-color: #24b05c;
    border-radius: 3px;
    display: block;
    color: #fff;
    width: 100%;
    text-align: center;
    letter-spacing: 1px;
    font-size: 12px;
    border-style: none;
  }
  body header .pc {
    display: none !important;
  }
  body.business header {
    zoom: 1;
    position: relative;
    z-index: 99;
    height: 50px;
    background-color: #fff;
    overflow: visible;
  }
  body.business header:before,
  body.business header:after {
    content: "";
    display: table;
  }
  body.business header:after {
    clear: both;
  }
  body.business header >.headerInner>.wrap {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #ededed;
  }
  body.business header >.headerInner>.wrap h1 {
    display: table;
  }
  body.business header >.headerInner>.wrap h1 a {
    display: table-cell;
    height: 50px;
    vertical-align: middle;
    text-align: center;
    padding: 0 16px;
  }
  body.business header >.headerInner>.wrap h1 a svg {
    width: 88px;
    height: 34px;
  }
  body.business header >.headerInner>.wrap nav {
    position: absolute;
    top: 50px;
    right: 0;
    background-color: #3578d0;
    width: 100%;
  }
  body.business header >.headerInner>.wrap nav a {
    color: #fff;
    text-decoration: none;
  }
  body.business header >.headerInner>.wrap nav > div {
    padding: 0 20px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    display: none;
  }
  body.business header >.headerInner>.wrap nav > div > ul {
    padding: 50px 0 25px;
    font-size: 0;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li {
    border-top: 1px solid #fff;
    background-position: left 9px;
    background-repeat: no-repeat;
    -webkit-background-size: 18px auto;
    -moz-background-size: 18px auto;
    background-size: 18px auto;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li:first-child {
    border-top: none;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li a {
    background-image: url("/content/themes/healthcare/images/common/arrow2.svg");
    background-position: right center;
    background-repeat: no-repeat;
    -webkit-background-size: 14px auto;
    -moz-background-size: 14px auto;
    background-size: 14px auto;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > a {
    font-size: 14px;
    letter-spacing: 1px;
    display: block;
    padding: 11px 0 11px 5px;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu {
    padding-left: 34px;
    font-size: 0;
    border-top: 1px solid rgba(255,255,255,0.4);
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu li {
    display: inline-block;
    width: 49.9%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu li:nth-child(even) {
    position: relative;
    padding-left: 10px;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu li:nth-child(odd) {
    position: relative;
    padding-right: 10px;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu li:nth-child(odd):after {
    position: absolute;
    top: 50%;
    right: 0;
    content: '';
    display: block;
    width: 1px;
    height: 13px;
    margin-top: -6px;
    background-color: rgba(255,255,255,0.4);
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu li:nth-child(odd):before {
    position: absolute;
    top: 0;
    left: 0;
    content: '';
    display: block;
    width: 200%;
    height: 1px;
    background-color: rgba(255,255,255,0.4);
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu li:first-child:before {
    display: none;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li > .sub-menu li a {
    display: block;
    font-size: 11px;
    padding: 13px 10px 13px 0;
  }
  body.business header >.headerInner>.wrap nav > div > ul > li.home {
  /**background-image: url("/content/themes/healthcare/images/common/icon1-1.svg");**/
  }
  body.business header >.headerInner>.wrap nav > div > ul > li.products {
    /**background-image: url("/content/themes/healthcare/images/common/icon2-1.svg");**/
  }
  body.business header >.headerInner>.wrap nav > div > ul > li.seminar {
    /**background-image: url("/content/themes/healthcare/images/common/icon2-1.svg");**/
  }
  body.business header >.headerInner>.wrap nav > div > ul > li.column {
    /**background-image: url("/content/themes/healthcare/images/common/icon3-1.svg");**/
  }
  body.business header >.headerInner>.wrap nav > div > ul > li.corporate {
    /**background-image: url("/content/themes/healthcare/images/common/icon4-1.svg");**/
  }
  body.business header >.headerInner>.wrap nav > div > ul > li.business,
  body.business header >.headerInner>.wrap nav > div > ul > li.casestudy {
    /**background-image: url("/content/themes/healthcare/images/common/icon5-1.svg");**/
  }
  body.business header >.headerInner>.wrap nav > div > ul > li.contact2 {
    /**background-image: url("/content/themes/healthcare/images/common/icon6-1.svg");**/
  }
  body.business header >.headerInner>.wrap nav .burger {
    position: fixed;
    top: 0;
    right: 0;
    height: 50px;
    width: 62px;
    border-left: 1px solid #ededed;
    font-size: 0;
    background-color: #fff;
    background-image: url("/content/themes/healthcare/images/common/icon16.svg");
    background-position: center center;
    background-repeat: no-repeat;
    -webkit-background-size: 18px auto;
    -moz-background-size: 18px auto;
    background-size: 18px auto;
  }
  body.business header >.headerInner>.wrap nav .contact {
    position: fixed;
    top: 0;
    right: 63px;
    height: 50px;
    width: 72px;
    border-left: 1px solid #ededed;
    font-size: 10px;
    background-color: #216bcc;
    background-image: url("/content/themes/healthcare/images/common/icon6-1.svg");
    background-position: center 6px;
    background-repeat: no-repeat;
    -webkit-background-size: 25px auto;
    -moz-background-size: 25px auto;
    background-size: 25px auto;
  }
  body.business header >.headerInner>.wrap nav .contact span {
    padding: 32px 0 0;
    display: block;
    text-align: center;
  }
  body.business header >.headerInner>.wrap nav.open > div {
    display: block;
  }
  body.business header >.headerInner>.wrap nav.open .burger {
    background-image: url("/content/themes/healthcare/images/common/icon17.svg");
    border-left: 1px solid #fff;
    background-color: #ddd;
  }
  body.business header.no_scroll >.headerInner>.wrap nav .burger {
    display: block;
  }
}
@media print, screen and (min-width: 768px) {
  body header {
    height: auto;
  }
  body header {
    max-width: 1600px;
    min-width: 1000px;
    width: 100%;
    margin: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    zoom: 1;
    position: relative;
    z-index: 99;
    font-size: 30px;
    background-color: #fff;
    border-bottom: 1px solid #ededed;
  }
  body header:before,
  body header:after {
    content: "";
    display: table;
  }
  body header:after {
    clear: both;
  }
  body header h1 {
    width: 20%;
padding: 20px 0 20px 20px;
  }
  body header h1 a {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
  }
  body header h1 a svg {
    width: 153px;
    height: 38px;
  }
  body header nav {
    margin-left: 30%;
  }
  body header nav > div {
    margin-right: 120px;
  }
  body header nav > div > ul {
    display: table;
    table-layout: fixed;
    width: 100%;
  }
  body header nav > div > ul > li {
    display: table-cell;
    border-left: 1px solid #ededed;
    height: 100px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body header nav > div > ul > li.current-menu-item > a:after,
  body header nav > div > ul > li.current-menu-ancestor > a:after,
  body header nav > div > ul > li.current-menu-parent > a:after {
    position: absolute;
    bottom: 0;
    left: 50%;
    content: '';
    display: block;
    width: 45%;
    height: 6px;
    background-color: #5acc87;
    border-radius: 4px 4px 0 0;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
  }
  body header nav > div > ul > li > a {
    background-repeat: no-repeat;
    background-position: center top 25px;
    -webkit-background-size: 33px auto;
    -moz-background-size: 33px auto;
    background-size: 33px auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    font-size: 13px;
    text-decoration: none;
    color: #222;
    font-weight: bold;
    padding: 62px 5px 0;
    display: block;
    letter-spacing: 1px;
    line-height: 16px;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
    -webkit-transition-property: background-color;
    -moz-transition-property: background-color;
    -o-transition-property: background-color;
    -ms-transition-property: background-color;
    transition-property: background-color;
    position: relative;
    height: 99px;
  }
  body header nav > div > ul > li > a:hover,
  body header nav > div > ul > li > a:link,
  body header nav > div > ul > li > a:visited,
  body header nav > div > ul > li > a:active {
    color: #222;
  }
  body header nav > div > ul > li > a:hover {
    background-color: #cfebd9;
  }
  body header nav > div > ul > li > a.open {
    background-color: #f2f2f0;
  }
  body header nav > div > ul > li > .sub-menu {
    display: none;
  }
  body header nav > div > ul > li > .sub-menu.open {
    display: block;
    position: absolute;
    top: 100px;
    left: 0;
    width: 100%;
    background-color: #5acc87;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner {
    width: 1000px;
    margin: 0 auto;
    padding: 32px 0 33px;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .categoryTitle {
    font-size: 0;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .categoryTitle .en {
    padding-right: 10px;
    font-size: 20px;
    font-weight: bold;
    letter-spacing: 0.025em;
    color: #fff;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .categoryTitle .ja {
    font-size: 11px;
    font-weight: bold;
    letter-spacing: 0.025em;
    color: #fff;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList {
    margin: 19px auto 0;
    padding: 29px 0 0;
    border-top: 1px solid #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul {
    font-size: 0;
    margin: -20px -17px;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li {
    display: inline-block;
    vertical-align: top;
    text-align: left;
    width: 50%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 20px 17px;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li a {
    display: table;
    width: 100%;
    font-size: 0;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li a:hover {
    opacity: 0.85;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
    filter: alpha(opacity=85);
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li a .thumbnail {
    display: table-cell;
    width: 200px;
    vertical-align: top;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li a .textBlock {
    display: table-cell;
    width: 200px;
    padding-left: 22px;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .subMenuList > ul > li a .textBlock > p {
    text-align: left;
    vertical-align: top;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: 0.05em;
    line-height: 1.571;
    color: #fff;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory {
    margin-top: 33px;
    border-top: 1px dotted #fff;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn {
    position: relative;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    border: 1px solid rgba(34,34,34,0.2);
    border-radius: 4px;
    display: block;
    display: block;
    margin-top: 33px;
    padding: 14px 20px;
    text-align: center;
    color: #fff;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
    font-size: 14px;
    border: 1px solid #fff;
    border-radius: 4px;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 14px;
    width: 13px;
    height: 11px;
    background-image: url("/content/themes/healthcare/images/common/arrow.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 13px 11px;
    -moz-background-size: 13px 11px;
    background-size: 13px 11px;
    margin-top: -6px;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn:after {
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
    border-color: #fff;
    background-image: url("/content/themes/healthcare/images/common/arrow6.svg");
    -webkit-background-size: 12px auto;
    -moz-background-size: 12px auto;
    background-size: 12px auto;
    right: 22px;
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn:hover {
    opacity: 0.85;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
    filter: alpha(opacity=85);
  }
  body header nav > div > ul > li > .sub-menu.open .sub-menuInner .linkCategory > a.btn:hover:after {
    right: 19px;
  }
  body header nav > div > ul > li > .sub-menu.open .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    width: 50px;
    height: 50px;
    top: 0;
    right: 0;
    font-size: 0;
    background-color: #fff;
    background-image: url("/content/themes/healthcare/images/common/icon8.svg");
    background-position: center center;
    background-repeat: no-repeat;
    -webkit-background-size: 15px auto;
    -moz-background-size: 15px auto;
    background-size: 15px auto;
    cursor: pointer;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
  }
  body header nav > div > ul > li > .sub-menu.open .close:hover {
    opacity: 0.85;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
    filter: alpha(opacity=85);
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList {
    padding: 29px 0 0;
    border-top: 1px solid #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul {
    margin: -20px -17px;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li {
    width: 100%;
    padding: 20px 17px;
    display: table;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li > a {
    display: table-cell;
    width: 24%;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock {
    padding-left: 0;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li > a > .thumbnail {
    display: block;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .contentsTitle {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    padding-top: 12px;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li > a > p {
    margin-top: 8px;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner {
    margin: 0 auto 0;
    padding: 0 0 0 41px;
    border-left: 1px dotted #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul {
    font-size: 0;
    margin: -20px -17px;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li {
    display: inline-block;
    vertical-align: top;
    text-align: left;
    width: 50%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 20px 17px 0;
    border-right: 1px dotted #fff;
    min-height: 180px;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li:nth-of-type(1) {
    border-bottom: 1px dotted #fff;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li:nth-of-type(2) {
    border-bottom: 1px dotted #fff;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a {
    display: table;
    width: 100%;
    font-size: 0;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a:hover {
    opacity: 0.85;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
    filter: alpha(opacity=85);
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .thumbnail {
    display: table-cell;
    width: 120px;
    vertical-align: top;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .textBlock {
    display: table-cell;
    padding-left: 22px;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .textBlock .contentsTitle {
    font-size: 18px;
    line-height: 1.333;
    font-weight: bold;
    color: #fff;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .textBlock .contentsTitle .small {
    font-size: 16px;
  }
  body header nav > div > ul > li.business .sub-menu.open .sub-menuInner .subMenuList > ul > li .subMenuListInner > ul > li > a .textBlock > p {
    margin-top: 8px;
    text-align: left;
    vertical-align: top;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: 0.05em;
    line-height: 1.571;
    color: #fff;
  }
  body header nav > div > ul > li.corporate .sub-menu.open .sub-menuInner .subMenuList {
    padding: 29px 0 0;
    border-top: 1px solid #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
  }
  body header nav > div > ul > li.corporate .sub-menu.open .sub-menuInner .subMenuList > ul {
    margin: -16px -7px;
  }
  body header nav > div > ul > li.corporate .sub-menu.open .sub-menuInner .subMenuList > ul > li {
    width: 25%;
    padding: 16px 7px;
  }
  body header nav > div > ul > li.corporate .sub-menu.open .sub-menuInner .subMenuList > ul > li > a {
    display: block;
  }
  body header nav > div > ul > li.corporate .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .thumbnail {
    display: block;
    width: 239px;
  }
  body header nav > div > ul > li.corporate .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock {
    display: block;
    padding: 12px 0 0 0;
  }
  body header nav > div > ul > li.corporate .sub-menu.open .sub-menuInner .subMenuList > ul > li > a .textBlock .contentsTitle {
    font-size: 14px;
    font-weight: bold;
    line-height: 1.597;
    color: #fff;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain {
    zoom: 1;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain:before,
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain:after {
    content: "";
    display: table;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain:after {
    clear: both;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices {
    width: 620px;
    float: left;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList {
    padding: 12px 0 0;
    border-top: 1px solid #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul {
    margin: -5px -4px;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li {
    width: 50%;
    padding: 5px 4px;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a {
    position: relative;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-decoration: none;
    border: 1px solid rgba(34,34,34,0.2);
    border-radius: 4px;
    display: block;
    padding: 9px 33px 9px 9px;
    border: 1px solid #fff;
    border-radius: 4px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 14px;
    width: 13px;
    height: 11px;
    background-image: url("/content/themes/healthcare/images/common/arrow.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 13px 11px;
    -moz-background-size: 13px 11px;
    background-size: 13px 11px;
    margin-top: -6px;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a:after {
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
    border-color: #fff;
    background-image: url("/content/themes/healthcare/images/common/arrow6.svg");
    -webkit-background-size: 12px auto;
    -moz-background-size: 12px auto;
    background-size: 12px auto;
    right: 19px;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a:hover {
    opacity: 0.85;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
    filter: alpha(opacity=85);
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a:hover:after {
    right: 16px;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .thumbnail {
    width: 40px;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .textBlock {
    padding-left: 11px;
    vertical-align: middle;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .textBlock .contentsTitle {
    font-size: 15px;
    font-weight: bold;
    color: #fff;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .textBlock > p {
    margin-top: 2px;
    font-size: 11px;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaAppsAndServices .subMenuList > ul > li > a .textBlock > p.ls-001 {
    letter-spacing: -0.01em;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts {
    width: 350px;
    float: right;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList {
    padding: 12px 0 0;
    border-top: 1px solid #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul {
    margin: -5px 0;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li {
    width: 100%;
    padding: 5px 0;
  }
  body header nav > div > ul > li.products .sub-menu.open .sub-menuInner .sub-menuMain .areaProducts .subMenuList > ul > li > a .thumbnail {
    visibility: visible;
  }
  body header nav > div > ul > li.home > a {
    background-image: url("/content/themes/healthcare/images/common/icon1.svg");
  }
  body header nav > div > ul > li.products > a {
    background-image: url("/content/themes/healthcare/images/common/icon2.svg");
  }
  body header nav > div > ul > li.column > a {
    background-image: url("/content/themes/healthcare/images/common/icon3.svg");
  }
  body header nav > div > ul > li.corporate > a {
    background-image: url("/content/themes/healthcare/images/common/icon4.svg");
  }
  body header nav > div > ul > li.business > a {
    background-image: url("/content/themes/healthcare/images/common/icon5.svg");
  }
  body header nav > a {
    position: absolute;
    top: 0;
    right: 120px;
    background-repeat: no-repeat;
    background-position: center top 25px;
    -webkit-background-size: 33px auto;
    -moz-background-size: 33px auto;
    background-size: 33px auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    font-size: 13px;
    text-decoration: none;
    color: #222;
    font-weight: bold;
    padding: 62px 5px 0;
    display: block;
    letter-spacing: 1px;
    line-height: 16px;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
    -webkit-transition-property: background-color;
    -moz-transition-property: background-color;
    -o-transition-property: background-color;
    -ms-transition-property: background-color;
    transition-property: background-color;
    width: 120px;
    height: 100%;
    background-color: #5acc87;
    background-image: url("/content/themes/healthcare/images/common/icon6.svg");
    color: #fff;
    font-size: 11px;
    padding: 56px 5px 0;
    line-height: 14px;
  }
  body header nav > a:hover,
  body header nav > a:link,
  body header nav > a:visited,
  body header nav > a:active {
    color: #222;
  }
  body header nav > a:hover {
    background-color: #24b05c;
  }
  body header nav > a:hover,
  body header nav > a:link,
  body header nav > a:visited,
  body header nav > a:active {
    color: #fff;
  }
  body header nav > a span {
    display: inline-block;
  }
  body header .searchWrap a {
    position: absolute;
    top: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center top 25px;
    -webkit-background-size: 33px auto;
    -moz-background-size: 33px auto;
    background-size: 33px auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    font-size: 13px;
    text-decoration: none;
    color: #222;
    font-weight: bold;
    padding: 62px 5px 0;
    display: block;
    letter-spacing: 1px;
    line-height: 16px;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
    -webkit-transition-property: background-color;
    -moz-transition-property: background-color;
    -o-transition-property: background-color;
    -ms-transition-property: background-color;
    transition-property: background-color;
    width: 120px;
    height: 100%;
    background-color: #5acc87;
    background-image: url("/content/themes/healthcare/images/common/icon11.svg");
    color: #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-left: 1px solid #8ddbac;
    font-size: 11px;
  }
  body header .searchWrap a:hover,
  body header .searchWrap a:link,
  body header .searchWrap a:visited,
  body header .searchWrap a:active {
    color: #222;
  }
  body header .searchWrap a:hover {
    background-color: #24b05c;
  }
  body header .searchWrap a:hover,
  body header .searchWrap a:link,
  body header .searchWrap a:visited,
  body header .searchWrap a:active {
    color: #fff;
  }
  body header .searchWrap a.open {
    background-color: #8cdbab;
  }
  body header .searchWrap > .searchForm {
    position: absolute;
    top: 100px;
    right: 0;
    display: none;
    width: 447px;
    height: 57px;
    margin: 0;
    background-color: #f2f2f0;
    overflow: hidden;
    padding: 0 10px 0 45px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background-image: url("/content/themes/healthcare/images/common/icon12.svg");
    background-position: left 15px center;
    background-repeat: no-repeat;
  }
  body header .searchWrap > .searchForm.open {
    display: block;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr {
    height: 57px;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr .input-box {
    height: 21px;
    border: 0;
    padding: 5px;
    font-size: 16px;
    border: solid 1px #ccc;
    width: 300px;
    border-radius: 3px;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr .search-button {
    position: relative;
    width: 70px;
    padding: 0 15px;
  }
  body header .searchWrap > .searchForm form > table > tbody > tr .search-button input {
    display: block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 31px;
    background-color: #24b05c;
    padding: 5px 0;
    border-radius: 3px;
    display: block;
    color: #fff;
    width: 100%;
    text-align: center;
    letter-spacing: 1px;
    font-size: 12px;
    border-style: none;
  }
  body header.logo_only {
    border-bottom: 1px solid #ededed;
  }
  body header.logo_only h1 {
    border: 0;
  }
  body header .sp {
    display: none !important;
  }
  body.business header {
    max-width: 1600px;
    min-width: 1000px;
    width: 100%;
    margin: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    zoom: 1;
    position: relative;
    z-index: 99;
    font-size: 30px;
    background-color: #fff;
  }
  body.business header:before,
  body.business header:after {
    content: "";
    display: table;
  }
  body.business header:after {
    clear: both;
  }
  body.business header h1 {
    float: left;
    display: table;
    width: 20%;
    height: 100px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body.business header h1 a {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
  }
  body.business header h1 a svg {
    width: 177px;
    height: 69px;
  }
  body.business header nav {
    margin-left: 30%;
  }
  body.business header nav > .menu2 {
    text-align: right;
    font-size: 0;
    padding: 15px 20px 16px;
  }
  body.business header nav > .menu2 a {
    display: inline-block;
    color: #999;
    border-bottom: 1px solid #999;
    font-size: 11px;
    font-weight: bold;
    padding: 7px 15px;
    margin: 0 12px;
  }
  body.business header nav > .menu2 a.blue {

  }
  body.business header nav > .menu2 a:hover {
    opacity: 0.7;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
    filter: alpha(opacity=70);
    -webkit-transition: 200ms;
    -moz-transition: 200ms;
    -o-transition: 200ms;
    -ms-transition: 200ms;
    transition: 200ms;
  }
  body.business header nav > .menu2 a.red {
    color: #ee2245;
    border: 1px solid #ee2245;
    border-radius: 6px;
    font-size: 15px;
  }
  body.business header nav > div {
    margin-right: 0;
  }
  body.business header nav > div > ul {
    display: table;
    table-layout: fixed;
    width: 100%;
  }
  body.business header nav > div > ul > li {
    display: table-cell;
    border-left: none;
    height: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body.business header nav > div > ul > li > a {
    background-repeat: no-repeat;
    background-position: center top 25px;
    -webkit-background-size: 33px auto;
    -moz-background-size: 33px auto;
    background-size: 33px auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
    font-size: 15px;
    text-decoration: none;
    color: #222;
    font-weight: bold;
    padding: 5px 5px 5px;
    display: block;
    letter-spacing: 1px;
    line-height: 16px;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
    -webkit-transition-property: background-color;
    -moz-transition-property: background-color;
    -o-transition-property: background-color;
    -ms-transition-property: background-color;
    transition-property: background-color;
    position: relative;
    height: auto;
  }
  body.business header nav > div > ul > li > a:hover,
  body.business header nav > div > ul > li > a:link,
  body.business header nav > div > ul > li > a:visited,
  body.business header nav > div > ul > li > a:active {
    color: #222;
  }
  body.business header nav > div > ul > li > a:hover {
    background-color: #cfebd9;
  }
  body.business header nav > div > ul > li > a:before {
    content: '';
    background-color: #ededed;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 25px;
  }
  body.business header nav > div > ul > li > a:hover {
    background-color: #fcd7dd;
  }
  body.business header nav > div > ul > li > a.open {
    background-color: #f2f2f0;
  }
  body.business header nav > div > ul > li:first-child > a:before {
    content: none;
  }
  body.business header nav > div > ul > li.products {
    width: ;
  }
  body.business header nav > div > ul > li.business {
    width: 21%;
  }
  body.business header nav > div > ul > li.home > a {
    background-image: none;
  }
  body.business header nav > div > ul > li.products > a {
    background-image: none;
  }
  body.business header nav > div > ul > li.column > a {
    background-image: none;
  }
  body.business header nav > div > ul > li.corporate > a {
    background-image: none;
  }
  body.business header nav > div > ul > li.business > a {
    background-image: none;
  }
  body.business header.scrolled {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    min-width: auto;
    max-width: none;
    background: none;
  }
  body.business header.scrolled .headerInner {
    width: 100%;
    height: 70px;
    max-width: 1600px;
    min-width: 1100px;
    margin: 0 auto;
    position: relative;
    background: #fff;
  }
  body.business header.scrolled h1 {
    height: 70px;
  }
  body.business header.scrolled h1 a svg {
    width: 126px;
    height: 50px;
    margin-right: 51px;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  body.business header.scrolled nav {
    margin-top: 0;
  }
  body.business header.scrolled nav>.menu2 {
    padding: 0;
    position: absolute;
    right: 20px;
    top: 16px;
  }
  body.business header.scrolled nav>.menu2 a {
    display: none;
  }
  body.business header.scrolled nav>.menu2 a.contact {
    display: inline-block;
    margin: 0;
    font-size: 12px;
    padding: 12px 15px;
  }
  body.business header.scrolled nav>div {
    position: absolute;
    bottom: 11px;
    right: 240px;
    width: 700px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body.business header.scrolled nav>div>ul>li>a {
    font-size: 13px;
  }
  body.current-home header nav > div > ul > li.home > a:after,
  body.current-products header nav > div > ul > li.products > a:after,
  body.current-casestudy header nav > div > ul > li.casestudy > a:after,
    body.current-seminar header nav > div > ul > li.seminar > a:after,
  body.current-library header nav > div > ul > li.library > a:after,
  body.current-column header nav > div > ul > li.column > a:after,
  body.current-business header nav > div > ul > li.business > a:after,
  body.current-corporate header nav > div > ul > li.corporate > a:after {
    position: absolute;
    bottom: -12px;
    left: 50%;
    content: '';
    display: block;
    background-color: #cf0f30;
    width: 45%;
    height: 2px;
    border-radius: 0;
    pointer-events: none;
    cursor: default;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}
