@charset "utf-8";
@media screen and (max-width: 767px) {
  body footer .footer-sns {
  }
  body footer .footer-sns ul li {
    display: inline-block;
    vertical-align: top;
    padding-right: 10px;
  }
  body footer .pagetop {
    width: 35px;
    height: 35px;
    position: fixed;
    right: 20px;
    bottom: -35px;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    -webkit-transition-duration: 500ms;
    -moz-transition-duration: 500ms;
    -o-transition-duration: 500ms;
    -ms-transition-duration: 500ms;
    transition-duration: 500ms;
  }
  body footer .pagetop a {
    display: block;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color:#c03;
    border-color: #fff;
    background-image: url("../img/icon-top.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 8px 9px;
    -moz-background-size: 8px 9px;
    background-size: 8px 9px;
    background-position: center center;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
  }
  body footer .pagetop a:hover {
    background-color: #c03;
  }
  body footer.no_scroll .pagetop {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    bottom: -35px;
  }
  body footer.scrolled .pagetop {
    opacity: 1;
    -ms-filter: none;
    filter: none;
    bottom: 16px;
  }
  body footer >nav {
    width: 100%;
  }
  body footer >nav a {
    text-decoration: none;
    display: block;
    line-height: 1;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 15px 20px;
  }
  body footer >nav .menu-footer-container >ul >li >a {
    color: #222;
    font-size: 12px;
    font-weight: bold;
    border-bottom: 1px solid #24b05c;
    background-color: #cfebd9;
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_arrow-bk.svg");
    -webkit-background-size: 12px 10px;
    -moz-background-size: 12px 10px;
    background-size: 12px 10px;
    background-repeat: no-repeat;
    background-position: right 20px center;
  }
  body footer >nav .menu-footer-container >ul >li >ul >li >a {
    color: #fff;
    font-size: 11px;
    font-weight: normal;
    border-bottom: 1px solid #fff;
    background-color: #5acc87;
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_arrow-wt.svg");
    -webkit-background-size: 12px 10px;
    -moz-background-size: 12px 10px;
    background-size: 12px 10px;
    background-repeat: no-repeat;
    background-position: right 20px center;
  }
  body footer >nav .menu-footer-container >ul >li >ul >li >a.offsetFontSize {
    font-size: 14px;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: box;
    display: flex;
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    -o-box-orient: vertical;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(1) {
    -webkit-box-ordinal-group: 1;
    -moz-box-ordinal-group: 1;
    -o-box-ordinal-group: 1;
    -ms-flex-order: 1;
    -webkit-order: 1;
    order: 1;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(2) {
    -webkit-box-ordinal-group: 11;
    -moz-box-ordinal-group: 11;
    -o-box-ordinal-group: 11;
    -ms-flex-order: 11;
    -webkit-order: 11;
    order: 11;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(3) {
    -webkit-box-ordinal-group: 2;
    -moz-box-ordinal-group: 2;
    -o-box-ordinal-group: 2;
    -ms-flex-order: 2;
    -webkit-order: 2;
    order: 2;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(4) {
    -webkit-box-ordinal-group: 12;
    -moz-box-ordinal-group: 12;
    -o-box-ordinal-group: 12;
    -ms-flex-order: 12;
    -webkit-order: 12;
    order: 12;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(5) {
    -webkit-box-ordinal-group: 3;
    -moz-box-ordinal-group: 3;
    -o-box-ordinal-group: 3;
    -ms-flex-order: 3;
    -webkit-order: 3;
    order: 3;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(6) {
    -webkit-box-ordinal-group: 13;
    -moz-box-ordinal-group: 13;
    -o-box-ordinal-group: 13;
    -ms-flex-order: 13;
    -webkit-order: 13;
    order: 13;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(7) {
    -webkit-box-ordinal-group: 4;
    -moz-box-ordinal-group: 4;
    -o-box-ordinal-group: 4;
    -ms-flex-order: 4;
    -webkit-order: 4;
    order: 4;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(8) {
    -webkit-box-ordinal-group: 14;
    -moz-box-ordinal-group: 14;
    -o-box-ordinal-group: 14;
    -ms-flex-order: 14;
    -webkit-order: 14;
    order: 14;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(9) {
    -webkit-box-ordinal-group: 5;
    -moz-box-ordinal-group: 5;
    -o-box-ordinal-group: 5;
    -ms-flex-order: 5;
    -webkit-order: 5;
    order: 5;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(10) {
    -webkit-box-ordinal-group: 15;
    -moz-box-ordinal-group: 15;
    -o-box-ordinal-group: 15;
    -ms-flex-order: 15;
    -webkit-order: 15;
    order: 15;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(11) {
    -webkit-box-ordinal-group: 6;
    -moz-box-ordinal-group: 6;
    -o-box-ordinal-group: 6;
    -ms-flex-order: 6;
    -webkit-order: 6;
    order: 6;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(12) {
    -webkit-box-ordinal-group: 16;
    -moz-box-ordinal-group: 16;
    -o-box-ordinal-group: 16;
    -ms-flex-order: 16;
    -webkit-order: 16;
    order: 16;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(13) {
    -webkit-box-ordinal-group: 7;
    -moz-box-ordinal-group: 7;
    -o-box-ordinal-group: 7;
    -ms-flex-order: 7;
    -webkit-order: 7;
    order: 7;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(14) {
    -webkit-box-ordinal-group: 17;
    -moz-box-ordinal-group: 17;
    -o-box-ordinal-group: 17;
    -ms-flex-order: 17;
    -webkit-order: 17;
    order: 17;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(15) {
    -webkit-box-ordinal-group: 8;
    -moz-box-ordinal-group: 8;
    -o-box-ordinal-group: 8;
    -ms-flex-order: 8;
    -webkit-order: 8;
    order: 8;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(16) {
    -webkit-box-ordinal-group: 18;
    -moz-box-ordinal-group: 18;
    -o-box-ordinal-group: 18;
    -ms-flex-order: 18;
    -webkit-order: 18;
    order: 18;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(17) {
    -webkit-box-ordinal-group: 9;
    -moz-box-ordinal-group: 9;
    -o-box-ordinal-group: 9;
    -ms-flex-order: 9;
    -webkit-order: 9;
    order: 9;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(18) {
    -webkit-box-ordinal-group: 19;
    -moz-box-ordinal-group: 19;
    -o-box-ordinal-group: 19;
    -ms-flex-order: 19;
    -webkit-order: 19;
    order: 19;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(19) {
    -webkit-box-ordinal-group: 10;
    -moz-box-ordinal-group: 10;
    -o-box-ordinal-group: 10;
    -ms-flex-order: 10;
    -webkit-order: 10;
    order: 10;
  }
  body footer >nav .menu-footer-container >ul >li.column-2 >ul >li:nth-child(20) {
    -webkit-box-ordinal-group: 20;
    -moz-box-ordinal-group: 20;
    -o-box-ordinal-group: 20;
    -ms-flex-order: 20;
    -webkit-order: 20;
    order: 20;
  }
  body footer >nav .menu-footer-container >ul >li.other >a {
    display: none;
  }
  body footer >nav .menu-footer-container >ul >li.other >ul >li >a {
    color: #222;
    font-size: 12px;
    font-weight: bold;
    border-bottom: 1px solid #24b05c;
    background-color: #cfebd9;
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_arrow-bk.svg");
  }
  body footer >nav .menu-footer-container >ul >li.menu-item-has-children >a,
  body footer >nav .menu-footer-container >ul >li.close >a {
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_plus.svg");
    -webkit-background-size: 12px 12px;
    -moz-background-size: 12px 12px;
    background-size: 12px 12px;
  }
  body footer >nav .menu-footer-container >ul >li.menu-item-has-children >ul,
  body footer >nav .menu-footer-container >ul >li.close >ul {
    display: none;
  }
  body footer >nav .menu-footer-container >ul >li.open >a {
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_minus.svg");
    -webkit-background-size: 12px 2px;
    -moz-background-size: 12px 2px;
    background-size: 12px 2px;
  }
  body footer >nav .menu-footer-container >ul >li.open >ul {
    display: block;
  }
  body footer >nav .menu-footer-container >ul >li.open.column-2 >ul {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: box;
    display: flex;
  }
  body footer .copyright {

  }
  body footer .copyright small {

  }
  body footer .pc {
    display: none !important;
  }
  body.business footer .footer-sns {
  }
  body.business footer .footer-sns ul li {
    display: inline-block;
    vertical-align: top;
    padding-right: 10px;
  }
  body.business footer >nav .menu-footer-container >ul >li >a {
    background-color: #efefef;
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_arrow2.svg");
    border-bottom-color: #ccc;
  }
  body.business footer >nav .menu-footer-container >ul >li >ul >li >a {
    background-color: #ddd;
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_arrow2.svg");
    border-bottom-color: #ccc;
    color: #222;
  }
  body.business footer >nav .menu-footer-container >ul >li.menu-item-has-children >a,
  body.business footer >nav .menu-footer-container >ul >li.close >a {
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_plus-bl.svg");
  }
  body.business footer >nav .menu-footer-container >ul >li.open >a {
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_minus-bl.svg");
  }
  body.business footer >nav .menu-footer-container >ul >li.other >ul >li >a {
    background-color: #efefef;
    background-image: url("/content/themes/healthcare/images/common/sp_menu_icon_arrow2.svg");
    border-bottom-color: #ccc;
  }
  body.business footer .copyright {

  }
  body.business footer .copyright small {
    color: #fff;
  }
}
@media print, screen and (min-width: 768px) {
  body footer {

    min-width: 1000px;
    width: 100%;
    margin: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body footer .footer-sns {
    background-color: #fff;
  }
  body footer .footer-sns ul {
    min-width: 1000px;
    width: 90%;
    margin: auto;
    padding: 32px 0;
  }
  body footer .footer-sns ul li {
    display: inline-block;
    vertical-align: top;
    padding-right: 10px;
  }
  body footer .pagetop {
    width: 48px;
    height: 48px;
    position: fixed;
    right: 100px;
    bottom: -48px;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    -webkit-transition-duration: 500ms;
    -moz-transition-duration: 500ms;
    -o-transition-duration: 500ms;
    -ms-transition-duration: 500ms;
    transition-duration: 500ms;
    margin: 0;
    z-index: 9;
  }
  body footer .pagetop a {
    display: block;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color:#c03;
    border-color: #fff;
    background-image: url("../img/icon-top.svg");
    background-repeat: no-repeat;
    -webkit-background-size: 13px 14px;
    -moz-background-size: 13px 14px;
    background-size: 13px 14px;
    background-position: center center;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
  }
  body footer .pagetop a:hover {
    background-color:#c03;
  }
  body footer.no_scroll .pagetop {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    bottom: -48px;
  }
  body footer.scrolled .pagetop {
    opacity: 1;
    -ms-filter: none;
    filter: none;
    bottom: 16px;
  }
  body footer > nav {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 70px 0 30px;
    background-color: #d1edd9;
  }
  body footer > nav a {
    text-decoration: none;
    color: #222;
    font-size: 12px;
    line-height: 1.6;
    -webkit-transition-duration: 200ms;
    -moz-transition-duration: 200ms;
    -o-transition-duration: 200ms;
    -ms-transition-duration: 200ms;
    transition-duration: 200ms;
  }
  body footer > nav a:hover {
    opacity: 0.7;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
    filter: alpha(opacity=70);
  }
  body footer > nav a:hover,
  body footer > nav a:link,
  body footer > nav a:visited,
  body footer > nav a:active {
    color: #222;
  }
  body footer > nav a.offsetFontSize {
    font-size: 15px;
  }
  body footer > nav .menu-footer-container {
    min-width: 1000px;
    width: 90%;
    margin: auto;
  }
  body footer > nav .menu-footer-container > ul {
    display: table;
    width: 100%;
  }
  body footer > nav .menu-footer-container > ul > li {
    display: table-cell;
    vertical-align: top;
    width: 14.285%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 10px;
  }
  body footer > nav .menu-footer-container > ul > li > ul {
    margin-top: 13px;
  }
  body footer > nav .menu-footer-container > ul > li > ul > li {
    padding: 1px 0;
  }
  body footer > nav .menu-footer-container > ul > li.column-2 {
    width: 28.57%;
    overflow: hidden;
  }
  body footer > nav .menu-footer-container > ul > li.column-2 > ul {
    zoom: 1;
  }
  body footer > nav .menu-footer-container > ul > li.column-2 > ul:before,
  body footer > nav .menu-footer-container > ul > li.column-2 > ul:after {
    content: "";
    display: table;
  }
  body footer > nav .menu-footer-container > ul > li.column-2 > ul:after {
    clear: both;
  }
  body footer > nav .menu-footer-container > ul > li.column-2 > ul > li {
    float: left;
    width: 50%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  body footer > nav .menu-footer-container > ul > li.column-2 > ul > li:nth-child(even) {
    padding-left: 5px;
  }
  body footer > nav .menu-footer-container > ul > li.column-2 > ul > li:nth-child(odd) {
    padding-right: 5px;
  }
  body footer > nav .menu-footer-container > ul > li.other > a {
    display: none;
  }
  body footer > nav .menu-footer-container > ul > li.other > ul {
    margin-top: 0;
  }
  body footer > nav .menu-footer-container > ul > li.other > ul > li {
    margin-top: 13px;
  }
  body footer > nav .menu-footer-container > ul > li.other > ul > li:first-child {
    margin-top: 0;
  }
  body footer > nav .menu-footer-container > ul > li > a,
  body footer > nav .menu-footer-container > ul > li.other li a {
    display: block;
    padding: 0 0 10px 0;
    border-bottom: 1px solid #24b05c;
    font-weight: bold;
  }
  body footer .copyright {

  }
  body footer .copyright small {

  }
  body footer .sp {
    display: none !important;
  }
  body.business footer .footer-sns {
    background-color: #fff;
  }
  body.business footer .footer-sns ul {
    min-width: 1000px;
    width: 90%;
    margin: auto;
    padding: 32px 0;
  }
  body.business footer .footer-sns ul li {
    display: inline-block;
    vertical-align: top;
    padding-right: 10px;
  }
  body.business footer > nav {
    background-color: #efefef;
    height: auto;
  }
  body.business footer > nav .menu-footer-container > ul > li > a,
  body.business footer > nav .menu-footer-container > ul > li.other li a {
    border-bottom: none;
    position: relative;
  }
  body.business footer > nav .menu-footer-container > ul > li > a:not([href]),
  body.business footer > nav .menu-footer-container > ul > li.other li a:not([href]) {
    cursor: default;
  }
  body.business footer > nav .menu-footer-container > ul > li > a:not([href]):hover,
  body.business footer > nav .menu-footer-container > ul > li.other li a:not([href]):hover {
    opacity: 1;
    -ms-filter: none;
    filter: none;
  }
  body.business footer > nav .menu-footer-container > ul > li > a:after,
  body.business footer > nav .menu-footer-container > ul > li.other li a:after {
    content: '';

    position: absolute;
    bottom: 0;
    left: 0;
    width: 20px;
    height: 1px;
  }
  body.business footer > nav .menu-footer-container > ul > li > .sub-menu > li > a {
    position: relative;
    padding-left: 10px;
    display: inline-block;
  }
  body.business footer > nav .menu-footer-container > ul > li > .sub-menu > li > a:before {
    content: '';
    display: block;
    position: absolute;
    top: 4px;
    left: 0;
    border-top: 1px solid #bcbcbc;
    border-right: 1px solid #bcbcbc;
    width: 5px;
    height: 5px;
    -webkit-transform-origin: left top;
    -moz-transform-origin: left top;
    -o-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  body.business footer > nav .menu-footer-container > ul > li.other > ul > li {
    margin-top: 20px;
  }
  body.business footer > nav .menu-footer-container > ul > li.other > ul > li:first-child {
    margin-top: 0;
  }
  body.business footer > nav .menu-footer-container > ul > li.other > ul.sub-menu > li > a {
    padding-left: 0;
  }
  body.business footer > nav .menu-footer-container > ul > li.other > ul.sub-menu > li > a:before {
    content: none;
  }
  body.business footer > nav .menu-footer-container > ul > li.other > ul.sub-menu > li.btn-style > a {
    color: #999;
    font-size: 11px;
    border: 1px solid #999;
    border-radius: 3px;
    background-color: #fff;
    padding: 5px 10px;
  }
  body.business footer > nav .menu-footer-container > ul > li.other > ul.sub-menu > li.btn-style > a:after {
    content: none;
  }
  body.business footer > nav .menu-footer-container > ul > li > ul > li {
    padding: 5px 0;
  }
  body.business footer .copyright {

  }
  body.business footer .copyright small {
    color: #fff;
  }
}
