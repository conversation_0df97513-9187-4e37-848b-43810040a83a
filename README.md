# Next.js プロジェクト

これは [Next.js](https://nextjs.org) プロジェクトです。

## はじめに

まず、開発サーバーを起動します：

```bash
yarn install

yarn dev
```

## コード品質チェックと開発ワークフロー

### コード品質チェックコマンド

```bash
# コードの問題をチェック
yarn lint

# コードの問題をチェックして自動修正
yarn lint:fix

# コードをフォーマット
yarn format

# 包括的なチェックと修正（インポートの順序を含む）
yarn check
```

### 開発ワークフロー

1. **コードの作成**: プロジェクト規約に従ってコードを書く
2. **ローカルチェック**: `yarn check` で包括的なチェックを実行
3. **コードのコミット**: コミット時に Lefthook が自動的にチェックを実行
4. **問題の対応**: 問題がある場合は修正して再コミット

## Docker ビルドと実行

このプロジェクトは Docker を使用してビルドおよび実行することができます。

### Docker イメージのビルド

```bash
# Docker イメージをビルド
docker build -t mileage-webview .

# 環境変数を設定してイメージをビルド
docker build \
  --build-arg NEXT_PUBLIC_API_URL=https://your-api-url.com \
  --build-arg NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-key \
  -t mileage-webview-with-arg .
```

### Docker コンテナの実行

```bash
# ポート 3000 でコンテナを実行
docker run -it -p 3000:3000 mileage-webview

# or
docker run -it -p 3000:3000 mileage-webview-with-arg
```

アプリケーションは http://localhost:3000 でアクセスできます。

### 注意事項

- Docker ビルドには `next.config.js` ファイルで `output: 'standalone'` の設定が必要です
- このプロジェクトの Dockerfile は Next.js アプリケーションの最適化されたマルチステージビルドを使用しています
- 本番環境では環境変数を適切に設定してください

## 詳細情報

Next.js についてさらに学ぶには、以下のリソースをご覧ください：

- [Next.js ドキュメント](https://nextjs.org/docs) - Next.js の機能と API について学ぶ
- [Next.js を学ぶ](https://nextjs.org/learn) - インタラクティブな Next.js チュートリアル

[Next.js GitHub リポジトリ](https://github.com/vercel/next.js)もチェックできます - フィードバックや貢献を歓迎します！