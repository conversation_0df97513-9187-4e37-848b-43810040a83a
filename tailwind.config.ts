import type { Config } from 'tailwindcss';
import { COLORS } from './src/const/colors';

export default {
  darkMode: ['class'],
  content: [
    'src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    'src/components/**/*.{js,ts,jsx,tsx,mdx}',
    'src/app/**/*.{js,ts,jsx,tsx,mdx}',
    'src/story/**/*.{js,ts,jsx,tsx,mdx}',
    'src/hooks/**/*.{js,ts,jsx,tsx,mdx}',
    '.ladle/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontSize: {
        xxs: '0.625rem',
      },
      colors: COLORS,
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      boxShadow: {
        'card-base': '0 0 8px 0 rgba(0, 0, 0, 0.15)',
        'card-hover': '0 0 12px 0 rgba(0, 0, 0, 0.2)',
        'card-soft': '0 0 6px 0 rgba(0, 0, 0, 0.1)',
        'card-strong': '0 0 16px 0 rgba(0, 0, 0, 0.15)',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;
