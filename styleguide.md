# Next.js 14 + Shadcn/UI + Tailwind CSS 组件开发规范

## 目录

1.  [项目结构](#%E9%A1%B9%E7%9B%AE%E7%BB%93%E6%9E%84)
2.  [组件命名与文件规范](#%E7%BB%84%E4%BB%B6%E5%91%BD%E5%90%8D%E4%B8%8E%E6%96%87%E4%BB%B6%E8%A7%84%E8%8C%83)
3.  [组件设计原则](#%E7%BB%84%E4%BB%B6%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99)
4.  [Props 与类型定义](#props-%E4%B8%8E%E7%B1%BB%E5%9E%8B%E5%AE%9A%E4%B9%89)
5.  [样式与 Tailwind 使用](#%E6%A0%B7%E5%BC%8F%E4%B8%8E-tailwind-%E4%BD%BF%E7%94%A8)
6.  [状态管理](#%E7%8A%B6%E6%80%81%E7%AE%A1%E7%90%86)
7.  [组件文档与注释](#%E7%BB%84%E4%BB%B6%E6%96%87%E6%A1%A3%E4%B8%8E%E6%B3%A8%E9%87%8A)
8.  [可访问性 (A11y)](#%E5%8F%AF%E8%AE%BF%E9%97%AE%E6%80%A7-a11y)
9.  [性能优化](#%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96)
10. [最佳实践示例](#%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5%E7%A4%BA%E4%BE%8B)

## 项目结构

```auto
src/
├── app/                  # 应用路由和页面
├── components/           # 组件目录
│   ├── ui/               # Shadcn UI 基础组件
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   └── ...
│   └── [feature]/        # 功能模块组件
│       ├── feature-card.tsx
│       └── ...
├── shared/               # 通用组件
│   ├── header/
│   │   ├── index.tsx
│   │   └── header.module.css (可选)
│   ├── footer/
│   │   └── index.tsx
│   └── ...
├── hooks/                # 自定义 Hooks
├── lib/                  # 工具函数和助手
│   ├── utils.ts
│   └── ...
├── types/                # 类型定义
└── styles/               # 全局样式
```

## 组件命名与文件规范

### 文件命名

1. **组件文件**：
   - 使用 kebab-case 命名文件：`feature-card.tsx`
   - 组件文件夹使用 kebab-case：`user-profile/`
   - 组件导出使用 PascalCase：`export function FeatureCard() {...}`
2. **通用组件**：
   - 在 `shared` 目录下按功能创建子目录
   - 主文件命名为 `index.tsx`
   - 组件名使用 PascalCase：`export function Header() {...}`
3. **客户端组件声明**：
   - 所有组件默认为客户端组件
   - 在文件顶部添加 `'use client'` 指令

```tsx
// shared/header/index.tsx
"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";

export function Header({ className, ...props }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <header className={cn("bg-background border-b", className)} {...props}>
      {/* 组件内容 */}
    </header>
  );
}
```

## 组件设计原则

### 组件拆分原则

1. **单一职责**：每个组件只负责一个功能
2. **大小控制**：组件代码不超过 200 行，超过时考虑拆分
3. **逻辑分离**：将复杂逻辑提取到自定义 hooks 中

### 组合模式

使用组合而非继承构建组件：

```tsx
// shared/card/index.tsx
"use client";

import { cn } from "@/lib/utils";

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

export function Card({
  children,
  header,
  footer,
  className,
  ...props
}: CardProps) {
  return (
    <div
      className={cn("rounded-lg border bg-card shadow-sm", className)}
      {...props}
    >
      {header && <div className="p-4 border-b">{header}</div>}
      <div className="p-4">{children}</div>
      {footer && <div className="p-4 border-t">{footer}</div>}
    </div>
  );
}
```

### 变体模式

使用 `cva` 定义组件变体：

```tsx
// components/ui/badge.tsx
"use client";

import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline:
          "text-foreground border border-input hover:bg-accent hover:text-accent-foreground",
      },
      size: {
        default: "h-6",
        sm: "h-5",
        lg: "h-7",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

export function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant, size }), className)}
      {...props}
    />
  );
}
```

## Props 与类型定义

### Props 定义规范

1. **接口命名**：组件名 + Props，如 `ButtonProps`
2. **扩展 HTML 属性**：通过扩展 HTML 元素属性提供完整的类型支持
3. **必选 Props 标记**：明确标记必选 props
4. **默认值处理**：为可选 props 提供合理的默认值

```tsx
// shared/button/index.tsx
"use client";

import { forwardRef } from "react";
import { cn } from "@/lib/utils";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "outline" | "ghost" | "link";
  size?: "default" | "sm" | "lg";
  isLoading?: boolean;
  loadingText?: string;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = "default",
      size = "default",
      isLoading = false,
      loadingText,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    return (
      <button
        className={cn(
          "inline-flex items-center justify-center rounded-md font-medium transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
          "disabled:opacity-50 disabled:pointer-events-none",
          {
            "bg-primary text-primary-foreground hover:bg-primary/90":
              variant === "default",
            "border border-input bg-background hover:bg-accent hover:text-accent-foreground":
              variant === "outline",
            "hover:bg-accent hover:text-accent-foreground": variant === "ghost",
            "text-primary underline-offset-4 hover:underline":
              variant === "link",
          },
          {
            "h-10 px-4 py-2": size === "default",
            "h-9 px-3": size === "sm",
            "h-11 px-8": size === "lg",
          },
          className
        )}
        ref={ref}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading ? (
          <>
            <svg
              className="mr-2 h-4 w-4 animate-spin"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            {loadingText || children}
          </>
        ) : (
          children
        )}
      </button>
    );
  }
);
Button.displayName = "Button";

export { Button };
```

### 类型导出

为组件和相关类型创建统一的导出：

```tsx
// types/index.ts
export * from "@/shared/button";
export * from "@/shared/card";
// ...其他组件类型
```

## 样式与 Tailwind 使用

### Tailwind 类名组织

1. **类名排序**：按照布局、尺寸、排版、视觉、交互的顺序组织
2. **使用** `cn` **函数**：用于条件类名和合并类名
3. **避免内联样式**：优先使用 Tailwind 类名而非内联样式

```tsx
// 良好的类名组织示例
<div
  className={cn(
    // 布局类
    "flex items-center justify-between",
    // 尺寸类
    "w-full h-16 px-4 py-2",
    // 排版类
    "text-sm font-medium",
    // 视觉类
    "bg-white border-b rounded-lg shadow-sm",
    // 交互类
    "hover:bg-gray-50",
    // 响应式类
    "md:h-20 md:px-6",
    // 暗黑模式
    "dark:bg-gray-900 dark:border-gray-800",
    // 自定义类名
    className
  )}
>
  内容
</div>
```

### 主题变量使用

使用 Shadcn/UI 的主题变量而非硬编码颜色：

```tsx
// ✅ 推荐：使用主题变量
<div className="bg-background text-foreground border-border">
  内容
</div>

// ❌ 避免：硬编码颜色
<div className="bg-white text-black border-gray-200">
  内容
</div>
```

### 响应式设计

采用移动优先的响应式设计：

```tsx
<div
  className="
  grid grid-cols-1 gap-4
  sm:grid-cols-2
  md:grid-cols-3
  lg:grid-cols-4
"
>
  {/* 卡片内容 */}
</div>
```

## 状态管理

### 本地状态

使用 React 的 `useState` 和 `useReducer` 管理组件内部状态：

```tsx
"use client";

import { useState } from "react";

export function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div className="flex items-center space-x-2">
      <button
        className="px-2 py-1 border rounded"
        onClick={() => setCount((prev) => prev - 1)}
      >
        -
      </button>
      <span>{count}</span>
      <button
        className="px-2 py-1 border rounded"
        onClick={() => setCount((prev) => prev + 1)}
      >
        +
      </button>
    </div>
  );
}
```

### 复杂状态

对于复杂状态，使用自定义 hooks 提取逻辑：

```tsx
// hooks/use-counter.ts
import { useState, useCallback } from "react";

export function useCounter(initialValue = 0, step = 1) {
  const [count, setCount] = useState(initialValue);

  const increment = useCallback(() => {
    setCount((prev) => prev + step);
  }, [step]);

  const decrement = useCallback(() => {
    setCount((prev) => prev - step);
  }, [step]);

  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);

  return {
    count,
    increment,
    decrement,
    reset,
  };
}

// 使用
("use client");

import { useCounter } from "@/hooks/use-counter";

export function Counter() {
  const { count, increment, decrement, reset } = useCounter(0);

  return (
    <div className="flex items-center space-x-2">
      <button className="px-2 py-1 border rounded" onClick={decrement}>
        -
      </button>
      <span>{count}</span>
      <button className="px-2 py-1 border rounded" onClick={increment}>
        +
      </button>
      <button className="px-2 py-1 border rounded" onClick={reset}>
        重置
      </button>
    </div>
  );
}
```

## 组件文档与注释

### JSDoc 注释

使用 JSDoc 注释为组件和函数提供文档：

```tsx
/**
 * 分页组件
 *
 * @param {object} props - 组件属性
 * @param {number} props.currentPage - 当前页码
 * @param {number} props.totalPages - 总页数
 * @param {function} props.onPageChange - 页码变更回调
 * @param {string} [props.className] - 自定义类名
 * @returns {React.ReactElement} 分页组件
 */
export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  className,
}: PaginationProps) {
  // 组件实现
}
```

### 组件示例

在组件文件中提供使用示例：

````tsx
/**
 * 警告提示组件
 *
 * @example
 * ```tsx
 * <Alert variant="warning">
 *   <AlertTitle>注意</AlertTitle>
 *   <AlertDescription>请确保填写所有必填字段。</AlertDescription>
 * </Alert>
 * ```
 */
````

## 可访问性 (A11y)

### 可访问性基本规则

1. **语义化 HTML**：使用正确的 HTML 元素表达内容含义
2. **键盘可访问性**：确保可交互元素可通过键盘操作
3. **ARIA 属性**：适当使用 ARIA 属性增强可访问性
4. **颜色对比度**：确保文本与背景有足够的对比度

```tsx
// 良好的可访问性示例
"use client";

import { forwardRef } from "react";
import { cn } from "@/lib/utils";

interface ToggleProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  pressed: boolean;
  label: string;
}

export const Toggle = forwardRef<HTMLButtonElement, ToggleProps>(
  ({ pressed, label, className, onClick, ...props }, ref) => {
    return (
      <button
        ref={ref}
        type="button"
        aria-pressed={pressed}
        className={cn(
          "inline-flex items-center justify-center rounded-md px-3 py-2",
          "text-sm font-medium transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
          pressed ? "bg-accent text-accent-foreground" : "bg-transparent",
          className
        )}
        onClick={onClick}
        {...props}
      >
        <span className="sr-only">{label}</span>
        {props.children}
      </button>
    );
  }
);
Toggle.displayName = "Toggle";
```

## 性能优化

### 组件优化

1. **使用** `memo`：对于纯展示组件，使用 `React.memo` 避免不必要的重渲染
2. **使用** `useCallback` **和** `useMemo`：优化事件处理函数和计算值
3. **懒加载组件**：使用 `React.lazy` 和 `Suspense` 懒加载组件

```tsx
"use client";

import { memo, useCallback, useMemo } from "react";

interface DataTableProps {
  data: any[];
  onRowClick: (id: string) => void;
}

export const DataTable = memo(function DataTable({
  data,
  onRowClick,
}: DataTableProps) {
  const handleRowClick = useCallback(
    (id: string) => {
      onRowClick(id);
    },
    [onRowClick]
  );

  const processedData = useMemo(() => {
    return data.map((item) => ({
      ...item,
      formattedDate: new Date(item.date).toLocaleDateString(),
    }));
  }, [data]);

  return (
    <table className="w-full border-collapse">
      <thead>
        <tr className="bg-muted">
          <th className="p-2 text-left">ID</th>
          <th className="p-2 text-left">名称</th>
          <th className="p-2 text-left">日期</th>
        </tr>
      </thead>
      <tbody>
        {processedData.map((item) => (
          <tr
            key={item.id}
            className="border-b hover:bg-muted/50 cursor-pointer"
            onClick={() => handleRowClick(item.id)}
          >
            <td className="p-2">{item.id}</td>
            <td className="p-2">{item.name}</td>
            <td className="p-2">{item.formattedDate}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
});
```

## 最佳实践示例

### 完整组件示例

下面是一个符合规范的完整组件示例：

````tsx
// shared/data-table/index.tsx
"use client";

import { useState, useCallback, memo } from "react";
import { cn } from "@/lib/utils";
import { ChevronDown, ChevronUp, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useDebounce } from "@/hooks/use-debounce";

export interface DataTableColumn<T> {
  key: string;
  title: string;
  render?: (item: T) => React.ReactNode;
  sortable?: boolean;
}

export interface DataTableProps<T extends { id: string }> {
  /**
   * 表格数据
   */
  data: T[];
  /**
   * 表格列定义
   */
  columns: DataTableColumn<T>[];
  /**
   * 是否显示搜索框
   * @default false
   */
  searchable?: boolean;
  /**
   * 搜索字段
   * @default 'name'
   */
  searchField?: string;
  /**
   * 行点击事件
   */
  onRowClick?: (item: T) => void;
  /**
   * 自定义类名
   */
  className?: string;
}

/**
 * 数据表格组件
 *
 * @example
 * ```tsx
 * <DataTable
 *   data={users}
 *   columns={[
 *     { key: 'name', title: '姓名' },
 *     { key: 'email', title: '邮箱' },
 *     {
 *       key: 'status',
 *       title: '状态',
 *       render: (user) => <Badge>{user.status}</Badge>
 *     }
 *   ]}
 *   searchable
 *   onRowClick={(user) => router.push(`/users/${user.id}`)}
 * />
 * ```
 */
export function DataTable<T extends { id: string }>({
  data,
  columns,
  searchable = false,
  searchField = "name",
  onRowClick,
  className,
}: DataTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // 处理排序
  const handleSort = useCallback((key: string) => {
    setSortConfig((prevConfig) => {
      if (!prevConfig || prevConfig.key !== key) {
        return { key, direction: "asc" };
      }

      if (prevConfig.direction === "asc") {
        return { key, direction: "desc" };
      }

      return null;
    });
  }, []);

  // 处理搜索
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  }, []);

  // 过滤并排序数据
  const filteredAndSortedData = data
    // 搜索过滤
    .filter((item) => {
      if (!debouncedSearchTerm) return true;

      const field = item[searchField as keyof T];
      if (typeof field === "string") {
        return field.toLowerCase().includes(debouncedSearchTerm.toLowerCase());
      }
      return true;
    })
    // 排序
    .sort((a, b) => {
      if (!sortConfig) return 0;

      const aValue = a[sortConfig.key as keyof T];
      const bValue = b[sortConfig.key as keyof T];

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortConfig.direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortConfig.direction === "asc"
          ? aValue - bValue
          : bValue - aValue;
      }

      return 0;
    });

  // 渲染排序图标
  const renderSortIcon = useCallback(
    (key: string) => {
      if (!sortConfig || sortConfig.key !== key) {
        return null;
      }

      return sortConfig.direction === "asc" ? (
        <ChevronUp className="ml-1 h-4 w-4" />
      ) : (
        <ChevronDown className="ml-1 h-4 w-4" />
      );
    },
    [sortConfig]
  );

  return (
    <div className={cn("space-y-4", className)}>
      {searchable && (
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索..."
            value={searchTerm}
            onChange={handleSearch}
            className="pl-8"
          />
        </div>
      )}

      <div className="rounded-md border">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-muted border-b">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    "h-10 px-4 text-left text-xs font-medium text-muted-foreground",
                    column.sortable && "cursor-pointer select-none"
                  )}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center">
                    {column.title}
                    {column.sortable && renderSortIcon(column.key)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredAndSortedData.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className="h-24 text-center text-muted-foreground p-4"
                >
                  没有数据
                </td>
              </tr>
            ) : (
              filteredAndSortedData.map((item) => (
                <tr
                  key={item.id}
                  className={cn(
                    "border-b transition-colors hover:bg-muted/50",
                    onRowClick && "cursor-pointer"
                  )}
                  onClick={() => onRowClick?.(item)}
                >
                  {columns.map((column) => (
                    <td key={`${item.id}-${column.key}`} className="p-4">
                      {column.render
                        ? column.render(item)
                        : String(item[column.key as keyof T] || "")}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// 导出记忆化版本，用于性能优化
export const MemoizedDataTable = memo(DataTable) as typeof DataTable;
````

### 使用示例

```tsx
// app/users/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "@/hooks/use-next-navigation";
import { DataTable } from "@/shared/data-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { userAPI } from "@/lib/api/user";

interface User {
  id: string;
  name: string;
  email: string;
  status: "active" | "inactive" | "pending";
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchUsers() {
      try {
        const data = await userAPI.getList();
        setUsers(data);
      } catch (error) {
        console.log("Failed to fetch users:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchUsers();
  }, []);

  const handleCreateUser = () => {
    router.push("/users/create");
  };

  const handleUserClick = (user: User) => {
    router.push(`/users/${user.id}`);
  };

  const statusVariants = {
    active: "success",
    inactive: "destructive",
    pending: "warning",
  };

  return (
    <div className="container py-10">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
        <Button onClick={handleCreateUser}>
          <Plus className="mr-2 h-4 w-4" />
          创建用户
        </Button>
      </div>

      <DataTable
        data={users}
        columns={[
          { key: "name", title: "姓名", sortable: true },
          { key: "email", title: "邮箱", sortable: true },
          {
            key: "status",
            title: "状态",
            sortable: true,
            render: (user) => (
              <Badge variant={statusVariants[user.status] as any}>
                {user.status === "active"
                  ? "活跃"
                  : user.status === "inactive"
                  ? "停用"
                  : "待审核"}
              </Badge>
            ),
          },
        ]}
        searchable
        searchField="name"
        onRowClick={handleUserClick}
      />
    </div>
  );
}
```

通过遵循这些规范，您可以在 Next.js 14 + Shadcn/UI + Tailwind CSS 项目中构建一致、可维护且高性能的组件。这些规范有助于团队协作，提高代码质量，并确保良好的用户体验。

# Next.js 标准项目的命名规范

在 Next.js 标准项目中，遵循一致的命名规范可以提高代码可读性、可维护性，并减少团队协作中的混淆。以下是 Next.js 项目中常用的命名规范：

## 文件命名规范

### 目录命名

- **使用小写字母**
- **使用连字符（kebab-case）**
- **保持简洁明了**

```auto
├── components/
├── hooks/
├── lib/
├── pages/
├── public/
├── styles/
├── utils/
```

### 组件文件命名

- **使用小写字母和连字符（kebab-case）**

```auto
  components/
  ├── ui/
  │   ├── button.tsx
  │   ├── card.tsx
  │   └── input.tsx
  ├── forms/
  │   ├── login-form.tsx
  │   └── signup-form.tsx
  ├── shared/
  │   ├── date-range-select.tsx
  │   └── wareki-calendar.tsx
  └── layouts/
      ├── sidebar.tsx
      └── header.tsx
```

### 页面文件命名

- **在** `pages` **目录中使用小写字母和连字符（kebab-case）**
- **动态路由使用方括号** `[param]`

```auto
pages/
  ├── index.tsx
  ├── about.tsx
  ├── blog/
  │   ├── index.tsx
  │   └── [slug].tsx
  └── users/
      └── [id].tsx
```

### 工具函数文件命名

- **使用小写字母和连字符（kebab-case）或驼峰命名法（camelCase）**

```auto
utils/
  ├── date-formatter.ts
  ├── api-helpers.ts
  └── validation.ts
```

### 钩子函数文件命名

- **使用** `use` **前缀 + 驼峰命名法**
- 放在 `hooks`文件夹下

```auto
hooks/
  ├── use-auth.ts
  ├── use-fetch.ts
  └── use-localstorage.ts
```

## 变量命名规范

### 普通变量

- **使用驼峰命名法（camelCase）**
- **名称应该具有描述性**

```javascript
const userName = "John";
const isLoggedIn = true;
const pageCount = 10;
```

### 常量

- **使用大写字母和下划线（SNAKE_CASE）**

```javascript
const API_URL = "https://api.example.com";
const MAX_RETRY_COUNT = 3;
```

### 布尔变量

- **使用** `is`**、**`has`**、**`should` **等前缀**

```javascript
const isLoading = true;
const hasPermission = false;
const shouldRedirect = true;
```

### React 组件状态

- **使用描述性的驼峰命名法**
- **对于 setter 函数，使用** `set` **前缀**

```javascript
const [count, setCount] = useState(0);
const [isVisible, setIsVisible] = useState(false);
const [userData, setUserData] = useState(null);
```

## 函数命名规范

### 普通函数

- **使用驼峰命名法（camelCase）**
- **动词开头，表明行为**

```javascript
function fetchUserData() {
  /* ... */
}
function calculateTotal() {
  /* ... */
}
function validateForm() {
  /* ... */
}
```

### 事件处理函数

- **使用** `handle` **前缀 + 事件名**

```javascript
function handleClick() {
  /* ... */
}
function handleSubmit() {
  /* ... */
}
function handleInputChange() {
  /* ... */
}
```

### 异步函数

- **可以使用** `async` **前缀或动词表示异步操作**

```javascript
async function fetchData() {
  /* ... */
}
async function asyncGetUserProfile() {
  /* ... */
}
```

### 组件渲染函数

- **使用** `render` **前缀**

```javascript
function renderUserList() {
  /* ... */
}
function renderTableHeader() {
  /* ... */
}
```

## React 组件命名规范

### 组件命名

- **使用 PascalCase（大驼峰）**
- **名称应该表明组件的用途**

```javascript
function UserProfile() {
  /* ... */
}
function NavigationBar() {
  /* ... */
}
function LoginForm() {
  /* ... */
}
function MiniappPage() {
  /* ... */
}
```

### 高阶组件（HOC）

- **使用** `with` **前缀**

```javascript
function withAuth(Component) {
  /* ... */
}
function withTheme(Component) {
  /* ... */
}
```

## CSS 类名规范

### 使用 BEM 命名法（Block Element Modifier）

```css
.card {
  /* 块 */
}
.card__title {
  /* 元素 */
}
.card__button {
  /* 元素 */
}
.card--featured {
  /* 修饰符 */
}
```

### 统一使用 Tailwind CSS

- **使用小写字母和连字符（kebab-case）**

```jsx
// 使用 Tailwind CSS
function Button() {
  return (
    <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
      Click me
    </button>
  );
}
```

## TypeScript 类型和接口命名

### 接口（Interface）

- **使用 PascalCase（大驼峰）**
- \*\*通常不使用 `I` 前缀

```typescript
interface User {
  id: string;
  name: string;
  email: string;
}

interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}
```

### 类型别名（Type Alias）

- **使用 PascalCase（大驼峰）**

```typescript
type UserRole = "admin" | "editor" | "viewer";

type ButtonProps = {
  variant: "primary" | "secondary";
  size: "small" | "medium" | "large";
  onClick: () => void;
};
```

### 路由参数类型

- **使用** `Params` **后缀**

```typescript
// app/posts/[id]/page.tsx
interface PostParams {
  id: string;
}

export default function Post({ params }: { params: PostParams }) {
  // ...
}
```

### 泛型

- **使用单个大写字母或描述性的 PascalCase**

```typescript
function fetchData<T>() {
  /* ... */
}

interface List<ItemType> {
  items: ItemType[];
}
```
