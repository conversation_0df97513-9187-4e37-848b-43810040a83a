# lefthook.yml
pre-commit:
  parallel: true
  commands:
    lint:
      glob: "*.{js,jsx,ts,tsx,json}"
      run: npx @biomejs/biome check --no-errors-on-unmatched {staged_files}
    format:
      glob: "*.{js,jsx,ts,tsx,json}"
      run: npx @biomejs/biome format --write --no-errors-on-unmatched {staged_files}
      stage_fixed: true

commit-msg:
  commands:
    lint-commit-message:
      run: npx @biomejs/biome check --write --no-errors-on-unmatched {1}
