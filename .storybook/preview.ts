import type { Preview } from '@storybook/nextjs-vite';
import '../src/app/globals.css';

// 利用可能なテーマを定義する
const themeOptions = {
  Blue: 'theme-blue',
  Pink: 'theme-pink',
};

const preview: Preview = {
  parameters: {
    visualViewport: true,
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },

    a11y: {
      // 'todo' - show a11y violations in the test UI only
      // 'error' - fail CI on a11y violations
      // 'off' - skip a11y checks entirely
      test: 'todo',
    },
  },

  // グローバルなテーマを設定する
  globalTypes: {
    theme: {
      name: 'Theme',
      description: 'Global theme for components',
      defaultValue: 'Blue (Default)',
      toolbar: {
        icon: 'paintbrush',
        items: Object.keys(themeOptions).map((key) => ({
          value: key,
          title: key,
        })),
        showName: true,
        dynamicTitle: true,
      },
    },
  },

  decorators: [
    (Story, context) => {
      const selectedTheme = context.globals.theme;
      const themeClass = themeOptions[selectedTheme as keyof typeof themeOptions] || 'theme-blue';

      // テーマクラスを適用する
      if (typeof document !== 'undefined') {
        document.documentElement.className = themeClass;
        document.body.className = themeClass;
      }

      return Story();
    },
  ],
};

export default preview;
