// 環境変数をチェックするスクリプト
const fs = require('node:fs');
// 判断.envファイルが存在するか
if (!fs.existsSync('.env')) {
  // 存在しない場合は.env.exampleをコピー
  fs.copyFileSync('.env.example', '.env');
  console.log('.envファイルが存在しないので.env.exampleをコピーしました。');
} else {
  console.log('.envファイルが存在します。');
  // 存在する場合は.envファイルを読み込む
  let env = fs.readFileSync('.env', 'utf8');
  // #から始まる行を削除
  const envLines = env.split('\n').filter((line) => !line.startsWith('#'));
  env = envLines.join('\n');
  const NEXT_PUBLIC_API_URL = env.match(/NEXT_PUBLIC_API_URL=(.*)/)[1];
  const NEXT_PUBLIC_GOOGLE_MAPS_API_KEY = env.match(/NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=(.*)/)[1];
  console.log('.env-NEXT_PUBLIC_API_URL:', NEXT_PUBLIC_API_URL);
  console.log('.env-NEXT_PUBLIC_GOOGLE_MAPS_API_KEY:', NEXT_PUBLIC_GOOGLE_MAPS_API_KEY);

  // 環境変数
  const dockerEnv = process.env;
  console.log('dockerEnv-NEXT_PUBLIC_API_URL:', dockerEnv.NEXT_PUBLIC_API_URL);
  console.log(
    'dockerEnv-NEXT_PUBLIC_GOOGLE_MAPS_API_KEY:',
    dockerEnv.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
  );
}
