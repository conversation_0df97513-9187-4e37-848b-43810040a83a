# !!!Bundle environment variables for the browser by prefixing with NEXT_PUBLIC_

# Host
NEXT_PUBLIC_APP_HOST=dev-app-api.kenkomileage-renewal.net

# Cloud Mock
# NEXT_PUBLIC_API_URL=https://m1.apifoxmock.com/m1/5931300-5618480-default/api/v1/app

# Dev Env
NEXT_PUBLIC_API_URL=https://dev-app-api.kenkomileage-renewal.net/api/v1/app

# dev stg pro
NEXT_PUBLIC_APP_ENV=dev

# Google Maps 
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=

# Google Map ID 
NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID=

#dev code
NEXT_PUBLIC_DEV_CODE=f97bdf1cb735bb23fd9959da

# ビルド時に環境変数を含める
# docker build --build-arg NEXT_PUBLIC_API_URL=https://your-api-url.com --build-arg NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=xxxx -t mileage-webview-with-arg .

