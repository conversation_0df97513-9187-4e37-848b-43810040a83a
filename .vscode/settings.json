{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}}