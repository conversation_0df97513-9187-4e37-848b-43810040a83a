{"$schema": "https://biomejs.dev/schemas/1.8.3/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"useExhaustiveDependencies": "warn", "noUnusedImports": "off"}, "suspicious": {"noExplicitAny": "warn", "noArrayIndexKey": "off"}, "style": {"noNonNullAssertion": "warn"}, "a11y": {"useKeyWithClickEvents": "warn", "useButtonType": "error", "noSvgWithoutTitle": "off"}}, "ignore": ["node_modules", ".next", "public", "**/*.min.js"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf", "ignore": ["node_modules", ".next", "public", "**/*.min.js"]}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "always"}}, "files": {"ignore": [".next/**/*", "node_modules/**/*", "public/**/*", "build/**/*", "dist/**/*", "components/ui/**", "coverage/**/*"], "maxSize": 500000}}