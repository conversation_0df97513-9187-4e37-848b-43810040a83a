import type { QuestionOption } from '@/types/health-checkup-input';

// 基本的な「はい/いいえ」選択肢
export const yesNoOptions: QuestionOption[] = [
  { value: 1, label: 'はい' },
  { value: 2, label: 'いいえ' },
];

// 質問票2: 喫煙習慣の選択肢（2024年度以降）
export const smokingAfterFY2024Options: QuestionOption[] = [
  { value: 3, label: 'はい (条件1と条件2を両方満たす)' },
  { value: 4, label: '以前は吸っていたが、最近１ヶ月間は吸っていない（条件２のみを満たす）' },
  { value: 5, label: 'いいえ（上記以外）' },
];

// 質問票2: 喫煙習慣の選択肢（2024年度前）
export const smokingBeforeFY2024Options: QuestionOption[] = [
  { value: 1, label: 'はい' },
  { value: 2, label: 'いいえ' },
];

// 後方互換性のため
export const smokingOptions: QuestionOption[] = smokingAfterFY2024Options;

// 質問票3: 食事をかんで食べる時の状態
export const eatingStateOptions: QuestionOption[] = [
  { value: 1, label: '何でもかんで食べることができる' },
  { value: 2, label: '歯や歯ぐき、かみあわせなど気になる部分があり、かみにくいことがある' },
  { value: 3, label: 'ほとんどかめない' },
];

// 質問票3: 食べる速度
export const eatingSpeedOptions: QuestionOption[] = [
  { value: 1, label: '速い' },
  { value: 2, label: 'ふつう' },
  { value: 3, label: '遅い' },
];

// 質問票3: 間食や甘い飲み物の摂取頻度
export const snackFrequencyOptions: QuestionOption[] = [
  { value: 1, label: '毎日' },
  { value: 2, label: '時々' },
  { value: 3, label: 'ほとんど摂取しない' },
];

// 質問票4: 飲酒頻度（2024年度以降）
export const drinkingFrequencyAfterFY2024Options: QuestionOption[] = [
  { value: 4, label: '毎日' },
  { value: 5, label: '週5〜6日' },
  { value: 6, label: '週3〜4日' },
  { value: 7, label: '週1〜2日' },
  { value: 8, label: '月に1〜3日' },
  { value: 9, label: '月に1日未満' },
  { value: 10, label: 'やめた' },
  { value: 11, label: '飲まない（飲めない）' },
];

// 質問票4: 飲酒頻度（2024年度前）
export const drinkingFrequencyBeforeFY2024Options: QuestionOption[] = [
  { value: 1, label: '毎日' },
  { value: 2, label: '時々' },
  { value: 3, label: 'ほとんど飲まない（飲めない）' },
];

// 後方互換性のため
export const drinkingFrequencyOptions: QuestionOption[] = drinkingFrequencyAfterFY2024Options;

// 質問票4: 飲酒量（2024年度以降）
export const drinkingAmountAfterFY2024Options: QuestionOption[] = [
  { value: 5, label: '1合未満' },
  { value: 6, label: '1~2合未満' },
  { value: 7, label: '2~3合未満' },
  { value: 8, label: '3~5合未満' },
  { value: 9, label: '5合以上' },
];

// 質問票4: 飲酒量（2024年度前）
export const drinkingAmountBeforeFY2024Options: QuestionOption[] = [
  { value: 1, label: '1合未満' },
  { value: 2, label: '1~2合未満' },
  { value: 3, label: '2~3合未満' },
  { value: 4, label: '3合以上' },
];

// 後方互換性のため
export const drinkingAmountOptions: QuestionOption[] = drinkingAmountAfterFY2024Options;

// 質問票4: 生活習慣改善の意向
export const habitImprovementOptions: QuestionOption[] = [
  { value: 1, label: '改善するつもりはない' },
  { value: 2, label: '改善するつもりである（概ね6か月以内）' },
  { value: 3, label: '近いうちに（概ね1か月以内）改善するつもりであり、少しずつ始めている' },
  { value: 4, label: '既に改善に取り組んでいる（6か月未満）' },
  { value: 5, label: '既に改善に取り組んでいる（6か月以上）' },
];

// 質問項目ごとの選択肢マッピング
export const questionOptionsMap = {
  // 質問票1 - すべて「はい/いいえ」
  mPressure: yesNoOptions,
  mSugar: yesNoOptions,
  mFat: yesNoOptions,
  brainDisease: yesNoOptions,
  heartDisease: yesNoOptions,
  kidneyDisease: yesNoOptions,
  anemia: yesNoOptions,

  // 質問票2
  smokeAfterFY2024: smokingAfterFY2024Options,
  smokeBeforFY2024: smokingBeforeFY2024Options,
  tenFrom20: yesNoOptions,
  sweatSport: yesNoOptions,
  exercise1hour: yesNoOptions,
  wsf: yesNoOptions,

  // 質問票3
  eatEverything: eatingStateOptions,
  eatSpeed: eatingSpeedOptions,
  eatNight3: yesNoOptions,
  eatSuger: snackFrequencyOptions,
  noBreakfast3: yesNoOptions,

  // 質問票4
  wineFreAfterFY2024: drinkingFrequencyAfterFY2024Options,
  wineFreBeforFY2024: drinkingFrequencyBeforeFY2024Options,
  wineMountAfterFY2024: drinkingAmountAfterFY2024Options,
  wineMountBeforFY2024: drinkingAmountBeforeFY2024Options,
  sleepEnough: yesNoOptions,
  habitImprove: habitImprovementOptions,
  habitLessonAfterFY2024: yesNoOptions,
  habitLessonBeforeFY2024: yesNoOptions,
} as const;

// 選択肢の値からラベルを取得するヘルパー関数
export const getQuestionOptionLabel = (
  questionKey: keyof typeof questionOptionsMap,
  value: string | number,
): string => {
  const options = questionOptionsMap[questionKey];
  const numValue = typeof value === 'string' ? Number.parseInt(value, 10) : value;
  const option = options.find((opt) => opt.value === numValue);
  return option?.label || '';
};

// Others相关的选择项目定义

// 検査未実施の理由选择项
export const urineUncheckReasonOptions: QuestionOption[] = [
  { value: 1, label: '生理中' },
  { value: 2, label: '腎疾患等の基礎疾患があるため排尿障害を有する' },
  { value: 3, label: 'その他' },
];

// 診察項目の選択肢（既往歴、自覚症状、他覚症状）
export const medicalHistoryOptions: QuestionOption[] = [
  { value: 1, label: '特記事項あり' },
  { value: 2, label: '特記事項なし' },
];

// Others項目の選択肢マッピング
export const othersOptionsMap = {
  othersUrineUncheckReason: urineUncheckReasonOptions,
  past: medicalHistoryOptions,
  symptoms: medicalHistoryOptions,
  objective: medicalHistoryOptions,
} as const;

// Others選択肢の値からラベルを取得するヘルパー関数
export const getOthersOptionLabel = (
  questionKey: keyof typeof othersOptionsMap,
  value: string | number,
): string => {
  const options = othersOptionsMap[questionKey];
  const numValue = typeof value === 'string' ? Number.parseInt(value, 10) : value;
  const option = options.find((opt) => opt.value === numValue);
  return option?.label || '';
};

// 後方互換性のため
export const questionOptions = yesNoOptions;
