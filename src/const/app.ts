import { getHost } from '@/utils/get-host';
import { ROUTES } from './routes';

// 3301:フレイル予防 3302:血圧上昇習慣 3:Well-Being 3303:免疫力 3304:血糖値·中性脂肪改善習慣 6:ストレス
export enum RiskTypeNum {
  frail = '3301',
  bloodPressure = '3302',
  wellBeing = '3',
  immunity = '3303',
  bloodGlucoseAndNeutralFat = '3304',
  stress = '6',
}

// 1,2,4,5
export const SUPPORT_RISK: RiskTypeNum[] = [
  RiskTypeNum.frail,
  RiskTypeNum.bloodPressure,
  RiskTypeNum.immunity,
  RiskTypeNum.bloodGlucoseAndNeutralFat,
];
export enum RISK_PERMISSION {
  force = '1',
  random = '2',
}

export enum PrizeType {
  ITEM = 1, // 品物
  GIFTEE = 2, // 電子ギフト
  DPOINT = 3, // dポイント
}
export enum DataSourceDeviceType {
  input = 0,
  HealthCare = 1,
  AppleWatch = 2,
  HealthConnect = 4,
  Fitbit = 6,
  Omron = 7,
}
export enum DeviceType {
  HealthCare = 1,
  HealthConnect = 2,
  Fitbit = 3,
  Omron = 4,
}
export enum VitalType {
  Step = '1',
  Weight = '2',
  Blood = '3',
  BloodSugar = '4',
  Sleep = '5',
}

export enum SelectType {
  select = 1,
  update = 2,
}
const vitalTypeMap = new Map<number, string>([
  [1, 'Step'],
  [2, 'Weight'],
  [3, 'Blood'],
  [4, 'BloodSugar'],
  [5, 'Sleep'],
]);
export const VitalTypeLabels: Record<number, string> = {
  1: '体重',
  2: '睡眠時間',
  3: '血圧',
  4: '血糖',
  5: '歩数',
};
export const FitbitScope: Record<number, string> = {
  1: 'weight',
  2: 'sleep',
  5: 'activity',
};
export const initialData = [{ id: 1, label: '' }];

//DEV,STG環境
export const fitbit = {
  client_id_dev: '23QJ8P',
  client_id_stg: '23QJC2',
  callback: `https://${getHost()}${ROUTES.DATA_CONNECT.FITBIT_FINISH}`,
  auth_url: 'https://www.fitbit.com/oauth2/authorize?response_type=code',
  query_url: 'https://api.fitbit.com/1/user/-/',
  scope: 'activity weight sleep',
};

//Local環境
// export const fitbit = {
//   client_id: '23QJLN',
//   client_secret: '79dcd0836d157df745499a2917f839ae',
//   callback: `http://localhost:4399${ROUTES.DATA_CONNECT.FITBIT_FINISH}`,
//   auth_url: 'https://www.fitbit.com/oauth2/authorize?response_type=code',
//   scope: 'activity weight sleep',
// };

export const omron = {
  group: {
    id: '2de9aec01f15',
    name: 'HANYO-STG5',
  },
  partner: {
    id: 'cac10d28-ace6-4c6b-9d1a-275f1af19980',
  },
  login: {
    domain: 'https://data-stg-jp-sp.omronconnect.mobi/api/apps',
    call_back_uri: 'https://dev-app-api.kenkomileage-renewal.net/other-connect/omron/finish',
    password: 'h3T0l2K7s3Q5',
  },
  auth: {
    domain: 'https://data-stg-jp-sp.omronconnect.mobi/api/apps/xmcf9xtr7kkq',
    username: 'xiao.wang',
    password: ' h3T0l2K7s3Q5',
  },

  resource: {
    domain: 'https://b2bdsp-gw-stg-sp.odhpf.mobi/a/b2b-data-api/v1.0/wps/9/',
    wp_id: '85aaab70-b862-11e7-810a-06d42aa1d829',
    bp_id: 'bdf72f34',
  },
};
