export const COLORS = {
  text: {
    primary: 'hsl(var(--text-primary))',
    secondary: 'hsl(var(--text-secondary))',
    muted: 'hsl(0 0% 70%)',
    light: 'hsl(var(--primary-light))',
  },
  button: 'hsl(var(--button))',
  background: 'hsl(var(--background))',
  foreground: 'hsl(var(--foreground))',
  card: {
    DEFAULT: 'hsl(var(--card))',
    foreground: 'hsl(var(--card-foreground))',
    mainLight3: 'hsl(227, 100%, 98%)',
  },
  popover: {
    DEFAULT: 'hsl(var(--popover))',
    foreground: 'hsl(var(--popover-foreground))',
  },
  primary: {
    DEFAULT: 'hsl(var(--primary))',
    foreground: 'hsl(var(--primary-foreground))',
    light: 'hsl(var(--primary-light))',
    soft: 'hsl(var(--primary-soft))',
    softer: 'hsl(var(--primary-softer))',
    ranking: 'hsl(var(--primary-ranking))',

    5: 'hsl(var(--primary-5))',
    10: 'hsl(var(--primary-10))',
    20: 'hsl(var(--primary-20))',
    30: 'hsl(var(--primary-30))',
    40: 'hsl(var(--primary-40))',
    50: 'hsl(var(--primary-50))',
    60: 'hsl(var(--primary-60))',
    70: 'hsl(var(--primary-70))',
    80: 'hsl(var(--primary-80))',
    90: 'hsl(var(--primary-90))',
    100: 'hsl(var(--primary-100))',
  },
  gray: {
    5: 'hsl(0, 0%, 95%)',
    10: 'hsl(0, 0%, 90%)',
    20: 'hsl(0, 0%, 80%)',
    30: 'hsl(0, 0%, 70%)',
    40: 'hsl(0, 0%, 60%)',
    50: 'hsl(0, 0%, 50%)',
    60: 'hsl(0, 0%, 40%)',
    70: 'hsl(0, 0%, 30%)',
    80: 'hsl(0, 0%, 20%)',
    90: 'hsl(0, 0%, 10%)',
  },
  secondary: {
    DEFAULT: 'hsl(var(--secondary))',
    foreground: 'hsl(var(--secondary-foreground))',
  },
  switch: {
    DEFAULT: 'hsl(var(--switch))',
  },
  muted: {
    DEFAULT: 'hsl(var(--muted))',
    foreground: 'hsl(var(--muted-foreground))',
  },
  accent: {
    DEFAULT: 'hsl(var(--accent))',
    foreground: 'hsl(var(--accent-foreground))',
  },
  destructive: {
    DEFAULT: 'hsl(var(--destructive))',
    foreground: 'hsl(var(--destructive-foreground))',
  },
  border: {
    DEFAULT: 'hsl(var(--border))',
    input: 'hsl(0 0% 40%)',
    badge: 'hsl(var(--badge-border))',
  },
  input: 'hsl(var(--input))',
  ring: 'hsl(var(--ring))',
  chart: {
    riskScore1: 'hsl(var(--risk-score-1))',
    riskScore2: 'hsl(var(--risk-score-2))',
    riskScore3: 'hsl(var(--risk-score-3))',
    riskScoreHistory1: 'hsl(var(--risk-score-history-1))', // フレイル予防
    riskScoreHistory2: 'hsl(var(--risk-score-history-2))', // 血圧上昇習慣
    riskScoreHistory3: 'hsl(var(--risk-score-history-3))', // 免疫力
    riskScoreHistory4: 'hsl(var(--risk-score-history-4))', // 血糖値改善習慣
    riskScoreHistory5: 'hsl(var(--risk-score-history-5))', // 中性脂肪改善習慣
    // riskScoreHistory6: 'hsl(var(--risk-score-history-6))', // Well-being
    // riskScoreHistory7: 'hsl(var(--risk-score-history-7))', // ストレス推定
  },
  drawer: {
    bar: 'hsl(0 0 40%)',
  },
  scrollbar: {
    thumb: 'hsl(0 0 40%)',
  },
  success: 'hsl(151 54% 47%)',
  warning: 'hsl(32 100% 55%)',
  info: 'hsl(214 92% 60%)',
  error: 'hsl(348 78% 49%)',
  invalid: {
    border: 'hsl(0 100% 40%)',
    background: 'hsl(0 73% 97%)',
    message: 'hsl(0 100% 40%)',
  },
  main: {
    color: 'hsl(var(--main-color))',
    fix: 'hsl(var(--main-fix-color))',
  },
};
