import type { CodeOption } from '@/types/code-types';

// 開催頻度パータン
// 0:指定なし
// 1:回数
// 2:日付から選択
export const FREQUENCY_PATTERN_OPTIONS: CodeOption[] = [
  { label: '指定なし', value: '0' },
  { label: '回数', value: '1' },
  { label: '日付から選択', value: '2' },
];

// ポイント付与パータン
// 1:均一
// 2:段階的
// 3:一括
// POINT
export const POINT_PATTERN_OPTIONS: CodeOption[] = [
  { label: '均一', value: '1' },
  { label: '段階的', value: '2' },
  { label: '一括', value: '3' },
];

// 予約要否フラグ
// 0:予約不要
// 1:事前予約制
export const RESERVATION_REQUIRED_OPTIONS: CodeOption[] = [
  { label: '予約不要', value: '0' },
  { label: '事前予約制', value: '1' },
];

// 料金フラグ
export const HAS_FEE_OPTIONS: CodeOption[] = [
  { label: 'すべて', value: '0' },
  { label: '有料', value: '1' },
  { label: '無料', value: '2' },
];

// 時間単位選択肢
// 1:年度, 2:月, 3:週, 4:日
export const TIME_UNIT_OPTIONS: CodeOption[] = [
  { label: '年度', value: '1' },
  { label: '月', value: '2' },
  { label: '週', value: '3' },
  { label: '日', value: '4' },
];

// ポイントの有無
// all:すべて
// true:ポイントあり
// false:ポイントなし
export const HAS_POINT_OPTIONS: CodeOption[] = [
  { label: 'すべて', value: 'all' },
  { label: 'ポイントあり', value: 'true' },
  { label: 'ポイントなし', value: 'false' },
];

// 距離选择肢
// 0:指定なし, 500:500m, 1000:1km, 2000:2km, 5000:5km, 10000:10km, 20000:20km
export const DISTANCE_OPTIONS: CodeOption[] = [
  { label: '指定なし', value: '0' },
  { label: '500m', value: '500' },
  { label: '1km', value: '1000' },
  { label: '2km', value: '2000' },
  { label: '5km', value: '5000' },
  { label: '10km', value: '10000' },
  { label: '20km', value: '20000' },
];

export const SORT_OPTIONS: CodeOption[] = [
  { label: '人気順', value: 'popular' },
  { label: '距離近い順', value: 'distance' },
  { label: '新着順', value: 'new' },
];
