import type { CodeOption } from '@/types/code-types';

// 距離选择肢
// 0:指定なし, 500:500m, 1000:1km, 2000:2km, 5000:5km, 10000:10km, 20000:20km
export const DISTANCE_OPTIONS: CodeOption[] = [
  { label: '指定なし', value: '0' },
  { label: '500m', value: '500' },
  { label: '1km', value: '1000' },
  { label: '2km', value: '2000' },
  { label: '5km', value: '5000' },
  { label: '10km', value: '10000' },
  { label: '20km', value: '20000' },
];

// 時間単位選択肢
// 1:年度, 2:月, 3:週, 4:日
export const TIME_UNIT_OPTIONS: CodeOption[] = [
  { label: '年度', value: '1' },
  { label: '月', value: '2' },
  { label: '週', value: '3' },
  { label: '日', value: '4' },
];

// sortKey
export const SORT_OPTIONS: CodeOption[] = [
  { label: '人気順', value: 'popular' },
  { label: '距離近い順', value: 'distance' },
  { label: '新着順', value: 'new' },
];
