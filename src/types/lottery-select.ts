export interface LotteryDrawingResponse {
  lotteryDetail: LotteryDetail;
  currAppInfo: CurrAppInfo;
}
export interface LotteryHistoryResponse {
  historyLottery: LotteryDetail[];
}
export interface PrizeData {
  prizeId: number;
  prizeNm: string;
  prizeAppNum: number;
  prizeDonation: number;
  needPoint: number;
  chosenPrizePoint: number;
}

export interface CurrAppInfo {
  point: number;
  choicedPoint: number;
  remainPoint: number;
  choiceList: PrizeData[];
}

export interface LotteryDetail {
  lotteryId: number;
  organizerNm: string;
  prizeApplyTo: string;
  pointValidFrom: string;
  pointValidTo: string;
  lotteryOn: string;
  lotteryTitle: string;
  lotteryDetail: string;
  lotteryImageFile: string;
  lotteryMode: number;
  lotteryYyyyMm?: string | undefined;
  status?: number | undefined;
  lotteryStatus?: number | undefined;
}
//ユーザ抽選結果取得API
export interface LotteryResultResponse {
  UsrLottoRes: UsrLottoRes;
}

export interface WinPrize {
  prizeId: number;
  prizeDispPrio: number;
  prizeType: number;
  prizeNm: string;
  prizeImageFile: string;
  donation: number; //1:頂いた 2:寄付した
  recvDispFlg: number; //0: 未受取　1:受取済
  prizeSchedDt: string;
  pickUpDeadline: string;
  invalidFlg: number; //0: 有効　1:無効
  uniqId: number;
}

export interface UsrLottoRes {
  lotteryId: number;
  lotteryOn: string;
  lotteryTitle: string;
  winPrizes?: WinPrize[];
}
//ユーザ抽選景品選択取得API
export interface UserLotteryChoiceListResponse {
  userLotteryChoiceList: PrizeData[];
  cntPrize: number;
  sumPrizeAppNum: number;
  sumChosenPrizePoint: number;
}
export interface UserLotteryChoice {
  prizeId: number;
  prizeNm: string;
  prizeAppNum: number;
  prizeDonation: number;
  needPoint: number;
  chosenPrizePoint: number;
}
// 景品一覧取得API
export interface PrizeItem {
  prizeId: number;
  prizeType: number;
  prizeNm: string;
  prizeNum: number;
  prizeImageFile: string;
  prizeDonation: number;
  needPoint: number;
}

export interface LotteryResponse {
  lotteryId: number;
  lotteryMode: number;
  prizeList: PrizeItem[];
}

//抽選詳細取得API
export interface LotteryDetailResponse {
  lotteryDetail: LotteryDetail;
}
export interface LotteryInfoResponse {
  lotteryDetail: LotteryDetail;
}

export interface GifteeInfo {
  initial_point: number;
  url: string;
  expired_at: string;
}
// export interface LotteryDetail {
//   lotteryId: number;
//   organizerNm: string;
//   prizeApplyTo: string; // 日期格式：YYYY-MM-DD
//   pointValidFrom: string; // 日期格式：YYYY-MM-DD
//   pointValidTo: string; // 日期格式：YYYY-MM-DD
//   lotteryOn: string; // 日期格式：YYYY-MM-DD
//   lotteryTitle: string;
//   lotteryDetail: string;
//   lotteryImageFile: string;
//   lotteryMode: number;
// }
