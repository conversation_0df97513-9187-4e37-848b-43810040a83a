export interface FavoriteRequest {
  targetId: string; // 目標ID
  favoriteType: string; // event walkingCourse coupon
}

export interface PinRequest {
  latitude?: string;
  longitude?: string;
  distance?: number;
  inputContent?: string;
  pinType?: string;
  hasPoints?: string;
  remainingChallenges?: string;
  distanceFromHere?: number;
  hideCompletedCourses?: boolean;
  hasFee?: string;
  eventTypeList: string[];
  couponCategoryList: string[];
}

export interface PinResponse {
  walkingCourseDispFlg: string;
  eventDispFlg: string;
  couponDispFlg: string;
  pinList: Pin[];
}

export interface Pin extends WalkingCoursePin, EventPin, CouponPin {
  pinType: string; //1:イベント 2:クーポン 3:ウォーキングコース
  courseId: string;
  courseName: string;
  stampSpotList: StampSpot[];
  courseRouteList: CourseRoute[];
  createdAt: string;
  eventId: string;
  eventName: string;
  eventLatitude: string;
  eventLongitude: string;
  shopId: string;
  shopName: string;
  shopLatitude: string;
  shopLongitude: string;
  favoriteFlg: string;
  distanceFromMapCenter: number;
}

export interface WalkingCoursePin {
  pinType: string;
  courseId: string;
  courseName: string;
  stampSpotList: StampSpot[];
  courseRouteList: CourseRoute[];
  createdAt: string;
  favoriteFlg: string;
  distanceFromMapCenter: number;
}

export interface EventPin {
  pinType: string;
  eventId: string;
  eventName: string;
  eventLatitude: string;
  eventLongitude: string;
  favoriteFlg: string;
  distanceFromMapCenter: number;
}

export interface CouponPin {
  pinType: string;
  shopId: string;
  shopName: string;
  shopLatitude: string;
  shopLongitude: string;
  favoriteFlg: string;
  distanceFromMapCenter: number;
}

export interface StampSpot {
  spotId: string;
  spotName: string;
  spotLatitude: string;
  spotLongitude: string;
}

export interface CourseRoute {
  routeId: number;
  path: Path[];
}

export interface Path {
  lng: string;
  lat: string;
}

export interface MapSearchRequest {
  latitude: string;
  longitude: string;
  courseIdList: string[];
  eventIdList: string[];
  shopIdList: string[];
}

export interface MapSearchResponse {
  resultList: MapResult[];
}

export interface MapResult {
  favoriteFlg?: string;
  pinType: string;
  resultId: string;
  imagePath: string;
  resultName: string;
  organizerNm?: string;
  eventStartDate?: string;
  eventEndDate?: string;
  venueName?: string;
  hasFee?: string;
  isReservationRequired?: string;
  pointPattern?: string;
  pointPatternDetails?: PointPatternDetails;
  distanceFromHere?: number;
  challengeStatus?: string;
  rechallengeAllowed?: string;
  rechallengeInterval?: string;
  stampPoints?: number;
  completeBonus?: number;
  estimatedTimeHours?: number;
  estimatedTimeMinutes?: number;
  estimatedDistance?: number;
  steps?: number;
  shopTypeCodeKey?: string;
  couponList?: CouponList[];
}

export interface PointPatternDetails {
  totalTimes: number;
  details: Detail[];
}

export interface Detail {
  sequence: number;
  points: number;
}

export interface CouponList {
  couponId: string;
  couponImageName: string;
  couponName: string;
  usageLimitPerUser: number;
  endDate: string;
  couponFavoriteFlg: string;
  usedByCount?: number;
}

export interface SearchCandidateResponse {
  candidateList: Candidate[];
}

export interface Candidate {
  candidateId: string;
  pinType: string;
  candidateName: string;
  candidateDetail: string;
}

export interface PostRequest {
  latitude: string;
  longitude: string;
  distance: number;
}

export interface PostResponse {
  postList: Post[];
}

export interface Post {
  postId: string;
  latitude: string;
  longitude: string;
  thumbnailImage: string;
}
