import type { TimeRangeType } from '@/types/enums';

export interface GraphSearchParam {
  startDate: string;
  endDate: string;
  timeRange: TimeRangeType;
}

// 毎日歩数記録
export interface DailyStep {
  date: string; // 形式: YYYY-MM-DD
  step: number;
}

// 月間平均歩数記録
export interface MonthlyAverageStep {
  date: string; // 形式: YYYY-MM
  monthAverageStep: number;
}

// 歩数グラフデータ構造
export interface StepGraphData {
  // 毎日歩数リスト
  stepList: DailyStep[];

  // 年度月別歩数平均値リスト
  yearStepList: MonthlyAverageStep[];

  // 選択期間の平均歩数
  periodAverageStep: number;

  // 歩数目標値
  stepTarget: number;

  // 目標達成日数
  targetAchieve: number;
}

// 毎日歩行距離・時間記録
export interface DailyStepDistanceTime {
  date: string; // 形式: YYYY-MM-DD
  distance: number; // 距離、単位はキロメートルまたはメートル
  exerciseTime: number; // 運動時間、単位は分
}

// 月間平均歩行距離・時間記録
export interface MonthlyAverageStepDistanceTime {
  date: string; // 形式: YYYY-MM
  monthAverageDistance?: number; // 月平均距離
  monthAverageTime?: number; // 月平均時間
  monthAverageWeight?: number; // 月平均体重（注意：データ内で2月は異なる属性名を使用）
  monthAverageBmi?: number; // 月平均BMI（注意：データ内で2月は異なる属性名を使用）
}

// 歩行距離・時間グラフデータ構造
export interface StepDistanceTimeGraphData {
  // 毎日歩行距離・時間リスト
  stepDistanceTime: DailyStepDistanceTime[];

  // 年度月別平均歩行距離・時間リスト
  yearStepDistanceTime: MonthlyAverageStepDistanceTime[];

  // 選択期間の平均距離
  periodAverageDistance: number;

  // 選択期間の平均運動時間
  periodAverageTime: number;

  // 距離目標値
  distanceTarget: number;

  // 運動時間目標値
  exerciseTimeTarget: number;
}

// 毎日睡眠時間記録
export interface DailySleep {
  date: string; // 形式: YYYY-MM-DD
  sleepTime: number; // 睡眠時間、単位は分
}

// 月間平均睡眠時間記録
export interface MonthlyAverageSleep {
  date: string; // 形式: YYYY-MM
  monthAverageSleepTime: number; // 月平均睡眠時間、単位は分
}

// 睡眠時間グラフデータ構造
export interface SleepGraphData {
  // 毎日睡眠時間リスト
  sleep: DailySleep[];

  // 年度月別平均睡眠時間リスト
  yearSleep: MonthlyAverageSleep[];

  // 選択期間の平均睡眠時間、単位は時間（注意：dailyとmonthlyの単位と異なる可能性）
  periodAverageSleepTime: number;
}

// 毎日体脂肪率記録
export interface DailyFatPercentage {
  date: string; // 形式: YYYY-MM-DD
  percentage: number; // 体脂肪率、パーセント値
}

// 月間平均体脂肪率記録
export interface MonthlyAverageFatPercentage {
  date: string; // 形式: YYYY-MM
  monthAveragePercentage: number; // 月平均体脂肪率、パーセント値
}

// 体脂肪率グラフデータ構造
export interface FatPercentageGraphData {
  // 毎日体脂肪率リスト
  fatPercentage: DailyFatPercentage[];

  // 年度月別平均体脂肪率リスト
  yearFatPercentage: MonthlyAverageFatPercentage[];

  // 選択期間の平均体脂肪率
  periodAveragePercentage: number;
}

// 毎日体重・BMI記録
export interface DailyWeightBmi {
  date: string; // 形式: YYYY-MM-DD
  weight: number; // 体重、単位はキログラム
  bmi: number; // 体格指数（BMI）
}

// 月間平均体重・BMI記録
export interface MonthlyAverageWeightBmi {
  date: string; // 形式: YYYY-MM
  monthAverageWeight: number; // 月平均体重
  monthAverageBmi: number; // 月平均BMI
}

// 体重・BMIグラフデータ構造
export interface WeightBmiGraphData {
  // 毎日体重・BMIリスト
  weightBmi: DailyWeightBmi[];

  // 年度月別平均体重・BMIリスト
  yearWeightBmi: MonthlyAverageWeightBmi[];

  // 選択期間の平均体重
  periodAverageWeight: number;

  // 選択期間の平均BMI
  periodAverageBmi: number;

  // 選択期間の平均体重と前期間との比較値（差分の可能性）
  periodAverageWeightCompare: number;

  // 選択期間の平均BMIと前期間との比較値（差分の可能性）
  periodAverageBmiCompare: number;
}

// 毎日エネルギー消費記録
export interface DailyEnergy {
  date: string; // 形式: YYYY-MM-DD
  energy: number; // 毎日消費エネルギー・カロリー
}

// 月間平均エネルギー消費記録
export interface MonthlyAverageEnergy {
  date: string; // 形式: YYYY-MM-DD（注意：他のインターフェースと異なり、具体的な日付を含む）
  monthAverageEnergy: number; // 月平均消費エネルギー・カロリー
}

// エネルギー消費グラフデータ構造
export interface EnergyGraphData {
  // 毎日エネルギー消費リスト
  energyList: DailyEnergy[];

  // 年度月別平均エネルギー消費リスト
  yearEnergyList: MonthlyAverageEnergy[];

  // 選択期間の平均エネルギー消費
  periodAverageEnergy: number;

  // エネルギー消費目標値
  energyTarget: number;

  // 目標達成回数または割合
  targetAchieve: number;
}

// 毎日血圧記録
export interface DailyBloodPressure {
  date: string; // 日付形式: YYYY-MM-DD
  // 午前測定データ
  measureAtAm: string; // 午前測定時刻、形式: HH:MM
  highAm: number; // 午前収縮期血圧（高血圧）
  lowAm: number; // 午前拡張期血圧（低血圧）
  // 午後測定データ
  measureAtPm: string; // 午後測定時刻、形式: HH:MM
  highPm: number; // 午後収縮期血圧（高血圧）
  lowPm: number; // 午後拡張期血圧（低血圧）
}

// 年間平均血圧記録
export interface YearlyAverageBloodPressure {
  date: string; // 月形式: YYYY-MM
  highAmAverage: number; // 年間午前平均収縮期血圧
  lowAmAverage: number; // 年間午前平均拡張期血圧
  highPmAverage: number; // 年間午後平均収縮期血圧
  lowPmAverage: number; // 年間午後平均拡張期血圧
}

// 血圧グラフデータ構造
export interface BloodPressureGraphData {
  // 毎日血圧記録リスト
  bloodPressure: DailyBloodPressure[];

  // 年間平均血圧記録リスト
  yearBloodPressure: YearlyAverageBloodPressure[];

  // 選択期間の午前平均収縮期血圧
  periodHighAmAverage: string | number; // 注意：サンプルデータで文字列のため、string|number型

  // 選択期間の午前平均拡張期血圧
  periodLowAmAverage: number;

  // 選択期間の午後平均収縮期血圧
  periodHighPmAverage: number;

  // 選択期間の午後平均拡張期血圧
  periodLowPmAverage: number;
}

// 毎日血糖記録
export interface DailyBloodGlucose {
  date: string; // 日付形式: YYYY-MM-DD
  onceDailyBg: number; // 1日1回血糖値
  beforeBfBg: number; // 朝食前血糖値
  afterBfBg: number; // 朝食後血糖値
  beforeLnBg: number; // 昼食前血糖値
  afterLnBg: number; // 昼食後血糖値
  beforeDnBg: number; // 夕食前血糖値
  afterDnBg: number; // 夕食後血糖値
  beforeSlBg: number; // 就寝前血糖値
}

// 月間平均血糖記録
export interface MonthlyAverageBloodGlucose {
  date: string; // 月形式: YYYY-MM
  monthAverageOnceDailyBg: number; // 月平均1日1回血糖値
  monthAverageBeforeBfBg: number; // 月平均朝食前血糖値
  monthAverageAfterBfBg: number; // 月平均朝食後血糖値
  monthAverageBeforeLnBg: number; // 月平均昼食前血糖値
  monthAverageAfterLnBg: number; // 月平均昼食後血糖値
  monthAverageBeforeDnBg: number; // 月平均夕食前血糖値
  monthAverageAfterDnBg: number; // 月平均夕食後血糖値
  monthAverageBeforeSlBg: number; // 月平均就寝前血糖値
}

// 血糖グラフデータ構造
export interface BloodGlucoseGraphData {
  // 毎日血糖記録リスト
  bloodGlucose: DailyBloodGlucose[];

  // 月間平均血糖記録リスト
  yearBloodGlucose: MonthlyAverageBloodGlucose[];

  // 選択期間の平均血糖値
  periodAverageBloodGlucose: number;
}

export type GraphTab =
  | 'step'
  | 'stepDistanceTime'
  | 'sleep'
  | 'fatPercentage'
  | 'weightBmi'
  | 'energy'
  | 'bloodPressure'
  | 'bloodGlucose';

export type TimeRange = 'week' | 'month' | 'year';
