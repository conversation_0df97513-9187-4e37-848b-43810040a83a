/**
 * Code
 */
export interface Code {
  createdAt: string;
  description: string;
  enabled: boolean;
  id: string;
  label: string;
  sort: number;
  type: string;
  updatedAt: string;
  value: number;
}

/**
 * CodeOption
 */
export interface CodeOption {
  label: string;
  value: string;
}

/**
 * BaseCode
 */
export interface BaseCode {
  codeKey: string;
  codeName: string;
  metaData?: {
    data?: number;
    name?: string;
    val?: number | string | null | boolean;
  };
}

/**
 * BaseType
 */
export interface BaseType {
  typeKey: string;
  typeName: string;
  codeList: BaseCode[];
}

/**
 * CommonCodesData
 */
export interface CommonCodesData {
  organizerTypeList: BaseType[];
  typeList: BaseType[];
}

export interface CommonCodesResponse {
  code: string;
  message: string;
  data: CommonCodesData;
}
