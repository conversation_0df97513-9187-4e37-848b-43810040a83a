// 団体情報取
export interface OrganizerInfoResponse {
  optionUseStatus?: OptionUseStatusType[];
  organizerList?: OrganizerType[];
}
// オプション機能利用状態
export interface OptionUseStatusType {
  fcnId?: number;
  useSts?: number;
}

export interface OrganizerType {
  organizerId?: number;
  organizerCode?: string;
  organizerName?: string;
  zipCd?: string;
  address?: string;
  loginUrl?: string;
  registerUrl?: string;
  organizerSetting?: OrganizerSettingType;
  groupList?: GroupType[];
}

export interface OrganizerSettingType {
  icon?: string;
  character?: string;
}

export interface GroupType {
  groupId?: number;
  organizerId?: number;
  groupCd?: string;
  groupKind?: number;
  groupNm?: string;
  groupKana?: string;
  webPref?: string;
  webAddress1?: string;
  rankingFlg?: number;
  lotteryJoinFlg?: number;
  pointExchangeFlg?: number;
  isPublic?: number;
  groupType?: number;
}

export interface FriendType {
  friendUserId?: number;
}
export interface FriendOrganizerResponse extends OrganizerType {
  friendUserId?: number;
}

export type MergedType = FriendOrganizerResponse | OrganizerInfoResponse;

export interface OrganizerSetupResponse {
  fcnId?: number;
  organizerId?: number;
  baseSetting?: BaseSettingType[];
  optionSetting?: BaseSettingType[];
}

export interface BaseSettingType {
  childFcnId?: number;
  configurations?: LableType[];
}
export interface LableType {
  key?: string;
  value?: string;
}

export interface AccountProfileResponse {
  externalUserId?: string;
  userInfoLoginFlg?: number;
  externalLoginFlg?: number;
  jpkiLoginFlg?: number;
  loginMethod?: number;
  biologicalAuth?: number;
  smsAuth?: number;
  lastUsedDate?: string;
  checkAdvanceFlg?: number;
  nickName?: string;
  sex?: number;
  birthDt?: string;
  organizers?: OrganizersType[];
}

export interface LitaTokenResponse {
  litaId?: string;
  nickName?: string;
  lastName?: string;
  firstName?: string;
  lastNameKana?: string;
  firstNameKana?: string;
  sex?: number;
  birthDt?: string;
  zipCd?: string;
  pref?: number;
  address1?: string;
  address2?: string;
  isWorking?: number;
  companyName?: string;
  departmentName?: string;
  phoneNo?: string;
  height?: number;
  weight?: number;
  pace?: number;
  email?: string;
  level?: number;
  iconUrl?: string;
  insuranceCardNo?: string;
  remark?: RemarkType[];
  organizers?: OrganizersType[];
}

export interface RemarkType {
  index?: number;
  key?: string;
  value?: string;
}

export interface OrganizersType {
  organizerId?: number;
  organizerName?: string;
  organizerCode?: string;
  groupId?: number;
  groupName?: string;
  isDefault?: number;
}
export interface OrganizersLoginType {
  organizerId?: number;
  organizerName?: string;
  organizerCode?: number;
  groupName?: string;
  groupId?: number;
  isDefault?: number;
}

export interface AuthLoginResponse {
  userId?: number;
  accessToken?: string;
  refreshToken?: string;
  organizers?: OrganizersType[];
}

export interface AuthLoginRequest {
  deviceId?: string;
  organizerId?: number;
  groupId?: number;
  loginMethod?: string;
  litaAccessToken?: string;
  litaRefreshToken?: string;
  litaId?: string;
  nickName?: string;
  lastName?: string;
  firstName?: string;
  lastNameKana?: string;
  firstNameKana?: string;
  sex?: number;
  birthDt?: string;
  zipCd?: string;
  pref?: number;
  address1?: string;
  address2?: string;
  isWorking?: number;
  companyName?: string;
  departmentName?: string;
  phoneNo?: string;
  height?: number;
  weight?: number;
  pace?: number;
  email?: string;
  level?: number;
  iconUrl?: string;
  insuranceCardNo?: number;
  insuredSymbol?: number;
  insuredNumber?: number;
  insuredBranchNo?: number;
  remark?: RemarkType[];
}

export interface FormData {
  sex?: string;
  birthDt: Date | string;
  nickName?: string;
  groupId: string;
}

export interface RegisterAnonymousRequest {
  deviceId?: string;
  organizerId?: number;
  groupId?: number;
  sex?: number;
  birthDt: string;
  nickName?: string;
}
export interface RegisterAnonymousResponse {
  userId?: number;
  accessToken?: string;
  refreshToken?: string;
}

export interface UserProfileRequest {
  litaAccessToken?: string;
  litaRefreshToken?: string;
  nickName?: string;
  lastName?: string;
  firstName?: string;
  lastNameKana?: string;
  firstNameKana?: string;
  sex?: number;
  birthDt?: string;
  zipCd?: string;
  pref?: number;
  address1?: string;
  address2?: string;
  isWorking?: number;
  companyName?: string;
  departmentName?: string;
  phoneNo?: string;
  height?: number;
  weight?: number;
  pace?: number;
  email?: string;
  level?: number;
  iconUrl?: string;
  insuranceCardNo?: string;
  remark?: RemarkType[];
}

export interface AccountProfilePatchRequest {
  pushSendFlg?: number;
  deviceToken?: string;
  externalUserId?: string;
  userInfoLoginFlg?: number;
  externalLoginFlg?: number;
  jpkiLoginFlg?: number;
  loginMethod?: number;
  biologicalAuth?: number;
  smsAuth?: number;
  deviceId?: string;
  lastUsedDate?: string;
}

export interface OptionsType {
  value: string;
  name: string;
}

export interface OthersInfoType {
  height?: string; //身長
  weight?: string; //体重
  pace?: string; //歩幅
  insuranceCardNo?: string; // 保険証番号
  insuranceCardNoA?: string; // 被保険者記号
  insuranceCardNoAB?: string; // 被保険者番号
  insuranceCardNoABc?: string; // 被保険者番号
  insuranceCardNoABD?: string; // 枝番
}

export interface LooseObject {
  title?: string;
  step?: number;
  info?: {
    key?: string | number | symbol | undefined;
    value?: string;
    lable?: string;
    index?: string;
  }[];
}
export interface LooseObjectItem {
  title?: string;
  step?: number;
  info?: { key?: string | number; value?: string; lable?: string; index?: string }[];
}

export interface LitaUserInfoResponse {
  litaAccessToken?: string;
  litaRefreshToken?: string;
  litaId?: string;
  nickName?: string;
  lastName?: string;
  firstName?: string;
  lastNameKana?: string;
  firstNameKana?: string;
  sex?: number;
  birthDt?: string;
  zipCd?: string;
  pref?: number;
  address1?: string;
  address2?: string;
  isWorking?: number;
  companyName?: string;
  departmentName?: string;
  phoneNo?: string;
  height?: number;
  weight?: number;
  pace?: number;
  email?: string;
  level?: number;
  iconUrl?: string;
  insuranceCardNo?: number;
  insuredSymbol?: number;
  insuredNumber?: number;
  insuredBranchNo?: number;
  remark?: RemarkType[];
  organizers?: OrganizersType[];
}
export interface registerWithAuthResponse {
  userId?: number;
  userName?: string;
  accessToken?: string;
  refreshToken?: string;
}
export interface registerWithAuthRequest {
  deviceId?: string;
  organizerId?: number;
  groupId?: number;
  litaId?: string;
  litaAccessToken?: string;
  litaRefreshToken?: string;
  loginMethod?: string;
  nickName?: string;
  lastName?: string;
  firstName?: string;
  lastNameKana?: string;
  firstNameKana?: string;
  sex?: number;
  birthDt?: string;
  zipCd?: string;
  pref?: number;
  address1?: string;
  address2?: string;
  isWorking?: number;
  companyName?: string;
  departmentName?: string;
  phoneNo?: string;
  height?: number;
  weight?: number;
  pace?: number;
  email?: string;
  level?: number;
  iconUrl?: string;
  insuranceCardNo?: number;
  insuredSymbol?: number;
  insuredNumber?: number;
  insuredBranchNo?: number;
  remark?: RemarkType[];
}
export interface baseSettingType {
  fcnId?: number;
  organizerId?: number;
  childFcnId?: string;
  baseSetting?: { childFcnId?: number; configurations?: { key?: string; value?: string }[] }[];
  optionSetting?: { childFcnId?: number; configurations?: { key?: string; value?: string }[] }[];
}

export interface organizerTermsResponse {
  organizerId?: number;
  commonTerm?: string;
  organizerTerms?: {
    organizerTerm?: string;
    individualTerm1Content?: string;
    individualTerm2Content?: string;
    individualTerm3Content?: string;
  };
}

export interface UserOrganizersResponse {
  organizers: OrganizersType[];
}
