import type { X } from 'lucide-react';

export type IconType = typeof X;

export type RiskType =
  | 'frail'
  | 'hypertension'
  | 'immunity'
  | 'bloodSugar'
  | 'neuterFat'
  | 'wellBeing'
  | 'stress';

export type IconNameType =
  | 'Circle'
  | 'Diamond'
  | 'Hexagon'
  | 'Pentagon'
  | 'Square'
  | 'Star'
  | 'Triangle';

export interface ScoreChartConfig {
  value: string;
  key: RiskType;
  name: string;
  icon: IconType;
  svgIcon: (props: { cx: number; cy: number; color: string }) => React.ReactNode;
  color: string;
}

export interface ScoreData {
  date: string;
  year: string;
  month: string;
  day: string;
  label: string;
  isHeadOfYear: boolean;
  isEmpty?: boolean;
  frail?: number;
  hypertension?: number;
  immunity?: number;
  bloodSugar?: number;
  neuterFat?: number;
  wellBeing?: number;
  stress?: number;
}

export interface AssetScoreEntity {
  day: string;
  score: string;
}

export interface ChartDateRange {
  value: string;
  name: string;
  gapType: string;
  gap: number;
  maxTicks: number;
}

export interface HealthScoreHistoryListResponse {
  graph_list?: HealthScoreHistoryListResponseUnwrap;
}

export interface HealthScoreHistoryListResponseUnwrap {
  frail?: AssetScoreEntity[];
  hypertension?: AssetScoreEntity[];
  immunity?: AssetScoreEntity[];
  health_checkup_hba1c?: AssetScoreEntity[];
  health_checkup_tg?: AssetScoreEntity[];
  well_being?: AssetScoreEntity[];
  stress?: AssetScoreEntity[];
}

export interface HealthScoreHistoryListResponseFlat {
  date: string;
  frail?: string;
  hypertension?: string;
  immunity?: string;
  health_checkup_hba1c?: string;
  health_checkup_tg?: string;
  well_being?: string;
  stress?: string;
}

export interface HealthScoreJudgmentResponse {
  judgment_result?: {
    judgment_date: string;
    judgment_result_data: {
      judgment_result_type: string;
      step: string;
      score: string;
      asset_type: string;
      improve_point_type: string;
      asset_failure_type: string[];
      target_spe_name: string;
      target_spe_value: string;
    };
    top_rand_msg: number;
    top_rand_img: number;
    next_judgment_date: string;
  };
}

export interface HealthScoreMissionResponse {
  judgment_result_mission_list?: HealthScoreMission[];
}

export interface HealthScoreMission {
  mission_id: string;
  mission_title: string; //"今週は毎日_OBJECTIVE_頃までに寝る"
  mission_detail: string; //"「からだとこころの健康」リスクの改善を目指して、このミッションに取り組んでみましょう！"
  achieve_day: string; //"0"
  target_achieve_day: string; //"1"
  daily_point: string; //"1"
  achieve_point: string; //"1"
}
