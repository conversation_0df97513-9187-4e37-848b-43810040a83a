export enum ChallengeType {
  //1回のみ挑戦可能
  ONE_TIME = 'one_time',
  //複数回挑戦可能(1日1回)
  DAILY = 'daily',
  //複数回挑戦可能(1週1回)
  WEEKLY = 'weekly',
  //複数回挑戦可能(1月1回)
  MONTHLY = 'monthly',
}

export interface FixedCourse {
  id: number;
  name: string;
}

export interface StampSpotSlideData {
  courseId: number;
  courseName: string;
  stampSpots: StampSpot[];
}

export interface WalkingCourseListResponse {
  data: WalkingCourseListData[];
}

export interface WalkingCourseListData {
  id: number;
  name: string;
}

export interface WalkingCourseListRequest {
  id: number;
  name: string;
}

// API Response
export interface WalkingCourseDetailResponse {
  courseId: number;
  courseName: string;
  description: string;
  imagePath: string;
  endDate: string;
  estimatedTimeHours: number;
  estimatedTimeMinutes: number;
  estimatedDistance: number;
  steps: number;
  stampPoints: number;
  completeBonus: number;
  rechallengeAllowed: number;
  rechallengeInterval: number;
  rechallengePoints: number;
  organizerNm: string;
  rechallengeState: number;
  rechallengeDate?: string;
  stampSpotList: StampSpotResponse[];
  courseRouteList: CourseRouteResponse[];
  participations?: ParticipationResponse[];
  canRechallenge?: boolean;
}

export interface CourseRouteResponse {
  routeId: number;
  path: {
    lat: string;
    lng: string;
  }[];
}

export interface StampSpotResponse {
  spotId: number;
  spotName: string;
  address?: string;
  latitude: string;
  longitude: string;
  imagePath?: string;
}

// スタンプ情報
export interface ParticipationStampSpotCheckData {
  spotId: number;
  isChecked?: boolean;
  createdAt?: string;
}

export interface ParticipationResponse {
  challengeCount: number;
  isCompleted: boolean;
  stampList: ParticipationStampSpotCheckData[];
}

export interface WalkingCourseDetailData {
  id: number;
  name: string;
}

export interface ListWalkingCourseRequest {
  latitude?: string;
  longitude?: string;
  filters: ListWalkingCourseFilters;
  sort?: string;
}

export interface ListWalkingCourseFilters {
  courseNameQuery?: string;
  hasPoints?: boolean;
  remainingChallenges?: string;
  distanceFromHere?: number;
  hideCompletedCourses?: boolean;
}

export interface ListWalkingCourseResponse {
  coursesList: WalkingCourse[];
}

// element List
export interface WalkingCourse {
  courseId: number;
  imagePath?: string;
  distanceFromHere: number;
  courseName: string;
  challengeStatus: number;
  rechallengeAllowed: boolean;
  rechallengeInterval?: number;
  organizerNm: string;
  stampPoints: number;
  completeBonus: number;
  estimatedTimeHours: number;
  estimatedTimeMinutes: number;
  estimatedDistance: string;
  steps: number;
}

export interface SearchWalkingCourseNameRequest {
  latitude: string;
  longitude: string;
  courseNameQuery: string;
}

export interface SearchWalkingCourseNameResponse {
  coursesList: WalkingCourseName[];
}

export interface WalkingCourseName {
  courseId: number;
  courseName: string;
}

// マップウォーキングコース取得API
export interface MapWalkingCourseRequest {
  courseId: number;
  latitude: string;
  longitude: string;
  spotId: number;
}

export interface MapWalkingCourseResponse {
  walkingCoursesList: WalkingCoursesList[];
}

export interface WalkingCoursesList {
  courseId: number;
  courseName: string;
  description: string;
  imagePath: string;
  endDate: string;
  estimatedTimeHours: number;
  estimatedTimeMinutes: number;
  estimatedDistance: string;
  steps: number;
  stampPoints: number;
  completeBonus: number;
  rechallengeAllowed: boolean;
  rechallengeInterval: number;
  rechallengePoints: number;
  organizerNm: string;
  stampSpotList: StampSpot[];
  courseRouteList: CourseRouteList[];
  participations: Participation[];
}

export interface StampSpot {
  imagePath?: string;
  latitude: string;
  longitude: string;
  spotId: number;
  spotName: string;
}

export interface CourseRouteList {
  path: number[];
  routeId: number;
}

export interface Participation {
  challengeCount: number;
  isCompleted: boolean;
  stampList: StampList[];
}

export interface StampList {
  isChecked: boolean;
  spotId: number;
}

// スタンプ打刻API
export interface RecordStampRequest {
  courseId: number;
  latitude: string;
  longitude: string;
  spotId: number;
}

export interface RecordStampResponse {
  challengeCount: number;
  isCompleted: boolean;
  stampCount: number;
}

export interface StampSpotCardData {
  courseId: number;
  courseName: string;
  runCount: number;
  stampPoints: number;
  completeBonus: number;
  isCompleted: boolean;
  rechallengePoints: number;
  stampList: SortedStampSpotItemData[];
}

export interface SortedStampSpotItemData {
  spotId: number;
  createdAt?: string;
  isChecked?: boolean;
  stampPoints?: number;
  completeBonus?: number;
  isGoal: boolean;
  runCount: number;
  rechallengePoints?: number;
}
