export interface KeywordItem {
  id: string;
  text: string;
}

/**
 * クーポンキーワード候補検索 レスポンスモデル
 */
export interface KeywordSearchResponse {
  suggestions: KeywordItem[];
}

/**
 * クーポン検索 リクエストモデル
 */
export interface SearchCouponRequest {
  sortType: string;
  latitude?: string;
  longitude?: string;
  filters?: {
    keyword?: string;
    categories: string[];
    distance?: number;
  };
}

export interface SearchCouponListItem {
  couponId: string;
  couponName: string;
  imagePath?: string;
  usageLimitPerUser: number;
  endDate: string;
  userCount: number;
}

export interface SearchShopListItem {
  shopId: string;
  shopName: string;
  shopIconPath: string;
  distanceFromHere: number;
  shopType: string;
  couponList: SearchCouponListItem[];
  latitude: string;
  longitude: string;
  address: string;
}

/**
 * クーポン検索レスポンス
 */
export interface SearchCouponResponse {
  shopList: SearchShopListItem[];
}

/**
 * クーポン詳細 レスポンスモデル
 */
export interface CouponDetailResponse {
  couponInfo: couponInfo;
}

export interface couponInfo {
  couponName: string;
  imagePath?: string;
  endDate: string;
  conditions: string;
  usageLimitPerUser: number;
  description: string;
  shopId: string;
  shopName: string;
  shopIconPath?: string;
  userCount: number;
  remainingUsageCount?: number;
}

/**
 * 店舗詳細 レスポンスモデル
 */
export interface ShopDetailResponse {
  shopInfo: shopInfo;
}

export interface shopInfo {
  shopName: string;
  shopIconPath?: string;
  distanceFromHere: number;
  shopType: string;
  address: string;
  latitude: string;
  longitude: string;
  buildingName?: string;
  floor?: string;
  phoneNumber: string;
  relatedLink: string;
  couponList: SearchCouponListItem[];
}

export interface CouponListItem {
  couponId: string;
  couponName: string;
  imagePath?: string;
  endDate: string;
  shopName: string;
}

/**
 * クーポンおすすめ レスポンスモデル
 */
export interface CouponListResponse {
  couponList: CouponListItem[];
}

export interface Filters {
  keyword?: string;
  categories?: string[];
  distance?: number;
}

export interface SuggestedCouponResponse {
  suggestions: CouponSuggestion[];
}

export interface CouponSuggestion {
  id: string;
  text: string;
}
