import { post } from '../api/request';
// 本人複数同意・本人確認要求API
export interface HealthInfoRequest {
  inquiryInfomationType: string;
  infoInquiryCond: string;
  certifiedToken: string;
}

export interface HealthInfoResponse {
  location: string;
  responseType: string;
  clientId: string;
  redirectUrl: string;
  state: string;
  status: string;
  userType: number;
  infoInquiryCond: string;
  executionMode: number;
  loginType: number;
}

// mark: マイナポータル確認完了API
export interface SpecMedicalExamInfo {
  reportCode: string;
  effectiveTime: string;
  physicalCode: string;
  hospitalId: string;
  hospitalName: string;
  height: string;
  weight: string;
  bmi: string;
  vfa: string;
  waistActual: string;
  waistDeter: string;
  waistDeclar: string;
  past: string;
  pastDetail: string;
  symptoms: string;
  symptomsDetail: string;
  objective: string;
  objectiveDetail: string;
  sbp1: string;
  sbp2: string;
  sbpOther: string;
  dbp1: string;
  dbp2: string;
  dbpOther: string;
  bctAfterMeal: string;
  nfVis: string;
  nfUv: string;
  nfOther: string;
  uNfVis: string;
  uNfUv: string;
  uNfOther: string;
  hdlVis: string;
  hdlUv: string;
  hdlOther: string;
  ldlVis: string;
  ldlUv: string;
  ldlCal: string;
  ldlOther: string;
  nonHdl: string;
  gotVis: string;
  gotOther: string;
  gptVis: string;
  gptOther: string;
  ygtVis: string;
  ygtOther: string;
  crVis: string;
  crOther: string;
  crTarget: string;
  crReason: string;
  egfr: string;
  fbgPd: string;
  fbgVis: string;
  fbgUv: string;
  fbgOther: string;
  cbgPd: string;
  cbgVis: string;
  cbgUv: string;
  cbgOther: string;
  hba1cQhtc: string;
  hba1cHplc: string;
  hba1cEia: string;
  hba1cOther: string;
  uaMr: string;
  uaVom: string;
  upMr: string;
  upVom: string;
  hct: string;
  mch: string;
  rbc: string;
  mchReason: string;
  ecg: string;
  ecgDetail: string;
  ecgTarget: string;
  ecgReason: string;
  efeKwn: string;
  efeH: string;
  efeS: string;
  efeScott: string;
  efeWong: string;
  efeDavis: string;
  efeOther: string;
  efeTarget: string;
  efeReason: string;
  msj: string;
  shglLevel: string;
  doctorComment: string;
  doctorName: string;
}

export interface SpecMedicalExamQuestionnaire {
  medicationBp: string;
  medicationBpName: string;
  medicationBpReason: string;
  medicationBpConfirmer: string;
  medicationBg: string;
  medicationBgName: string;
  medicationBgReason: string;
  medicationBgConfirmer: string;
  medicationHdl: string;
  medicationHdlName: string;
  medicationHdlReason: string;
  medicationHdlConfirmer: string;
  pastCvd: string;
  pastMco: string;
  pastPhd: string;
  anemia: string;
  smoking: string;
  weightChange: string;
  exerciseHabits: string;
  activity: string;
  walkingSpeed: string;
  chew: string;
  dietVdc: string;
  dietVds: string;
  dietZde: string;
  dietHabit: string;
  drinking: string;
  drinkingAmount: string;
  sleep: string;
  lifestyleImprovement: string;
  healthGuidance: string;
  informationProvision: string;
  firstInterview: string;
}

export interface MedicalExamInformations {
  specMedicalExamInfo: SpecMedicalExamInfo;
  specMedicalExamQuestionnaire: SpecMedicalExamQuestionnaire;
}

export interface MycardResponseSendRequest {
  sex: number;
  medicalExamInformations: MedicalExamInformations;
}
// 接続サービス設定取得(連携)
export interface MedicalService {
  fcnId: number;
  useSts: number;
}

export interface ConnectionServiceResponse {
  serviceList: MedicalService[];
}

// 健康診断情報取得API
export interface GetMedicalExamsResponse {
  medicalExams: MedicalExams[];
}
export interface MedicalExams {
  examDay: string;
  examData: ExamData;
  questionData: Questionnaire;
  inquiryData: InquiryData;
}

// JSONデータ（検査データ）
export interface ExamData {
  bodyMeasHeight: number; // 身長
  bodyMeasWeight: number; // 体重
  bodyMeasWaistCircumference: number; // 腹囲
  bodyMeasBmi: number; // ＢＭＩ
  bodyMeasWCJudge: string; // 腹囲判定
  bodyMeasBmiJudge: string; // ＢＭＩ判定
  bodyMeasJudge: string; // 身体測定判定

  bpSystolic: number; // 収縮期血圧
  bpDiastolic: number; // 拡張期血圧
  bpSystolicJudge: string; // 収縮期血圧判定
  bpDiastolicJudge: string; // 拡張期血圧判定
  bpJudge: string; // 血圧判定

  blTriglyceride: number; // 空腹時中性脂肪
  blNormalTriglyceride: number; // 随時中性脂肪
  blHdl: number; // ＨＤＬ－コレステロール
  blLdl: number; // ＬＤＬ－コレステロール
  blNonHdl: number; // non_hdl
  blTriglycerideJudge: string; // 空腹時中性脂肪判定
  blHdlJudge: string; // ＨＤＬ－コレステロール判定
  blLdlJudge: string; // ＬＤＬ－コレステロール判定
  blNonHdlJudge: string; // non_hdl判定
  blJudge: string; // 血中脂質判定

  lfGot: number; // ＧＯＴ
  lfGtp: number; // ＧＰＴ
  lfyGtp: number; // γ－ＧＴＰ
  lfGotJudge: string; // ＧＯＴ判定
  lfGptJudge: string; // ＧＰＴ判定
  lfyGtpJudge: string; // γ－ＧＴＰ判定
  lfJudge: string; // 肝機能判定

  bgFbg: number; // 空腹時血糖
  bgHba1c: number; // ヘモグロビンA1C
  bgCbg: number; // 随時血糖
  bgFbgJudge: string; // 空腹時血糖判定
  bgHba1cJudge: string; // ヘモグロビンA1C判定
  bgJudge: string; // 血糖判定

  uaSugar: number; // 尿糖
  uaProtein: number; // 尿蛋白
  uaSugarJudge: string; // 尿糖判定
  uaProteinJudge: string; // 尿蛋白判定
  urinalysisJudge: string; // 尿検査判定

  anRbc: number; // 赤血球数
  anHb: number; // 血色素量
  anHematocrit: number; // ヘマトクリット値
  anHbJudge: string; // 血色素量判定
  anemiaJudge: string; // 貧血判定

  kfSerumCreatinine: number; // 血清クレアチニン値
  kfeGFR: number; // eGFR
  kfsCrJudge: string; // 血清クレアチニン値判定
  kfeGFRJudge: string; // eGFR判定
  kfJudge: string; // 腎機能判定

  othersECGComment: string; // 心電図検査
  othersEyegroundComment: string; // 眼底検査
  othersMetabolicRank: string; // メタボリックシンドローム判定
  othersDoctorComment: string; // 医師の判断
  othersUrineUncheckReason: number; // 検査未実施の理由（1 ：生理中　2：腎患等の基礎患があるため排尿障害が有する 3：その他）
  othersRemarks: string; // 備考
}

// JSONデータ（問診データ）
// m_pressure	//血圧を下げる薬（高血圧の薬）
// m_sugar	//インスリン注射又は血糖を下げる薬
// m_fat	//コレステロールや中性脂肪を下げる薬
// brain_disease	//医師から、脳卒中（脳出血、脳梗塞等）にかかっているといわれたり、治療を受けたことがありますか。
// heart_disease	//医師から、心臓病（狭心症、心筋梗塞、不整脈等）にかかっているといわれたり、治療を受けたことがありますか。
// kidney_disease	//医師から、慢性腎不全にかかっているといわれたり、治療（人工透析など）を受けていますか。
// anemia	//医師から、貧血と診断されたり治療したことがありますか。
// smoke_beforeFY2024	//現在、たばこを習慣的に吸っていますか。（beforFY2024）
// smoke_afterFY2024	//現在、たばこを習慣的に吸っていますか。（afterFY2024）
// ten_from20	//20歳の時の体重から10kg以上増加していますか。
// sweat_sport	//1回30分以上の軽く汗をかく運動を週2日以上、1年以上実施していますか。
// exercise_1hour	//日常生活において歩行又は同等の身体活動を1日1時間以上実施していますか。
// wsf: 	//ほぼ同じ年齢の同性と比較して歩く速度が速いですか。
// eat_everything	//食事をかんで食べる時の状態はどれにあてはまりますか。
// eat_speed	//人と比較して食べる速度は速いですか。
// eat_night3	//就寝前の2時間以内に夕食をとることが週に3回以上ありますか。
// eat_sugar	//朝昼夕の3食以外に間食や甘い飲み物を摂取していますか。
// no_breakfast3	//朝食を抜くことが週に3回以上ありますか。
// wine_fre_beforeFY2024	//お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度をお答えください。（beforFY2024）
// wine_fre_afterFY2024	"//お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度をお答えください。（afterFY2024）
// "
// wine_mount_beforeFY2024	"//飲酒日の1日当たりの飲酒量はどれくらいですか。（日本酒に換算してお答えください）
// 日本酒1合（180ml）の目安：ビール中瓶1本（500ml）、焼酎25度（110ml）、ウイスキーダブル1杯（60ml）、ワイン2杯（240ml）
// （beforFY2024）"
// wine_mount_afterFY2024	"//飲酒日の1日当たりの飲酒量はどれくらいですか。（日本酒に換算してお答えください）
// 日本酒1合（180ml）の目安：ビール中瓶1本（500ml）、焼酎25度（110ml）、ウイスキーダブル1杯（60ml）、ワイン2杯（240ml）
// （afterFY2024）"
// sleep_enough	//睡眠で休養が十分とれていますか。
// habit_improve	//運動や食生活等の生活習慣を改善してみようと思いますか。
// habit_lesson_beforeFY2024	"//生活習慣の改善について保健指導を受ける機会があれば、利用しますか。
// （beforFY2024）"
// habit_lesson_afterFY2024	"//生活習慣の改善について、これまでに特定保健指導を受けたことがありますか。
export interface Questionnaire {
  m_pressure: number; // 血圧を下げる薬（高血圧の薬）
  m_sugar: number; // インスリン注射又は血糖を下げる薬
  m_fat: number; // コレステロールや中性脂肪を下げる薬
  brain_disease: number; // 医師から、脳卒中（脳出血、脳梗塞等）にかかっているといわれたり、治療を受けたことがありますか。
  heart_disease: number; // 医師から、心臓病（狭心症、心筋梗塞、不整脈等）にかかっているといわれたり、治療を受けたことがありますか。
  kidney_disease: number; // 医師から、慢性腎不全にかかっているといわれたり、治療（人工透析など）を受けていますか。
  anemia: number; // 医師から、貧血と診断されたり治療したことがありますか。
  smoke_beforFY2024?: number; // 現在、たばこを習慣的に吸っていますか。（beforeFY2024）
  smoke_afterFY2024?: number; // 現在、たばこを習慣的に吸っていますか。（afterFY2024）
  ten_from20: number; // 20歳の時の体重から10kg以上増加していますか。
  sweat_sport: number; // 1回30分以上の軽く汗をかく運動を週2日以上、1年以上実施していますか。
  exercise_1hour: number; // 日常生活において歩行又は同等の身体活動を1日1時間以上実施していますか。
  wsf: number; // ほぼ同じ年齢の同性と比較して歩く速度が速いですか。
  eat_everything: number; // 食事をかんで食べる時の状態はどれにあてはまりますか。
  eat_speed: number; // 人と比較して食べる速度は速いですか。
  eat_night3: number; // 就寝前の2時間以内に夕食をとることが週に3回以上ありますか。
  eat_suger: number; // 朝昼夕の3食以外に間食や甘い飲み物を摂取していますか。
  no_breakfast3: number; // 朝食を抜くことが週に3回以上ありますか。
  wine_mount_beforFY2024?: number; // お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度をお答えください。（beforeFY2024）
  wine_fre_afterFY2024?: number; // お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度をお答えください。（afterFY2024）
  wine_mount_beforeFY2024?: number; // 飲酒日の1日当たりの飲酒量はどれくらいですか。（beforeFY2024）
  wine_mount_afterFY2024?: number; // 飲酒日の1日当たりの飲酒量はどれくらいですか。（afterFY2024）
  sleep_enough: number; // 睡眠で休養が十分とれていますか。
  habit_improve: number; // 運動や食生活等の生活習慣を改善してみようと思いますか。
  habit_lesson_beforFY2024?: number; // 生活習慣の改善について保健指導を受ける機会があれば、利用しますか。（beforeFY2024）
  habit_lesson_afterFY2024?: number; // 生活習慣の改善について、これまでに特定保健指導を受けたことがありますか。（afterFY2024）
}

// JSONデータ（診察データ）
export interface InquiryData {
  past: number; //既往歴（1：特記事項あり、2：特記事項なし）
  pastDetail: string; //具体的な既往歴
  symptoms: number; //自覚症状（1：特記事項あり、2：特記事項なし）
  symptomsDetail: string; //自覚症状所見
  objective: number; //他覚症状（1：特記事項あり、2：特記事項なし）
  objectiveDetail: string; //他覚所見
}

// 生活習慣病チェック推定(連携)
export interface WellcleUploadRequest {
  phb: WellcleUploadphd;
  providerUserId: string;
  vfat?: string; //内臓脂肪面積
}
export interface WellcleUploadphd {
  age?: string; //年齢
  sex?: string; //男女区分
  dateExam?: string; //実施年月日
  height?: string; //身長
  weight?: string; //体重
  bmi?: string; //BMI
  waist?: string; //腹囲
  waistActual?: string; //腹囲（実測）
  waistSelfassess?: string; //腹囲（自己判定）
  waistSelfreport?: string; //腹囲（自己申告）
  past?: string; //既往歴
  pastDetail?: string; //具体的な既往歴
  subjectiveSymptoms?: string; //自覚症状
  subjectiveSymptomsFindings?: string; //自覚症状所見
  objectiveSymptoms?: string; //他覚症状
  objectiveSymptomsFindings?: string; //他覚症状所見
  sbp?: string; //収縮期血圧
  sbpOther?: string; //収縮期血圧（その他）
  sbpSecond?: string; //収縮期血圧（2回目）
  sbpFirst?: string; //収縮期血圧（1回目）
  dbp?: string; //拡張期血圧
  dbpFirst?: string; //拡張期血圧（1回目）
  bctaftermeal?: string; //採血時間（食後）
  fnfat?: string; //空腹時中性脂肪
  fnfatVis?: string; //空腹時中中性脂肪（可視吸光光度法）
  fnfatUv?: string; //空腹時中中性脂肪（紫外線吸光光度法）
  fnfatOther?: string; //空腹時中中性脂肪（その他）
  cnfat?: string; //随時中性脂肪
  cnfatVis?: string; //随時中性脂肪（可視吸光光度法）
  cnfatUv?: string; //随時中性脂肪（紫外線吸光光度法）
  cnfatOther?: string; //随時中性脂肪（その他）
  hdl?: string; //HDLコレステロール
  hdlVis?: string; //HDLコレステロール（可視吸光光度法）
  hdlUv?: string; //HDL
  ldlUv?: string; //LDLコレステロール（紫外線吸光光度法）
  ldlCalc?: string; //LDLコレステロール（計算法）
  ldlOther?: string; //LDLコレステロール（その他）
  nhdl?: string; //non - HDLコレステロール
  ast?: string; //GOT（AST）
  astUv?: string; //GOT（AST）紫外線吸光光度法
  astOther?: string; //GOT（AST）その他
  alt?: string; //GPT（ALT）
  altUv?: string; //GPT（ALT）紫外線吸光光度法
  altOther?: string; //GPT（ALT）その他
  ygt?: string; //γ - GT（γ - GTP）
  ygtVis?: string; //γ - GT（γ - GTP）可視吸光光度法
  ygtOther?: string; //γ - GT（γ - GTP）その他
  scre?: string; //血清クレアチニン
  screVis?: string; //血清クレアチニン（可視吸光光度法）
  screOther?: string; //血清クレアチニン（その他）
  screTarget?: string; //血清クレアチニン（対象者）
  screReason?: string; //血清クレアチニン（実施理由）
  egfr?: string; //eGFR
  fbg?: string; //空腹時血糖
  fbgDf?: string; //空腹時血糖（電位差法）
  fbgVis?: string; //空腹時血糖（可視吸光光度法）
  fbgUv?: string; //空腹時血糖（紫外線吸光光度法）
  fbgOther?: string; //空腹時血糖（その他）
  cbg?: string; //随時血糖
  cbgDf?: string; //随時血糖（電位差法）
  cbgVis?: string; //随時血糖（可視吸光光度法）
  cbgUv?: string; //随時血糖（紫外線吸光光度法）
  cbgOther?: string; //随時血糖（その他）
  hba1c?: string; //HbA1c
  hba1cImmune?: string; //HbA1c（免疫学的方法）
  hba1cHplc?: string; //HbA1c（HPLC）
  hba1cEnzyme?: string; //HbA1c（酵素法）
  hba1cOther?: string; //HbA1c（その他）
  us?: string; //尿糖
  usMr?: string; //尿糖（機械読み取り）
  usVisr?: string; //尿糖（目視法）
  up?: string; //尿蛋白
  upMr?: string; //尿蛋白（機械読み取り）
  upVisr?: string; //尿蛋白（目視法）
  hct?: string; //ヘマトクリット値
  mch?: string; //血色素量（ヘモグロビ
  rbc?: string; //赤血球数
  anemiaTestreason?: string; //貧血検査実施理由
  ecg?: string; //心電図（所見の有無）
  ecgFindings?: string; //心電図所見
  ecgTarget?: string; //心電図（対象者）
  ecgReason?: string; //心電図実施理由
  funduscopyKw?: string; //眼底検査（キースワーガナー分類）
  funduscopySh?: string; //眼底検査（シェイエ分類：H）
  funduscopySs?: string; //眼底検査（シェイエ分類：S）
  funduscopyScott?: string; //眼底検査（SCOTT 分類）
  funduscopyWm?: string; //眼底検査（Wong - Mitchell 分類）
  funduscopyMd?: string; //眼底検査（改変 Davis 分類）
  funduscopyFindings?: string; //眼底検査（その他の所見）
  funduscopyTarget?: string; //眼底検査（対象者）
  funduscopyreason?: string; //眼底検査実施理由
  shgMetaboLevel?: string; //メタボリックシンドローム判定
  shgLevel?: string; //保健指導レベル
  doctorcomment?: string; //医師の診断（判定）
  qsnMedicineBp?: string; //服薬 1（血圧）
  qsnMedicineBpName?: string; //服薬 1（血圧）（薬剤名）
  qsnMedicineBpReason?: string; //服薬 1（血圧）（実施理由）
  qsnMedicineBpConfirmer?: string; //服薬確認者（血圧）
  qsnMedicineBg?: string; //服薬 2（血糖）
  qsnMedicineBgName?: string; //服薬 2（血糖）（薬剤名）
  qsnMedicineBgReason?: string; //服薬 2（血糖）（実施理由）
  qsnMedicineBgConfirmer?: string; //服薬確認者（血糖）
  qsnMedicineFat?: string; //服薬 3（脂質）
  qsnMedicineFatName?: string; //服薬 3（脂質）（薬剤名）
  qsnMedicineFatReason?: string; //服薬 3（脂質）（実施理由）
  qsnMedicineFatConfirmer?: string; //服薬確認者（脂質）
  qsnPastCvd?: string; //既往歴 1（脳血管）
  qsnPastMace?: string; //既往歴 2（心血管）
  qsnPastPrihd?: string; //既往歴 3（腎不全・人工透析）
  qsnAnemia?: string; //貧血
  qsnSmoking?: string; //喫煙
  qsnWeightchange?: string; //20 歳からの体重変化
  qsnExercisehabits?: string; //30 分以上の運動習慣
  qsnActivity?: string; //歩行又は身体活動
  qsnWalkingspeed?: string; //歩行速度
  qsnChew?: string; //咀嚼
  qsnDietVde?: string; //食べ方 1（早食い等）
  qsnDietVds?: string; //食べ方 2（就寢前）
  qsnDietZde?: string; //食べ方 3（間食）
  qsnDietHabit: string; //食習慣
  qsnDrinking?: string; //飲酒
  qsnDrinkingAmount?: string; //飲酒量
  qsnSleep?: string; //睡眠
  qsnLifestyle?: string; //生活習慣の改善
  qsnShg?: string; //保健指導の希望
  informationProvis?: string; //情報提供の方法
  shgFirst?: string; //初回面談実施
  qslHealthstatus?: string; //あなたの現在の健康状態はいかがですか（後期）
  qslSatisfied?: string; //毎日の生活に満足していますか（後期）
  qslMeals?: string; //1 日 3 食きちんと食べていますか（後期）
  qslEatharder?: string; //半年前に比べて固いもの (*) が食べにくくなりましたか（後期） * さきか、たくあんなど
  qslChocked?: string; //お茶や汁物等でむせる
  qslLoseweight?: string; //6 ヶ月間で 2～3kg 以上の体重減少がありましたか（後期）
  qslWalkingspeed?: string; //以前に比べて歩く速度が遅くなってきたと思いますか（後期）
  qslEverfallen?: string; //この 1 年間に転んだことがありますか（後期）
  qslExercise?: string; //ウォーキング等の運動を週に 1 回以上していますか（後期）
  qslForgetalways?: string; //周りの人から「いつも同じことを聞く」などの物忘れがあると言われていますか（後期）
  qslUnknow?: string; //今日が何月何日かわからない時がありますか（後期）
  qslSmoke?: string; //あなたはたばこを吸いますか（後期）
  qslOutside?: string; //週に 1 回以上は外出していますか（後期）
  qslAssociate?: string; //ふだんから家族や友人と付き合いがありますか（後期）
  qslTalking?: string; //体調が悪いときに、身近に相談できる人がいますか（後期）
}

export interface WellcleUploadResponse {
  url: string;
}

// マイナポータル確認完了(連携)
export interface MycardRequest {
  user_id: number;
  code: string;
  state: string;
  logged_in_type: string;
  error: string;
  error_description: string;
  connectionAt: string;
}
