export interface GetHomePointCardInfoResponse {
  pointCardList?: GetHomePointCardInfo[];
}

export interface GetHomePointCardInfo {
  organizerId?: number;
  availablePoint?: number;
  diffPoint?: number;
  pointAuthority?: number;
}

export class GetPointCardInfoResponse {
  organizerId?: number;
  availablePoints?: number;
  premiumAvailablePoints?: number;
  currentYearPlusPoints?: number;
  totalPlusPoints?: number;
}

export class GetResetInfoResponse {
  resetFlg?: number;
  resetDate?: string;
}

export class GetLotteryInfoResponse {
  lotteryShowFlg?: number;
  lotteryId?: number;
  prizeApplyEndDate?: string;
}

export class GetPointHistoryListRequest {
  showMonth?: string;
  categoryId?: number;
  page?: number;
  limit?: number;
}

export class GetPointHistoryListResponse {
  pointHistoryList?: GetPointHistoryList[];
  pagination?: Pagination;
}

export class GetPointHistoryList {
  categoryName?: string;
  categoryId?: number;
  pointName?: string;
  historyDate?: string;
  changeKind?: number;
  point?: number;
}

export class Pagination {
  total?: number;
  page?: number;
  limit?: number;
  pages?: number;
}
