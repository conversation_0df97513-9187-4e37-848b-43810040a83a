/**
 * マイページ情報取得API Response
 */
export interface MyPageResponse {
  nickName: string;
  currentLevel: string;
  currentLevelIcon: string;
  iconName: string;
  appStartDate: string;
  currAchvStatus: string;
  missionChangeFlg?: string;
  newIconGetLvlCnts: string;
  levelUpHistoryList?: MissionHistoryList[]; //ミッション一覧情報リスト
}

/**
 * ミッション一覧情報リスト Model
 */
export interface MissionHistoryList {
  level: string;
  newIcon: string;
  newIconGettingFlg: string;
  missionId: string;
  missionContent: string;
  achievementCap: string;
  achievementStatus: string;
  achievementCounts: string;
}

/**
 * レベルアップミッション一覧取得API Response
 */
export interface MissionListResponse {
  levelBasicInfo?: {
    currentLevel?: string;
    currentLevelIcon?: string;
  };
  levelUpInfo?: LevelUpInfo;
  levelUpMissionCardInfo: MissionCardInfo;
}

/**
 * ミッション一覧情報リスト Model
 */
export interface LevelUpInfo {
  levelUpFlg?: string;
  iconName?: string;
  speed?: string;
  newIconGetFlg?: string;
  newIconGetLvlCnts?: string;
}

/**
 * レベルアップミッションのカード情報 Model
 */
export interface MissionCardInfo {
  missionId?: string;
  missionTitle?: string;
  missionAchievedCount?: string;
  missionAchievedLimit?: string;
  todayAchievedFlg?: string;
  missionChangeFlg?: string;
}

/**
 * レベルアップミッション更新API Request
 */
export interface UpdateMissionRequest {
  level?: number;
  preMissionId?: number;
}

/**
 * レベルアップミッション更新API Response
 */
export interface UpdateMissionResponse {
  missionId: string;
  missionTitle: string;
  missionAchievedCount: string;
  missionAchievedLimit: string;
}

/**
 * レベルアップミッション詳細取得API Response
 */
export interface MissionDetailResponse {
  missionContent?: string;
  missionDetail?: string;
}
