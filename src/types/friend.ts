export interface FriendRanking {
  friendUserId: number;
  rank: number;
  iconUrl: string;
  nickname: string;
  stepCount: number;
  lastUsedDate: string;
  hasSentYell: boolean;
  hasReceivedYell: boolean;
  level?: number;
}

export interface Friend {
  friendUserId: number;
  nickname: string;
  iconUrl: string;
  level?: number;
}

export interface StepRankingRequest {
  page: number;
  size: number;
}

export interface StepRankingResponse {
  friendInfo: {
    friendCount: number;
  };
  friendList: FriendRanking[];
}

export interface CancelFriendRequest {
  arrFriendUserId: number[];
}

export interface InviteLinkResponse {
  inviteInfo: {
    uuid: string;
  };
}

export enum ProcessType {
  SEND_YELL = 0,
  CANCEL_YELL = 1,
}

export interface SendCancelYellRequest {
  friendUserId: number;
  processType: ProcessType;
}

export interface FriendsResponse {
  friendList: Friend[];
}
