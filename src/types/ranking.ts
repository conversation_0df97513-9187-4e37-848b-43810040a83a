import type { MYSELF_FLG } from '@/app/(webview)/ranking/_const';

export interface UserRankingInfoListResponse {
  userRankingInfoList?: UserRankingInfoList[];
}
export interface UserRankingInfoList {
  organizerId?: number;
  organizerName?: string;
  categoryList?: CategoryList[];
}

export interface CategoryList {
  category?: number;
  categoryName?: string;
  iconUrl?: number;
  order?: number;
  headCnt?: number;
}

export class CommonRequest {
  organizerId?: number;
  statDateFrom?: string;
  statDateTo?: string;
  myselfFlg?: MYSELF_FLG;
  pagination?: Pagination;
  page?: number;
  limit?: number;
}

export interface Pagination {
  total?: number;
  page?: number;
  limit?: number;
  pages?: number;
}

export interface CommonResponse {
  categoryName?: string;
  upstatDatedAt?: string;
  rankingList?: RankingList[];
  pagination?: Pagination;
}

export interface RankingList {
  order?: number;
  iconUrl?: string;
  level?: string;
  nickName?: string;
  steps?: number;
  selfFlg?: number;
  missionCompCnt?: number;
  groupName?: string;
  avgSteps?: number;
}

export interface RankingExclUserResponse {
  rankExclFlg?: number;
}
