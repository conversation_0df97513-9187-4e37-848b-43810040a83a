import type { CodeOption } from './code-types';

/**
 * 開催頻度詳細の型定義
 */
export interface FrequencyPatternDetailsCount {
  timeUnit: number; // 時間単位 (1年, 1ヶ月, 1週間, 1日等)
  count: number; // 回数
}

export interface FrequencyPatternDetailsDate {
  totalTimes: number; // 総回数
  details: string[]; // 日付の配列 (YYYY-MM-DD形式)
}

export type FrequencyPatternDetails =
  | FrequencyPatternDetailsCount
  | FrequencyPatternDetailsDate
  | null;

/**
 * ポイント付与詳細の型定義
 */
interface PointPatternDetailsUniform {
  points: number; // 均一ポイント数
}

export interface PointPatternDetailsStep {
  totalTimes: number; // 総回数
  details: Array<{
    sequence: number; // 順序
    points: number; // ポイント数
  }>;
}

interface PointPatternDetailsBatch {
  totalTimes: number; // 総回数
  finalBonusPoints: number; // 最終ボーナスポイント
}

/**
 * ポイント付与詳細の型定義
 */
export type PointPatternDetails =
  | PointPatternDetailsUniform
  | PointPatternDetailsStep
  | PointPatternDetailsBatch
  | null;

/**
 * イベントList ComplexEventListItem
 */
export interface ComplexEventListItem {
  eventId: string; // イベントID (必須)
  distance: number; // 距離
  eventImageFilePath: string; // イベント画像ファイルパス (任意)
  eventName: string; // イベント名 (必須)
  eventStartDate: string; // 開催開始日 (必須)
  eventEndDate: string; // 開催終了日 (必須)
  venueName: string; // 会場名 (任意)
  hasFee: string; // 料金フラグ (必須)
  isReservationRequired: string; // 予約要否フラグ (任意)
  pointPattern: string; // ポイント付与パータン (任意) 1:均一, 2:段階的, 3:一括
  pointPatternDetails: PointPatternDetails; // ポイント付与詳細 (任意)
  organizerName?: string; // イベント主催者名 (任意)
  isFavorite: boolean; // お気に入りフラグ (任意)
  latitude: string; // 緯度
  longitude: string; // 経度
  address: string; // 住所
}

/**
 * イベントList SimpleEventListItem
 */
export interface SimpleEventListItem {
  eventId: string; // イベントID (必須)
  eventImageFilePath: string; // イベント画像ファイルパス (任意)
  eventName: string; // イベント名 (必須)
  eventStartDate: string; // 開催開始日 (必須)
  eventEndDate: string; // 開催終了日 (必須)
  venueName: string; // 会場名 (任意)
  isFavorite: boolean; // お気に入りフラグ (任意)
}

/**
 * recommended イベントList レスポンスモデル
 */
export interface RecommendedEventListResponse {
  eventList?: SimpleEventListItem[];
}

/**
 * popular イベントList レスポンスモデル
 */
export interface PopularEventListResponse {
  eventList?: SimpleEventListItem[];
}

/**
 * home イベントList レスポンスモデル
 */
export interface HomeEventListResponse {
  eventList?: SimpleEventListItem[];
}

/**
 * search イベントList リクエストモデル
 */
export interface SearchEventListRequest {
  sortType: string;
  latitude?: string;
  longitude?: string;
  filters?: {
    hasPoint?: string;
    keyword?: string;
    hasFee?: string;
    type?: string[];
    distance?: number;
  };
}

/**
 * search イベントList レスポンスモデル
 */
export interface SearchEventListResponse {
  eventList?: ComplexEventListItem[];
}

export interface KeywordItem {
  id: string;
  text: string;
}

/**
 * イベントキーワード候補検索レスポンス
 */
export interface EventKeywordSearchResponse {
  suggestions: KeywordItem[];
}

export interface Stamp {
  isParticipated: string;
  points: number;
  attendDate: string;
  isGoal: string;
}

// メインのEventインターフェース
interface Event {
  eventId: string;
  // 住所・位置情報
  distance: number; // 距離
  address: string; // 住所 (必須)
  buildingName?: string; // 建物名 (任意)
  venueName?: string; // 会場名 (任意)

  // 日時情報
  publicationDatetime: string; // 公開日時 (必須)
  eventStartDate: string; // 開催開始日 (必須)
  eventEndDate: string; // 開催終了日 (必須)
  eventDatetime: string; // 開催日時 (必須)

  // 開催頻度関連
  frequencyPattern: string; // 開催頻度パータン (必須) 0:指定なし, 1:回数, 2:日付から選択
  frequencyPatternDetails?: FrequencyPatternDetails; // 開催頻度詳細 (任意)

  // イベント分類
  type1: string; // 種類１ (必須)
  type2?: string; // 種類２ (任意)
  otherCategoryDefinitions?: string; // その他の種類の定義 (任意)

  // イベント基本情報
  eventName: string; // イベント名 (必須)
  eventDescription: string; // イベント内容 (必須)
  eventDetailsUrl?: string; // イベント情報詳細のURL (任意)

  // 連絡先情報
  publicPhoneNumber?: string; // 公開用電話番号 (任意)
  publicEmail?: string; // 公開用メールアドレス (任意)

  // 料金・定員情報
  hasFee: string; // 料金フラグ (必須)
  feeDescription?: string; // 料金 (任意)
  capacity?: number; // 定員 (任意)

  // ポイント関連
  pointPattern: string; // ポイント付与パータン (任意) 1:均一, 2:段階的, 3:一括
  pointPatternDetails?: PointPatternDetails; // ポイント付与詳細 (任意)

  // 予約・画像関連
  isReservationRequired?: string; // 予約要否フラグ (任意)
  eventImageFilePath?: string; // イベント画像ファイルパス (任意)

  organizerName?: string; // イベント主催者名 (任意)

  isFavorite: boolean; // お気に入りフラグ (任意)
}

/**
 * イベントDetail レスポンスモデル
 */
export interface EventDetail extends Event {
  participantsCount: number; // 参加者数
  latitude: string; // 緯度
  longitude: string; // 経度
}

/**
 * イベントDetail レスポンスモデル
 */
export interface EventDetailResponse {
  eventInfo: EventDetail;
  stampList?: Stamp[]; // API返回的stamp数据
}

/**
 * イベント参加リクエストモデル
 */
export interface EventAttendRequest {
  qrString: string;
}

/**
 * イベントOptions初期化
 */
export interface EventOptions {
  // ポイントパータンの選択肢
  eventPointPatternOptions?: CodeOption[];
  // 種類１と2の選択肢
  eventTypeOptions: CodeOption[];
  // 開催頻度パータンの選択肢
  eventFrequencyPatternOptions?: CodeOption[];
  // 回数_時間単位の選択肢
  eventFrequencyCountOptions?: CodeOption[];
}

export interface InitOptionsResponse {
  initInfo: EventOptions;
}
