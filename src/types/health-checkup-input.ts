/**
 * 健診データのカテゴリ
 */
export type ExamCategory =
  | 'bodyMeasure' // 身体測定
  | 'bloodPressure' // 血圧
  | 'bloodLipid' // 脂質
  | 'liverFunction' // 肝機能
  | 'bloodGlucose' // 血糖
  | 'urinalysis' // 尿
  | 'anemia' // 貧血
  | 'kidneyFunction' // 腎機能
  | 'others' // そのほか
  | 'question' // 質問票（合并后）
  | 'question1' // 質問票1
  | 'question2' // 質問票2
  | 'question3' // 質問票3
  | 'question4' // 質問票4
  | 'inquiry'; // 診察項目

interface GraphRange {
  from: string;
  to: string;
}

/**
 * 基準値（A、B、C、Dの判定基準）
 */
export interface GraphBase {
  A?: GraphRange[] | null;
  B?: GraphRange[] | null;
  C?: GraphRange[] | null;
  D?: GraphRange[] | null;
  graphMaxValue?: string;
  graphMinValue?: string;
  graphStepValue?: string;
}

/**
 * 検査結果の基本構造
 */
export interface TestResultItem {
  base: string | null;
  testResult: string | null;
  judge: string | null;
}

/**
 * 健診データの構造
 */
export interface ExamData {
  // 身体測定関連
  bodyMeasHeight?: TestResultItem;
  bodyMeasWeight?: TestResultItem;
  bodyMeasWaistCircumference?: TestResultItem;
  bodyMeasBmi?: TestResultItem;

  // 血圧関連
  bpSystolic?: TestResultItem;
  bpDiastolic?: TestResultItem;

  // 血中脂質関連
  // "blTriglyceride":"150",
  // "blNormalTriglyceride":"150",
  // "blHdl": "40",
  // "blLdl": "150",
  // "blNonHdl": "20"
  blTriglyceride?: TestResultItem;
  blNormalTriglyceride?: TestResultItem;
  blHdl?: TestResultItem;
  blLdl?: TestResultItem;
  blNonHdl?: TestResultItem;

  // 肝機能関連
  lfGot?: TestResultItem;
  lfGpt?: TestResultItem;
  lfyGtp?: TestResultItem;

  // 血糖関連
  bgFbg?: TestResultItem;
  bgHba1c?: TestResultItem;
  bgCbg?: TestResultItem;
  bgFbgJudge?: TestResultItem;
  bgHba1cJudge?: TestResultItem;
  bgJudge?: TestResultItem;

  // 尿検査関連
  uaSugar?: TestResultItem;
  uaProtein?: TestResultItem;

  // 貧血関連
  anRbc?: TestResultItem;
  anHb?: TestResultItem;
  anHematocrit?: TestResultItem;

  // 腎機能関連
  kfSerumCreatinine?: TestResultItem;
  kfeGFR?: TestResultItem;

  // その他の検査
  othersECGComment?: string;
  othersEyegroundComment?: string;
  othersMetabolicRank?: string;
  othersDoctorComment?: string;
  othersUrineUncheckReason?: string;
  othersRemarks?: string;
}

export interface GraphDataItem {
  date: string;
  itemValue: string;
}

export interface GraphData {
  base: GraphBase;
  itemExamData: GraphDataItem[];
}

/**
 * 健診グラフデータの構造
 */
export interface ExamGraphData {
  // 身体測定関連のグラフデータ
  bodyMeasHeight?: GraphData;
  bodyMeasWeight?: GraphData;
  bodyMeasWaistCircumference?: GraphData;
  bodyMeasBmi?: GraphData;

  // その他の測定値のグラフデータ
  bpSystolic?: GraphData;
  bpDiastolic?: GraphData;
  blTriglyceride?: GraphData;
  blNormalTriglyceride?: GraphData;
  blHdl?: GraphData;
  blLdl?: GraphData;
  blNonHdl?: GraphData;
  lfGot?: GraphData;
  lfGpt?: GraphData;
  lfyGtp?: GraphData;
  bgFbg?: GraphData;
  bgHba1c?: GraphData;
  bgCbg?: GraphData;
  uaSugar?: GraphData;
  uaProtein?: GraphData;
  anRbc?: GraphData;
  anHb?: GraphData;
  anHematocrit?: GraphData;
  kfSerumCreatinine?: GraphData;
  kfeGFR?: GraphData;
}

/**
 * 質問票1のデータ構造
 */
export interface Question1Data {
  mPressure?: string; // 血圧を下げる薬
  mSugar?: string; // 血糖を下げる薬
  mFat?: string; // 中性脂肪を下げる薬
  brainDisease?: string; // 脳卒中の診断や治療歴あるか
  heartDisease?: string; // 心臓病の診断や治療歴あるか
  kidneyDisease?: string; // 慢性腎不全の診断や治療歴あるか
  anemia?: string; // 貧血の診断や治療歴あるか
}

/**
 * 質問票2のデータ構造
 */
export interface Question2Data {
  smokeBeforFY2024?: string; // たばこを習慣的に吸っているか（２０２４年度前）
  smokeAfterFY2024?: string; // たばこを習慣的に吸っているか（２０２４年度以降）
  tenFrom20?: string; // 体重10kg以上増加しているか
  sweatSport?: string; // 週２回以上汗かく運動実施しているか
  exercise1hour?: string; // 身体活動を1日1時間以上実施しているか
  wsf?: string; // 歩く速度が速いか
}

/**
 * 質問票3のデータ構造
 */
export interface Question3Data {
  eatEverything?: string; // 食事をかんで食べる時の状態
  eatSpeed?: string; // 食べる速度
  eatNight3?: string; // 就寝前夕食週3回以上か
  eatSuger?: string; // 甘い飲み物を摂取しているか
  noBreakfast3?: string; // 朝食を抜くことが週3回以上か
}

/**
 * 質問票4のデータ構造
 */
export interface Question4Data {
  wineFreBeforFY2024?: string; // 飲酒頻度（２０２４年度前）
  wineFreAfterFY2024?: string; // 飲酒頻度（２０２４年度以降）
  wineMountBeforFY2024?: string; // 飲酒量（２０２４年度前）
  wineMountAfterFY2024?: string; // 飲酒量（２０２４年度以降）
  sleepEnough?: string; // 睡眠充足か
  habitImprove?: string; // 生活習慣を改善

  habitLessonBeforeFY2024?: string; // 保健指導を受けるか（２０２４年度前）
  habitLessonAfterFY2024?: string; // 保健指導を受けるか（２０２４年度以降）
}

/**
 * 診察データの構造
 */
export interface InquiryData {
  past?: string; // 既往歴
  pastDetail?: string; // 具体的な既往歴
  symptoms?: string; // 自覚症状
  symptomsDetail?: string; // 自覚症状所見
  objective?: string; // 他覚症状
  objectiveDetail?: string; // 他覚所見
}

/**
 * 健診結果データの全体構造
 */
export interface HealthExamResultData {
  examDay: string; // 受診日
  examData: ExamData; // 健診データ
  examGraphData: ExamGraphData; // 健診グラフデータ
  question1Data?: Question1Data; // 質問票1データ
  question2Data?: Question2Data; // 質問票2データ
  question3Data?: Question3Data; // 質問票3データ
  question4Data?: Question4Data; // 質問票4データ
  inquiryData?: InquiryData; // 診察データ
}

export interface ExamResultQuery {
  // カテゴリ
  categories: string;
  // 受診日
  examDay: string;
}

/**
 * ------------------------------------------------------------------
 */

/**
 * 身体測定データ
 */
export interface BodyMeasureData {
  bodyMeasHeight?: string; // 身長
  bodyMeasWeight?: string; // 体重
  bodyMeasWaistCircumference?: string; // 腹囲
  bodyMeasBmi?: string; // BMI
}

/**
 * 血圧データ
 */
export interface BloodPressureData {
  bpSystolic?: string; // 収縮期血圧
  bpDiastolic?: string; // 拡張期血圧
}

/**
 * 脂質データ
 */
export interface BloodLipidData {
  blTriglyceride?: string; // 空腹時中性脂肪
  blNormalTriglyceride?: string; // 随時中性脂肪
  blHdl?: string; // ＨＤＬ－コレステロール
  blLdl?: string; // ＬＤＬ－コレステロール
  blNonHdl?: string; // non_hdl
}

/**
 * 肝機能データ
 */
export interface LiverFunctionData {
  lfGot?: string; // ＧＯＴ
  lfGpt?: string; // ＧＰＴ
  lfyGtp?: string; // γ－ＧＴＰ
}

/**
 * 血糖データ
 */
export interface BloodGlucoseData {
  bgFbg?: string; // 空腹時血糖
  bgHba1c?: string; // ヘモグロビンA1C
  bgCbg?: string; // 随時血糖
}

/**
 * 尿データ
 */
export interface UrinalysisData {
  uaSugar?: string; // 尿糖
  uaProtein?: string; // 尿蛋白
}

/**
 * 貧血データ
 */
export interface AnemiaData {
  anRbc?: string; // 赤血球数
  anHb?: string; // 血色素量
  anHematocrit?: string; // ヘマトクリット値
}

/**
 * 腎機能データ
 */
export interface KidneyFunctionData {
  kfSerumCreatinine?: string; // 血清クレアチニン値
  kfeGFR?: string; // eGFR
}

/**
 * そのほかデータ
 */
export interface OthersData {
  othersECGComment?: string; // 心電図検査
  othersEyegroundComment?: string; // 眼底検査
  othersMetabolicRank?: string; // メタボリックシンドローム判定
  othersDoctorComment?: string; // 医師の判定
  othersUrineUncheckReason?: string; // 検査未実施の理由
  othersRemarks?: string; // 備考
}

/**
 * 質問票１データ
 */
export interface Question1Data {
  mPressure?: string; // 血圧を下げる薬
  mSugar?: string; // 血糖を下げる薬
  mFat?: string; // 中性脂肪を下げる薬
  brainDisease?: string; // 脳卒中の診断や治療歴あるか
  heartDisease?: string; // 心臓病の診断や治療歴あるか
  kidneyDisease?: string; // 慢性腎不全の診断や治療歴あるか
  anemia?: string; // 貧血の診断や治療歴あるか
}

/**
 * 質問票２データ
 */
export interface Question2Data {
  smokeBeforFY2024?: string; // たばこを習慣的に吸っているか（２０２４年度前）
  smokeAfterFY2024?: string; // たばこを習慣的に吸っているか（２０２４年度以降）
  tenFrom20?: string; // 体重10kg以上増加しているか
  sweatSport?: string; // 週２回以上汗かく運動実施しているか
  exercise1hour?: string; // 身体活動を1日1時間以上実施しているか
  wsf?: string; // 歩く速度が速いか
}

/**
 * 質問票３データ
 */
export interface Question3Data {
  eatEverything?: string; // 食事をかんで食べる時の状態
  eatSpeed?: string; // 食べる速度
  eatNight3?: string; // 就寝前夕食週3回以上か
  eatSugar?: string; // 甘い飲み物を摂取しているか
  noBreakfast3?: string; // 朝食を抜くことが週3回以上か
}

/**
 * 質問票４データ
 */
export interface Question4Data {
  wineFreBeforFY2024?: string; // 飲酒頻度（２０２４年度前）
  wineFreAfterFY2024?: string; // 飲酒頻度（２０２４年度以降）
  wineMountBeforFY2024?: string; // 飲酒量（２０２４年度前）
  wineMountAfterFY2024?: string; // 飲酒量（２０２４年度以降）
  sleepEnough?: string; // 睡眠充足か
  habitImprove?: string; // 生活習慣を改善
  habitLessonBeforeFY2024?: string; // 保健指導を受けるか（２０２４年度前）
  habitLessonAfterFY2024?: string; // 特定保健指導を受けたことあるか（２０２４年度以降）
}

/**
 * 診察データ
 */
export interface InquiryData {
  past?: string; // 既往歴の有無
  pastDetail?: string; // 具体的な既往歴
  symptoms?: string; // 自覚症状の有無
  symptomsDetail?: string; // 具体的な自覚症状
  objective?: string; // 他覚症状の有無
  objectiveDetail?: string; // 具体的な他覚症状
}

/**
 * 健診結果リクエストの構造
 */
export interface HealthExamResultRequest {
  userId?: number; // 会員番号
  sex: string; // 性別
  examDay: string; // 受診日
  category: ExamCategory[]; // カテゴリ
  autoFlag: number; // 自動入力フラグ
  maxPagesReached: number; // 到達最大ページ数
  bodyMeasureData?: BodyMeasureData; // 身体測定データ
  bloodPressureData?: BloodPressureData; // 血圧データ
  bloodLipidData?: BloodLipidData; // 脂質データ
  liverFunctionData?: LiverFunctionData; // 肝機能データ
  bloodGlucoseData?: BloodGlucoseData; // 血糖データ
  urinalysisData?: UrinalysisData; // 尿データ
  anemiaData?: AnemiaData; // 貧血データ
  kidneyFunctionData?: KidneyFunctionData; // 腎機能データ
  othersData?: OthersData; // そのほかデータ
  question1Data?: Question1Data; // 質問票１データ
  question2Data?: Question2Data; // 質問票２データ
  question3Data?: Question3Data; // 質問票３データ
  question4Data?: Question4Data; // 質問票４データ
  inquiryData?: InquiryData; // 診察データ
}

// ------------------------------------------------------------------

/**
 * 検査結果データの全体構造
 */
export interface ExamResultData {
  examDay: string; // 受診日
  autoFlag: number; // 自動入力フラグ
  bodyMeasureData?: BodyMeasureData; // 身体測定データ
  bloodPressureData?: BloodPressureData; // 血圧データ
  bloodLipidData?: BloodLipidData; // 脂質データ
  liverFunctionData?: LiverFunctionData; // 肝機能データ
  bloodGlucoseData?: BloodGlucoseData; // 血糖データ
  urinalysisData?: UrinalysisData; // 尿データ
  anemiaData?: AnemiaData; // 貧血データ
  kidneyFunctionData?: KidneyFunctionData; // 腎機能データ
  othersData?: OthersData; // そのほかデータ
}

// ------------------------------------------------------------------

// 受診日データの型定義
interface ExamDay {
  examDay: string; // 受診日 (YYYY-MM-DD形式)
  autoFlag: number; // 自動入力フラグ ("1" または "2")
}

// 受診日一覧のレスポンス型定義
export interface ExamDaysData {
  examDays: ExamDay[];
}

// ------------------------------------------------------------------
// 受診日変更リクエスト
export interface ExamDaysRequest {
  examDayFrom: string; // 受診日変更From Day
  examDayTo: string; // 受診日変更To Day
}

// ------------------------------------------------------------------

// 日次健診結果データ
export interface DailyExamData {
  examDay: string; // 受診日
  autoFlag: number; // 自動入力フラグ
  maxPagesReached: number; // 到達最大ページ数
  createdAt: string; // 登録日時
  examResultData: {
    // 健診判定結果データ
    bodyMeasWGJudge: string | null; // 腹囲判定
    bodyMeasBmiJudge: string | null; // ＢＭＩ判定
    bodyMeasJudge: string | null; // 身体測定判定
    bpSystolicJudge: string | null; // 収縮期血圧判定
    bpDiastolicJudge: string | null; // 拡張期血圧判定
    bpJudge: string | null; // 血圧判定
    blTriglycerideJudge: string | null; // 空腹時中性脂肪判定
    blHdlJudge: string | null; // ＨＤＬ－コレステロール判定
    blLdlJudge: string | null; // ＬＤＬ－コレステロール判定
    blNonHdlJudge?: string | null; // non_hdl判定（JSONにないがリストにある）
    blJudge: string | null; // 血中脂質判定
    lfGotJudge: string | null; // ＧＯＴ判定
    lfGtpJudge: string | null; // ＧＰＴ判定
    lfyGtpJudge: string | null; // γ－ＧＴＰ判定
    lfJudge: string | null; // 肝機能判定
    bgFbgJudge: string | null; // 空腹時血糖判定
    bgHba1cJudge: string | null; // ヘモグロビンA1C判定
    bgJudge: string | null; // 血糖判定
    uaSugarJudge: string | null; // 尿糖判定
    uaProteinJudge: string | null; // 尿蛋白判定
    urinalysisJudge: string | null; // 尿検査判定
    anHbJudge: string | null; // 血色素量判定
    anemiaJudge: string | null; // 貧血判定
    kfsCrJudge: string | null; // 血清クレアチニン値判定
    kfeGFRJudge: string | null; // eGFR判定
    kfJudge: string | null; // 腎機能判定
  };
}

// 質問票のオプション
export interface QuestionOption {
  value: number;
  label: string;
}
