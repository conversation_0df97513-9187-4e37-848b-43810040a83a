export enum AssetOpState {
  ON = '1',
  OFF = '0',
}

export class Asset {
  asset_type: string | undefined;
  op_state: string | undefined;
  op_permission: string | undefined;
}

export class SendAsset {
  asset_type: string | undefined;
  op_state: string | undefined;
  op_permission: string | undefined;
}

export class PutAsset {
  asset_type: string | undefined;
  op_state: string | undefined;
}

export class PageAsset {
  asset_type: string | undefined;
  op_state: string | undefined;
  op_permission: string | undefined;
  id: string | undefined;
  label: string | undefined;
  changeable: string | undefined;
}
