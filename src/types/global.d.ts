interface Window {
  webkit?: {
    messageHandlers?: {
      nativeHandler?: {
        postMessage: (message: any) => void;
      };
    };
  };
  AndroidInterface?: {
    postMessage: (message: any) => void;
  };
  receiveMessageFromNative: (jsonMessage: string) => void;
  receiveCallbackFromNative: (jsonMessage: string) => void;
  pushDataFunction?: (message: string) => void;
  addGroupFunction?: (message: string) => void;
}
