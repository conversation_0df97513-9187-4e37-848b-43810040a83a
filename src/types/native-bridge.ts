export type BridgeHandler = (message: NativeMessage) => void;

export type MessageData = Record<string, any>;

export interface NativeMessage {
  type: string;
  data?: MessageData;
  callback?: MessageHandler;
}

export type MessageHandler = (data?: MessageData) => void;

export interface GetDataParams {
  key: string;
  defaultValue?: any;
}

export interface NativeResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}
