export interface QuizAchieveUpdatelRequest {
  missionId?: number;
  quizId?: number;
  selectedOption?: string;
  missionOrganizerIdList?: number[];
}

export interface QuizAchieveUpdatelResponse {
  checkStatus: number;
  pointAuthorityFlg: boolean;
  pointTitle: number;
  pointDetailsShowFlg: boolean;
  iconFlg: number;
  getPointInfoList: PointQuizType[];
  explanation: string;
  correctAnswer: string;
  image: string;
  todayAchievedInfoList?: MissionOrganizerData[];
}

export interface QuizMissionDetailRequest {
  missionId: number;
  missionOrganizerIdList: number[];
}

export interface QuizMissionDetailResponse {
  category: number;
  missionId: number;
  missionTitle: number;
  missionType: number;
  pointInfoList: PointInfoQuizType[];
  quizId: number;
  quizType: string;
  question: string;
  optionList: OptionListType[];
  image: string;
}

export interface PointInfoQuizType {
  organizerId: number;
  organizerName: string;
  correctPoint?: number;
  incorrectPoint?: number;
}

export interface OptionListType {
  key: string;
  value: string;
}

export interface MissionSectionRequest {
  category: number;
}

export interface MissionSectionResponse {
  category: number;
  categoryMissionList: MissionCardType[];
}

export interface MissionListResponse {
  achieveFlg: boolean;
  achievedMissionExistFlg: boolean;
  detailList: MissionOrganizerData[];
  missionList: MissionInterface[];
}

export interface MissionDetailRequest {
  missionId: number;
  missionOrganizerIdList: number[];
  missionType: number;
  isRead?: boolean;
}

export interface MissionAchieveUpdateRequest {
  missionId?: number;
  missionOrganizerIdList: number[];
  missionType?: number;
  kind?: number;
  judgmentMissionFlg: number;
}

export interface MissionPopupDetailResponse {
  pointAuthorityFlg?: boolean;
  pointTitle?: number;
  pointDetailsShowFlg?: boolean;
  getPointInfoList?: PointInfoType[];
  todayAchievedInfoList?: MissionOrganizerData[];
  iconFlg?: number;
  organizerNameShowFlg?: boolean;
  stepMissionExistFlg?: boolean;
}

export interface MissionOrganizerData {
  organizerId: number;
  organizerName: string;
  totalRemaining: number; // 団体別残ミッション数
  totalLimit: number; // 団体別ミッション上限
  totalAchieved: number; // 団体達成ミッション数
  categoryList?: CategoryType[];
}

export interface CategoryType {
  category: number;
  categoryLimit: number;
  categoryRemaining: number;
}

export interface MissionInterface {
  category: number;
  isRead?: boolean;
  pointLimitFlg?: string;
  categoryMissionList?: MissionCardType[];
}
export interface MissionType {
  category: number;
  missionId: number;
  missionTitle: string;
  missionType: number;
  iconType: number;
  targetDays: number;
  achievedDays: number;
  todayAchievedFlg: boolean;
  organizerList?: OrganizerType[];
}
export interface MissionCardType {
  category: number;
  missionId: number;
  missionTitle: string;
  missionType: number;
  iconType: number;
  targetDays: number;
  achievedDays: number;
  todayAchievedFlg: boolean;
  isRead?: boolean;
  organizerList?: OrganizerType[];
  pointLimitFlg?: string;
}
export interface MissionListType {
  origin: string;
  missionId: number;
  missionTitle: string;
  missionType: number;
  iconType: number;
  targetDays: number;
  achievedDays: number;
  todayAchievedFlg: boolean;
  isRead?: boolean;
  organizerList?: OrganizerType[];
  pointLimitFlg?: string;
}
export interface OrganizerType {
  organizerId: number;
  organizerName: string;
  pointGotFlg?: boolean;
}
export interface ContentInterface {
  category: number;
  missionId: number;
  missionTitle?: string;
  missionPointInfoList?: MissionPointInfoType[];
  missionContent?: string | null;
  missionUrl?: string;
  missionDetailUrl?: string;
  contentType: number;
  achievedFlg: boolean;
  judgmentMissionFlg: number;
  missionType: number;
}
export interface MissionPointInfoType {
  organizerId: number;
  organizerName: string;
  dailyPoint: number;
  achievePoint: number;
  pointGotFlg?: boolean;
}

// 改善ミッションフラグ
export interface ButtonTextType {
  textStart: string;
  textFinish: string;
}

export interface PointInfoType {
  organizerId: number;
  organizerName: string;
  getDailyPoint: number;
  getAchievePoint: number;
}

export interface PointQuizType {
  organizerId: number;
  organizerName: string;
  getPoint: number;
}

export interface missionReadDetailRequest {
  missionId: number;
}

export interface QuizTypeItem {
  quizType: string | undefined;
  text: string;
}

export interface ExtendedQuizUpdateResponse extends QuizAchieveUpdatelResponse {
  quizType: string | undefined;
}

declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

export interface YouTubePlayerProps {
  videoId: string;
  width?: number;
  height?: number;
  autoplay?: boolean;
  controls?: boolean;
  onReady?: (player: any) => void;
  onStateChange?: (state: number) => void;
  onPlaybackRateChange?: (rate: number) => void;
}

export type PlayerEvent = {
  target: any;
  data: number;
};
