export interface LevelInfoResponse {
  userId: number;
  nickName: string;
  level: number;
  iconUrl: string;
  animalName: string;
}

export interface PushSendFlagRequest {
  pushSendFlg?: string;
}
export interface PushFlagData {
  pushSendFlg?: number;
}

export interface Term {
  termName: string;
  termUrl: string;
  termType: string;
}

export interface TermsResponse {
  termList: Term[];
}

export interface MenuItem {
  label: string;
  href: string;
  onClick: (item: MenuItem) => void;
  subLabel?: React.ReactNode;
  imgIcon: string;
  type: string;
}
