// 通用过滤参数
export interface FilterParams {
  [key: string]: string | number | boolean;
}

// 通用分页参数
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// 通用排序参数
export interface SortParams {
  sortBy?: string;
  order?: 'asc' | 'desc';
}

// 通用查询参数
export interface QueryParams {
  filters?: FilterParams;
  sort?: SortParams;
  pagination?: PaginationParams;
}

// ID类型
export type ID = string | number;

// 分页接口
export interface Pagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// 过滤条件接口
export interface Filters {
  [key: string]: string | number | boolean;
}

// 排序接口
export interface Sort {
  field: string;
  order: 'asc' | 'desc';
}
