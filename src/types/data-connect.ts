export class DataConnectSettingResponse {
  deviceConInfo: DeviceConInfo | undefined;
  vitalDataInfo: VitalDataInfo | undefined;
  vitaDataInfo: VitalDataInfo | undefined;
  weightOptionInfo: OptionInfo[] | undefined;
  sleepOptionInfo: OptionInfo[] | undefined;
  bpOptionInfo: OptionInfo[] | undefined;
}

export class DeviceConInfo {
  healthConConFlg: number | undefined;
  fitbitConFlg: number | undefined;
  omronUsingFlg: number | undefined;
  omronConFlg: number | undefined;
}

export class VitalDataInfo {
  weightFrom: number | undefined;
  sleepFrom: number | undefined;
  bpFrom: number | undefined;
}

export class OptionInfo {
  deviceType: number | undefined;
  iconUrl: number | undefined;
  title: string | undefined;
  value: string | undefined;
  tip: string | undefined;
}

export class VitalHistoryData {
  fromUpdFlg: number | undefined;
}

export class VitalFromResponse {
  weightFrom: number | undefined;
  sleepFrom: number | undefined;
  bloodPressureFrom: number | undefined;
  queryWeightStartDate: string | undefined;
  querySleepStartDate: string | undefined;
  queryBPStartDate: string | undefined;
}

export class FitBitRedirectResponse {
  scope: number[] | undefined;
  accountId: string | undefined;
  loginedUserId: string | undefined;
}

export class FitbitTokenResponse {
  accountId: string | undefined;
  accessToken!: string;
  refreshToken: string | undefined;
}
export class OmronTokenResponse {
  accountId: string | undefined;
  accessToken: string | undefined;
  refreshToken: string | undefined;
}
export class FitbitWeightEntry {
  bmi!: number;
  weight!: number; // kg
  date!: string;
  time!: string;
}

export class FitbitWeightResponse {
  weight: [FitbitWeightEntry] | undefined;
}

export class FitbitSleepLog {
  dateOfSleep: string | undefined;
  duration!: number;
  efficiency: number | undefined;
  levels: FitbitLevels | undefined;
  startTime: string | undefined;
  endTime: string | undefined;
}

export class FitbitSleepStage {
  stage: string | undefined; // "deep", "light", "rem", "wake"
  minutes: number | undefined;
  startTime: string | undefined;
}

export class FitbitLevels {
  summary: [FitbitSleepStage] | undefined;
  data: [FitbitSleepInterval] | undefined;
}

export class FitbitSleepInterval {
  dateTime: string | undefined;
  level: string | undefined;
  seconds: number | undefined;
}

export class FitbitActivityData {
  summary: FitbitActivitySummary | undefined;
}

export class FitbitActivitySummary {
  steps: number | undefined;
  distance: number | undefined; //km
  caloriesOut: number | undefined;
  activeMinutes: number | undefined;
}
export class VitalTypeResponse {
  vitalTypeList: number[] = []; // 改为
}
