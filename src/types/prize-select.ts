export class PrizeListResponse {
  lotteryId: number | undefined;
  lotPartFlg: number | undefined;
  lotteryMode: number | undefined;
  sourceFlag?: string | undefined;
  prizeList: PrizeListInfo[] | undefined;
  selectedPrizes?: Record<number, number>;
}

export class RecordData {
  isModify: boolean | undefined;
  lotteryId: number | undefined;
  lotteryMode: number | undefined;
  sourceFlag?: string | undefined;
  prizeList: PrizeListInfo[] | undefined;
  selectedPrizes?: Record<number, number>;
}

export class PrizeDetailResponse {
  prizeDetail: PrizeDetailInfo | undefined;
}

export class PrizeCompletionParam {
  announcementDate: string | undefined;
  selectedPoints: number | undefined;
  remainingPoints: number | undefined;
  prizeList: selectedPrizesInfo[] | undefined;
  recordData: RecordData | undefined;
}

export class PrizeListInfo {
  prizeId: number | undefined;
  prizeType: number | undefined;
  prizeNm: string | undefined;
  prizeNum: number | undefined;
  prizeImageFile: string | undefined;
  prizeDonation: number | undefined;
  needPoint: number | undefined;
  quantity: number | undefined;
}

export class PrizeDetailInfo {
  prizeId: number | undefined;
  prizeType: number | undefined;
  prizeNm: string | undefined;
  prizeDet: string | undefined;
  prizeNum: number | undefined;
  prizeImageFile: string | undefined;
  prizeDonation: number | undefined;
  needPoint: number | undefined;
  prizeScheduledDate: string | undefined;
  pickUpDeadline: string | undefined;
}

export class selectedPrizesInfo {
  prizeId: number | undefined;
  prizeNm: string | undefined;
  needPoint: number | undefined;
  prizeAppNum: number | undefined;
  quantity: number | undefined;
  prizeDonation: number | undefined;
}

export class UserLotteryChoiceResponse {
  userLotteryChoiceList: UserLotteryChoiceInfo[] | undefined;
  cntPrize: number | undefined;
  sumPrizeAppNum: number | undefined;
  sumChosenPrizePoint: number | undefined;
}

export class UserLotteryChoiceInfo {
  prizeId: number | undefined;
  prizeNm: string | undefined;
  prizeAppNum: number | undefined;
  prizeDonation: string | undefined;
  needPoint: number | undefined;
  chosenPrizePoint: number | undefined;
}

export class LotteryChoiceParamInfo {
  prizeId: number | undefined;
  prizeAppNum: number | undefined;
  prizeDonation: number | string | undefined;
}

export class LotteryChoiceParam {
  LotteryId: number | undefined;
  PrizeData: LotteryChoiceParamInfo[] | undefined;
}

export class AppLotteryResponse {
  EntryInfo: EntryInfo | undefined;
  pushSendFlg: number | undefined;
  entryDetail: EntryDetail | undefined;
}

export class EntryInfo {
  lotteryId: number | undefined;
  lotteryOn: string | undefined;
  lotteryMode: number | undefined;
}

export class EntryDetail {
  point: number | undefined;
  choicedPoint: number | undefined;
  remainPoint: number | undefined;
  choiceList: LotteryChoiceParamInfo[] | undefined;
}
