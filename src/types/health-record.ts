export enum HealthRecordSource {
  MANUAL = '0', // 手入力
  HEALTH_APP_IPHONE = '1', // ヘルスケア　iPhone
  HEALTH_APP_APPLE_WATCH = '2', // ヘルスケア　Apple Watch
  SMARTPHONE = '3', // スマートフォン
  HEALTH_CONNECT_SMARTPHONE = '4', // ヘルスコネクト　スマートフォン（Google Fit）
  HEALTH_CONNECT_SMARTWATCH = '5',
  FITBIT = '6',
}

export interface VitalFrom {
  bodyFrom: HealthRecordSource;
  sleepFrom: HealthRecordSource;
  bloodPressureFrom: HealthRecordSource;
  bloodGlucoseFrom: HealthRecordSource;
}

export interface VitalDataRequest {
  measureDate: string;
  weightData?: {
    weight?: string;
    fatPercentage?: string;
    bodyFrom: number;
  };
  sleepData?: {
    sleepAt?: string;
    wakeAt?: string;
    sleepFrom: number;
  };
  bloodPressureData?: {
    bpAmMeasureAt?: string;
    highBpAm?: number;
    lowBpAm?: number;
    bpPmMeasureAt?: string;
    highBpPm?: number;
    lowBpPm?: number;
    bloodPressureFrom: number;
  };
  bloodGlucoseData?: (SingleMeasureData | MultiMeasureData) & {
    bloodGlucoseFrom: number;
  };
}
export interface VitalDataResponse {
  popupInfo?: {
    isPopupShow?: boolean;
    pointInfo?: {
      totalPoint?: string;
      details?: Details[];
    };
  };
}

export interface Details {
  organizerId?: string;
  organizerName?: string;
  organizerTotalPoint?: string;
  pointDetail?: {
    weight?: string;
    bloodPressure?: string;
    sleepTime?: string;
    bloodGlucose?: string;
    fat?: string;
  };
}

type BgMeasurementFrequency = 1 | 2; // 1: 1日1回 2: 1日複数回

interface MultiMeasureData {
  bgMeasurementFrequency: 2;
  beforeBfBg?: number;
  afterBfBg?: number;
  beforeLnBg?: number;
  afterLnBg?: number;
  beforeDnBg?: number;
  afterDnBg?: number;
  beforeSlBg?: number;
}

interface SingleMeasureData {
  bgMeasurementFrequency: 1;
  onceDailyBg?: number;
}

export interface DailyVitalDataRequest {
  measureDate: string;
}

export interface DailyVitalDataResponse {
  measureDate: string;
  vitalData: HealthRecord;
}

export interface HealthRecord {
  date?: string;
  step?: number;
  distance?: number;
  exerciseTime?: number;
  energy?: number;
  weight?: string;
  bmi?: string;
  fatPercentage?: string;
  sleepAtDate?: string;
  sleepAtTime?: string;
  wakeAtDate?: string;
  wakeAtTime?: string;
  sleepTime?: number;
  bpAmMeasureAt?: string;
  highBpAm?: number;
  lowBpAm?: number;
  bpPmMeasureAt?: string;
  highBpPm?: number;
  lowBpPm?: number;
  bgMeasurementFrequency?: BgMeasurementFrequency;
  onceDailyBg?: number;
  beforeBfBg?: number;
  afterBfBg?: number;
  beforeLnBg?: number;
  afterLnBg?: number;
  beforeDnBg?: number;
  afterDnBg?: number;
  beforeSlBg?: number;
  bodyFrom: HealthRecordSource;
  sleepFrom: HealthRecordSource;
  bloodPressureFrom: HealthRecordSource;
  bloodGlucoseFrom: HealthRecordSource;
}

export interface ThumbnailDataRequest {
  measureDate: string;
}

export interface ThumbnailData {
  stepThumnData?: StepThumbnailData[];
  weightThumnData?: WeightThumbnailData[];
  sleepTimeThumnData?: SleepTimeThumbnailData[];
  bloodPressureThumnData?: BloodPressureThumbnailData[];
  bloodGlucoseThumnData?: BloodGlucoseThumbnailData[];
}

export interface ThumbnailDataResponse extends ThumbnailData {}

export interface StepThumbnailData {
  measureDate: string;
  step: number;
}

export interface WeightThumbnailData {
  measureDate: string;
  weight: number;
}

export interface SleepTimeThumbnailData {
  measureDate: string;
  sleepTime: number;
}

export interface BloodPressureThumbnailData {
  measureDate: string;
  highBpAm: number;
  lowBpAm: number;
  highBpPm: number;
  lowBpPm: number;
}

export type BloodGlucoseThumbnailData =
  | SingleBloodGlucoseThumbnailData
  | MultiBloodGlucoseThumbnailData;

export interface SingleBloodGlucoseThumbnailData {
  measureDate: string;
  bgMeasurementFrequency: 1;
  onceDailyBg: number;
}

export interface MultiBloodGlucoseThumbnailData {
  measureDate: string;
  bgMeasurementFrequency: 2;
  beforeBfBg: number;
  afterBfBg: number;
  beforeLnBg: number;
  afterLnBg: number;
  beforeDnBg: number;
  afterDnBg: number;
  beforeSlBg: number;
}

export type BarChartData = {
  date: string;
  value: number | undefined;
};

export type RangeBarChartData = {
  date: string;
  low: number;
  high: number;
};
