/**
 * 用户角色枚举
 */
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  GUEST = 'GUEST',
}

/**
 * 请求状态枚举
 */
export enum RequestStatus {
  IDLE = 'IDLE',
  LOADING = 'LOADING',
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
}

/**
 * 排序方向枚举
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * 通用状态枚举
 */
export enum CommonStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DELETED = 'DELETED',
}

/**
 * 主题模式枚举
 */
export enum ThemeMode {
  LIGHT = 'LIGHT',
  DARK = 'DARK',
  SYSTEM = 'SYSTEM',
}

/**
 * TimeRangeType
 */
export enum TimeRangeType {
  WEEK = '1',
  MONTH = '2',
  YEAR = '3',
}

//自動入力フラグ
export enum AutoFlag {
  AUTO = '1', // マイナポータル連携
  MANUAL = '2', // 手入力
}

// 健診結果評価
export enum ExamAssessment {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}
