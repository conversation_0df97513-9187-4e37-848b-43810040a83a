// types/api.ts
export interface ApiResponse<T = unknown> {
  code: number;
  data: T;
  message: string;
}

export interface ApiErrorResponse {
  code: number;
  message: string;
  data?: unknown;
}

export class ApiError extends Error {
  code: number;
  data?: unknown;

  constructor(response: ApiErrorResponse) {
    super(response.message);
    this.name = 'ApiError';
    this.code = response.code;
    this.data = response.data;
  }
}
