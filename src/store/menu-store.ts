import { homePageAPI } from '@/api/modules/home-page';
import type { MenuButton } from '@/types/home-data';
import storage from '@/utils/obfuscate-storage';
// src/stores/menu-store.ts
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const menuButtonItem1: MenuButton[] = [
  { id: 32, label: 'グラフ', url: '/graph', iconKey: 'graph' },
  { id: 22, label: '健康記録', url: '/health-record', iconKey: 'score' },
  { id: 42, label: 'ミッション', url: '/mission', iconKey: 'mission' },
  { id: 31, label: 'ランキング', url: '/ranking', iconKey: 'ranking' },
  { id: 41, label: 'ウォーキングコース', url: '/walking-course/search', iconKey: 'walking' },
  { id: 44, label: 'イベント', url: '/event', iconKey: 'event' },
  { id: 46, label: 'クーポン', url: '/coupon', iconKey: 'coupon' },
  { id: 13, label: 'プロフィール変更', url: '', iconKey: 'userSetting' },
];

const menuButtonItem2: MenuButton[] = [
  { id: 33, label: 'ヘルスチェックAI', url: '/health-score', iconKey: 'healthCheck' },
  { id: 24, label: '健康診断結果登録', url: '/health-checkup', iconKey: 'result' },
  {
    id: 25,
    label: '健診・検診日の記録',
    callback: () => useMenuStore.getState().openHealthCheckupLink(),
    iconKey: 'record',
  },
];

const newButtonList: MenuButton[] = [
  { id: 32, label: 'グラフ', url: '/graph', iconKey: 'graph' },
  { id: 22, label: '健康記録', url: '/health-record', iconKey: 'score' },
  { id: 42, label: 'ミッション', url: '/mission', iconKey: 'mission' },
  { id: 33, label: 'ヘルスチェックAI', url: '/health-score', iconKey: 'healthCheck' },
];

const order = [32, 22, 42, 31, 41, 44, 46, 33, 24, 25, 26, 13, 47];

interface MenuStore {
  buttonList: MenuButton[];
  refreshButtons: (organizerUseCodes: string[]) => Promise<void>;
  clearButtonList: () => void;
  openHealthCheckupLink: () => void;
}

export const useMenuStore = create<MenuStore>()(
  persist(
    (set) => ({
      buttonList: [],
      async refreshButtons(organizerUseCodes) {
        try {
          if (process.env.NEXT_PUBLIC_APP_ENV === 'dev') {
            const result = await homePageAPI.getOrganizerInfo({ organizerCode: organizerUseCodes });
            const enabledIds = (result?.optionUseStatus ?? [])
              .filter((item) => item.useSts === 1)
              .map((item) => item.fcnId);

            const newButtonList = menuButtonItem2.filter((btn) => enabledIds.includes(btn.id));
            const allButtons = [...menuButtonItem1, ...newButtonList];

            const sortedButtons: MenuButton[] = order
              .map((id) => allButtons.find((btn) => btn.id === id))
              .filter(Boolean) as MenuButton[];

            set({ buttonList: sortedButtons });
          } else {
            set({ buttonList: newButtonList });
          }
        } catch (error) {
          console.error('refreshButtons error:', error);
        }
      },

      clearButtonList: () => set({ buttonList: [] }),
      openHealthCheckupLink: () => {},
    }),
    {
      name: 'menu-storage',
      storage: createJSONStorage(() => storage),
      partialize: (state) => ({ buttonList: state.buttonList }),
    },
  ),
);
