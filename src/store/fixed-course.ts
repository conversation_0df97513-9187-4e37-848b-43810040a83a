import type { FixedCourse } from '@/types/walking-course';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface FixedCourseState {
  fixedCourse?: FixedCourse;
}

interface FixedCourseActions {
  setFixedCourse: (course: FixedCourse) => void;
  clearFixedCourse: () => void;
}

type FixedCourseStore = FixedCourseState & FixedCourseActions;

/**
 * fixedCourse 専用の状態管理 Store
 * localStorage に自動的に永続化される
 */
export const useFixedCourseStore = create<FixedCourseStore>()(
  persist(
    (set) => ({
      fixedCourse: undefined,
      setFixedCourse: (course: FixedCourse) => set({ fixedCourse: course }),
      clearFixedCourse: () => set({ fixedCourse: undefined }),
    }),
    {
      name: 'fixed-course-storage',
      storage: createJSONStorage(() => localStorage),
    },
  ),
);
