import type { ListWalkingCourseRequest } from '@/types/walking-course';
import { create } from 'zustand';

interface WalkingCourseStore {
  isLoading: boolean;
  error: string | null;
  // 搜索参数
  searchParams: ListWalkingCourseRequest;
  setGeolocation: (location: { lat: number; lng: number }) => void;
  setSearchParams: (params: ListWalkingCourseRequest) => void;
  updateSearchSort: (order: string, location: { lat: number; lng: number } | null) => void;
  updateSearchFilters: (
    filters: ListWalkingCourseRequest['filters'],
    location: { lat: number; lng: number } | null,
  ) => void;
  clearSearchParams: () => void;
  clearError: () => void;
}

export const useWalkingCourseStore = create<WalkingCourseStore>((set, get) => ({
  isLoading: false,
  error: null,
  // 初始化搜索参数
  searchParams: {
    filters: {},
    sort: 'popular',
  },

  setGeolocation: (location: { lat: number; lng: number }) => {
    const latitude = location.lat.toFixed(6) || undefined;
    const longitude = location.lng.toFixed(6) || undefined;
    set({
      isLoading: false,
      error: null,
      searchParams: {
        latitude,
        longitude,
        filters: {},
        sort: 'popular',
      },
    });
  },

  setSearchParams: (params: ListWalkingCourseRequest) => {
    set({ searchParams: params });
  },

  updateSearchSort: (order: string, location: { lat: number; lng: number } | null) => {
    const latitude = location?.lat.toFixed(6) || undefined;
    const longitude = location?.lng.toFixed(6) || undefined;
    set((state) => ({
      searchParams: {
        ...state.searchParams,
        latitude,
        longitude,
        sort: order,
      },
    }));
  },

  updateSearchFilters: (
    filters: ListWalkingCourseRequest['filters'],
    location: { lat: number; lng: number } | null,
  ) => {
    const latitude = location?.lat.toFixed(6) || undefined;
    const longitude = location?.lng.toFixed(6) || undefined;
    set((state) => ({
      searchParams: {
        ...state.searchParams,
        latitude,
        longitude,
        filters,
      },
    }));
  },

  clearSearchParams: () => {
    set({
      searchParams: {
        latitude: undefined,
        longitude: undefined,
        filters: {},
        sort: 'popular',
      },
    });
  },

  clearError: () => {
    set({ error: null });
  },
}));
