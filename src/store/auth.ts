import type { PopupInfoDetails } from '@/types/home-data';
import storage from '@/utils/obfuscate-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export enum Sex {
  MALE = '1',
  FEMALE = '2',
  UNKNOWN = '', // 未設定のオプション
}
export interface User {
  id: string;
  name: string;
  email: string;
  birthday: string;
  sex: Sex;
  userAgent?: string;
  organizerID: string;
  organizerCode?: string;
  useOrganizerID: string | null;
  lat: number;
  lng: number;
}

export interface PhoneType {
  appVersion: string;
  userAgent: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isRehydrated: boolean;
  appData: PhoneType | null;
  pointList: PopupInfoDetails[];
  scoreList: { key: string; value: unknown }[];
  pointDialogTitle: string;
  setPointDialogTitle: (pointDialogTitle: string) => void;
  setScoreList: (scoreList: { key: string; value: unknown }[]) => void;
  setPointList: (pointList: PopupInfoDetails[]) => void;
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  setIsRehydrated: (isRehydrated: boolean) => void;
  setAppData: (appData: PhoneType | null) => void;
  logout: () => void;
  getIsMultiOrg: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      appData: null,
      isAuthenticated: false,
      isRehydrated: false,
      pointList: [],
      scoreList: [],
      pointDialogTitle: '',
      setPointDialogTitle: (pointDialogTitle) => set({ pointDialogTitle }),
      setScoreList: (scoreList) => set({ scoreList }),
      setPointList: (pointList) => set({ pointList }),
      setUser: (user) => set({ user }),
      setToken: (token) => set({ token, isAuthenticated: !!token }),
      setAppData: (appData) => set({ appData }),
      logout: () => {
        set({ user: null, token: null, isAuthenticated: false });
      },
      setIsRehydrated: (value) => set({ isRehydrated: value }),
      getIsMultiOrg: () => {
        const state = get();
        if (state.user?.organizerID) {
          return state.user.organizerID.includes(',');
        }
        return false;
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => storage),
      partialize: (state: AuthState) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        state?.setIsRehydrated(true);
      },
    },
  ),
);
