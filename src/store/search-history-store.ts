import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface SearchHistoryItem {
  keyword: string;
  timestamp: number;
}

interface SearchHistoryStore {
  searchHistory: Record<string, SearchHistoryItem[]>;

  // 获取指定key的搜索历史
  getSearchHistory: (key: string) => SearchHistoryItem[];

  // 添加搜索历史
  addSearchHistory: (key: string, keyword: string) => void;

  // 清除指定key的搜索历史
  clearSearchHistory: (key: string) => void;

  // 删除指定的搜索历史项
  removeSearchHistoryItem: (key: string, keyword: string) => void;
}

export const useSearchHistoryStore = create<SearchHistoryStore>()(
  persist(
    (set, get) => ({
      searchHistory: {},

      getSearchHistory: (key: string) => {
        const history = get().searchHistory[key] || [];
        // 按时间戳降序排列，最新的在前面
        return history.sort((a, b) => b.timestamp - a.timestamp);
      },

      addSearchHistory: (key: string, keyword: string) => {
        if (!keyword.trim()) return;

        set((state) => {
          const currentHistory = state.searchHistory[key] || [];

          // 移除已存在的相同关键词
          const filteredHistory = currentHistory.filter((item) => item.keyword !== keyword.trim());

          // 添加新的搜索记录到开头
          const newHistory = [
            { keyword: keyword.trim(), timestamp: Date.now() },
            ...filteredHistory,
          ].slice(0, 10); // 最多保存10条记录

          return {
            searchHistory: {
              ...state.searchHistory,
              [key]: newHistory,
            },
          };
        });
      },

      clearSearchHistory: (key: string) => {
        set((state) => ({
          searchHistory: {
            ...state.searchHistory,
            [key]: [],
          },
        }));
      },

      removeSearchHistoryItem: (key: string, keyword: string) => {
        set((state) => {
          const currentHistory = state.searchHistory[key] || [];
          const filteredHistory = currentHistory.filter((item) => item.keyword !== keyword);

          return {
            searchHistory: {
              ...state.searchHistory,
              [key]: filteredHistory,
            },
          };
        });
      },
    }),
    {
      name: 'search-history-storage',
    },
  ),
);
