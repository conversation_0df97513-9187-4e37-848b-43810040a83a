import { commonCodesAPI } from '@/api/modules/code-api';
import type { BaseType, CodeOption, CommonCodesData } from '@/types/code-types';
import toast from 'react-hot-toast';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface CodeStoreState {
  // 元データ
  organizerTypeList: BaseType[];
  typeList: BaseType[];

  // 読み込み状態
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number | null;

  // アクション
  fetchCommonCodes: () => Promise<void>;
  initCommonCodes: () => Promise<void>;

  // 汎用取得メソッド
  getTypeByKey: (typeKey: string) => BaseType | undefined;
  getOptionsByTypeKey: (typeKey: string) => CodeOption[];
}

// 変換関数：BaseTypeをCodeOption配列に変換
const convertToOptions = (baseType: BaseType | undefined): CodeOption[] => {
  if (!baseType || !baseType.codeList) {
    return [];
  }

  return baseType.codeList.map((code) => {
    return {
      value: code.codeKey,
      label: code.codeName,
    };
  });
};

// 検索関数：typeKeyに基づいてBaseTypeを検索
const findTypeByKey = (typeList: BaseType[], typeKey: string): BaseType | undefined => {
  return typeList.find((type) => type.typeKey === typeKey);
};

export const useCodeStore = create<CodeStoreState>()(
  persist(
    (set, get) => ({
      // 初期状態
      organizerTypeList: [],
      typeList: [],
      isLoading: false,
      error: null,
      lastFetchTime: null,

      // アクション
      fetchCommonCodes: async () => {
        set({ isLoading: true, error: null });

        try {
          // APIからデータを取得を試行
          let data: CommonCodesData;
          try {
            data = await commonCodesAPI.listAllCommonCodes();
          } catch (apiError: unknown) {
            if (apiError instanceof Error) {
              toast.error(apiError.message);
            }
            // APIが失敗した場合、モックデータを使用
            const mockData = await import('@/data/codes.json');
            data = mockData.data;
          }

          set({
            organizerTypeList: data.organizerTypeList || [],
            typeList: data.typeList || [],
            isLoading: false,
            error: null,
            lastFetchTime: Date.now(),
          });
        } catch (error: unknown) {
          if (error instanceof Error) {
            toast.error(error.message);
          }
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch common codes',
          });
        }
      },

      // スマートデータ取得：必要な時のみ取得
      initCommonCodes: async () => {
        const state = get();
        const CACHE_DURATION = 5 * 60 * 1000; // 5分間キャッシュ
        const now = Date.now();

        // データが空またはキャッシュが期限切れの場合、データを取得
        const shouldFetch =
          (state.organizerTypeList.length === 0 && state.typeList.length === 0) ||
          (state.lastFetchTime && now - state.lastFetchTime > CACHE_DURATION);

        if (shouldFetch && !state.isLoading) {
          await get().fetchCommonCodes();
        } else {
          console.log('CommonCodesデータは既に存在し、期限切れではないため取得をスキップ');
        }
      },

      // 汎用取得メソッド
      getTypeByKey: (typeKey: string) => {
        const state = get();
        return findTypeByKey([...state.organizerTypeList, ...state.typeList], typeKey);
      },

      getOptionsByTypeKey: (typeKey: string) => {
        const state = get();
        const type = findTypeByKey([...state.organizerTypeList, ...state.typeList], typeKey);

        return convertToOptions(type);
      },
    }),
    {
      name: 'code-store',
      storage: createJSONStorage(() => localStorage),
      // コアデータのみ永続化、読み込み状態とエラー情報は永続化しない
      partialize: (state) => ({
        organizerTypeList: state.organizerTypeList,
        typeList: state.typeList,
      }),
    },
  ),
);
