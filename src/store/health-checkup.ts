import { healthCheckupAPI } from '@/api/modules/health-checkup';
import type {
  AnemiaData,
  BloodGlucoseData,
  BloodLipidData,
  BloodPressureData,
  BodyMeasureData,
  ExamCategory,
  ExamResultData,
  HealthExamResultRequest,
  InquiryData,
  KidneyFunctionData,
  LiverFunctionData,
  OthersData,
  Question1Data,
  Question2Data,
  Question3Data,
  Question4Data,
  UrinalysisData,
} from '@/types/health-checkup-input';
import { create } from 'zustand';

interface HealthCheckupState {
  // 現在のステップ
  currentStep: number;
  // 総ステップ数
  totalSteps: number;
  // 現在のカテゴリ
  currentCategory: ExamCategory;
  // 受診日
  examDay: string;
  // 選択された受診日（exam-resultページ用）
  selectedExamDay: string;
  // フォームデータ
  formData: {
    bodyMeasureData?: BodyMeasureData;
    bloodPressureData?: BloodPressureData;
    bloodLipidData?: BloodLipidData;
    liverFunctionData?: LiverFunctionData;
    bloodGlucoseData?: BloodGlucoseData;
    urinalysisData?: UrinalysisData;
    anemiaData?: AnemiaData;
    kidneyFunctionData?: KidneyFunctionData;
    othersData?: OthersData;
    question1Data?: Question1Data;
    question2Data?: Question2Data;
    question3Data?: Question3Data;
    question4Data?: Question4Data;
    inquiryData?: InquiryData;
  };
  // ローディング状態
  isLoading: boolean;
  // エラーメッセージ
  error: string | null;
}

interface HealthCheckupActions {
  // 現在のステップを設定
  setCurrentStep: (step: number) => void;
  // 現在のカテゴリを設定
  setCurrentCategory: (category: ExamCategory) => void;
  // 受診日を設定
  setExamDay: (examDay: string) => void;
  // 選択された受診日を設定
  setSelectedExamDay: (selectedExamDay: string) => void;
  // フォームデータを更新
  updateFormData: <T extends keyof HealthCheckupState['formData']>(
    key: T,
    data: HealthCheckupState['formData'][T],
  ) => void;
  // ローディング状態を設定
  setLoading: (loading: boolean) => void;
  // エラーメッセージを設定
  setError: (error: string | null) => void;
  // 状態をリセット
  reset: () => void;
  // 次のステップへ
  nextStep: () => void;
  // 前のステップへ
  prevStep: () => void;
  // 現在のカテゴリを取得
  getCurrentCategory: () => ExamCategory;
  // フォームデータを送信
  submitFormData: (data: any) => Promise<void>;
}

// フォームステップ設定
export const FORM_STEPS: { step: number; category: ExamCategory; title: string }[] = [
  { step: 1, category: 'bodyMeasure', title: '身体測定' },
  { step: 2, category: 'bloodPressure', title: '血圧' },
  { step: 3, category: 'bloodLipid', title: '血中脂質検査' },
  { step: 4, category: 'liverFunction', title: '肝機能' },
  { step: 5, category: 'bloodGlucose', title: '血糖' },
  { step: 6, category: 'urinalysis', title: '尿' },
  { step: 7, category: 'anemia', title: '貧血' },
  { step: 8, category: 'kidneyFunction', title: '腎機能' },
  { step: 9, category: 'others', title: 'そのほか' },
  { step: 10, category: 'question1', title: '質問票1' },
  { step: 11, category: 'question2', title: '質問票2' },
  { step: 12, category: 'question3', title: '質問票3' },
  { step: 13, category: 'question4', title: '質問票4' },
  { step: 14, category: 'inquiry', title: '診察項目' },
];

const initialState: HealthCheckupState = {
  currentStep: 1,
  totalSteps: 14,
  currentCategory: 'bodyMeasure',
  examDay: '', // 初期値は空文字列に変更
  selectedExamDay: '', // デフォルトは空、ページ初期化時に設定
  formData: {},
  isLoading: false,
  error: null,
};

export const useHealthCheckupStore = create<HealthCheckupState & HealthCheckupActions>()(
  (set, get) => ({
    ...initialState,

    setCurrentStep: (step: number) => {
      const stepConfig = FORM_STEPS.find((s) => s.step === step);
      if (stepConfig) {
        set({
          currentStep: step,
          currentCategory: stepConfig.category,
        });
      }
    },

    setCurrentCategory: (category: ExamCategory) => {
      set({ currentCategory: category });
    },

    setExamDay: (examDay: string) => {
      set({ examDay });
    },

    setSelectedExamDay: (selectedExamDay: string) => {
      set({ selectedExamDay });
    },

    updateFormData: (key, data) => {
      set((state) => ({
        formData: {
          ...state.formData,
          [key]: data,
        },
      }));
    },

    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },

    setError: (error: string | null) => {
      set({ error });
    },

    reset: () => {
      set(initialState);
    },

    nextStep: () => {
      const { currentStep, totalSteps } = get();
      if (currentStep < totalSteps) {
        const nextStep = currentStep + 1;
        const stepConfig = FORM_STEPS.find((s) => s.step === nextStep);
        if (stepConfig) {
          set({
            currentStep: nextStep,
            currentCategory: stepConfig.category,
          });
        }
      }
    },

    prevStep: () => {
      const { currentStep } = get();
      if (currentStep > 1) {
        const prevStep = currentStep - 1;
        const stepConfig = FORM_STEPS.find((s) => s.step === prevStep);
        if (stepConfig) {
          set({
            currentStep: prevStep,
            currentCategory: stepConfig.category,
          });
        }
      }
    },

    getCurrentCategory: () => {
      return get().currentCategory;
    },

    submitFormData: async (data: any) => {
      const { currentStep, currentCategory, examDay } = get();

      if (!examDay) {
        throw new Error('受診日が設定されていません');
      }

      try {
        set({ isLoading: true, error: null });

        // 今日の日付をデフォルト値として取得
        const today = new Date().toISOString().split('T')[0];
        const finalExamDay = examDay || today;

        // リクエストパラメータを構築
        const request: HealthExamResultRequest = {
          examDay: finalExamDay,
          category: [currentCategory],
          autoFlag: 2,
          maxPagesReached: currentStep,
          sex: '1', // TODO: ユーザー情報から取得
        };

        // 現在のカテゴリに応じて対応するデータを設定
        switch (currentCategory) {
          case 'bodyMeasure':
            request.bodyMeasureData = data;
            break;
          case 'bloodPressure':
            request.bloodPressureData = data;
            break;
          case 'bloodLipid':
            request.bloodLipidData = data;
            break;
          case 'liverFunction':
            request.liverFunctionData = data;
            break;
          case 'bloodGlucose':
            request.bloodGlucoseData = data;
            break;
          case 'urinalysis':
            request.urinalysisData = data;
            break;
          case 'anemia':
            request.anemiaData = data;
            break;
          case 'kidneyFunction':
            request.kidneyFunctionData = data;
            break;
          case 'others':
            request.othersData = data;
            break;
          case 'question1':
            request.question1Data = data;
            break;
          case 'question2':
            request.question2Data = data;
            break;
          case 'question3':
            request.question3Data = data;
            break;
          case 'question4':
            request.question4Data = data;
            break;
          case 'inquiry':
            request.inquiryData = data;
            break;
          default:
            throw new Error(`未対応のカテゴリです: ${currentCategory}`);
        }

        // API呼び出し
        await healthCheckupAPI.updateHealthExamResultData(request);

        // ローカル状態を更新
        get().updateFormData(
          `${currentCategory}Data` as keyof HealthCheckupState['formData'],
          data,
        );

        set({ isLoading: false });
      } catch (error) {
        console.error('Failed to submit form data:', error);
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'データの保存に失敗しました',
        });
        throw error;
      }
    },
  }),
);
