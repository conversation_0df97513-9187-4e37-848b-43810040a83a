import storage from '@/utils/obfuscate-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface GlobalState {
  isRiskEnabled: boolean;
  isRiskInited: boolean;
  isRiskShowed: boolean;
  isGlobalInited: boolean;
  fromPage: string;
  getAgreedAsset: string; // AI open state
  setAgreedAsset: (getAgreedAsset: string) => void;
  setIsRiskEnabled: (isRiskEnabled: boolean) => void;
  setIsRiskInited: (isRiskInited: boolean) => void;
  setIsRiskShowed: (isGlobalInited: boolean) => void;
  setIsGlobalInited: (isGlobalInited: boolean) => void;
  setFromPage: (fromPage: string) => void;
  clearGlobalStore: () => void;
}

export const useGlobalStore = create<GlobalState>()((set) => ({
  isRiskEnabled: false,
  isRiskInited: false,
  isRiskShowed: false,
  isGlobalInited: false,
  getAgreedAsset: '',
  fromPage: '',
  setAgreedAsset: (getAgreedAsset: string) => set({ getAgreedAsset }),
  setIsRiskEnabled: (isRiskEnabled: boolean) => set({ isRiskEnabled }),
  setIsRiskInited: (isRiskInited: boolean) => set({ isRiskInited }),
  setIsRiskShowed: (isRiskShowed: boolean) => set({ isRiskShowed }),
  setIsGlobalInited: (isGlobalInited: boolean) => set({ isGlobalInited }),
  setFromPage: (fromPage: string) => set({ fromPage }),
  clearGlobalStore: () =>
    set({
      isRiskEnabled: false,
      isRiskInited: false,
      isRiskShowed: false,
      isGlobalInited: false,
      getAgreedAsset: '',
    }),
}));
