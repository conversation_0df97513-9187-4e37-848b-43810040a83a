import type { DateType } from '@/app/(webview)/ranking/_components/update-date';
import {
  CATEGORY_TYPE,
  DAY_WEEK_TAB,
  GROUP_ALL_TAB,
  MYSELF_FLG,
} from '@/app/(webview)/ranking/_const';
import type { Pagination } from '@/types/point';
import storage from '@/utils/obfuscate-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface RankingState {
  // 現在選択されている機能タブ
  activeTab: number;
  groupAllTab: string;
  dateType: string;
  selectDate: DateType;
  myselfFlg: MYSELF_FLG;
  pagination: Pagination;

  // アクション
  setPagination: (pagination: Pagination) => void;
  setActiveTab: (activeTab: number) => void;
  setGroupAllTab: (groupAllTab: string) => void;
  setDateType: (dateType: string) => void;
  setSelectDate: (selectDate: DateType) => void;
  setMyselfFlg: (myselfFlg: MYSELF_FLG) => void;
}

export const useRankingStore = create<RankingState>()(
  persist(
    (set, get) => ({
      activeTab: CATEGORY_TYPE.STEP,
      groupAllTab: GROUP_ALL_TAB.GROUP,
      dateType: DAY_WEEK_TAB.WEEK,
      selectDate: {
        startDate: new Date(),
        endDate: new Date(),
      },
      myselfFlg: MYSELF_FLG.DEFAULT,
      pagination: {
        total: 0,
        page: 1,
        limit: 30,
        pages: 1,
      },
      setPagination: (pagination) => set({ pagination }),
      setActiveTab: (activeTab) => set({ activeTab }),
      setGroupAllTab: (groupAllTab) => set({ groupAllTab }),
      setDateType: (dateType) => set({ dateType }),
      setSelectDate: (selectDate) => set({ selectDate }),
      setMyselfFlg: (myselfFlg) => set({ myselfFlg }),
    }),
    {
      name: 'ranking-storage',
      storage: createJSONStorage(() => storage),
    },
  ),
);
