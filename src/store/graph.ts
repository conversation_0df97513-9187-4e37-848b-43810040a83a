import { graphAPI } from '@/api/modules/graph';
import { TimeRangeType } from '@/types/enums';
import type {
  BloodGlucoseGraphData,
  BloodPressureGraphData,
  EnergyGraphData,
  FatPercentageGraphData,
  GraphSearchParam,
  GraphTab,
  SleepGraphData,
  StepDistanceTimeGraphData,
  StepGraphData,
  WeightBmiGraphData,
} from '@/types/graph';
import toast from 'react-hot-toast';
import { create } from 'zustand';

// グラフデータタイプマッピングの定義
type GraphDataMap = {
  step: StepGraphData;
  stepDistanceTime: StepDistanceTimeGraphData;
  sleep: SleepGraphData;
  fatPercentage: FatPercentageGraphData;
  weightBmi: WeightBmiGraphData;
  energy: EnergyGraphData;
  bloodPressure: BloodPressureGraphData;
  bloodGlucose: BloodGlucoseGraphData;
};

interface GraphState {
  // 現在選択されている機能タブ
  activeTab: GraphTab;
  // 現在の時間範囲タイプ
  timeRange: TimeRangeType;
  // 現在選択されている時間範囲
  startDate: Date;
  endDate: Date;
  // ローディング状態
  isLoading: boolean;
  // データ - unknownではなく具体的なタイプを使用
  data: {
    [K in GraphTab]?: GraphDataMap[K];
  };

  // アクション
  setActiveTab: (tab: GraphTab) => void;
  setTimeRange: (timeRange: TimeRangeType) => void;
  setDateRange: (start: Date, end: Date) => void;
  fetchData: () => Promise<void>;
  // 日付をYYYY-MM-DD形式でフォーマット
  formatDate: (date: Date) => string;

  // 日付範囲切り替えメソッド
  goToPreviousPeriod: () => void;
  goToNextPeriod: () => void;

  // 表示日付のフォーマット
  getFormattedDateRangeTitle: () => string;

  // 現在の期間の日付範囲を設定
  setCurrentWeekRange: () => void;
  setCurrentMonthRange: () => void;
  setCurrentYearRange: () => void;

  // 現在が最新の期間かどうかを判定
  isCurrentPeriod: () => boolean;
}

// 当前周の開始日と終了日を計算する関数
const getCurrentWeekRange = () => {
  const today = new Date();
  const currentDay = today.getDay(); // 0は日曜日、1は月曜日、...

  // 今週の開始日（月曜日）を計算
  const startDate = new Date(today);
  const daysToMonday = currentDay === 0 ? 6 : currentDay - 1;
  startDate.setDate(today.getDate() - daysToMonday);

  // 今週の終了日（日曜日）を計算
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  return { startDate, endDate };
};

export const useGraphStore = create<GraphState>((set, get) => {
  const { startDate, endDate } = getCurrentWeekRange();

  return {
    // 初期状態
    activeTab: 'step',
    timeRange: TimeRangeType.WEEK,
    startDate: startDate,
    endDate: endDate,
    isLoading: false,
    data: {},

    // アクション
    setActiveTab: (tab) => set({ activeTab: tab }),
    setTimeRange: (timeRange) => {
      const currentState = get();

      // 時間範囲タイプが変更された場合のみ日付範囲を更新
      if (currentState.timeRange !== timeRange) {
        set({ timeRange: timeRange });

        // 時間範囲タイプに応じて対応する日付範囲を設定
        const state = get();
        switch (timeRange) {
          case TimeRangeType.WEEK:
            state.setCurrentWeekRange();
            break;
          case TimeRangeType.MONTH:
            state.setCurrentMonthRange();
            break;
          case TimeRangeType.YEAR:
            state.setCurrentYearRange();
            break;
        }
      }
    },
    setDateRange: (start, end) => set({ startDate: start, endDate: end }),

    // 日付をYYYY-MM-DD形式でフォーマット
    formatDate: (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 現在の週の日付範囲を設定
    setCurrentWeekRange: () => {
      const today = new Date();
      const currentDay = today.getDay(); // 0は日曜日、1は月曜日、...

      // 今週の開始日（月曜日）と終了日（日曜日）を計算
      const startDate = new Date(today);
      // 今日が日曜日(0)の場合、月曜日は6日前；そうでなければ(currentDay-1)日前
      const daysToMonday = currentDay === 0 ? 6 : currentDay - 1;
      startDate.setDate(today.getDate() - daysToMonday);

      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 6);

      set({ startDate, endDate });
    },

    // 現在の月の日付範囲を設定
    setCurrentMonthRange: () => {
      const today = new Date();

      // 当月の最初の日
      const startDate = new Date(today.getFullYear(), today.getMonth(), 1);

      // 当月の最後の日
      const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      set({ startDate, endDate });
    },

    // 現在の年の日付範囲を設定（4月1日から翌年3月31日まで）
    setCurrentYearRange: () => {
      const today = new Date();
      const currentMonth = today.getMonth();

      // 現在の月が4月未満の場合、年度範囲は昨年4月から今年3月
      // そうでなければ今年4月から来年3月
      const fiscalYearStart = currentMonth < 3 ? today.getFullYear() - 1 : today.getFullYear();

      const startDate = new Date(fiscalYearStart, 3, 1); // 4月1日
      const endDate = new Date(fiscalYearStart + 1, 2, 31); // 翌年3月31日

      set({ startDate, endDate });
    },

    // 前の期間に切り替え
    goToPreviousPeriod: () => {
      const { timeRange, startDate, endDate } = get();
      let newStartDate: Date = new Date();
      let newEndDate: Date = new Date();

      switch (timeRange) {
        case TimeRangeType.WEEK: {
          // 前の週
          newStartDate = new Date(startDate);
          newStartDate.setDate(startDate.getDate() - 7);

          newEndDate = new Date(endDate);
          newEndDate.setDate(endDate.getDate() - 7);
          break;
        }

        case TimeRangeType.MONTH: {
          // 前の月
          newStartDate = new Date(startDate.getFullYear(), startDate.getMonth() - 1, 1);
          newEndDate = new Date(startDate.getFullYear(), startDate.getMonth(), 0);
          break;
        }

        case TimeRangeType.YEAR: {
          // 前の年
          newStartDate = new Date(startDate);
          newStartDate.setFullYear(startDate.getFullYear() - 1);

          newEndDate = new Date(endDate);
          newEndDate.setFullYear(endDate.getFullYear() - 1);
          break;
        }
      }

      set({ startDate: newStartDate, endDate: newEndDate });
      // ここでfetchDataを呼び出す必要はない、page.tsxのuseEffectがstartDateとendDateの変化時に自動的に呼び出す
    },

    // 次の期間に切り替え
    goToNextPeriod: () => {
      const { timeRange, startDate, endDate } = get();
      let newStartDate: Date = new Date();
      let newEndDate: Date = new Date();

      switch (timeRange) {
        case TimeRangeType.WEEK: {
          // 次の週
          newStartDate = new Date(startDate);
          newStartDate.setDate(startDate.getDate() + 7);

          newEndDate = new Date(endDate);
          newEndDate.setDate(endDate.getDate() + 7);
          break;
        }

        case TimeRangeType.MONTH: {
          // 次の月
          newStartDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 1);
          newEndDate = new Date(startDate.getFullYear(), startDate.getMonth() + 2, 0);
          break;
        }

        case TimeRangeType.YEAR: {
          // 次の年
          newStartDate = new Date(startDate);
          newStartDate.setFullYear(startDate.getFullYear() + 1);

          newEndDate = new Date(endDate);
          newEndDate.setFullYear(endDate.getFullYear() + 1);
          break;
        }
      }

      set({ startDate: newStartDate, endDate: newEndDate });
      // ここでfetchDataを呼び出す必要はない、page.tsxのuseEffectがstartDateとendDateの変化時に自動的に呼び出す
    },

    // フォーマットされた日付範囲タイトルを取得
    getFormattedDateRangeTitle: () => {
      const { timeRange, startDate, endDate } = get();
      const dayNames = ['日', '月', '火', '水', '木', '金', '土'];

      switch (timeRange) {
        case TimeRangeType.WEEK: {
          // 形式：2025年3月2日(日) - 3月8日(土)
          const startYear = startDate.getFullYear();
          const startMonth = startDate.getMonth() + 1;
          const startDay = startDate.getDate();
          const startDayName = dayNames[startDate.getDay()];

          const endMonth = endDate.getMonth() + 1;
          const endDay = endDate.getDate();
          const endDayName = dayNames[endDate.getDay()];
          const endYear = endDate.getFullYear();

          return `${startYear}年${startMonth}月${startDay}日(${startDayName}) - ${endMonth}月${endDay}日(${endDayName})`;
        }

        case TimeRangeType.MONTH: {
          // 形式：2025年3月
          return `${startDate.getFullYear()}年${startDate.getMonth() + 1}月`;
        }

        case TimeRangeType.YEAR: {
          // 形式：2024年4月 - 2025年3月
          return `${startDate.getFullYear()}年${startDate.getMonth() + 1}月 - ${endDate.getFullYear()}年${endDate.getMonth() + 1}月`;
        }

        default:
          return '';
      }
    },

    fetchData: async () => {
      const { activeTab, timeRange, startDate, endDate, formatDate } = get();
      set({ isLoading: true });
      // clear data
      set({ data: {} });

      try {
        // クエリパラメータを準備
        const queryParams: GraphSearchParam = {
          startDate: formatDate(startDate),
          endDate: formatDate(endDate),
          timeRange: timeRange,
        };

        // 異なるタブに応じて対応するAPI関数を呼び出し
        let data: unknown;
        switch (activeTab) {
          case 'step':
            data = await graphAPI.getStepGraph(queryParams);
            break;
          case 'stepDistanceTime':
            data = await graphAPI.getStepDistanceTimeGraph(queryParams);
            break;
          case 'sleep':
            data = await graphAPI.getSleepGraph(queryParams);
            break;
          case 'fatPercentage':
            data = await graphAPI.getFatPercentageGraph(queryParams);
            break;
          case 'weightBmi':
            data = await graphAPI.getWeightBmiGraph(queryParams);
            break;
          case 'energy':
            data = await graphAPI.getEnergyGraph(queryParams);
            break;
          case 'bloodPressure':
            data = await graphAPI.getBloodPressureGraph(queryParams);
            break;
          case 'bloodGlucose':
            data = await graphAPI.getBloodGlucoseGraph(queryParams);
            break;
        }

        set((state) => ({
          data: {
            ...state.data,
            [activeTab]: data,
          },
          isLoading: false,
        }));
      } catch (error: unknown) {
        if (error instanceof Error) {
          toast.error(error.message);
        }
        set({ isLoading: false });
      }
    },

    // 現在が最新の期間かどうかを判定（現在の年、現在の月、または現在の週）
    isCurrentPeriod: () => {
      const { timeRange, startDate, endDate } = get();

      // 比較用の一時的な日付オブジェクトを作成
      const tempStartDate = new Date();
      const tempEndDate = new Date();

      switch (timeRange) {
        case TimeRangeType.WEEK: {
          // 現在の週の日付範囲を計算
          const today = new Date();
          const currentDay = today.getDay(); // 0は日曜日、1は月曜日、...

          // 今週の開始日（月曜日）と終了日（日曜日）を計算
          // 今日が日曜日(0)の場合、月曜日は6日前；そうでなければ(currentDay-1)日前
          const daysToMonday = currentDay === 0 ? 6 : currentDay - 1;
          tempStartDate.setDate(today.getDate() - daysToMonday);
          tempStartDate.setHours(0, 0, 0, 0);

          tempEndDate.setTime(tempStartDate.getTime());
          tempEndDate.setDate(tempStartDate.getDate() + 6);
          tempEndDate.setHours(23, 59, 59, 999);

          // 日付が同じかどうかを比較（時間部分は無視）
          return (
            tempStartDate.getFullYear() === startDate.getFullYear() &&
            tempStartDate.getMonth() === startDate.getMonth() &&
            tempStartDate.getDate() === startDate.getDate() &&
            tempEndDate.getFullYear() === endDate.getFullYear() &&
            tempEndDate.getMonth() === endDate.getMonth() &&
            tempEndDate.getDate() === endDate.getDate()
          );
        }

        case TimeRangeType.MONTH: {
          // 現在の月の日付範囲を計算
          const today = new Date();

          // 当月の最初の日
          tempStartDate.setFullYear(today.getFullYear(), today.getMonth(), 1);
          tempStartDate.setHours(0, 0, 0, 0);

          // 当月の最後の日
          tempEndDate.setFullYear(today.getFullYear(), today.getMonth() + 1, 0);
          tempEndDate.setHours(23, 59, 59, 999);

          // 年月が同じかどうかを比較
          return (
            tempStartDate.getFullYear() === startDate.getFullYear() &&
            tempStartDate.getMonth() === startDate.getMonth() &&
            tempEndDate.getFullYear() === endDate.getFullYear() &&
            tempEndDate.getMonth() === endDate.getMonth()
          );
        }

        case TimeRangeType.YEAR: {
          // 現在の年度の日付範囲を計算（4月1日から翌年3月31日まで）
          const today = new Date();
          const currentMonth = today.getMonth();

          // 現在の月が4月未満の場合、年度範囲は昨年4月から今年3月
          // そうでなければ今年4月から来年3月
          const fiscalYearStart = currentMonth < 3 ? today.getFullYear() - 1 : today.getFullYear();

          tempStartDate.setFullYear(fiscalYearStart, 3, 1); // 4月1日
          tempStartDate.setHours(0, 0, 0, 0);

          tempEndDate.setFullYear(fiscalYearStart + 1, 2, 31); // 翌年3月31日
          tempEndDate.setHours(23, 59, 59, 999);

          // 年月が同じかどうかを比較
          return (
            tempStartDate.getFullYear() === startDate.getFullYear() &&
            tempStartDate.getMonth() === startDate.getMonth() &&
            tempEndDate.getFullYear() === endDate.getFullYear() &&
            tempEndDate.getMonth() === endDate.getMonth()
          );
        }

        default:
          return false;
      }
    },
  };
});
