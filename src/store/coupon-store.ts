import type { SearchCouponRequest } from '@/types/coupon-types';
import { create } from 'zustand';

interface CouponStore {
  isLoading: boolean;
  error: string | null;

  // 搜索参数
  searchParams: SearchCouponRequest;
  setGeolocation: (location: { lat: number; lng: number }) => void;
  setSearchParams: (params: SearchCouponRequest) => void;
  updateSearchFilters: (filters: SearchCouponRequest['filters']) => void;
  clearSearchParams: () => void;

  clearError: () => void;
}

export const useCouponStore = create<CouponStore>((set, get) => ({
  isLoading: false,
  error: null,

  // 初始化搜索参数
  searchParams: {
    filters: { categories: [] },
    sortType: 'popular',
  },

  setGeolocation: (location: { lat: number; lng: number }) => {
    const latitude = location.lat.toFixed(6) || '';
    const longitude = location.lng.toFixed(6) || '';
    set((state) => ({
      isLoading: false,
      error: null,
      searchParams: {
        ...state.searchParams,
        latitude,
        longitude,
      },
    }));
  },

  setSearchParams: (params: SearchCouponRequest) => {
    set({ searchParams: params });
  },

  updateSearchFilters: (filters: SearchCouponRequest['filters']) => {
    set((state) => ({
      searchParams: {
        ...state.searchParams,
        filters,
      },
    }));
  },

  clearSearchParams: () => {
    set({
      searchParams: {
        sortType: 'popular',
        filters: { categories: [] },
      },
    });
  },

  clearError: () => {
    set({ error: null });
  },
}));
