import { create } from 'zustand';

import type {
  AccountProfileResponse,
  FriendOrganizerResponse,
  GroupType,
  LitaUserInfoResponse,
  MergedType,
  OrganizerInfoResponse,
  OrganizerSetupResponse,
} from '@/types/register';

interface OrganizerInfoState {
  QRGroupId: number | undefined;
  allOrganizerIds: string;
  businesstoken: string; //業務トークン
  deviceId: string;
  organizerInfo: OrganizerInfoResponse;
  friendInfo: FriendOrganizerResponse;
  accountProfile: AccountProfileResponse;
  organizerSetupDetail: OrganizerSetupResponse;
  litaUserInfo: LitaUserInfoResponse;
  newLoginFlag: boolean;
  groupAdditionFlag: boolean;
  homeDialog: boolean;
  useOrganizerId: string;
  litaOrganizerIds: string;
  groupList: GroupType[];
  //   modifyFlag: boolean;
  setQRGroupId: (QRGroupId: number | undefined) => void;
  setAllOrganizerIds: (allOrganizerIds: string) => void;
  setGroupList: (groupList: GroupType[]) => void;
  setLitaOrganizerIds: (litaOrganizerIds: string) => void;
  setUseOrganizerId: (useOrganizerId: string) => void;
  setBusinesstoken: (businesstoken: string) => void;
  setNewLoginFlag: (newLoginFlag: boolean) => void;
  setOrganizerInfo: (organizerInfo: OrganizerInfoResponse) => void;
  setFriendInfo: (friendInfo: FriendOrganizerResponse) => void;
  setAccountProfile: (accountProfile: AccountProfileResponse) => void;
  setDeviceId: (deviceId: string) => void;
  setOrganizerSetupDetail: (organizerSetupDetail: OrganizerSetupResponse) => void;
  setLitaUserInfo: (litaUserInfo: LitaUserInfoResponse) => void;
  setGroupAdditionFlag: (groupAdditionFlag: boolean) => void;
  setHomeDialog: (newLoginFlag: boolean) => void;
  reset: () => void;
}

export const ORGANIZER_INFO_STATE: OrganizerInfoResponse = {
  optionUseStatus: [],
  organizerList: [],
};

export const useRegisterState = create<OrganizerInfoState>()((set) => ({
  // 初期化
  QRGroupId: undefined,
  allOrganizerIds: '',
  litaOrganizerIds: '',
  businesstoken: '',
  deviceId: '',
  organizerInfo: { ...ORGANIZER_INFO_STATE },
  friendInfo: {},
  accountProfile: {},
  organizerSetupDetail: {},
  useOrganizerId: '',
  litaUserInfo: {},
  newLoginFlag: true, // true 新規モード
  groupAdditionFlag: false, // 団体追加フラグ＝１ true
  homeDialog: false,
  groupList: [],

  setQRGroupId: (QRGroupId: number | undefined) =>
    set(() => ({
      QRGroupId,
    })),

  setAllOrganizerIds: (allOrganizerIds: string) =>
    set(() => ({
      allOrganizerIds,
    })),

  setGroupList: (groupList: GroupType[]) =>
    set(() => ({
      groupList,
    })),

  setLitaOrganizerIds: (litaOrganizerIds: string) =>
    set(() => ({
      litaOrganizerIds,
    })),
  setUseOrganizerId: (useOrganizerId: string) =>
    set(() => ({
      useOrganizerId,
    })),
  //   modifyFlag: false,
  setHomeDialog: (homeDialog: boolean) =>
    set(() => ({
      homeDialog,
    })),

  setGroupAdditionFlag: (groupAdditionFlag: boolean) =>
    set(() => ({
      groupAdditionFlag,
    })),

  setOrganizerInfo: (organizerInfo: OrganizerInfoResponse) =>
    set(() => ({
      organizerInfo,
    })),

  setFriendInfo: (friendInfo: FriendOrganizerResponse) =>
    set(() => ({
      friendInfo,
    })),

  setAccountProfile: (accountProfile: AccountProfileResponse) =>
    set(() => ({
      accountProfile,
    })),

  reset: () => set({ organizerInfo: ORGANIZER_INFO_STATE }),

  setDeviceId: (deviceId: string) =>
    set(() => ({
      deviceId,
    })),

  setOrganizerSetupDetail: (organizerSetupDetail: OrganizerSetupResponse) =>
    set(() => ({
      organizerSetupDetail,
    })),

  setLitaUserInfo: (litaUserInfo: LitaUserInfoResponse) =>
    set(() => ({
      litaUserInfo,
    })),
  // true 新規モード
  setNewLoginFlag: (newLoginFlag: boolean) =>
    set(() => ({
      newLoginFlag,
    })),

  setBusinesstoken: (businesstoken: string) =>
    set(() => ({
      businesstoken,
    })),
}));
