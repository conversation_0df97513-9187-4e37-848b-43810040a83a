import type { OrganizerItem } from '@/types/out-login-types';
import storage from '@/utils/obfuscate-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface OutLoginState {
  isMult: boolean;
  choosedOrganizers: OrganizerItem[];
  initOrganizers: OrganizerItem[];

  getInitOrganizers: () => OrganizerItem[];
  setInitOrganizers: (initOrganizers: OrganizerItem[]) => void;
  getConfirmedOrganizers: () => OrganizerItem[];
  setConfirmedOrganizers: (choosedOrganizers: OrganizerItem[]) => void;
  getIsMult: () => boolean;
  setIsMult: (isMult: boolean) => void;
}

export const useOutLoginStore = create<OutLoginState>()(
  persist(
    (set, get) => ({
      choosedOrganizers: [],
      initOrganizers: [],
      getInitOrganizers: () => {
        return get().initOrganizers || [];
      },
      setInitOrganizers: (initOrganizers: OrganizerItem[]) =>
        set(() => ({
          initOrganizers,
        })),
      getConfirmedOrganizers: () => {
        return get().choosedOrganizers || [];
      },
      setConfirmedOrganizers: (choosedOrganizers: OrganizerItem[]) =>
        set(() => ({
          choosedOrganizers,
        })),
      isMult: false,
      getIsMult: () => {
        return get().isMult;
      },
      setIsMult: (isMult: boolean) =>
        set(() => ({
          isMult,
        })),
    }),
    {
      name: 'out-login-storage',
      storage: createJSONStorage(() => storage),
    },
  ),
);
