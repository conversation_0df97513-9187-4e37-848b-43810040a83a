import { eventAPI } from '@/api/modules/event-api';
import type { EventOptions, SearchEventListRequest } from '@/types/event-types';
import { create } from 'zustand';

interface EventStore {
  eventOptions: EventOptions | null;
  isLoading: boolean;
  error: string | null;

  // 搜索参数
  searchParams: SearchEventListRequest;
  setSearchParams: (params: SearchEventListRequest) => void;
  updateSearchFilters: (filters: SearchEventListRequest['filters']) => void;
  clearSearchParams: () => void;

  initEventOptions: () => Promise<void>;
  setEventOptions: (options: EventOptions) => void;
  clearError: () => void;
  eventRefreshTimeStamp: string;
  setEventRefreshTimeStamp: (timestamp: string) => void;
}

export const useEventStore = create<EventStore>((set, get) => ({
  eventOptions: null,
  isLoading: false,
  error: null,
  eventRefreshTimeStamp: '',
  setEventRefreshTimeStamp: (timestamp: string) => {
    set({ eventRefreshTimeStamp: timestamp });
  },

  // 初始化搜索参数
  searchParams: {
    sortType: 'popular',
  },

  setSearchParams: (params: SearchEventListRequest) => {
    set({ searchParams: params });
  },

  updateSearchFilters: (filters: SearchEventListRequest['filters']) => {
    set((state) => ({
      searchParams: {
        ...state.searchParams,
        filters,
      },
    }));
  },

  clearSearchParams: () => {
    set({
      searchParams: {
        sortType: 'popular',
      },
    });
  },

  initEventOptions: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await eventAPI.initOptions();
      set({
        eventOptions: response.initInfo,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      set({
        isLoading: false,
        error:
          error instanceof Error ? error.message : 'イベントオプションの読み込みに失敗しました',
      });
    }
  },

  setEventOptions: (options: EventOptions) => {
    set({ eventOptions: options, error: null });
  },

  clearError: () => {
    set({ error: null });
  },
}));
