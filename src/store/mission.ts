import { create } from 'zustand';

import type { MissionListType } from '@/types/mission';

interface MissionInfoState {
  missionInfo: MissionListType;
  setMissionInfo: (organizerInfo: MissionListType) => void;
}
export const useMissionState = create<MissionInfoState>()((set) => ({
  missionInfo: {
    missionId: 0,
    missionTitle: '',
    missionType: 0,
    iconType: 0,
    targetDays: 0,
    achievedDays: 0,
    todayAchievedFlg: false,
    origin: '',
  },

  setMissionInfo: (missionInfo: MissionListType) =>
    set(() => ({
      missionInfo,
    })),
}));
