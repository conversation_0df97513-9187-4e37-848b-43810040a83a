import type { OrganizerInfoBean } from '@/types/home-data';
import storage from '@/utils/obfuscate-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface PointState {
  homeCardInfo: OrganizerInfoBean;
  setHomeCardInfo: (homeCardInfo: OrganizerInfoBean) => void;
}

export const usePointStore = create<PointState>()(
  persist(
    (set, get) => ({
      homeCardInfo: {
        organizerId: 0,
        organizerName: '',
      },
      setHomeCardInfo: (homeCardInfo) => set({ homeCardInfo }),
    }),
    {
      name: 'point-storage',
      storage: createJSONStorage(() => storage),
    },
  ),
);
