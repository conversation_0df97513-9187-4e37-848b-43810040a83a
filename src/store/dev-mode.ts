import { nlog } from '@/utils/logger';
import storage from '@/utils/obfuscate-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const isProduction = process.env.NODE_ENV === 'production';

interface Log {
  index: number;
  message: string;
  datetime: string;
  path: string;
}

interface DevModeState {
  devMode: boolean;
  logs: Log[];
  setDevMode: (devMode: boolean) => void;
  log: (message: string) => void;
  clearLogs: () => void;
  clearDevModeStore: () => void;
}

export const useDevModeStore = create<DevModeState>()(
  persist(
    (set, get) => ({
      devMode: false,
      logs: [],
      setDevMode: (devMode: boolean) => set({ devMode }),
      log: (message: string) => {
        const { logs } = get();
        const newLog = {
          index: logs.length + 1,
          message,
          datetime: new Date().toLocaleString(),
          path: window.location.pathname,
        };
        //TODO:
        // if (!isProduction) {
        nlog(message);
        // }
        if (logs.length > 300) {
          set({
            logs: [newLog],
          });
        } else {
          set({
            logs: [...logs, newLog],
          });
        }
      },
      clearLogs: () => set({ logs: [] }),
      clearDevModeStore: () => set({ devMode: false, logs: [] }),
    }),
    {
      name: 'dev-mode-storage',
      storage: createJSONStorage(() => storage),
      partialize: (state: DevModeState) => ({
        devMode: state.devMode,
        logs: state.logs,
      }),
    },
  ),
);
