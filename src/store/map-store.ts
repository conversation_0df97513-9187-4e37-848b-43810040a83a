import type { PinRequest } from '@/types/map-types';
import { create } from 'zustand';

interface MapStore {
  isLoading: boolean;
  error: string | null;
  // 搜索参数
  searchParams: PinRequest;
  setGeolocation: (location: { lat: number; lng: number }) => void;
  setSearchParams: (params: PinRequest) => void;
  clearSearchParams: () => void;
  clearError: () => void;
}

export const useMapStore = create<MapStore>((set, get) => ({
  isLoading: false,
  error: null,
  // 初始化搜索参数
  searchParams: {
    eventTypeList: [],
    couponCategoryList: [],
  },

  setGeolocation: (location: { lat: number; lng: number }) => {
    const latitude = location.lat.toFixed(6) || undefined;
    const longitude = location.lng.toFixed(6) || undefined;
    set({
      isLoading: false,
      error: null,
      searchParams: {
        ...get().searchParams,
        latitude,
        longitude,
      },
    });
  },

  setSearchParams: (params: PinRequest) => {
    set({ searchParams: params });
  },



  clearSearchParams: () => {
    set({
      searchParams: {
        eventTypeList: [],
        couponCategoryList: [],
      },
    });
  },

  clearError: () => {
    set({ error: null });
  },
}));
