import { useNavigation } from '@/hooks/use-navigation';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import React from 'react';

const RouterLink = React.forwardRef<
  React.ElementRef<typeof Link>,
  React.ComponentPropsWithoutRef<typeof Link>
>(({ className, children, onClick, href, ...props }, ref) => {
  const { push } = useNavigation();
  // for analytics
  // for tracking
  // for redirect if external link

  // const router = useRouter();
  // const href = props.href;
  // const isExternal = isExternalLink(href as string);

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    console.log(`href:${href}`);
    if (onClick) {
      onClick?.(e);
      return;
    }
    if (href) {
      const url = href as string;
      if (url.startsWith('mailto:')) {
        window.location.href = url;
      } else {
        push(url);
      }
    }
  };

  return (
    <Link className={cn(className)} ref={ref} onClick={handleClick} href={href} {...props}>
      {children}
    </Link>
  );
});

export default RouterLink;

// function isExternalLink(href: string) {
//   return href.startsWith('http');
// }
