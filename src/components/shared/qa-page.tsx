'use client';

import { TextButton } from '@/components/shared/text-button';
import { ChevronDown } from 'lucide-react';

export interface QaData {
  id: string;
  title: string;
  content: string | React.ReactNode;
}

interface QaPageProps {
  data: QaData[];
}

export function QaPage({ data }: QaPageProps) {
  const handleScrollTo = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  return (
    <div className="bg-card pb-12 pt-4">
      <div className="flex flex-col gap-2 items-start px-6">
        {data.map((section) => (
          <TextButton key={section.id} onClick={() => handleScrollTo(section.id)}>
            <span>{section.title}</span>
            <ChevronDown className="w-5 h-5" />
          </TextButton>
        ))}
      </div>

      <div className="px-6">
        {data.map((section, index) => (
          <section key={section.id} id={section.id} className="py-6 border-b">
            <h2 className="text-2xl font-bold text-gray-700">{section.title}</h2>
            {typeof section.content === 'string' ? (
              <p className="text-gray-700 leading-relaxed text-base">{section.content}</p>
            ) : (
              section.content
            )}
          </section>
        ))}
      </div>
    </div>
  );
}
