import {
  DialogClose,
  DialogContent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/shared/dialog';
import type * as DialogPrimitive from '@radix-ui/react-dialog';
import React from 'react';
import { Button } from './button';
const DialogTipsContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & {
    ariaDescribedby?: string;
  }
>(({ className, children, title, ariaDescribedby, ...props }, ref) => (
  <DialogContent
    aria-describedby={ariaDescribedby}
    className="sm:max-w-[425px] w-[calc(100vw-48px)] rounded-3xl p-8"
  >
    <DialogHeader>
      <DialogTitle className="text-xl font-bold text-center">{title}</DialogTitle>
    </DialogHeader>
    {children}
    <DialogFooter className="mt-4">
      <DialogClose asChild>
        <Button>閉じる</Button>
      </DialogClose>
    </DialogFooter>
  </DialogContent>
));

DialogTipsContent.displayName = 'DialogTipsContent';

export default DialogTipsContent;
