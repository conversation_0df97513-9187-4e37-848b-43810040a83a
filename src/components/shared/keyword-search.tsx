'use client';
import { eventAPI } from '@/api/modules/event-api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useSearchHistoryStore } from '@/store/search-history-store';

import { ScrollArea, ScrollBar } from '@/components/shared/scroll-area';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useVirtualKeyboardEvent } from '@/hooks/use-virtual-keyboard-event';
import type { KeywordItem } from '@/types/event-types';
import { Clock, Search, Tent, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
/**
 * イベントキーワード検索コンポーネントのProps
 */
interface KeywordSearchProps {
  /** 初期キーワード */
  initialKeyword?: string;
  /** キーワード選択時のコールバック関数 */
  onKeywordSelect: (keyword: string) => void;
  /** 閉じるボタンのコールバック関数 */
  onClose: () => void;
  /** 機能タイプ（デフォルト: 'event'） */
  functionType?: string;
  functionTypeName?: string;
  /** キーワード検索候補取得関数 */
  keywordSearchFunction?: (searchTerm: string) => Promise<{ suggestions: KeywordItem[] } | null>;
  suggestionsIcon?: React.ReactNode;
  placeholder?: string;
  /** 詳細ページのURL（例: "/event/[eventId]"） */
  detailUrl?: string;
}

/**
 * イベントキーワード検索コンポーネント
 * 検索候補表示と検索履歴管理機能を提供
 */
export default function KeywordSearch({
  initialKeyword = '',
  onKeywordSelect,
  onClose,
  functionType = 'event',
  functionTypeName,
  keywordSearchFunction,
  placeholder = 'キーワードを入力',
  suggestionsIcon = <Search className="h-6 w-6 text-muted-foreground" />,
  detailUrl,
}: KeywordSearchProps) {
  const router = useRouter();
  // 現在の入力キーワード
  const [keyword, setKeyword] = useState(initialKeyword);
  // 検索候補リスト
  const [keywordList, setKeywordList] = useState<KeywordItem[]>([]);
  // ローディング状態
  const [isLoading, setIsLoading] = useState(false);
  // 防抖タイマー
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);
  const [scrollAreaHeight, setScrollAreaHeight] = useState<string>('');
  const safeArea = useSafeArea();
  const virtualKeyboardInfo = useVirtualKeyboardEvent();

  // 検索履歴ストアから必要な関数を取得
  const { getSearchHistory, addSearchHistory, removeSearchHistoryItem } = useSearchHistoryStore();
  // 現在の機能タイプの検索履歴を取得
  const searchHistory = getSearchHistory(functionType);

  /**
   * キーワード検索候補を取得する関数
   * @param searchTerm 検索語
   */
  const searchKeyword = async (searchTerm: string) => {
    // 空文字の場合は候補リストをクリア
    if (!searchTerm.trim()) {
      setKeywordList([]);
      return;
    }

    setIsLoading(true);
    try {
      // カスタム検索関数が渡されている場合はそれを使用、なければデフォルトのeventAPIを使用
      const searchFunction = keywordSearchFunction || eventAPI.keywordSearch;
      const data = await searchFunction(searchTerm);
      if (data?.suggestions && Array.isArray(data.suggestions)) {
        setKeywordList(data.suggestions);
      } else {
        setKeywordList([]);
      }
    } catch (error) {
      console.log('キーワード検索に失敗しました:', error);
      setKeywordList([]);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 防抖搜索函数
   */
  const debouncedSearch = useCallback(
    (searchTerm: string) => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      const timer = setTimeout(() => {
        searchKeyword(searchTerm);
      }, 300); // 300ms 防抖延迟

      setDebounceTimer(timer);
    },
    [debounceTimer],
  );

  /**
   * 入力値変更時の処理
   * @param e 入力イベント
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);
    // 使用防抖搜索
    debouncedSearch(value);
  };

  // 清理防抖计时器
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  useEffect(() => {
    const topBarHeight = 48;
    const searchBarHeight = 81;
    const scrollAreaTop = topBarHeight + searchBarHeight + safeArea.top;
    setScrollAreaHeight(`${virtualKeyboardInfo.currentHeight - scrollAreaTop}px`);
  }, [safeArea, virtualKeyboardInfo]);

  /**
   * キーボードイベント処理（Enterキーで検索実行）
   * @param e キーボードイベント
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && keyword.trim()) {
      handleKeywordSelect(keyword.trim());
    }
  };

  /**
   * キーワード選択時の処理
   * @param selectedKeyword 選択されたキーワード
   */
  const handleKeywordSelect = (selectedKeyword: string) => {
    // 検索履歴に追加
    addSearchHistory(functionType, selectedKeyword);
    // 親コンポーネントにコールバック
    onKeywordSelect(selectedKeyword);
  };

  /**
   * 検索履歴項目選択時の処理
   * @param historyKeyword 履歴のキーワード
   */
  const handleHistorySelect = (historyKeyword: string) => {
    setKeyword(historyKeyword);
    handleKeywordSelect(historyKeyword);
    onClose();
  };

  /**
   * 検索候補項目選択時の処理（詳細ページへの遷移）
   * @param item 選択された候補項目
   */
  const handleSuggestionSelect = (item: KeywordItem) => {
    if (detailUrl && item.id) {
      // 詳細ページに遷移
      const url = detailUrl.replace('[eventId]', item.id);
      router.push(url);
      onClose();
    } else {
      // 従来の検索処理
      setKeyword(item.text);
      handleKeywordSelect(item.text);
      onClose();
    }
  };

  /**
   * 検索履歴項目削除処理
   * @param historyKeyword 削除するキーワード
   * @param e マウスイベント
   */
  const handleRemoveHistory = (historyKeyword: string, e: React.MouseEvent) => {
    // イベントバブリングを防止
    e.stopPropagation();
    removeSearchHistoryItem(functionType, historyKeyword);
  };

  return (
    <div className="bg-card h-full flex flex-col">
      {/* 検索入力ボックス */}
      <div className="p-4 border-b border-border">
        <div className="relative">
          {/* 検索アイコン */}
          <Search className="absolute left-3 top-4 h-4 w-4 text-muted-foreground pointer-events-none z-10" />
          {/* 検索入力フィールド */}
          <Input
            value={keyword}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            autoFocus
            className="pl-10 pr-10 w-full h-12"
          />
          {/* クリアボタン（入力がある場合のみ表示） */}
          {keyword && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setKeyword('');
                setKeywordList([]);
              }}
              className=" absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            >
              <img src="/images/event/close-icon.svg" alt="" />
            </Button>
          )}
        </div>
      </div>

      {/* メインコンテンツエリア */}
      <ScrollArea style={{ height: scrollAreaHeight }} type="hover">
        {keyword.trim() ? (
          // 検索候補表示エリア
          <div className="p-4">
            {isLoading ? (
              // ローディング表示
              <div className="text-center py-8 text-muted-foreground">検索中...</div>
            ) : keywordList?.length > 0 ? (
              // 検索候補がある場合
              <div className="space-y-2">
                {/* 現在入力中のキーワード */}
                <div
                  onClick={() => {
                    handleKeywordSelect(keyword.trim());
                    onClose();
                  }}
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted cursor-pointer transition-colors"
                >
                  <div className="p-1 bg-background rounded-full">
                    <Search className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <span className="flex-1 ">{keyword.trim()}</span>
                </div>

                {/* 検索候補キーワードリスト */}
                {keywordList?.map((item, index) => (
                  <div
                    key={index}
                    onClick={() => handleSuggestionSelect(item)}
                    className="border-b flex items-center gap-3 p-3 hover:bg-muted cursor-pointer transition-colors"
                  >
                    <div className="p-1 bg-background rounded-full">{suggestionsIcon}</div>
                    <div className="flex-1">
                      <div className="font-medium line-clamp-1">{item.text}</div>
                      {functionTypeName && (
                        <div className="text-xs text-muted-foreground">{functionTypeName}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // 検索候補がない場合（現在の入力のみ表示）
              <div className="space-y-2">
                <div
                  onClick={() => {
                    handleKeywordSelect(keyword.trim());
                    onClose();
                  }}
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted cursor-pointer transition-colors"
                >
                  <Search className="h-6 w-6 text-muted-foreground" />
                  <span className="flex-1 line-clamp-1">{keyword.trim()}</span>
                </div>
              </div>
            )}
          </div>
        ) : (
          // 検索履歴表示エリア
          <div className="p-6">
            {searchHistory.length > 0 ? (
              <>
                {/* 検索履歴ヘッダー */}
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-muted-foreground">検索履歴</h3>
                </div>
                {/* 検索履歴リスト */}
                <div className="space-y-2">
                  {searchHistory.map(
                    (item: { keyword: string; timestamp: number }, index: number) => (
                      <div
                        key={index}
                        onClick={() => handleHistorySelect(item.keyword)}
                        className="flex items-center gap-3 p-3 pl-0 hover:bg-muted border-b cursor-pointer transition-colors group"
                      >
                        {/* 履歴アイコン */}
                        <div className="p-1 bg-background rounded-full">
                          <Clock className="h-5 w-5 text-muted-foreground" />
                        </div>
                        {/* 履歴キーワード */}
                        <span className="flex-1 line-clamp-1">{item.keyword}</span>
                        {/* 削除ボタン（ホバー時のみ表示） */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => handleRemoveHistory(item.keyword, e)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                        >
                          <img src="/images/event/close-icon.svg" alt="" />
                        </Button>
                      </div>
                    ),
                  )}
                </div>
              </>
            ) : (
              // 検索履歴がない場合
              <div className="text-center py-8 text-muted-foreground">検索履歴がありません</div>
            )}
          </div>
        )}
        <div style={{ height: safeArea.bottom }} />
        <ScrollBar orientation="vertical" />
      </ScrollArea>
    </div>
  );
}
