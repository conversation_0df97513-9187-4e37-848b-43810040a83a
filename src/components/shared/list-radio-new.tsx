import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { CircleHelp } from 'lucide-react';

interface Option {
  value: string;
  title: string;
  tip: string;
  help?: string;
  icon?: string;
}

interface ListRadioProps {
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  className?: string;
}

export default function ListRadio({ value, onChange, options, className }: ListRadioProps) {
  return (
    <RadioGroup
      value={value}
      onValueChange={onChange}
      className={cn('flex flex-col gap-2', className)} // 保留垂直方向，外层控制排列
    >
      {options.map((option) => (
        <label
          key={option.value}
          htmlFor={`radio-${option.value}`}
          className={cn(
            'flex items-center gap-4 p-4 border rounded-lg text-left w-full cursor-pointer transition-colors',
            'border-border',
            value === option.value && 'border-primary',
          )}
        >
          <RadioGroupItem
            value={option.value}
            id={`radio-${option.value}`}
            className="shrink-0 peer border-2 border-border rounded-full w-5 h-5"
          />
          <div className="flex flex-col">
            <span className="text-lg  flex items-center gap-2">
              {option.icon && <img className="h-8 w-8" alt="desc" src={option.icon} />}
              <div className="flex flex-col">
                <span className="text-lg  flex items-center gap-2">
                  {option.title}
                  {option.help && <CircleHelp size={22} className="text-muted-foreground" />}
                </span>
                <span className="text-sm text-muted-foreground">{option.tip}</span>
              </div>
            </span>
          </div>
        </label>
      ))}
    </RadioGroup>
  );
}
