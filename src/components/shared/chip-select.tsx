'use client';
import { Check } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Option {
  value: string;
  label: string;
}

interface ChipSelectProps {
  options: Option[];
  selectedValues?: string[];
  onChange?: (selectedValues: string[]) => void;
  placeholder?: string;
  className?: string;
  maxSelections?: number;
}

export const ChipSelect: React.FC<ChipSelectProps> = ({
  options,
  selectedValues = [],
  onChange,
  placeholder = '选择选项',
  className = '',
  maxSelections,
}) => {
  const [selected, setSelected] = useState<string[]>(selectedValues);

  // 同步外部 selectedValues 的变化
  useEffect(() => {
    setSelected(selectedValues);
  }, [selectedValues]);

  const handleToggle = (value: string) => {
    let newSelected: string[];

    if (selected.includes(value)) {
      // 取消选择
      newSelected = selected.filter((item) => item !== value);
    } else {
      // 添加选择
      if (maxSelections && selected.length >= maxSelections) {
        return; // 达到最大选择数量
      }
      newSelected = [...selected, value];
    }

    setSelected(newSelected);
    onChange?.(newSelected);
  };

  const isSelected = (value: string) => selected.includes(value);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex flex-wrap gap-3">
        {options.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => handleToggle(option.value)}
            className={`
              relative px-2 rounded-md border  transition-all duration-200 ease-in-out
              text-sm min-h-[37px] flex items-center justify-center text-muted-foreground font-normal
              ${
                isSelected(option.value)
                  ? 'border-primary bg-primary/5 text-primary  shadow-sm !font-bold'
                  : ' border-border  text-foreground hover:border-primary  '
              }
              ${
                maxSelections && selected.length >= maxSelections && !isSelected(option.value)
                  ? 'opacity-50 cursor-not-allowed'
                  : 'cursor-pointer'
              }
            `}
            disabled={
              !!(maxSelections && selected.length >= maxSelections && !isSelected(option.value))
            }
          >
            {isSelected(option.value) && <Check className="mr-2 h-4 w-4 flex-shrink-0" />}
            <span className="text-center leading-tight">{option.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};
