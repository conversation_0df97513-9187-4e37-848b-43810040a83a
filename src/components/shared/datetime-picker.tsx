'use client';
import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import React, { useEffect, useState } from 'react';
import CalendarIcon from './calendar-icon';

export interface DateTimePickerProps {
  className?: string;
  disabled?: boolean;
  onChange?: (value: string) => void;
  defaultValue?: string;
  value?: string;
  max?: string;
  min?: string;
  'aria-invalid'?: boolean;
}

export const DateTimePicker = React.forwardRef<HTMLInputElement, DateTimePickerProps>(
  (
    {
      className,
      disabled = false,
      onChange,
      defaultValue,
      value,
      max,
      min,
      'aria-invalid': ariaInvalid,
      ...props
    },
    ref,
  ) => {
    const [date, setDate] = useState<string>('');
    const [time, setTime] = useState<string>('');

    useEffect(() => {
      if (value) {
        const [d, t] = value.split(' ');
        setDate(d);
        setTime(t || '');
      } else {
        setDate('');
        setTime('');
      }
    }, [value]);

    useEffect(() => {
      if (date && time) {
        onChange?.(`${date} ${time}`);
      } else if (date) {
        onChange?.(`${date} 00:00`);
      } else if (time) {
        const defaultDate = defaultValue || new Date().toISOString().split('T')[0];
        onChange?.(`${defaultDate} ${time}`);
      } else {
        onChange?.('');
      }
    }, [date, time, onChange, defaultValue]);

    return (
      <div className={cn('flex items-center', className)}>
        <input
          type="hidden"
          name="datetime"
          ref={ref}
          readOnly
          value={date && time ? `${date} ${time}` : ''}
          {...props}
        />
        <DatePicker
          className="flex-1"
          disabled={disabled}
          value={date}
          onChange={setDate}
          max={max}
          min={min}
          aria-invalid={ariaInvalid}
        />
        <TimePicker
          className="ml-4 mr-4"
          disabled={disabled}
          value={time}
          onChange={setTime}
          aria-invalid={ariaInvalid}
        />
      </div>
    );
  },
);

DateTimePicker.displayName = 'DateTimePicker';

export interface DatePickerProps {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  max?: string;
  min?: string;
  'aria-invalid'?: boolean;
}

export const DatePicker = React.forwardRef<HTMLInputElement, DatePickerProps>(
  (
    { value, onChange, disabled, className, max, min, 'aria-invalid': ariaInvalid, ...props },
    ref,
  ) => {
    const [selectedDate, setSelectedDate] = useState<string | undefined>(value);

    useEffect(() => {
      setSelectedDate(value);
    }, [value]);

    const handleDateChange = (date: string) => {
      setSelectedDate(date);
      onChange?.(date);
    };

    return (
      <div
        className={cn(
          'flex items-center pl-4 relative h-12 border border-border rounded-lg',
          className,
          disabled && '!text-gray-60 !bg-muted cursor-not-allowed',
          ariaInvalid && '!border-destructive !bg-invalid-background',
        )}
      >
        <input
          type="date"
          value={selectedDate || ''}
          onChange={(e) => handleDateChange(e.target.value)}
          disabled={disabled}
          ref={ref}
          className={cn('absolute right-0 left-0 top-0 bottom-0 opacity-0 z-10 w-full')}
          max={max}
          min={min}
          {...props}
        />
        <div className={cn('text-md', disabled && 'text-text-secondary')}>
          {selectedDate ? formatDate(selectedDate, 'yyyy年M月d日(d)') : '日付を選択'}
        </div>
        <CalendarIcon
          className={cn('absolute right-3 top-1/2 -translate-y-1/2', disabled && 'text-gray-400')}
        />
      </div>
    );
  },
);

export interface TimePickerProps {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  'aria-invalid'?: boolean;
}

function formatTime(time: string | undefined) {
  if (!time) {
    return '--:--';
  }
  const [h, m] = time.split(':');
  return `${h}:${m}`;
}

export const TimePicker = React.forwardRef<HTMLInputElement, TimePickerProps>(
  ({ value = '', onChange, disabled, className, 'aria-invalid': ariaInvalid, ...props }, ref) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      onChange?.(newValue);
    };

    return (
      <div
        className={cn(
          'relative flex border-border items-center h-12 border rounded-lg',
          'w-[80px]',
          disabled && '!text-gray-60 !bg-muted cursor-not-allowed',
          ariaInvalid && '!border-destructive !bg-invalid-background',
          className,
        )}
      >
        <span className="text-md ml-4 pointer-events-none">{formatTime(value)}</span>
        <input
          type="time"
          ref={ref}
          value={value}
          onChange={handleChange}
          disabled={disabled}
          className="absolute opacity-0 w-full h-full inset-0 cursor-pointer"
          {...props}
        />
      </div>
    );
  },
);
