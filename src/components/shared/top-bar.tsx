'use client';

import { useRouter } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import { ChevronLeft, X } from 'lucide-react';

export default function TopBar({
  title,
  className,
  showBack,
  onBack,
  showRight,
  onRight,
}: {
  title: string;
  showBack?: boolean;
  className?: string;
  showRight?: boolean;
  onRight?: () => void;
  onBack?: () => void;
}) {
  const router = useRouter();
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };
  const handleRight = () => {
    if (onRight) {
      onRight();
    }
  };

  return (
    <>
      <div className="h-[env(safe-area-inset-top,20px)] mt-[48px]" />
      <div
        className={cn(
          'fixed top-0 pt-[env(safe-area-inset-top,20px)] left-0 right-0 z-50 bg-card shadow-sm',
          className,
        )}
      >
        <div className="flex h-[48px] items-center relative">
          {showBack && (
            <button
              type="button"
              onClick={handleBack}
              aria-label="back"
              className="absolute left-[15px]"
            >
              <ChevronLeft size={32} />
            </button>
          )}
          <div className="flex-1 text-lg font-bold text-center">{title}</div>
          {showRight && (
            <button
              type="button"
              onClick={handleRight}
              aria-label="close"
              className="absolute right-[15px]"
            >
              <X size={32} />
            </button>
          )}
        </div>
      </div>
    </>
  );
}
