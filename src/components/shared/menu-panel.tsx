'use client';

import RouterLink from '@/components/shared/router-link';
import SectionTitle from '@/components/shared/section-title';
import { cn } from '@/lib/utils';
import { ChevronRight } from 'lucide-react';

interface MenuItem {
  label: string;
  href: string;
  onClick?: () => void;
  subLabel?: React.ReactNode;
}

interface SettingsMenuPanelProps {
  title?: string;
  className?: string;
  menuItems: MenuItem[];
}

export default function MenuPanel({ title, menuItems, className }: SettingsMenuPanelProps) {
  return (
    <>
      {title && <SectionTitle>{title}</SectionTitle>}
      <div className={cn('bg-card mx-6 rounded-2xl px-6', className)}>
        {menuItems.map((item, index) => (
          <RouterLink
            key={index}
            href={item.href}
            onClick={item.onClick}
            className="flex items-center border-b border-text-muted last:border-b-0"
          >
            <div className="flex-1 items-center py-4">
              <div className="text-base text-left">{item.label}</div>
              {item.subLabel}
            </div>
            <ChevronRight className="w-5 h-5" />
          </RouterLink>
        ))}
      </div>
    </>
  );
}
