'use client';

import { mapAPI } from '@/api/modules/map-api';
import { Button } from '@/components/shared/button';
import { COLORS } from '@/const/colors';
import useCenterToast from '@/hooks/use-center-toast';
import type { FavoriteRequest } from '@/types/map-types';
import { StarIcon } from 'lucide-react';
import { useCallback, useState } from 'react';

// ==================== FavoriteToggle Component ====================
interface FavoriteToggleProps {
  targetId: string;
  favoriteType: string; // 1:イベント,2:クーポン,3:ウォーキングコース;
  initialIsFavorited?: boolean;
  onToggle?: (isFavorited: boolean) => void;
  className?: string;
  disabled?: boolean;
}

export const FavoriteToggle = ({
  targetId,
  favoriteType,
  initialIsFavorited = false,
  onToggle,
  className = '',
  disabled = false,
}: FavoriteToggleProps) => {
  const [isFavorited, setIsFavorited] = useState<boolean>(initialIsFavorited);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { toast } = useCenterToast();

  const handleToggleFavorite = useCallback(async () => {
    if (isLoading || disabled) return;

    setIsLoading(true);
    try {
      const favoriteRequest: FavoriteRequest = {
        targetId,
        favoriteType,
      };

      await mapAPI.toggleFavorite(favoriteRequest);
      const newFavoriteState = !isFavorited;
      setIsFavorited(newFavoriteState);
      onToggle?.(newFavoriteState);

      if (isFavorited) {
        toast({
          message: (
            <div>
              <div>お気に入りを</div>
              <div>解除しました</div>
            </div>
          ),
          duration: 1000,
          type: 'success',
        });
      } else {
        toast({
          message: (
            <div>
              <div>お気に入りに</div>
              <div>登録しました</div>
            </div>
          ),
          duration: 1000,
          type: 'success',
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        toast({
          message: error.message,
          duration: 1000,
          type: 'error',
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [targetId, favoriteType, isLoading, disabled, isFavorited, onToggle, toast]);

  return (
    <Button
      variant="icon"
      size="xs"
      className={`w-8 h-8 ${
        isFavorited ? 'border-[#EFAF00] border' : 'border border-gray-30'
      } ${className}`}
      onClick={handleToggleFavorite}
      disabled={isLoading || disabled}
    >
      <StarIcon
        size={16}
        className={isFavorited ? 'text-[#EFAF00]' : ''}
        stroke={isFavorited ? '#EFAF00' : COLORS.gray['30']}
        fill={isFavorited ? '#EFAF00' : 'none'}
      />
    </Button>
  );
};

// ==================== FavoriteBadge Component ====================
interface FavoriteBadgeProps {
  isFavorited: boolean;
  className?: string;
  size?: 'sm' | 'md';
  text?: string;
}

export const FavoriteBadge = ({
  isFavorited,
  className = '',
  size = 'md',
  text = 'お気に入り',
}: FavoriteBadgeProps) => {
  if (!isFavorited) return null;

  const sizeClasses = {
    sm: {
      container: 'h-[18px] text-[10px] px-1',
      icon: 12,
    },
    md: {
      container: 'h-[18px] text-[10px] px-1',
      icon: 12,
    },
  };

  const currentSize = sizeClasses[size];

  return (
    <div
      className={`font-normal flex items-center border-[1px] border-[#EFAF00] rounded ${currentSize.container} ${className}`}
    >
      <StarIcon className="text-[#EFAF00] mr-1" size={currentSize.icon} fill={'#EFAF00'} />
      <span>{text}</span>
    </div>
  );
};

// ==================== FavoriteIcon Component ====================
interface FavoriteIconProps {
  isFavorited: boolean;
  size?: number;
  className?: string;
}

export const FavoriteIcon = ({ isFavorited, size = 16, className = '' }: FavoriteIconProps) => {
  if (!isFavorited) return null;

  return (
    <StarIcon
      className={`text-[#EFAF00] flex-shrink-0 ${className}`}
      style={{ width: size, height: size }}
      size={size}
      fill={'#EFAF00'}
    />
  );
};
