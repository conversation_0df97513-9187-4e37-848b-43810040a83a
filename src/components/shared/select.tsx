import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/shared/drawer';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';

import { Label } from '@/components/ui/label';
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import { ChevronDown, Circle, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

export function Select({
  className,
  defaultValue,
  options,
  title,
  onSelect,
  onChange,
  placeholder,
  disabled,
  showClearButton,
}: {
  className?: string;
  defaultValue?: string;
  options: { value: string; name: string }[];
  title: string;
  disabled?: boolean;
  onSelect?: (value: string) => void;
  onChange?: (value: string) => void;
  placeholder?: string;
  showClearButton?: boolean;
}) {
  const [selectedItem, setSelectedItem] = useState<{ value: string; name: string }>();
  const [open, setOpen] = useState(false);
  const safeArea = useSafeArea();

  // defaultValue が変更されたら、selectedItem を更新
  useEffect(() => {
    const defaultItem = options.find((option) => option.value === defaultValue);
    setSelectedItem(defaultItem);
  }, [defaultValue, options]);

  const handleSelect = (item: { value: string; name: string }) => {
    setSelectedItem(item);
    onSelect?.(item.value);
    if (onSelect) {
      onSelect(item.value);
    } else {
      onChange?.(item.value);
    }
  };
  const handleClick = () => {
    setOpen(true);
  };
  return (
    <>
      <button
        type="button"
        className={cn(
          'flex h-12 w-full items-center justify-between rounded-lg border border-input bg-transparent px-3 py-2 text-base shadow-sm ring-offset-background placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50',
          className,
        )}
        disabled={disabled}
        onClick={handleClick}
      >
        <span className={!selectedItem?.value ? 'text-muted-foreground' : ''}>
          {options.find((option) => option.value === selectedItem?.value)?.name ||
            placeholder ||
            ''}
        </span>
        <ChevronDown className="h-6 w-6" />
      </button>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent className="height-auto-important">
          <DrawerHeader className="relative">
            <DrawerTitle>{title}</DrawerTitle>
            <DrawerDescription className="sr-only">HealthRecordCreateButton</DrawerDescription>
            <DrawerClose asChild>
              <button type="button" className="absolute right-5 top-3">
                <X size={24} />
              </button>
            </DrawerClose>
          </DrawerHeader>
          <RadioGroup
            style={{ paddingBottom: `${safeArea.bottom}px` }}
            value={selectedItem?.value}
            onValueChange={(value) => {
              const item = options.find((option) => option.value === value);
              if (item) {
                handleSelect(item);
                setOpen(false);
              }
            }}
          >
            {options.map((item) => (
              <div key={item.value} className="flex items-center h-14 px-6">
                <RadioGroupItem value={item.value} id={`select-${item.value}`} />
                <Label
                  htmlFor={`select-${item.value}`}
                  className="text-base ml-4 flex-1 flex items-center h-16"
                >
                  {item.name}
                </Label>
              </div>
            ))}
            {showClearButton && (
              <DrawerClose asChild onClick={() => handleSelect({ value: '', name: '' })}>
                <div className="flex justify-start   px-6">
                  <span className="text-primary font-bold">選択を解除</span>
                </div>
              </DrawerClose>
            )}
          </RadioGroup>
        </DrawerContent>
      </Drawer>
    </>
  );
}

const RadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return <RadioGroupPrimitive.Root className={cn('grid', className)} {...props} ref={ref} />;
});
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName;

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        'aspect-square h-5 w-5 rounded-full border border-text-secondary text-text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 relative',
        className,
      )}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="flex h-5 w-5 bg-primary rounded-full items-center justify-center absolute top-[-1px] left-[-1px]">
        <Circle className="h-3 w-3 fill-card" strokeWidth="0" />
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  );
});
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

export { RadioGroup, RadioGroupItem };
