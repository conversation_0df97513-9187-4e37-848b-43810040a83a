import { fireEvent, screen } from '@testing-library/dom';
import { render } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { Switch } from '../switch';

describe('Switch', () => {
  it('renders correctly with default props', () => {
    render(<Switch />);
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toBeInTheDocument();
    expect(switchElement).toHaveAttribute('data-state', 'unchecked');
  });

  it('renders correctly when checked', () => {
    render(<Switch checked />);
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toHaveAttribute('data-state', 'checked');
  });

  it('handles checked state changes', () => {
    const handleCheckedChange = vi.fn();
    render(<Switch onCheckedChange={handleCheckedChange} />);

    const switchElement = screen.getByRole('switch');
    fireEvent.click(switchElement);

    expect(handleCheckedChange).toHaveBeenCalledWith(true);
  });

  it('handles multiple checked state changes', () => {
    const handleCheckedChange = vi.fn();
    render(<Switch onCheckedChange={handleCheckedChange} />);

    const switchElement = screen.getByRole('switch');

    // First click - turn on
    fireEvent.click(switchElement);
    expect(handleCheckedChange).toHaveBeenCalledWith(true);

    // Second click - turn off
    fireEvent.click(switchElement);
    expect(handleCheckedChange).toHaveBeenCalledWith(false);

    // Verify total number of calls
    expect(handleCheckedChange).toHaveBeenCalledTimes(2);
  });

  it('does not trigger onCheckedChange when disabled', () => {
    const handleCheckedChange = vi.fn();
    render(<Switch disabled onCheckedChange={handleCheckedChange} />);

    const switchElement = screen.getByRole('switch');
    fireEvent.click(switchElement);

    expect(handleCheckedChange).not.toHaveBeenCalled();
  });

  it('can be disabled', () => {
    render(<Switch disabled />);
    const switchElement = screen.getByRole('switch');

    expect(switchElement).toBeDisabled();
    expect(switchElement).toHaveClass('disabled:cursor-not-allowed');
  });

  it('applies custom className', () => {
    render(<Switch className="custom-class" />);
    const switchElement = screen.getByRole('switch');

    expect(switchElement).toHaveClass('custom-class');
  });

  describe('Snapshot Tests', () => {
    it('matches snapshot for default state', () => {
      const { container } = render(<Switch />);
      expect(container).toMatchSnapshot();
    });

    it('matches snapshot for checked state', () => {
      const { container } = render(<Switch checked />);
      expect(container).toMatchSnapshot();
    });

    it('matches snapshot for disabled state', () => {
      const { container } = render(<Switch disabled />);
      expect(container).toMatchSnapshot();
    });

    it('matches snapshot with custom className', () => {
      const { container } = render(<Switch className="custom-class" />);
      expect(container).toMatchSnapshot();
    });
  });
});
