import { fireEvent, screen } from '@testing-library/dom';
import { render } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { Button } from '../button';

describe('Button', () => {
  it('renders correctly with default props', () => {
    render(<Button>Click me</Button>);
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-primary');
  });

  it('renders with different variants', () => {
    const { rerender } = render(<Button variant="destructive">Destructive</Button>);
    expect(screen.getByRole('button')).toHaveClass('border-destructive-foreground');

    rerender(<Button variant="outline">Outline</Button>);
    expect(screen.getByRole('button')).toHaveClass('border-primary');

    rerender(<Button variant="secondary">Secondary</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-secondary');

    rerender(<Button variant="muted">Muted</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-muted');
  });

  it('renders with different sizes', () => {
    const { rerender } = render(<Button size="default">Default Size</Button>);
    expect(screen.getByRole('button')).toHaveClass('h-12');

    rerender(<Button size="sm">Small Size</Button>);
    expect(screen.getByRole('button')).toHaveClass('h-10');
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('can be disabled', () => {
    render(<Button disabled>Disabled</Button>);
    const button = screen.getByRole('button');

    expect(button).toBeDisabled();
    expect(button).toHaveClass('disabled:opacity-50');
  });

  it('applies custom className', () => {
    render(<Button className="custom-class">Custom</Button>);
    expect(screen.getByRole('button')).toHaveClass('custom-class');
  });

  describe('Snapshot Tests', () => {
    it('matches snapshot for default button', () => {
      const { container } = render(<Button>Default Button</Button>);
      expect(container).toMatchSnapshot();
    });

    it('matches snapshot for all variants', () => {
      const variants = ['default', 'destructive', 'outline', 'secondary', 'muted'] as const;
      for (const variant of variants) {
        const { container } = render(<Button variant={variant}>{variant} Button</Button>);
        expect(container).toMatchSnapshot();
      }
    });

    it('matches snapshot for all sizes', () => {
      const sizes = ['default', 'sm'] as const;
      for (const size of sizes) {
        const { container } = render(<Button size={size}>{size} Size Button</Button>);
        expect(container).toMatchSnapshot();
      }
    });

    it('matches snapshot for disabled state', () => {
      const { container } = render(<Button disabled>Disabled Button</Button>);
      expect(container).toMatchSnapshot();
    });

    it('matches snapshot with custom className', () => {
      const { container } = render(<Button className="custom-class">Custom Button</Button>);
      expect(container).toMatchSnapshot();
    });
  });
});
