// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Switch > Snapshot Tests > matches snapshot for checked state 1`] = `
<div>
  <button
    aria-checked="true"
    class="peer relative inline-flex h-8 w-12 shrink-0 cursor-pointer items-center rounded-full border-4 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
    data-state="checked"
    role="switch"
    type="button"
    value="on"
  >
    <span
      class="pointer-events-none block h-6 w-6 rounded-full bg-card shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
      data-state="checked"
    />
    <div
      class="absolute left-[1px] top-[7px] w-[11px] h-[10px]"
    >
      <svg
        fill="none"
        height="10"
        viewBox="0 0 11 9"
        width="11"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1 3.625L4.20792 7L10 1"
          stroke="white"
          stroke-width="2"
        />
      </svg>
    </div>
  </button>
</div>
`;

exports[`Switch > Snapshot Tests > matches snapshot for default state 1`] = `
<div>
  <button
    aria-checked="false"
    class="peer relative inline-flex h-8 w-12 shrink-0 cursor-pointer items-center rounded-full border-4 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
    data-state="unchecked"
    role="switch"
    type="button"
    value="on"
  >
    <span
      class="pointer-events-none block h-6 w-6 rounded-full bg-card shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
      data-state="unchecked"
    />
    <div
      class="absolute left-[1px] top-[7px] w-[11px] h-[10px]"
    >
      <svg
        fill="none"
        height="10"
        viewBox="0 0 11 9"
        width="11"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1 3.625L4.20792 7L10 1"
          stroke="white"
          stroke-width="2"
        />
      </svg>
    </div>
  </button>
</div>
`;

exports[`Switch > Snapshot Tests > matches snapshot for disabled state 1`] = `
<div>
  <button
    aria-checked="false"
    class="peer relative inline-flex h-8 w-12 shrink-0 cursor-pointer items-center rounded-full border-4 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
    data-disabled=""
    data-state="unchecked"
    disabled=""
    role="switch"
    type="button"
    value="on"
  >
    <span
      class="pointer-events-none block h-6 w-6 rounded-full bg-card shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
      data-disabled=""
      data-state="unchecked"
    />
    <div
      class="absolute left-[1px] top-[7px] w-[11px] h-[10px]"
    >
      <svg
        fill="none"
        height="10"
        viewBox="0 0 11 9"
        width="11"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1 3.625L4.20792 7L10 1"
          stroke="white"
          stroke-width="2"
        />
      </svg>
    </div>
  </button>
</div>
`;

exports[`Switch > Snapshot Tests > matches snapshot with custom className 1`] = `
<div>
  <button
    aria-checked="false"
    class="peer relative inline-flex h-8 w-12 shrink-0 cursor-pointer items-center rounded-full border-4 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input custom-class"
    data-state="unchecked"
    role="switch"
    type="button"
    value="on"
  >
    <span
      class="pointer-events-none block h-6 w-6 rounded-full bg-card shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
      data-state="unchecked"
    />
    <div
      class="absolute left-[1px] top-[7px] w-[11px] h-[10px]"
    >
      <svg
        fill="none"
        height="10"
        viewBox="0 0 11 9"
        width="11"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1 3.625L4.20792 7L10 1"
          stroke="white"
          stroke-width="2"
        />
      </svg>
    </div>
  </button>
</div>
`;
