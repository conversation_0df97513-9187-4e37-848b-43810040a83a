// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Button > Snapshot Tests > matches snapshot for all sizes 1`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground h-12 px-4 text-base"
  >
    default
     Size Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for all sizes 2`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground h-10 text-[15px]"
  >
    sm
     Size Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for all variants 1`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground h-12 px-4 text-base"
  >
    default
     Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for all variants 2`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-destructive-foreground bg-transparent text-destructive-foreground h-12 px-4 text-base"
  >
    destructive
     Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for all variants 3`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-primary bg-transparent text-primary h-12 px-4 text-base"
  >
    outline
     Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for all variants 4`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground h-12 px-4 text-base"
  >
    secondary
     Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for all variants 5`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-muted text-muted-foreground h-12 px-4 text-base"
  >
    muted
     Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for default button 1`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground h-12 px-4 text-base"
  >
    Default Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot for disabled state 1`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground h-12 px-4 text-base"
    disabled=""
  >
    Disabled Button
  </button>
</div>
`;

exports[`Button > Snapshot Tests > matches snapshot with custom className 1`] = `
<div>
  <button
    class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground h-12 px-4 text-base custom-class"
  >
    Custom Button
  </button>
</div>
`;
