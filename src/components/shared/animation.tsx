import { useDeviceDetect } from '@/hooks/use-device-detect';
import { Slot } from '@radix-ui/react-slot';
import { type AnimationSequence, useAnimate, useMotionValue } from 'motion/react';
import { useEffect } from 'react';

interface SlideFromBottomProps {
  children: React.ReactElement;
  show: boolean;
}

export function SlideFromBottom({ children, show }: SlideFromBottomProps) {
  const [scope, animate] = useAnimate();
  const y = useMotionValue('100%');

  useEffect(() => {
    if (show) {
      animate(scope.current, { y: 0, opacity: 1 }, { duration: 0.2, ease: 'easeOut' });
    } else {
      animate(scope.current, { y: '100%', opacity: 0 }, { duration: 0.2, ease: 'easeIn' });
    }
  }, [show, animate, scope]);

  return (
    <Slot ref={scope} style={{ transform: `translateY(${y.get()})` }}>
      {children}
    </Slot>
  );
}

interface SlideFromRightProps {
  children: React.ReactElement;
  show: boolean;
}

export function SlideFromRight({ children, show }: SlideFromRightProps) {
  const [scope, animate] = useAnimate();
  const x = useMotionValue(64);

  useEffect(() => {
    if (show) {
      animate(scope.current, { x: 0, opacity: 1 }, { duration: 0.2, ease: 'easeOut' });
    } else {
      animate(scope.current, { x: 64, opacity: 0 }, { duration: 0.2, ease: 'easeIn' });
    }
  }, [show, animate, scope]);

  return (
    <Slot ref={scope} style={{ transform: `translateX(${x.get()}px)` }}>
      {children}
    </Slot>
  );
}

interface HeartAnimationProps {
  children: React.ReactElement;
}

export function HeartAnimation({ children }: HeartAnimationProps) {
  const [scope, animate] = useAnimate();
  const { isMobile } = useDeviceDetect();

  const handleTap = () => {
    const sequence: AnimationSequence = [
      [scope.current, { scale: 0.8 }, { duration: 0.1 }],
      [scope.current, { scale: 1 }, { type: 'spring', stiffness: 500 }],
    ];
    animate(sequence);
  };

  if (!isMobile) {
    return (
      <Slot ref={scope} onClick={handleTap}>
        {children}
      </Slot>
    );
  }

  return (
    <Slot ref={scope} onTouchEnd={handleTap}>
      {children}
    </Slot>
  );
}

export function FadeInOut({ children, show }: SlideFromBottomProps) {
  const [scope, animate] = useAnimate();
  const y = useMotionValue('20%');

  useEffect(() => {
    if (show) {
      animate(scope.current, { y: 0, opacity: 1 }, { duration: 0.15, ease: 'easeOut' });
    } else {
      animate(scope.current, { y: '20%', opacity: 0 }, { duration: 0.15, ease: 'easeIn' });
    }
  }, [show, animate, scope]);

  return (
    <Slot ref={scope} style={{ transform: `translateY(${y.get()})` }}>
      {children}
    </Slot>
  );
}
