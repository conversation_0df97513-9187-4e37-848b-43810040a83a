import { cn } from '@/lib/utils';

export default function YellIcon({ className }: { className?: string }) {
  return (
    <div className={cn('w-6 h-6 [&>svg]:w-full [&>svg]:h-full', className)}>
      <svg
        width="24"
        height="24"
        viewBox="0 0 17 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M14.3145 10.3755L13.4102 10.7642L7.03418 13.4966L6.20117 15.1636L5.78711 15.9908L1.65039 11.8541L4.14453 10.607L6.87793 4.23199L7.26562 3.32672L14.3145 10.3755ZM5.27539 11.356L5.17773 11.5806L3.90234 12.2193L5.42188 13.7388L5.95117 12.6812L6.06055 12.4634L6.28516 12.3658L11.9795 9.92535L7.71484 5.66168L5.27539 11.356ZM15.9902 7.18317L12.7695 8.04547L12.4238 6.75739L15.6445 5.8941L15.9902 7.18317ZM14.5615 4.02301L12.2041 6.38043L11.2607 5.43707L13.6182 3.07965L14.5615 4.02301ZM11.6299 1.87946L10.7676 5.10016L9.47852 4.75446L10.3418 1.53375L11.6299 1.87946Z"
          fill="white"
        />
      </svg>
    </div>
  );
}
