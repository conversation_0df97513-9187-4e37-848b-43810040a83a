'use client';

import { format, setMonth, setYear } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import * as React from 'react';
import type { DateRange } from 'react-day-picker';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface DateRangePickerProps {
  className?: string;
  value?: DateRange;
  onChange?: (date: DateRange | undefined) => void;
}

// 直接在组件内定义中文月份和星期
const MONTHS = [
  '一月',
  '二月',
  '三月',
  '四月',
  '五月',
  '六月',
  '七月',
  '八月',
  '九月',
  '十月',
  '十一月',
  '十二月',
];
// const WEEKDAYS = ["日", "一", "二", "三", "四", "五", "六"];
const WEEKDAYS = ['日', '月', '火', '水', '木', '金', '土'];
export function DateRangeSelect({ className, value, onChange }: DateRangePickerProps) {
  // Add custom CSS for calendar row alignment
  React.useEffect(() => {
    // Create a style element
    const style = document.createElement('style');
    // Add CSS rules for calendar row alignment
    style.innerHTML = `
      /* First row (right-aligned) */
      .rdp-tbody tr:first-child {
        justify-content: flex-end;
      }
      
      /* Last row (left-aligned) */
      .rdp-tbody tr:last-child {
        justify-content: flex-start;
      }
    `;
    // Append the style element to the document head
    document.head.appendChild(style);

    // Clean up on component unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const [month, setCurrentMonth] = React.useState<Date>(value?.from || new Date());

  // 生成年份选项（前后 10 年）
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  // 生成月份选项
  const months = Array.from({ length: 12 }, (_, i) => i);

  const handleYearChange = (yearStr: string) => {
    const year = Number.parseInt(yearStr);
    const newDate = setYear(month, year);
    setCurrentMonth(newDate);
  };

  const handleMonthChange = (monthStr: string) => {
    const monthIndex = Number.parseInt(monthStr);
    const newDate = setMonth(month, monthIndex);
    setCurrentMonth(newDate);
  };

  // 自定义格式化月份和星期显示
  const formatters = {
    formatCaption: (date: Date) => MONTHS[date.getMonth()],
    formatWeekdayName: (date: Date) => WEEKDAYS[date.getDay()],
  };

  return (
    <div className={cn('grid gap-2', className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            className={cn(
              'w-[260px] justify-start text-left font-normal',
              !value && 'text-muted-foreground',
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {value?.from ? (
              value.to ? (
                <>
                  {format(value.from, 'yyyy/MM/dd')} - {format(value.to, 'yyyy/MM/dd')}
                </>
              ) : (
                format(value.from, 'yyyy/MM/dd')
              )
            ) : (
              <span>日付範囲を選択</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          <div className="flex items-center gap-2 p-3 border-b">
            <Select value={month.getFullYear().toString()} onValueChange={handleYearChange}>
              <SelectTrigger className="w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {years.map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}年
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={month.getMonth().toString()} onValueChange={handleMonthChange}>
              <SelectTrigger className="w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {months.map((monthIndex) => (
                  <SelectItem key={monthIndex} value={monthIndex.toString()}>
                    {MONTHS[monthIndex]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Calendar
            initialFocus
            mode="range"
            defaultMonth={month}
            month={month}
            selected={value}
            onSelect={onChange}
            numberOfMonths={2}
            showOutsideDays={false}
            weekStartsOn={1}
            formatters={formatters}
            classNames={{
              caption: 'flex justify-center pt-1 relative items-center',
              caption_label: 'text-sm font-medium',
              nav: 'space-x-1 flex items-center',
              nav_button: cn('h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'),
              nav_button_previous: 'absolute left-1',
              nav_button_next: 'absolute right-1',
              table: 'w-full border-collapse space-y-1',
              head_row: 'flex',
              head_cell:
                'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] flex justify-center items-center',
              row: 'flex w-full mt-2',

              day: cn('h-8 w-8 p-0 font-normal aria-selected:opacity-100 text-center'),
              day_range_end: 'day-range-end',
              day_selected:
                'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
              day_today: 'bg-accent text-accent-foreground',
              day_outside: 'text-muted-foreground opacity-50',
              day_disabled: 'text-muted-foreground opacity-50',
              day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',
              day_hidden: 'invisible',
            }}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
