import { cn } from '@/lib/utils';
import { CircleAlert } from 'lucide-react';
import { FormMessage, useFormField } from '../ui/form';

export function FormError({ className }: { className?: string }) {
  const { error } = useFormField();

  if (!error) {
    return null;
  }

  return (
    <div className="flex">
      <CircleAlert className="w-6 h-6 fill-destructive text-card flex-shrink-0 mt-1 ml-[-4px]" />
      <FormMessage className={cn('text-destructive', className)} />
    </div>
  );
}
