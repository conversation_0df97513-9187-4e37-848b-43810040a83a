'use client';

import type { Connection } from '@/types/map';
import { useMap } from '@vis.gl/react-google-maps';
import { useTheme } from 'next-themes';
import { useEffect } from 'react';
import { DEFAULT_THEME, THEME_COLOR } from './color';

export default function Line({
  connections,
}: {
  connections: Connection[];
}) {
  const map = useMap();
  const { theme } = useTheme();
  const strokeColor = THEME_COLOR[theme ?? DEFAULT_THEME].line;
  // 接続線の管理
  useEffect(() => {
    if (!map || !connections.length) return;

    const polylines: google.maps.Polyline[] = [];

    for (const connection of connections) {
      const strokeWeight = 2;

      const polyline = new google.maps.Polyline({
        path: connection.path,
        strokeColor: strokeColor,
        strokeWeight: strokeWeight,
        map: map,
      });

      polylines.push(polyline);
    }

    return () => {
      for (const polyline of polylines) {
        polyline.setMap(null);
      }
    };
  }, [map, connections, strokeColor]);

  return null;
}
