import type { Marker } from '@/types/map';
import { useCallback } from 'react';

import HealthMarkerComponent from './marker';

export type MergedMarkersProps = {
  markers: Marker[];
  onMarkerClick: (marker: Marker) => void;
};

export default function ClusteredMarkers({
  markers: healthMarkers,
  onMarkerClick,
}: MergedMarkersProps) {
  const handleMarkerClick = useCallback(
    (healthMarker: Marker) => {
      onMarkerClick(healthMarker);
    },
    [onMarkerClick],
  );

  return (
    <>
      {healthMarkers.map((healthMarker) => (
        <HealthMarkerComponent
          key={healthMarker.id}
          marker={healthMarker}
          onClick={handleMarkerClick}
        />
      ))}
    </>
  );
}
