import type { Marker } from '@/types/map';
import {
  type Cluster,
  type ClusterStats,
  type Marker as GoogleMapsMarker,
  MarkerClusterer,
} from '@googlemaps/markerclusterer';
import { useMap } from '@vis.gl/react-google-maps';
import { type ReactNode, createElement, useCallback, useEffect, useMemo, useState } from 'react';
import { createRoot } from 'react-dom/client';
import HealthMarkerComponent from './marker';

export type ClusteredMarkersProps = {
  markers: Marker[];
  onMarkerClick: (marker: Marker) => void;
};

export default function ClusteredMarkers({
  markers: healthMarkers,
  onMarkerClick,
}: ClusteredMarkersProps) {
  const [markers, setMarkers] = useState<{ [key: string]: GoogleMapsMarker }>({});
  const [addedMarkers, setAddedMarkers] = useState<{ [key: string]: GoogleMapsMarker }>({});
  const map = useMap();
  useEffect(() => {
    if (!map) return;
    let clusterer: MarkerClusterer | null = null;
    if (Object.values(markers).length > 0) {
      setAddedMarkers(markers);
      clusterer = new MarkerClusterer({
        map,
        markers: Object.values(markers),
        renderer: {
          render: (cluster, stats, map) => {
            return new google.maps.marker.AdvancedMarkerElement({
              position: cluster.position,
              content: createNodeFromReactNode(
                <div className="bg-card w-8 h-8 text-xl shadow-lg font-bold text-primary flex rounded-full items-center justify-center">
                  {cluster.count}
                </div>,
                'cluster-marker-container',
              ),
            });
          },
        },
      });
    }
    return () => {
      if (clusterer && Object.values(addedMarkers).length > 0) {
        clusterer.clearMarkers();
        setAddedMarkers({});
      }
    };
  }, [map, markers, addedMarkers]);

  const setMarkerRef = useCallback((marker: GoogleMapsMarker | null, key: string) => {
    if (marker) {
      setMarkers((markers) => {
        return { ...markers, [key]: marker };
      });
    }
  }, []);

  const handleMarkerClick = useCallback(
    (healthMarker: Marker) => {
      onMarkerClick(healthMarker);
    },
    [onMarkerClick],
  );

  return (
    <>
      {healthMarkers.map((healthMarker) => (
        <HealthMarkerComponent
          key={healthMarker.id}
          marker={healthMarker}
          onClick={handleMarkerClick}
          setMarkerRef={setMarkerRef}
        />
      ))}
    </>
  );
}

function createNodeFromReactNode(node: ReactNode, className: string): Node {
  const clusterElement = document.createElement('div');
  clusterElement.className = className;
  const root = createRoot(clusterElement);
  root.render(node);
  return clusterElement;
}
