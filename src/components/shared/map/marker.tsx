import { getMarkerPosition } from '@/hooks/use-map';
import { type Marker, MarkerType } from '@/types/map';
import type { Marker as GoogleMapsMarker } from '@googlemaps/markerclusterer';
import { AdvancedMarker } from '@vis.gl/react-google-maps';
import { motion } from 'motion/react';
import { useTheme } from 'next-themes';
import { useCallback } from 'react';
import { THEME_COLOR, Theme } from './color';

export type HealthMarkerProps = {
  marker: Marker;
  onClick: (healthMarker: Marker) => void;
  setMarkerRef?: (marker: GoogleMapsMarker | null, key: string) => void;
};

export default function HealthMarkerComponent({
  marker: healthMarker,
  onClick,
  setMarkerRef,
}: HealthMarkerProps) {
  const { theme } = useTheme();
  const handleClick = useCallback(() => {
    onClick(healthMarker);
  }, [onClick, healthMarker]);

  const ref = useCallback(
    (marker: google.maps.marker.AdvancedMarkerElement) =>
      setMarkerRef?.(marker, String(healthMarker.id)),
    [setMarkerRef, healthMarker.id],
  );

  const size = healthMarker.isCurrent ? 48 : 36;

  return (
    <AdvancedMarker position={getMarkerPosition(healthMarker)} onClick={handleClick} ref={ref}>
      {healthMarker.type === MarkerType.WALKING_COURSE && (
        <WalkingCourseMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
      {healthMarker.type === MarkerType.EVENT && (
        <EventMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
      {healthMarker.type === MarkerType.COUPON && (
        <CouponMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
      {healthMarker.type === MarkerType.PHOTO && (
        <PhotoMarker size={size} isFav={healthMarker.isFav} theme={theme} />
      )}
    </AdvancedMarker>
  );
}

interface MarkerProps {
  theme?: string;
  size?: number;
  isFav?: boolean;
}

function WalkingCourseMarker({ theme = Theme.BLUE, size = 36, isFav }: MarkerProps) {
  const fill = THEME_COLOR[theme].walkingCourse;
  return (
    <MarkerComponent size={size} fill={fill} isFav={isFav}>
      <path
        d="M30.0523 25.9521H13.9487C13.431 25.9521 13.0112 26.375 13.0112 26.8965V29.6756C13.0112 30.1972 13.431 30.62 13.9487 30.62H30.0523C30.5701 30.62 30.9898 30.1972 30.9898 29.6756V26.8965C30.9898 26.375 30.5701 25.9521 30.0523 25.9521Z"
        fill="#FF945F"
      />
      <path
        d="M18.2882 22.0449H25.5793C26.3775 22.0449 27.0204 22.6979 27.0204 23.4965V24.7593H16.8472V23.4965C16.8472 22.6925 17.4954 22.0449 18.2882 22.0449Z"
        fill="#FF945F"
      />
      <path
        d="M24.6569 14.3554C24.8069 14.0047 24.8926 13.6161 24.8926 13.206C24.8926 11.5979 23.5962 10.292 21.9998 10.292C20.4034 10.292 19.1069 11.5979 19.1069 13.206C19.1069 13.6161 19.1926 14.0047 19.3426 14.3554C19.3426 14.3554 20.5855 17.3018 20.5855 23.2378H23.2105C23.2105 23.2378 23.0444 18.5214 24.6569 14.3554Z"
        fill="#FF945F"
      />
    </MarkerComponent>
  );
}

function EventMarker({ theme = Theme.BLUE, size = 36 }: MarkerProps) {
  const fill = THEME_COLOR[theme].event;
  return (
    <MarkerComponent size={size} fill={fill}>
      <path
        d="M30.6729 20.2812C30.6729 21.8894 29.3765 23.1953 27.7801 23.1953C26.1836 23.1953 24.8872 21.8894 24.8872 20.2812"
        fill="#15B47F"
      />
      <path
        d="M19.1016 20.2812C19.1016 21.8894 17.8052 23.1953 16.2088 23.1953C14.6123 23.1953 13.3159 21.8894 13.3159 20.2812"
        fill="#15B47F"
      />
      <path
        d="M24.8873 20.2812C24.8873 21.8894 23.5908 23.1953 21.9944 23.1953C20.398 23.1953 19.1016 21.8894 19.1016 20.2812"
        fill="#15B47F"
      />
      <path
        d="M21.9999 14.6797C19.0909 18.8726 13.3267 20.2757 13.3267 20.2757H30.6784C30.6784 20.2757 24.9142 18.8726 22.0052 14.6797H21.9999Z"
        fill="#15B47F"
      />
      <path d="M22 14.6796V9.59082L27.4536 12.1325L22 14.6796Z" fill="#15B47F" />
      <path
        d="M21.9307 25.1808C19.7718 25.1808 17.7575 24.5548 16.0485 23.4863L13.8789 31.5485H29.9825L27.8128 23.4863C26.0985 24.5548 24.0896 25.1808 21.9307 25.1808Z"
        fill="#15B47F"
      />
    </MarkerComponent>
  );
}

function CouponMarker({ theme = Theme.BLUE, size = 36 }: MarkerProps) {
  const fill = THEME_COLOR[theme].coupon;
  return (
    <MarkerComponent size={size} fill={fill}>
      <path
        d="M20.4252 15.1572C20.4252 16.4362 19.5252 17.5046 18.3305 17.7529V18.3788H17.2591V17.7529C16.0645 17.5046 15.1645 16.4362 15.1645 15.1572H11.3823V28.7452H15.1645C15.1645 27.4663 16.0645 26.3978 17.2591 26.1496V25.5236H18.3305V26.1496C19.5252 26.3978 20.4252 27.4663 20.4252 28.7452H32.618V15.1572H20.4252ZM18.3305 24.5037H17.2591V22.4585H18.3305V24.5037ZM18.3305 21.4386H17.2591V19.3934H18.3305V21.4386Z"
        fill="#FC7DBF"
      />
    </MarkerComponent>
  );
}

function PhotoMarker({ theme = Theme.BLUE, size = 36 }: MarkerProps) {
  const fill = THEME_COLOR[theme].photo;
  return (
    <MarkerComponent size={size} fill={fill}>
      <path
        d="M21.855 26.4326C23.8991 26.4326 25.5561 24.7633 25.5561 22.7041C25.5561 20.6449 23.8991 18.9756 21.855 18.9756C19.8109 18.9756 18.1538 20.6449 18.1538 22.7041C18.1538 24.7633 19.8109 26.4326 21.855 26.4326Z"
        fill="#4457D1"
      />
      <path
        d="M28.379 15.9592L27.2274 12.1768H16.4828L15.3312 15.9592H11.6729V29.724H32.0373V15.9592H28.379ZM21.8551 27.7653C19.0805 27.7653 16.8256 25.4991 16.8256 22.6986C16.8256 19.8982 19.0752 17.6319 21.8551 17.6319C24.635 17.6319 26.8846 19.8982 26.8846 22.6986C26.8846 25.4991 24.635 27.7653 21.8551 27.7653ZM30.093 19.5475H27.8702V17.9287H30.093V19.5475Z"
        fill="#4457D1"
      />
    </MarkerComponent>
  );
}

function MarkerComponent({
  fill,
  size,
  children,
  isFav,
}: { fill: string; size: number; children: React.ReactNode; isFav?: boolean }) {
  return (
    <svg
      width={size * 0.7}
      height={size}
      viewBox="0 0 44 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M36.3155 36.7114C30.8617 43.3287 26.4463 51.2303 23.6793 59.4065C23.0754 61.1831 21.0725 61.7259 20.3947 59.7221C17.372 50.7822 12.6854 42.5871 6.33182 35.1399C2.04271 30.1098 1.01665 22.3534 2.90854 16.2662C8.01418 -0.158765 29.2101 -3.08717 38.6511 11.0784C42.6906 17.1434 43.1806 25.6604 39.5478 32.3093C39.027 33.2655 37.9486 34.7328 36.3186 36.7114H36.3155Z"
        fill={fill}
      />
      <ellipse cx="21.9999" cy="22.1467" rx="15.7143" ry="15.8293" fill="white" />
      {isFav && (
        <path
          d="M21.9999 39.415L23.7129 42.7962L27.4345 43.3924L24.7716 46.0783L25.3587 49.8279L21.9999 48.1067L18.6412 49.8279L19.2283 46.0783L16.5653 43.3924L20.287 42.7962L21.9999 39.415Z"
          fill="white"
        />
      )}
      {children}
    </svg>
  );
}

export function SelfMarker() {
  return (
    <div className="w-[24px] h-[24px] flex items-center justify-center">
      <motion.div
        className="absolute w-[24px] h-[24px] rounded-full bg-[#6393F2]"
        animate={{
          scale: [0.8, 1, 0.8],
          opacity: [0.3, 0.1, 0.3],
        }}
        transition={{
          duration: 2,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
          delay: 0.3,
        }}
      />
      <motion.div
        className="w-[14px] h-[14px] rounded-full bg-[#6393F2] border-2 border-white"
        animate={{
          scale: [1.0, 0.9, 1.0],
        }}
        transition={{
          duration: 4,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
          delay: 0.3,
        }}
      />
    </div>
  );
}
