'use client';

import { useMapBounds } from '@/hooks/use-map';
import { getMarkerPosition } from '@/hooks/use-map';
import type { Connection, Marker } from '@/types/map';
import { AdvancedMarker, Map as GoogleMap } from '@vis.gl/react-google-maps';
import { useCallback } from 'react';
import Line from './line';
import { SelfMarker } from './marker';
import MergedMarkers from './merged-markders';

interface MapComponentProps {
  onMarkerClick: (marker: Marker) => void;
  markers: Marker[];
  userLocationMarker?: Marker;
  lines: Connection[];
}

export default function MapComponent({
  onMarkerClick,
  markers,
  lines,
  userLocationMarker,
}: MapComponentProps) {
  const handleMarkerClick = useCallback(
    (marker: Marker) => {
      onMarkerClick(marker);
    },
    [onMarkerClick],
  );

  const { center, zoom } = useMapBounds(markers);

  if (center.lat === 0 && center.lng === 0) {
    return null;
  }

  return (
    <>
      <GoogleMap
        disableDefaultUI={true}
        defaultCenter={center}
        defaultZoom={zoom}
        mapId={'location-picker-map'}
        gestureHandling={'greedy'}
      >
        {userLocationMarker && (
          <AdvancedMarker position={getMarkerPosition(userLocationMarker)}>
            <SelfMarker />
          </AdvancedMarker>
        )}
        <MergedMarkers markers={markers} onMarkerClick={handleMarkerClick} />
        <Line connections={lines} />
      </GoogleMap>
    </>
  );
}
