'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Search } from 'lucide-react';
import React from 'react';
interface SearchInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  onClear?: () => void;
  showClearButton?: boolean;
}

export const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ className, onClear, showClearButton, ...props }, ref) => {
    const handleClearClick = (e: React.MouseEvent) => {
      e.stopPropagation(); // 阻止事件冒泡，防止触发外层的 openKeywordSlidePage
      onClear?.();
    };

    return (
      <div className={cn('relative flex-1', className)}>
        <Search className="absolute left-3 top-4 h-4 w-4 text-muted-foreground pointer-events-none z-10" />
        <Input ref={ref} className="px-10 w-full h-12 truncate" {...props} />

        {showClearButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearClick}
            className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
          >
            <img src="/images/event/close-icon.svg" alt="" />
          </Button>
        )}
      </div>
    );
  },
);
SearchInput.displayName = 'SearchInput';
