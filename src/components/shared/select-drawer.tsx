'use client';

import { ChevronDown, X } from 'lucide-react';
import * as React from 'react';
import { Drawer as DrawerPrimitive } from 'vaul';

import { cn } from '@/lib/utils';

const SelectDrawer = ({
  shouldScaleBackground = true,
  ...props
}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (
  <DrawerPrimitive.Root shouldScaleBackground={shouldScaleBackground} {...props} />
);
SelectDrawer.displayName = 'SelectDrawer';

const SelectDrawerTrigger = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Trigger> & {
    hideChevron?: boolean;
  }
>(({ className, children, hideChevron = false, ...props }, ref) => (
  <DrawerPrimitive.Trigger
    ref={ref}
    className={cn(
      'flex h-12 w-full items-center justify-between rounded-lg border border-input bg-transparent px-3 py-2 text-base shadow-sm ring-offset-background placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50',
      className,
    )}
    {...props}
  >
    {children}
    {!hideChevron && <ChevronDown className="h-6 w-6" />}
  </DrawerPrimitive.Trigger>
));
SelectDrawerTrigger.displayName = DrawerPrimitive.Trigger.displayName;

const DrawerPortal = DrawerPrimitive.Portal;

const SelectDrawerClose = DrawerPrimitive.Close;

const DrawerOverlay = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Overlay
    ref={ref}
    className={cn('fixed inset-0 z-[80] bg-black/80', className)}
    {...props}
  />
));
DrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName;

const SelectDrawerContent = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DrawerPortal>
    <DrawerOverlay />
    <DrawerPrimitive.Content
      ref={ref}
      className={cn(
        'fixed inset-x-0 bottom-0 z-[80] mt-24 pb-12 flex h-auto flex-col rounded-t-[10px] border bg-background',
        className,
      )}
      {...props}
    >
      <DrawerPrimitive.Title className="sr-only">Select Drawer</DrawerPrimitive.Title>
      {children}
    </DrawerPrimitive.Content>
  </DrawerPortal>
));
SelectDrawerContent.displayName = 'SelectDrawerContent';

const SelectDrawerHeader = ({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className="text-lg h-12 flex font-bold items-center justify-center rounded-t-2xl relative"
    {...props}
  >
    <SelectDrawerClose asChild>
      <button type="button" className="absolute left-5 top-3">
        <X size={24} />
      </button>
    </SelectDrawerClose>
    {children}
  </div>
);
SelectDrawerHeader.displayName = 'SelectDrawerHeader';

export {
  SelectDrawer,
  SelectDrawerClose,
  SelectDrawerContent,
  SelectDrawerHeader,
  SelectDrawerTrigger,
};
