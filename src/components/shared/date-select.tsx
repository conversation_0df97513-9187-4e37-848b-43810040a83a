'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Dialog, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { format, setMonth, setYear } from 'date-fns';
import { CalendarDays as CalendarIcon } from 'lucide-react';
import * as React from 'react';
import {
  SelectDrawer,
  SelectDrawerClose,
  SelectDrawerContent,
  SelectDrawerHeader,
  SelectDrawerTrigger,
} from './select-drawer';

// カスタムDialogContent、クローズアイコンを非表示にする
const CustomDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPrimitive.Portal>
    <DialogPrimitive.Overlay className="fixed inset-0 z-[70] bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        'fixed left-[50%] top-[50%] z-[71] grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-card p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',
        className,
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPrimitive.Portal>
));
CustomDialogContent.displayName = 'CustomDialogContent';

interface DatePickerProps {
  className?: string;
  value?: string | Date;
  onChange?: (date: string) => void;
  placeholder?: string;
  disabledDates?: string[]; // 禁用的日期列表 (YYYY-MM-DD格式)
  min?: string | Date; // 最小日期，min之前的日期均disabled
  max?: string | Date; // 最大日期，max之后的日期均disabled
  appendInner?: React.ReactNode;
}

// コンポーネント内で日本語の月と曜日を定義
const MONTHS = [
  '1月',
  '2月',
  '3月',
  '4月',
  '5月',
  '6月',
  '7月',
  '8月',
  '9月',
  '10月',
  '11月',
  '12月',
];
// 曜日の順序を変更：日から土まで
const WEEKDAYS = ['日', '月', '火', '水', '木', '金', '土'];

export function DateSelect({
  className,
  value,
  onChange,
  placeholder = '日付を選択',
  disabledDates = [],
  min,
  max,
  appendInner,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);

  // 文字列の値をDateオブジェクトに変換
  const dateValue = React.useMemo(() => {
    if (!value) return undefined;
    if (typeof value === 'string') {
      return new Date(value);
    }
    return value;
  }, [value]);

  // カレンダーの行の配置のためのカスタムCSSを追加
  React.useEffect(() => {
    // スタイル要素を作成
    const style = document.createElement('style');
    // カレンダーの行の配置のためのCSSルールを追加
    style.innerHTML = `
      /* 最初の行（右寄せ） */
      .rdp-tbody tr:first-child {
        justify-content: flex-end;
      }
      
      /* 最後の行（左寄せ） */
      .rdp-tbody tr:last-child {
        justify-content: flex-start;
      }
    `;
    // スタイル要素をドキュメントのヘッドに追加
    document.head.appendChild(style);

    // コンポーネントのアンマウント時にクリーンアップ
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const [month, setCurrentMonth] = React.useState<Date>(dateValue || new Date());

  // 一時的な状態、ユーザーがDrawer内で選択した内容を保存するため
  const [tempYear, setTempYear] = React.useState<number>(month.getFullYear());
  const [tempMonth, setTempMonth] = React.useState<number>(month.getMonth());

  // 年のオプションを生成（前後5年ずつ、計11年）
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  // 月のオプションを生成
  const months = Array.from({ length: 12 }, (_, i) => i);

  const handleYearChange = (yearStr: string) => {
    const year = Number.parseInt(yearStr);
    setTempYear(year);
    // 年が変更された時、月を1月（インデックス0）にリセット
    setTempMonth(0);
  };

  const handleMonthChange = (monthStr: string) => {
    const monthIndex = Number.parseInt(monthStr);
    setTempMonth(monthIndex);
  };

  // 選択を保存してDrawerを閉じる
  const handleSaveSelection = () => {
    const newDate = setMonth(setYear(month, tempYear), tempMonth);
    setCurrentMonth(newDate);
  };

  // Drawerが開かれた時、一時的な状態を現在の値にリセット
  const handleDrawerOpenChange = (open: boolean) => {
    if (open) {
      setTempYear(month.getFullYear());
      setTempMonth(month.getMonth());
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date && onChange) {
      // Dateを文字列形式（YYYY-MM-DD）に変換
      const dateString = format(date, 'yyyy-MM-dd');
      onChange(dateString);
      setOpen(false); // 日付選択後にDialogを閉じる
    }
  };

  const handleMonthNavigate = (date: Date) => {
    setCurrentMonth(date);
  };

  // 月と曜日の表示をカスタマイズ
  const formatters = {
    formatCaption: (date: Date) => MONTHS[date.getMonth()],
    formatWeekdayName: (date: Date) => WEEKDAYS[date.getDay()],
  };

  return (
    <div className={cn('grid gap-2', className)}>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              'w-full  text-left font-normal h-12 !bg-white',
              !value && 'text-muted-foreground',
            )}
          >
            <div className="flex-1 text-base">
              {dateValue ? (
                formatDate(dateValue, 'yyyy年M月d日(d)')
              ) : (
                <span className="text-sm">{placeholder}</span>
              )}
            </div>
            {appendInner}
            <CalendarIcon className="h-5 w-5" />
          </Button>
        </DialogTrigger>
        <CustomDialogContent
          className="w-auto p-0 max-w-sm rounded-2xl"
          aria-describedby="undefined"
        >
          <VisuallyHidden>
            <DialogTitle>日付を選択</DialogTitle>
          </VisuallyHidden>
          <div className="flex items-center gap-2 p-3 border-b">
            <SelectDrawer onOpenChange={handleDrawerOpenChange}>
              <SelectDrawerTrigger className="w-[140px] h-10">
                <span className="text-sm">
                  {month.getFullYear()}年 {MONTHS[month.getMonth()]}
                </span>
              </SelectDrawerTrigger>
              <SelectDrawerContent className="bg-card z-[80]">
                <div className="text-lg h-12 flex font-bold items-center justify-center rounded-t-2xl relative">
                  <SelectDrawerClose asChild>
                    <button
                      type="button"
                      className="absolute left-5 top-3 text-primary font-bold  text-sm"
                      onClick={handleSaveSelection}
                    >
                      完了
                    </button>
                  </SelectDrawerClose>
                  年月を選択
                </div>
                <div className="flex ">
                  {/* 年選択列 */}
                  <div className="flex-1 border-r">
                    <div className="h-[400px] overflow-y-auto">
                      <RadioGroup value={tempYear.toString()}>
                        {years.map((year) => (
                          <div
                            key={year}
                            className="flex items-center justify-center h-12 px-4 hover:bg-muted cursor-pointer"
                            onClick={() => handleYearChange(year.toString())}
                          >
                            <RadioGroupItem
                              value={year.toString()}
                              id={`year-${year}`}
                              className="sr-only"
                            />
                            <Label htmlFor={`year-${year}`} className="text-sm cursor-pointer ml-2">
                              {year}年
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>
                  </div>

                  {/* 月選択列 */}
                  <div className="flex-1">
                    <div className="h-[400px] overflow-y-auto">
                      <RadioGroup value={tempMonth.toString()}>
                        {months.map((monthIndex) => (
                          <div
                            key={monthIndex}
                            className="flex items-center justify-center h-12 px-4 hover:bg-muted cursor-pointer"
                            onClick={() => handleMonthChange(monthIndex.toString())}
                          >
                            <RadioGroupItem
                              value={monthIndex.toString()}
                              id={`month-${monthIndex}`}
                              className="sr-only"
                            />
                            <Label
                              htmlFor={`month-${monthIndex}`}
                              className="text-sm cursor-pointer ml-2"
                            >
                              {MONTHS[monthIndex]}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>
                  </div>
                </div>
              </SelectDrawerContent>
            </SelectDrawer>
          </div>

          <Calendar
            initialFocus
            mode="single"
            defaultMonth={month}
            month={month}
            onMonthChange={handleMonthNavigate}
            selected={dateValue}
            onSelect={handleDateSelect}
            numberOfMonths={1}
            showOutsideDays={false}
            weekStartsOn={0}
            disabled={(date) => {
              // 检查是否小于最小日期
              let isBeforeMin = false;
              if (min) {
                const minDate = typeof min === 'string' ? new Date(min) : min;
                minDate.setHours(0, 0, 0, 0); // 设置为当天的开始
                const currentDate = new Date(date);
                currentDate.setHours(0, 0, 0, 0);
                isBeforeMin = currentDate < minDate;
              }

              // 检查是否大于最大日期
              let isAfterMax = false;
              if (max) {
                const maxDate = typeof max === 'string' ? new Date(max) : max;
                maxDate.setHours(23, 59, 59, 999); // 设置为当天的最后一刻
                const currentDate = new Date(date);
                currentDate.setHours(0, 0, 0, 0);
                isAfterMax = currentDate > maxDate;
              }

              // 禁用已存在的日期
              const dateString = format(date, 'yyyy-MM-dd');
              const isExistingDate = disabledDates.includes(dateString);

              return isBeforeMin || isAfterMax || isExistingDate;
            }}
            formatters={formatters}
            classNames={{
              caption: 'flex justify-center pt-1 relative items-center',
              caption_label: 'text-sm font-medium',
              nav: 'space-x-1 flex items-center',
              nav_button: cn('h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'),
              nav_button_previous: 'absolute left-1',
              nav_button_next: 'absolute right-1',
              table: 'w-full border-collapse space-y-1',
              head_row: 'flex',
              head_cell:
                'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] flex justify-center items-center',
              row: 'flex w-full mt-2',
              day: cn('h-8 w-8 p-0 font-normal aria-selected:opacity-100 text-center'),
              day_selected:
                'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
              day_today: 'bg-accent text-accent-foreground',
              day_outside: 'text-muted-foreground opacity-50',
              day_disabled: 'text-muted-foreground opacity-50',
              day_hidden: 'invisible',
            }}
          />
        </CustomDialogContent>
      </Dialog>
    </div>
  );
}
