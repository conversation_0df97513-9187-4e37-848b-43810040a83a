'use client';

import { Slot } from '@radix-ui/react-slot';
import { type VariantProps, cva } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const textButtonVariants = cva(
  'inline-flex items-center justify-center gap-[0.25rem] whitespace-nowrap font-bold focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'text-primary',
        destructive: 'text-destructive',
        muted: 'text-muted-foreground',
      },
      size: {
        default: 'h-6 text-base [&_svg]:translate-y-[-2px]',
        sm: 'h-6 text-[15px] [&_svg]:translate-y-[-1px]',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface TextButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof textButtonVariants> {
  asChild?: boolean;
}

const TextButton = React.forwardRef<HTMLButtonElement, TextButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp className={cn(textButtonVariants({ variant, size, className }))} ref={ref} {...props} />
    );
  },
);
TextButton.displayName = 'TextButton';

export { TextButton, textButtonVariants };
