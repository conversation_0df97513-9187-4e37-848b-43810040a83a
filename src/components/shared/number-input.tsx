'use client';
import { cn } from '@/lib/utils';
import React from 'react';

export interface NumberInputProps {
  name: string;
  unit: string;
  maxIntLen?: number; // 最大整数位数
  maxDecLen?: number; // 最大小数位数
  disabled?: boolean;
  isAllowNegative?: boolean; // 負の数を許可するかどうか
  value: string;
  onChange?: (value: string) => void;
  className?: string;
  'aria-invalid'?: boolean; // 追加 aria-invalid 属性
}

export const NumberInput = React.forwardRef<HTMLInputElement, NumberInputProps>(
  (
    {
      unit,
      name,
      className,
      value = '',
      onChange,
      maxIntLen = 6,
      maxDecLen = 0,
      disabled,
      isAllowNegative = false,
      'aria-invalid': ariaInvalid,
      ...props
    },
    ref,
  ) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;

      // 空文字の場合
      if (val === '') {
        onChange?.(val);
        return;
      }

      // 許可された入力：-123.456
      const numericRegex = /^-?\d*\.?\d*$/;
      if (!numericRegex.test(val)) {
        return; // 保持原值不变
      }

      if (maxDecLen === 0) {
        if (val.includes('.')) {
          return;
        }
      }

      if (!isAllowNegative) {
        if (val.includes('-')) {
          return;
        }
      }

      // マイナス符号を削除
      const removedNegative = val.replace('-', '');

      // 整数部分の桁数を制限
      const integerPart = removedNegative.split('.')[0] || '';
      if (integerPart.length > maxIntLen) {
        return;
      }

      // 小数部分の桁数を制限
      const decimalPart = removedNegative.split('.')[1] || '';
      if (decimalPart.length > maxDecLen) {
        return;
      }
      onChange?.(val);
    };

    return (
      <div
        className={cn(
          'flex items-center relative min-w-[120px] h-12 border border-border rounded-lg',
          'focus-within:border-primary',
          disabled && '!bg-muted',
          ariaInvalid && '!border-invalid-border !bg-invalid-background',
          className,
        )}
        aria-invalid={ariaInvalid}
        {...props}
      >
        <input
          type="text"
          inputMode="decimal" // 数字キーボードを表示
          pattern="[0-9\-\.]*" // 数字キーボードを表示
          value={value}
          onChange={handleChange}
          ref={ref}
          id={`${name}-numberic-input`}
          disabled={disabled}
          className={cn(
            'flex-1 ml-4 w-12 focus:outline-none text-text-primary bg-transparent',
            disabled && '!text-gray-60 opacity-100 [&:disabled]:[-webkit-text-fill-color:#666]',
          )}
        />

        <label
          htmlFor={`${name}-numberic-input`}
          className={cn(' ml-2 mr-3 text-text-secondary', disabled && '!text-gray-60')}
        >
          {unit}
        </label>
      </div>
    );
  },
);
