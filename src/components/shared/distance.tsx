import { useGeolocation } from '@/hooks/use-geolocation';
import { calculateDistance } from '@/lib/utils';
import type { LatLng } from '@/types/map';
import { useEffect, useRef } from 'react';

export function Distance({
  targetLocation,
  className,
}: { targetLocation: LatLng; className?: string }) {
  const { location } = useGeolocation();
  const ref = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (location && targetLocation && ref.current) {
      const distance = calculateDistance(location, targetLocation);
      if (distance < 1000) {
        ref.current.textContent = `${distance.toFixed(0)}m`;
      } else {
        ref.current.textContent = `${(distance / 1000).toFixed(1)}km`;
      }
    }
  }, [location, targetLocation]);

  return (
    <span className={className} ref={ref}>
      0m
    </span>
  );
}
