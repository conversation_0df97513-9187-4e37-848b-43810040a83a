import { cn } from '@/lib/utils';

export default function QaIcon({
  className,
  fill = '#666666',
}: {
  className?: string;
  fill?: string;
}) {
  return (
    <div className={cn('w-6 h-6', className)}>
      <svg
        width="25"
        height="25"
        viewBox="0 0 25 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.75 23.6309C6.545 23.6309 1.5 18.5859 1.5 12.3809C1.5 6.17586 6.545 1.13086 12.75 1.13086C18.955 1.13086 24 6.17586 24 12.3809C24 18.5859 18.955 23.6309 12.75 23.6309ZM12.75 2.63086C7.375 2.63086 3 7.00586 3 12.3809C3 17.7559 7.375 22.1309 12.75 22.1309C18.125 22.1309 22.5 17.7559 22.5 12.3809C22.5 7.00586 18.125 2.63086 12.75 2.63086Z"
          fill={fill}
        />
        <path
          d="M11.3246 13.8359C11.2996 13.4209 11.3396 13.0509 11.4346 12.7309C11.5296 12.4109 11.6996 12.1209 11.9346 11.8559C12.1746 11.5959 12.4946 11.3459 12.8896 11.1059C13.2596 10.8859 13.5496 10.6859 13.7646 10.5009C13.9796 10.3159 14.1296 10.1209 14.2146 9.91587C14.2996 9.71087 14.3446 9.47587 14.3446 9.21087V9.19587C14.3446 8.92087 14.2746 8.67587 14.1346 8.46087C13.9946 8.24587 13.8046 8.08087 13.5596 7.96087C13.3146 7.84087 13.0296 7.78587 12.6996 7.78587C12.3696 7.78587 12.0846 7.85087 11.8346 7.98587C11.5846 8.12087 11.3896 8.30087 11.2446 8.53087C11.0996 8.76087 11.0196 9.02587 11.0046 9.31587L10.9946 9.38587H8.63965L8.64965 9.30587C8.67465 8.62587 8.84965 8.02087 9.17965 7.49087C9.50965 6.96087 9.98465 6.54087 10.6046 6.23087C11.2246 5.92087 11.9696 5.76587 12.8546 5.76587C13.6796 5.76587 14.3996 5.91087 15.0146 6.19587C15.6296 6.48087 16.1096 6.87587 16.4496 7.37087C16.7946 7.87087 16.9646 8.45087 16.9646 9.11587V9.13587C16.9646 9.57087 16.8946 9.96087 16.7546 10.3109C16.6146 10.6609 16.4096 10.9809 16.1346 11.2659C15.8596 11.5509 15.5196 11.8109 15.1096 12.0509C14.7296 12.2759 14.4296 12.4809 14.2196 12.6709C14.0096 12.8609 13.8546 13.0559 13.7696 13.2609C13.6846 13.4659 13.6396 13.7059 13.6396 13.9759V14.3309H11.3396L11.3246 13.8359ZM11.5246 18.0759C11.2496 17.8159 11.1146 17.4909 11.1146 17.1109C11.1146 16.7309 11.2496 16.4109 11.5246 16.1459C11.7996 15.8809 12.1296 15.7509 12.5246 15.7509C12.9196 15.7509 13.2596 15.8809 13.5346 16.1459C13.8096 16.4059 13.9446 16.7309 13.9446 17.1109C13.9446 17.4909 13.8096 17.8109 13.5346 18.0759C13.2596 18.3409 12.9246 18.4709 12.5246 18.4709C12.1246 18.4709 11.7996 18.3409 11.5246 18.0759Z"
          fill={fill}
        />
      </svg>
    </div>
  );
}
