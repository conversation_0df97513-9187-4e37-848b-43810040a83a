'use client';

import { SlideFromBottom } from '@/components/shared/animation';
import { COLORS } from '@/const/colors';
import { FooterMenuName, useGlobalVar } from '@/hooks/use-global-var';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface FooterItem {
  name: FooterMenuName;
  path: string;
  icon: React.FC<React.SVGProps<SVGSVGElement> & { isActive: boolean }>;
  label: string;
}

const ACTIVE_COLOR = COLORS.primary.foreground;
const INACTIVE_COLOR = COLORS.text.primary;

function HomeIcon({ isActive }: { isActive: boolean }) {
  const fillColor = isActive ? ACTIVE_COLOR : INACTIVE_COLOR;
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <g>
        <path
          d="M6.2998 19H9.2998V14C9.2998 13.7167 9.39564 13.4792 9.5873 13.2875C9.77897 13.0958 10.0165 13 10.2998 13H14.2998C14.5831 13 14.8206 13.0958 15.0123 13.2875C15.204 13.4792 15.2998 13.7167 15.2998 14V19H18.2998V10L12.2998 5.5L6.2998 10V19ZM4.2998 19V10C4.2998 9.68333 4.37064 9.38333 4.5123 9.1C4.65397 8.81667 4.8498 8.58333 5.0998 8.4L11.0998 3.9C11.4498 3.63333 11.8498 3.5 12.2998 3.5C12.7498 3.5 13.1498 3.63333 13.4998 3.9L19.4998 8.4C19.7498 8.58333 19.9456 8.81667 20.0873 9.1C20.229 9.38333 20.2998 9.68333 20.2998 10V19C20.2998 19.55 20.104 20.0208 19.7123 20.4125C19.3206 20.8042 18.8498 21 18.2998 21H14.2998C14.0165 21 13.779 20.9042 13.5873 20.7125C13.3956 20.5208 13.2998 20.2833 13.2998 20V15H11.2998V20C11.2998 20.2833 11.204 20.5208 11.0123 20.7125C10.8206 20.9042 10.5831 21 10.2998 21H6.2998C5.7498 21 5.27897 20.8042 4.8873 20.4125C4.49564 20.0208 4.2998 19.55 4.2998 19Z"
          fill={fillColor}
        />
      </g>
    </svg>
  );
}

function MapIcon({ isActive }: { isActive: boolean }) {
  const fillColor = isActive ? ACTIVE_COLOR : INACTIVE_COLOR;
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M15.2499 20.775L9.8999 18.9L5.2499 20.7C5.08324 20.7667 4.92074 20.7875 4.7624 20.7625C4.60407 20.7375 4.45824 20.6833 4.3249 20.6C4.19157 20.5167 4.0874 20.4042 4.0124 20.2625C3.9374 20.1208 3.8999 19.9583 3.8999 19.775V5.75C3.8999 5.53333 3.9624 5.34167 4.0874 5.175C4.2124 5.00833 4.38324 4.88333 4.5999 4.8L9.2499 3.225C9.3499 3.19167 9.45407 3.16667 9.5624 3.15C9.67074 3.13333 9.78324 3.125 9.8999 3.125C10.0166 3.125 10.1291 3.13333 10.2374 3.15C10.3457 3.16667 10.4499 3.19167 10.5499 3.225L15.8999 5.1L20.5499 3.3C20.7166 3.23333 20.8791 3.2125 21.0374 3.2375C21.1957 3.2625 21.3416 3.31667 21.4749 3.4C21.6082 3.48333 21.7124 3.59583 21.7874 3.7375C21.8624 3.87917 21.8999 4.04167 21.8999 4.225V18.25C21.8999 18.4667 21.8374 18.6583 21.7124 18.825C21.5874 18.9917 21.4166 19.1167 21.1999 19.2L16.5499 20.775C16.4499 20.8083 16.3457 20.8333 16.2374 20.85C16.1291 20.8667 16.0166 20.875 15.8999 20.875C15.7832 20.875 15.6707 20.8667 15.5624 20.85C15.4541 20.8333 15.3499 20.8083 15.2499 20.775ZM14.8999 18.55V6.85L10.8999 5.45V17.15L14.8999 18.55ZM16.8999 18.55L19.8999 17.55V5.7L16.8999 6.85V18.55ZM5.8999 18.3L8.8999 17.15V5.45L5.8999 6.45V18.3Z"
        fill={fillColor}
      />
    </svg>
  );
}

function ImageIcon({ isActive }: { isActive: boolean }) {
  const fillColor = isActive ? ACTIVE_COLOR : INACTIVE_COLOR;
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5.5 21C4.95 21 4.47917 20.8042 4.0875 20.4125C3.69583 20.0208 3.5 19.55 3.5 19V5C3.5 4.45 3.69583 3.97917 4.0875 3.5875C4.47917 3.19583 4.95 3 5.5 3H19.5C20.05 3 20.5208 3.19583 20.9125 3.5875C21.3042 3.97917 21.5 4.45 21.5 5V19C21.5 19.55 21.3042 20.0208 20.9125 20.4125C20.5208 20.8042 20.05 21 19.5 21H5.5ZM5.5 19H19.5V5H5.5V19ZM6.5 17H18.5L14.75 12L11.75 16L9.5 13L6.5 17ZM9 10C9.41667 10 9.77083 9.85417 10.0625 9.5625C10.3542 9.27083 10.5 8.91667 10.5 8.5C10.5 8.08333 10.3542 7.72917 10.0625 7.4375C9.77083 7.14583 9.41667 7 9 7C8.58333 7 8.22917 7.14583 7.9375 7.4375C7.64583 7.72917 7.5 8.08333 7.5 8.5C7.5 8.91667 7.64583 9.27083 7.9375 9.5625C8.22917 9.85417 8.58333 10 9 10Z"
        fill={fillColor}
      />
    </svg>
  );
}

function FriendIcon({ isActive }: { isActive: boolean }) {
  const fillColor = isActive ? ACTIVE_COLOR : INACTIVE_COLOR;
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <g>
        <path
          d="M0.0996094 18V16.425C0.0996094 15.7083 0.466276 15.125 1.19961 14.675C1.93294 14.225 2.89961 14 4.09961 14C4.31628 14 4.52461 14.0042 4.72461 14.0125C4.92461 14.0208 5.11628 14.0417 5.29961 14.075C5.06628 14.425 4.89128 14.7917 4.77461 15.175C4.65794 15.5583 4.59961 15.9583 4.59961 16.375V18H0.0996094ZM6.09961 18V16.375C6.09961 15.8417 6.24544 15.3542 6.53711 14.9125C6.82878 14.4708 7.24128 14.0833 7.77461 13.75C8.30794 13.4167 8.94544 13.1667 9.68711 13C10.4288 12.8333 11.2329 12.75 12.0996 12.75C12.9829 12.75 13.7954 12.8333 14.5371 13C15.2788 13.1667 15.9163 13.4167 16.4496 13.75C16.9829 14.0833 17.3913 14.4708 17.6746 14.9125C17.9579 15.3542 18.0996 15.8417 18.0996 16.375V18H6.09961ZM19.5996 18V16.375C19.5996 15.9417 19.5454 15.5333 19.4371 15.15C19.3288 14.7667 19.1663 14.4083 18.9496 14.075C19.1329 14.0417 19.3204 14.0208 19.5121 14.0125C19.7038 14.0042 19.8996 14 20.0996 14C21.2996 14 22.2663 14.2208 22.9996 14.6625C23.7329 15.1042 24.0996 15.6917 24.0996 16.425V18H19.5996ZM8.22461 16H15.9996C15.8329 15.6667 15.3704 15.375 14.6121 15.125C13.8538 14.875 13.0163 14.75 12.0996 14.75C11.1829 14.75 10.3454 14.875 9.58711 15.125C8.82878 15.375 8.37461 15.6667 8.22461 16ZM4.09961 13C3.54961 13 3.07878 12.8042 2.68711 12.4125C2.29544 12.0208 2.09961 11.55 2.09961 11C2.09961 10.4333 2.29544 9.95833 2.68711 9.575C3.07878 9.19167 3.54961 9 4.09961 9C4.66628 9 5.14128 9.19167 5.52461 9.575C5.90794 9.95833 6.09961 10.4333 6.09961 11C6.09961 11.55 5.90794 12.0208 5.52461 12.4125C5.14128 12.8042 4.66628 13 4.09961 13ZM20.0996 13C19.5496 13 19.0788 12.8042 18.6871 12.4125C18.2954 12.0208 18.0996 11.55 18.0996 11C18.0996 10.4333 18.2954 9.95833 18.6871 9.575C19.0788 9.19167 19.5496 9 20.0996 9C20.6663 9 21.1413 9.19167 21.5246 9.575C21.9079 9.95833 22.0996 10.4333 22.0996 11C22.0996 11.55 21.9079 12.0208 21.5246 12.4125C21.1413 12.8042 20.6663 13 20.0996 13ZM12.0996 12C11.2663 12 10.5579 11.7083 9.97461 11.125C9.39128 10.5417 9.09961 9.83333 9.09961 9C9.09961 8.15 9.39128 7.4375 9.97461 6.8625C10.5579 6.2875 11.2663 6 12.0996 6C12.9496 6 13.6621 6.2875 14.2371 6.8625C14.8121 7.4375 15.0996 8.15 15.0996 9C15.0996 9.83333 14.8121 10.5417 14.2371 11.125C13.6621 11.7083 12.9496 12 12.0996 12ZM12.0996 10C12.3829 10 12.6204 9.90417 12.8121 9.7125C13.0038 9.52083 13.0996 9.28333 13.0996 9C13.0996 8.71667 13.0038 8.47917 12.8121 8.2875C12.6204 8.09583 12.3829 8 12.0996 8C11.8163 8 11.5788 8.09583 11.3871 8.2875C11.1954 8.47917 11.0996 8.71667 11.0996 9C11.0996 9.28333 11.1954 9.52083 11.3871 9.7125C11.5788 9.90417 11.8163 10 12.0996 10Z"
          fill={fillColor}
        />
      </g>
    </svg>
  );
}

function MenuIcon({ isActive }: { isActive: boolean }) {
  const fillColor = isActive ? ACTIVE_COLOR : INACTIVE_COLOR;
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <g>
        <rect x="3.7002" y="6" width="18" height="2" fill={fillColor} />
        <rect x="3.7002" y="11" width="18" height="2" fill={fillColor} />
        <rect x="3.7002" y="16" width="18" height="2" fill={fillColor} />
      </g>
    </svg>
  );
}

const isProduction = (process.env.NEXT_PUBLIC_API_URL || '').indexOf('ntt.com') > -1;

export default function AppFooter() {
  const { footerMenuSetting, setCurrentActiveFooterMenuName } = useGlobalVar();
  const router = useRouter();
  const safeArea = useSafeArea();
  const [clickCount, setClickCount] = useState<number>(0);
  // const hideFooter = useMemo(() => {
  //   return NO_FOOTER_ROUTES.includes(pathname);
  // }, [pathname]);

  const footerItems: FooterItem[] = [
    {
      name: FooterMenuName.HOME,
      path: '/home',
      icon: HomeIcon,
      label: 'ホーム',
    },
    // {
    //   name: FooterMenuName.MAP,
    //   // path: '/map',
    //   icon: MapIcon,
    //   label: 'マップ',
    // },
    // {
    //   name: FooterMenuName.POST,
    //   // path: '/post',
    //   icon: ImageIcon,
    //   label: '写真投稿',
    // },
    {
      name: FooterMenuName.FRIEND,
      path: '/friend',
      icon: FriendIcon,
      label: 'フレンド',
    },
    {
      name: FooterMenuName.MENU,
      path: '/menu',
      icon: MenuIcon,
      label: 'メニュー',
    },
  ];

  const handleNavigation = (item: FooterItem) => {
    if (item.name === FooterMenuName.HOME) {
      if (isProduction) {
        router.push(item.path);
      } else {
        if (clickCount > 10) {
          setClickCount(0);
          router.push('/test-page');
        } else {
          setClickCount(clickCount + 1);
          router.push(item.path);
        }
      }
    } else {
      setClickCount(0);
      router.push(item.path);
    }
  };

  return (
    <SlideFromBottom show={footerMenuSetting.isShow}>
      <div
        className="fixed bottom-0 left-0 right-0 bg-card shadow-[0_-4px_10px_rgba(0,0,0,0.1)] px-4"
        style={{
          zIndex: footerMenuSetting.layerIndex,
          paddingTop: 8,
          paddingBottom: safeArea.bottom + 8,
        }}
      >
        <div className="flex h-[45px] w-full">
          {footerItems.map((item) => {
            const isActive = footerMenuSetting.activeMenuName === item.name;
            return (
              <button
                key={item.name}
                className={cn(
                  'flex flex-1 flex-col items-center justify-center h-full rounded-xl transition-colors',
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground',
                )}
                onClick={() => {
                  if (item.path) {
                    handleNavigation(item);
                  }
                }}
                type="button"
              >
                <div className="flex items-center justify-center">
                  <item.icon isActive={isActive} />
                </div>
                <span className="text-[11px]">{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>
    </SlideFromBottom>
  );
}
