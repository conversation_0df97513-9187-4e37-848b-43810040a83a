import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog'; // 根据你的实际路径调整
import { COMMON_TEXT } from '@/const/text/common';
import { useRouter } from '@/hooks/use-next-navigation';
import type React from 'react';

interface AlertDialogComponentProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string | React.ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
  navigateTo?: string; // 跳转路径
  className?: string;
}

export const AlertDialogComponent: React.FC<AlertDialogComponentProps> = ({
  open,
  onOpenChange,
  title,
  onConfirm,
  onCancel,
  navigateTo,
  className = '',
}) => {
  const router = useRouter();
  const handleConfirm = () => {
    if (navigateTo) {
      router.push(navigateTo);
    }
    onConfirm?.();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className={`rounded-[20px] bg-white ${className}`}>
        {title}
        <AlertDialogFooter>
          <AlertDialogAction
            onClick={handleConfirm}
            className="text-white bg-primary rounded-[23px] ml-4 mr-4 font-bold h-12 text-[16px]"
          >
            {COMMON_TEXT.BUTTON.FINISH}
          </AlertDialogAction>

          <AlertDialogCancel
            onClick={handleCancel}
            className="rounded-[23px] ml-4 mr-4 border-[0px] border-white bg-white font-bold h-12 text-gray-500 text-[16px] mb-4"
          >
            {COMMON_TEXT.BUTTON.CANCEL}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
