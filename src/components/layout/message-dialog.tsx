import { Button } from '@/components/shared/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

export type MessageDialogProps = {
  title?: string;
  content: React.ReactNode;
  footer?: React.ReactNode;
  header?: React.ReactNode;
  isOpen: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  outSideClickClose?: boolean;
};

export function MessageDialog({
  title,
  content,
  footer,
  header = false,
  isOpen,
  outSideClickClose = false,
  onOpenChange,
}: MessageDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        onInteractOutside={(e) => {
          if (!outSideClickClose) {
            e.preventDefault();
          }
        }}
        aria-describedby={title}
        className="sm:max-w-[425px] w-[calc(100vw-48px)] rounded-3xl p-8 [&>button:last-child]:hidden"
      >
        {header ? (
          <div className="flex justify-center items-center w-full">{header}</div>
        ) : (
          <DialogHeader className={cn(title ? '' : 'hidden')}>
            <DialogTitle className="text-xl font-bold text-center">{title}</DialogTitle>
          </DialogHeader>
        )}
        {content}
        {footer || (
          <DialogFooter className="mt-4">
            <DialogClose asChild>
              <Button>閉じる</Button>
            </DialogClose>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
