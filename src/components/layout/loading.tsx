import { Loader2 } from 'lucide-react';

export function Loading() {
  return (
    <div className="fixed z-[100] w-[160px] h-[160px] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-black/70 flex flex-col items-center justify-center rounded-3xl">
      <Loader2 className="w-[64px] h-[64px] text-white animate-spin" />
      <p className="text-white mt-2">ロード中...</p>
    </div>
  );
}

export function TextLoading({ children }: { children: React.ReactNode }) {
  return (
    <div className="fixed z-[100] w-[160px] h-[160px] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-black/70 flex flex-col items-center justify-center rounded-3xl">
      <Loader2 className="w-[64px] h-[64px] text-white animate-spin" />
      <p className="text-sm text-white mt-2">{children}</p>
    </div>
  );
}
