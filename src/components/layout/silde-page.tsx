import TopBar from '@/components/layout/top-bar';
import { Scroll<PERSON><PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useVirtualKeyboardEvent } from '@/hooks/use-virtual-keyboard-event';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';
import * as React from 'react';
import { Drawer as DrawerPrimitive } from 'vaul';

const Drawer = ({
  shouldScaleBackground = true,
  ...props
}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (
  <DrawerPrimitive.Root shouldScaleBackground={shouldScaleBackground} {...props} />
);

const DrawerOverlay = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Overlay
    ref={ref}
    className={cn('fixed inset-0 z-50 bg-black/80', className)}
    {...props}
  />
));

const DrawerContent = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DrawerPrimitive.Portal>
    <DrawerOverlay />
    <DrawerPrimitive.Content
      ref={ref}
      className={cn('fixed inset-x-0 bottom-0 z-50 flex h-auto flex-col bg-card', className)}
      {...props}
    >
      {children}
    </DrawerPrimitive.Content>
  </DrawerPrimitive.Portal>
));

const DrawerTitle = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Title ref={ref} className="sr-only" {...props} />
));

function DrawerDescription({
  className,
  ...props
}: React.ComponentProps<typeof DrawerPrimitive.Description>) {
  return (
    <DrawerPrimitive.Description data-slot="drawer-description" className="sr-only" {...props} />
  );
}

type SlidePageProps = {
  title?: string;
  content?: React.ReactNode;
  hideTopBar?: boolean;
  isOpen?: boolean;
  isOverAll?: boolean;
  enableClose?: boolean;
  enableBack?: boolean;
  slideFrom?: 'bottom' | 'right';
  onClose?: () => void;
};

export default function SlidePage({
  title = '',
  content,
  hideTopBar = false,
  isOpen = false,
  enableClose = false,
  enableBack = true,
  slideFrom,
  onClose,
}: SlidePageProps) {
  return (
    <Drawer open={isOpen} onOpenChange={onClose} direction={slideFrom} handleOnly={true}>
      <DrawerTitle>{title}</DrawerTitle>
      <DrawerDescription>{title}</DrawerDescription>
      <DrawerContent className="height-auto-important">
        <Content
          title={title}
          hideTopBar={hideTopBar}
          onClose={onClose}
          enableBack={enableBack}
          enableClose={enableClose}
        >
          {content}
        </Content>
      </DrawerContent>
    </Drawer>
  );
}

function Content({
  title,
  children,
  hideTopBar,
  onClose,
  enableBack,
  enableClose,
}: {
  title: string;
  children: React.ReactNode;
  hideTopBar?: boolean;
  onClose?: () => void;
  enableBack?: boolean;
  enableClose?: boolean;
}) {
  const safeArea = useSafeArea();
  const [safeHeight, setSafeHeight] = useState(0);
  const virtualKeyboardInfo = useVirtualKeyboardEvent();
  const [scrollAreaHeight, setScrollAreaHeight] = useState<string>('');

  useEffect(() => {
    setSafeHeight((safeArea.top ?? 0) + (hideTopBar ? 0 : 48));
  }, [hideTopBar, safeArea]);

  useEffect(() => {
    const topbarHeight = hideTopBar ? 0 : 48;
    const scrollAreaTop = topbarHeight + safeArea.top;
    setScrollAreaHeight(`${virtualKeyboardInfo.currentHeight - scrollAreaTop}px`);
  }, [safeArea, virtualKeyboardInfo]);

  return (
    <>
      {!hideTopBar && (
        <TopBar
          title={title}
          onBack={onClose}
          onClose={onClose}
          enableBack={enableBack}
          enableClose={enableClose}
        />
      )}
      {hideTopBar && <div style={{ height: safeHeight }} className="bg-card" />}
      <ScrollArea className="bg-card" style={{ height: scrollAreaHeight }} type="auto">
        {children}
        <ScrollBar orientation="vertical" />
      </ScrollArea>
    </>
  );
}
