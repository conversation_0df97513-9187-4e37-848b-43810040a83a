import { useSafeArea } from '@/hooks/use-safe-area';
import type { OrganizerInfoBean } from '@/types/home-data';
import { IconButtonWithBadge } from '../ui/icon-button';
interface HomeHeaderProps {
  organInfo: OrganizerInfoBean;
  onRightClick?: () => void;
  onLeftClick?: () => void;
}
const HomeHeader: React.FC<HomeHeaderProps> = ({ organInfo, onRightClick, onLeftClick }) => {
  const safeArea = useSafeArea();
  const safeTop = safeArea.top ?? 0;
  return (
    <>
      <div className="mt-[48px]" style={{ height: safeTop }} />
      <div
        style={{ paddingTop: safeTop }}
        className="fixed top-0 left-0 right-0 z-50 bg-white rounded-b-[16px]"
      >
        <div className="bg-white shadow px-4 h-[72px] flex justify-between items-center rounded-b-[16px]">
          <IconButtonWithBadge
            icon={'/images/header/heart.svg'}
            text={''}
            badgeCount={0}
            onClick={onRightClick}
            visible={false}
          />
          {organInfo.iconUrl ? (
            <img src={organInfo.iconUrl} alt="" className="mr-2 h-[48px] w-auto" />
          ) : (
            <span className="text-black	text-base font-bold mr-2">
              {organInfo.organizerName || '-'}
            </span>
          )}
          <IconButtonWithBadge
            icon={'/images/header/notice.svg'}
            text={''}
            badgeCount={100}
            onClick={onLeftClick}
            visible={false}
          />
        </div>
      </div>
    </>
  );
};

export default HomeHeader;
