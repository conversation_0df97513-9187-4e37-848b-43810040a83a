'use client';

import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { ChevronLeft, X } from 'lucide-react';

export default function TopBar({
  title,
  className,
  onBack,
  onClose,
  enableBack = true,
  enableClose = false,
  rightIcon,
  rightIconClick,
  hideShadow = false,
}: {
  title: string;
  className?: string;
  enableBack?: boolean;
  enableClose?: boolean;
  rightIcon?: React.ReactNode;
  onBack?: () => void;
  onClose?: () => void;
  rightIconClick?: () => void;
  hideShadow?: boolean;
}) {
  const router = useRouter();
  const safeArea = useSafeArea();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };
  const handleRightIcon = () => {
    if (rightIconClick) {
      rightIconClick();
    }
  };
  const safeTop = safeArea.top ?? 0;

  return (
    <>
      <div className="mt-[48px]" style={{ height: safeTop }} />
      <div
        style={{ paddingTop: safeTop }}
        className={cn(
          'fixed top-0 left-0 right-0 z-50 bg-card',
          hideShadow ? '' : 'shadow-sm',
          className,
        )}
      >
        {/* 原有的TopBar内容 */}
        <div className="flex h-[48px] items-center relative">
          {enableBack && (
            <button
              type="button"
              onClick={handleBack}
              aria-label="back"
              className="absolute left-5"
            >
              <ChevronLeft size={32} />
            </button>
          )}
          {enableClose && (
            <button
              type="button"
              onClick={handleClose}
              aria-label="close"
              className="absolute right-5"
            >
              <X size={24} />
            </button>
          )}
          {rightIcon && (
            <button
              type="button"
              onClick={handleRightIcon}
              aria-label="close"
              className="absolute right-5"
            >
              {rightIcon}
            </button>
          )}
          <div className="flex-1 text-lg font-bold text-center">{title}</div>
        </div>
      </div>
    </>
  );
}
