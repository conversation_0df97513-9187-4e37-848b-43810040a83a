'use client';

import { ScrollArea } from '@/components/shared/scroll-area';
import { useRouter } from '@/hooks/use-next-navigation';
import { useDevModeStore } from '@/store/dev-mode';
import { StethoscopeIcon } from 'lucide-react';
import { useState } from 'react';
import { Button } from '../shared/button';

export default function TestEnter() {
  const router = useRouter();
  const { devMode, logs, clearLogs } = useDevModeStore();
  const [isOpen, setIsOpen] = useState(false);
  const hadleGoToTestPage = () => {
    setIsOpen(false);
    router.push('/test-page');
  };
  return (
    <>
      {devMode && (
        <Button
          variant="icon"
          className="fixed top-[200px] left-4 z-[60] bg-primary w-10 h-10 text-white"
          onClick={() => setIsOpen(!isOpen)}
        >
          <StethoscopeIcon />
        </Button>
      )}
      {isOpen && (
        <div className="fixed bottom-0 left-0 right-0 h-full bg-white z-[59]">
          <Button className="mx-4 mt-[90px]" onClick={hadleGoToTestPage}>
            Go to test page
          </Button>
          <Button className="mx-4 mt-[90px]" onClick={() => clearLogs()}>
            Clear logs
          </Button>
          <ScrollArea className="h-[calc(100vh-150px)]">
            <div className="flex flex-col gap-2 p-4 border-t border-gray-200 text-wrap break-all">
              {logs.map((log) => (
                <div key={log.index}>
                  <div className=" bg-gray-5 rounded-md px-2 py-1">
                    [{String(log.index).padStart(3, '0')}] {log.datetime}
                    <br />
                    {log.path}
                  </div>
                  <div className="my-2">{log.message}</div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}
    </>
  );
}
