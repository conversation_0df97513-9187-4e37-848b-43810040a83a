// components/ui/icon-button-with-badge.tsx
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface IconButtonWithBadgeProps {
  icon: React.ReactNode | string;
  text: string;
  badgeCount?: number;
  onClick?: () => void;
  className?: string;
  variant?: "default" | "outline" | "ghost" | "link";
  visible?: boolean; // 添加 visible 属性
}

export function IconButtonWithBadge({
  icon,
  text,
  badgeCount = 0,
  onClick,
  className,
  variant = "ghost",
  visible = true, // 默认值设为 true
}: IconButtonWithBadgeProps) {
  return (
    <div className="relative">
      <Button
        variant={variant}
        size="sm"
        className={cn("flex flex-col items-center h-auto p-2", className, !visible && "invisible")}
        onClick={onClick}
      >
        <div className="relative">
          {typeof icon === "string" ? (
            // 如果是字符串，则渲染为 <img>
            <img src={icon} alt="icon" className="mr-1" />
          ) : (
            // 否则直接渲染 ReactNode
            icon
          )}
          {badgeCount > 0 && (
            // <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center">
            //   {badgeCount > 99 ? "99+" : badgeCount}
            // </Badge>
            <Badge className="absolute -top-0.5 h-2.5 w-2.5 rounded-full p-0 bg-red-500 border-2 border-background" />
          )}
        </div>

        {text.length > 0 ? <span className="text-xs mt-1">{text}</span> : <></>}
      </Button>
    </div>
  );
}