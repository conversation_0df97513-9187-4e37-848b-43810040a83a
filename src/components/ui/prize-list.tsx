import { APP_TEXT } from '@/const/text/app';
import { TextButton } from '@/components/shared/text-button'; // 修复TextButton的导入路径
import { Check } from 'lucide-react';

// 根据WinPrize接口调整类型定义
interface Prize {
  id: number; // 改为number类型，匹配WinPrize.prizeId
  name: string; // 对应WinPrize.prizeNm
  description?: string; // 可选字段
  image?: string; // 对应WinPrize.prizeImageFile，可为空
  type: 'digital' | 'physical'; // 需要映射WinPrize.prizeType
  deadline?: string; // 对应WinPrize.pickUpDeadline
  status?: 'claimed' | 'expired' | 'available'; // 状态映射
}

interface PrizeListProps {
  prizes: Prize[];
  onPrizeReceive?: (prizeId: number) => void; // 使用number类型
}

export default function PrizeList({ prizes, onPrizeReceive }: PrizeListProps) {
  // 处理奖品领取
  const handlePrizeReceive = (prizeId: number) => {
    onPrizeReceive?.(prizeId);
  };
  
  // 空状态处理
  if (!prizes || prizes.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {APP_TEXT.LOTTERY.NO_SELECTED_PRIZE}
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {prizes.map((prize) => {
        // 根据prizeType添加不同样式
        const isDigital = prize.type === 'digital';
        
        return (
          <div key={prize.id} className={`bg-card rounded-lg p-4 ${isDigital ? 'flex-col' : 'flex-row'} flex items-center gap-3`}>
            {prize.image && (
              <img 
                className="w-14 h-14 rounded-md flex-shrink-0" 
                src={prize.image} 
                alt={prize.name} 
              />
            )}
            <div className="flex-1">
              <h3 className="text-base font-normal text-foreground">{prize.name}</h3>
              <p className="text-sm text-muted-foreground">{prize.description}</p>
              
              {/* 数字奖品特有内容 */}
              {isDigital && prize.deadline && (
                <p className="text-sm text-muted-foreground mt-1">
                  {APP_TEXT.LOTTERY.CLAIM_PERIOD}：{prize.deadline}
                </p>
              )}
              
              {isDigital && prize.status === 'expired' && (
                <p className="text-sm text-destructive-foreground mt-1">
                  {APP_TEXT.LOTTERY.CLAIM_EXPIRED}
                </p>
              )}
            </div>
            
            {/* 数字奖品按钮 */}
            {isDigital && (
              <div className="flex-shrink-0">
                <TextButton
                  disabled={prize.status === 'claimed' || prize.status === 'expired'}
                  onClick={() => handlePrizeReceive(prize.id)}
                >
                  {prize.status === 'claimed' ? (
                    APP_TEXT.LOTTERY.RECEIVED
                  ) : prize.status === 'expired' ? (
                    APP_TEXT.LOTTERY.RECEIVE
                  ) : (
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-primary" />
                      <span>{APP_TEXT.LOTTERY.RECEIVED}</span>
                    </div>
                  )}
                </TextButton>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}