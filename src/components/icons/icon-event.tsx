import type React from 'react';

interface IconEventProps {
  /**
   * アイコンのサイズ、デフォルトは14
   */
  size?: number;
  /**
   * 追加のCSSクラス名
   */
  className?: string;
  /**
   * その他のSVG属性
   */
  [key: string]: any;
}

const IconEvent: React.FC<IconEventProps> = ({ size = 14, className = '', ...props }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M11.7218 6.76953C11.7218 7.6387 11.0159 8.34453 10.1468 8.34453C9.27761 8.34453 8.57178 7.6387 8.57178 6.76953"
        fill="currentColor"
      />
      <path
        d="M5.42173 6.76953C5.42173 7.6387 4.7159 8.34453 3.84673 8.34453C2.97756 8.34453 2.27173 7.6387 2.27173 6.76953"
        fill="currentColor"
      />
      <path
        d="M8.57163 6.76953C8.57163 7.6387 7.8658 8.34453 6.99663 8.34453C6.12746 8.34453 5.42163 7.6387 5.42163 6.76953"
        fill="currentColor"
      />
      <path
        d="M6.99967 3.74219C5.41592 6.00844 2.27759 6.76677 2.27759 6.76677H11.7247C11.7247 6.76677 8.58634 6.00844 7.00259 3.74219H6.99967Z"
        fill="currentColor"
      />
      <path d="M6.99976 3.74212V0.991699L9.96892 2.36545L6.99976 3.74212Z" fill="currentColor" />
      <path
        d="M6.96212 9.41779C5.7867 9.41779 4.69004 9.07945 3.75962 8.50195L2.57837 12.8595H11.3459L10.1646 8.50195C9.23129 9.07945 8.13754 9.41779 6.96212 9.41779Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconEvent;
