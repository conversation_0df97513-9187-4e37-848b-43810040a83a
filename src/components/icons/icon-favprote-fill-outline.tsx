import type React from 'react';

interface IconFavoriteOutlineProps {
  /**
   * アイコンのサイズ、デフォルトは20
   */
  size?: number;
  /**
   * 追加のCSSクラス名
   */
  className?: string;
  /**
   * その他のSVG属性
   */
  [key: string]: any;
}

const IconFavoriteOutline: React.FC<IconFavoriteOutlineProps> = ({
  size = 20,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M10 4.94609L11.4292 7.84193L11.7208 8.42943L12.3708 8.52526L15.5667 8.98776L13.2542 11.2419L12.7833 11.7003L12.8958 12.3461L13.4417 15.5294L10.5833 14.0253L10 13.7211L9.41667 14.0253L6.55833 15.5294L7.10417 12.3461L7.21667 11.7003L6.74583 11.2419L4.43333 8.98776L7.62917 8.52526L8.27917 8.42943L8.57083 7.84193L10 4.94609ZM10 2.12109L7.45 7.28776L1.75 8.11693L5.875 12.1378L4.9 17.8169L10 15.1336L15.1 17.8169L14.125 12.1378L18.25 8.11693L12.55 7.28776L10 2.12109Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconFavoriteOutline;
