import type React from 'react';

interface IconFavoriteFillProps {
  /**
   * アイコンのサイズ、デフォルトは24
   */
  size?: number;
  /**
   * 追加のCSSクラス名
   */
  className?: string;
  /**
   * その他のSVG属性
   */
  [key: string]: any;
}

const IconFavoriteFill: React.FC<IconFavoriteFillProps> = ({
  size = 24,
  className = '',
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M11.9996 2.54492L15.0596 8.74492L21.8996 9.73992L16.9496 14.5649L18.1196 21.3799L11.9996 18.1599L5.87961 21.3799L7.04961 14.5649L2.09961 9.73992L8.93961 8.74492L11.9996 2.54492Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconFavoriteFill;
