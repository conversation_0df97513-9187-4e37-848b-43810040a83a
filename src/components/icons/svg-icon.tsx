import { COLORS } from '@/const/colors';

export function PointIcon({
  size = 20,
  fill = COLORS.primary.DEFAULT,
}: {
  size?: number;
  fill?: string;
}) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_66307_15183)">
        <path
          d="M10 19.375C4.82917 19.375 0.625 15.1708 0.625 10C0.625 4.82917 4.82917 0.625 10 0.625C15.1708 0.625 19.375 4.82917 19.375 10C19.375 15.1708 15.1708 19.375 10 19.375ZM10 1.875C5.52083 1.875 1.875 5.52083 1.875 10C1.875 14.4792 5.52083 18.125 10 18.125C14.4792 18.125 18.125 14.4792 18.125 10C18.125 5.52083 14.4792 1.875 10 1.875Z"
          fill={fill}
        />
        <path
          d="M7.09998 5.37109H10.6083C11.0958 5.37109 11.5583 5.41693 11.9958 5.50443C12.4291 5.59193 12.8125 5.74609 13.1375 5.96276C13.4625 6.17943 13.7208 6.47526 13.9125 6.84193C14.1041 7.21276 14.2 7.67526 14.2 8.23359C14.2 8.79193 14.1125 9.24609 13.9333 9.62109C13.7541 9.99609 13.5125 10.2919 13.2 10.5128C12.8875 10.7336 12.5208 10.8919 12.0916 10.9878C11.6666 11.0794 11.2041 11.1294 10.7041 11.1294H9.18331V14.8086H7.10414V5.37109H7.09998ZM9.17914 9.37109H10.5666C10.7541 9.37109 10.9333 9.35443 11.1083 9.31693C11.2833 9.27943 11.4375 9.22109 11.575 9.13776C11.7125 9.05443 11.825 8.93776 11.9083 8.79193C11.9916 8.64609 12.0333 8.46276 12.0333 8.23776C12.0333 7.99609 11.9791 7.80443 11.8666 7.65859C11.7541 7.51276 11.6125 7.40026 11.4416 7.31693C11.2666 7.23776 11.075 7.18776 10.8625 7.16276C10.65 7.14193 10.4458 7.12943 10.25 7.12943H9.18331V9.37109H9.17914Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_66307_15183">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function StampIcon({
  size = 20,
  fill = COLORS.primary.DEFAULT,
}: { size?: number; fill?: string }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.2625 13.6207H14.5792V13.2707C14.5792 12.3082 13.7958 11.5249 12.8333 11.5249H11.5667C11.5917 10.3874 11.75 7.8249 12.6417 5.5249C12.7958 5.16657 12.8708 4.7874 12.8708 4.3999C12.8708 2.81657 11.5833 1.5249 9.99582 1.5249C8.40832 1.5249 7.12082 2.8124 7.12082 4.3999C7.12082 4.79157 7.19998 5.1749 7.35415 5.52907C7.36248 5.5499 8.18332 7.56657 8.26665 11.5207H7.16248C6.19998 11.5207 5.41665 12.3041 5.41665 13.2666V13.6166H3.73332C2.98748 13.6166 2.37915 14.2249 2.37915 14.9707V17.1166C2.37915 17.8624 2.98748 18.4707 3.73332 18.4707H16.2625C17.0083 18.4707 17.6167 17.8624 17.6167 17.1166V14.9707C17.6167 14.2249 17.0083 13.6166 16.2625 13.6166V13.6207ZM8.50832 5.04157C8.42082 4.8374 8.37498 4.62074 8.37498 4.3999C8.37498 3.50407 9.10415 2.7749 9.99998 2.7749C10.8958 2.7749 11.625 3.50407 11.625 4.3999C11.625 4.62074 11.5792 4.8374 11.4917 5.04157L11.4833 5.0624C10.5042 7.57907 10.3375 10.3041 10.3167 11.5249H9.52082C9.43748 7.28324 8.54998 5.1374 8.50832 5.04157ZM6.66665 13.2707C6.66665 12.9957 6.89165 12.7749 7.16248 12.7749H12.8333C13.1083 12.7749 13.3292 12.9999 13.3292 13.2707V13.6207H6.66665V13.2707ZM16.3667 17.1207C16.3667 17.1791 16.3208 17.2249 16.2625 17.2249H3.73748C3.67915 17.2249 3.63332 17.1791 3.63332 17.1207V14.9749C3.63332 14.9166 3.67915 14.8707 3.73748 14.8707H16.2667C16.325 14.8707 16.3708 14.9166 16.3708 14.9749V17.1207H16.3667Z"
        fill={fill}
      />
    </svg>
  );
}

export function WalkingIcon({
  fill = COLORS.primary.DEFAULT,
  size = 20,
}: {
  fill?: string;
  size?: number;
}) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.6292 4.7582C12.6579 4.7582 13.4917 3.92433 13.4917 2.8957C13.4917 1.86707 12.6579 1.0332 11.6292 1.0332C10.6006 1.0332 9.76672 1.86707 9.76672 2.8957C9.76672 3.92433 10.6006 4.7582 11.6292 4.7582Z"
        fill={fill}
      />
      <path
        d="M8.14163 18.5123L6.57079 17.9581L8.66246 12.0289L9.30413 7.89143L8.22079 8.6456L7.67496 11.3539L6.04163 11.0248L6.65413 7.98726C6.69579 7.77893 6.81663 7.59143 6.99579 7.4706L9.95413 5.4081C10.2291 5.21643 10.5875 5.2081 10.8708 5.3831C11.1541 5.5581 11.3041 5.88726 11.2541 6.21643L10.3 12.3581C10.2916 12.4081 10.2791 12.4581 10.2625 12.5081L8.14579 18.5123H8.14163Z"
        fill={fill}
      />
      <path
        d="M13.8958 18.4044L12.2291 18.321L12.4083 14.7002L10.0125 12.996L10.9791 11.6377L13.7458 13.6085C13.9791 13.7752 14.1083 14.046 14.0958 14.3294L13.8958 18.4044Z"
        fill={fill}
      />
      <path
        d="M11.4403 7.87625L10.4215 9.19531L13.8577 11.8492L14.8764 10.5301L11.4403 7.87625Z"
        fill={fill}
      />
      <path
        d="M10.2374 12.9957C10.1791 12.9957 10.1166 12.9957 10.0541 12.9832C9.25411 12.8832 8.68744 12.154 8.78744 11.354L9.40411 6.39986C9.50411 5.59986 10.2333 5.03736 11.0291 5.13319C11.8291 5.23319 12.3958 5.96236 12.2958 6.76236L11.6791 11.7165C11.5874 12.454 10.9583 12.9957 10.2333 12.9957H10.2374Z"
        fill={fill}
      />
    </svg>
  );
}

export function PinIcon({
  size = 19,
  fill = COLORS.primary.DEFAULT,
}: { size?: number; fill?: string }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.5762 15.6374L12.65 13.3124L11.84 11.3249L13.9437 8.53111L15.095 8.87986L16.1487 7.82611C16.595 7.37986 16.595 6.65986 16.1487 6.21361L12.0125 2.07736C11.5662 1.63111 10.8462 1.63111 10.4 2.07736L9.34622 3.13111L9.69497 4.28236L6.90122 6.38611L4.91372 5.57611L2.58872 7.64986L6.18872 11.2499L2.53247 14.9061L2.58122 15.6561L3.33122 15.7049L6.98747 12.0486L10.5875 15.6486L10.5762 15.6374ZM5.13122 6.87361L7.06247 7.66111L10.9962 4.70236L10.6137 3.44986L11.21 2.86861L15.3462 7.02736L14.7725 7.60111L13.5162 7.21861L10.5575 11.1524L11.345 13.0836L10.5312 13.9949L4.21997 7.68736L5.13122 6.87361Z"
        fill={fill}
      />
    </svg>
  );
}

export function UnpinIcon({
  size = 19,
  fill = COLORS.primary.DEFAULT,
}: { size?: number; fill?: string }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.31494 5.31004L9.11744 6.11254L10.9962 4.70254L10.6137 3.45004L11.2099 2.86879L15.3462 7.02754L14.7724 7.60129L13.5162 7.21879L12.1062 9.09754L12.9087 9.90004L13.9437 8.52379L15.0949 8.87254L16.1487 7.81879C16.5949 7.37254 16.5949 6.65254 16.1487 6.20629L12.0124 2.07004C11.5662 1.62379 10.8424 1.62379 10.3999 2.07004L9.34619 3.12379L9.69494 4.27504L8.31869 5.31004H8.31494Z"
        fill={fill}
      />
      <path
        d="M11.2212 13.2225L10.5274 13.9987L4.2199 7.68748L4.9924 6.99748L4.1974 6.19873L2.58115 7.64248L6.18115 11.2425L2.5249 14.8987L2.5699 15.645L3.3199 15.6937L6.97615 12.0375L10.5762 15.6375L12.0162 14.0212L11.2212 13.2225Z"
        fill={fill}
      />
      <path
        d="M2.83407 1.53849L2.03857 2.33398L16.1639 16.4593L16.9594 15.6638L2.83407 1.53849Z"
        fill={fill}
      />
    </svg>
  );
}

export function TimeIcon({
  fill = COLORS.primary.DEFAULT,
  size = 21,
}: { fill?: string; size?: number }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.5 18.1998C5.97922 18.1998 2.30005 14.5206 2.30005 9.99981C2.30005 5.47897 5.97922 1.7998 10.5 1.7998C15.0209 1.7998 18.7001 5.47897 18.7001 9.99981C18.7001 14.5206 15.0209 18.1998 10.5 18.1998ZM10.5 3.0498C6.66672 3.0498 3.55005 6.16647 3.55005 9.99981C3.55005 13.8331 6.66672 16.9498 10.5 16.9498C14.3334 16.9498 17.45 13.8331 17.45 9.99981C17.45 6.16647 14.3334 3.0498 10.5 3.0498Z"
        fill={fill}
      />
      <path
        d="M12.3708 12.7583L9.875 10.2583V4.7583H11.125V9.74163L13.2542 11.8791L12.3708 12.7583Z"
        fill={fill}
      />
    </svg>
  );
}

export function StepIcon({
  fill = COLORS.primary.DEFAULT,
  size = 21,
}: { fill?: string; size?: number }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.2333 17.8999H13.7292C12.5125 17.8999 11.3708 17.4249 10.5083 16.5666L3.20001 9.25827C2.76667 8.82493 2.52917 8.24993 2.52917 7.63327C2.52917 7.0166 2.76667 6.44577 3.20001 6.00827L7.44167 1.7666L7.87501 2.34993C8.57084 3.2916 8.53751 3.86244 8.43751 4.4166C8.37917 4.73744 8.34167 4.94993 9.00834 5.67077C10.2375 6.99577 11.5667 6.09994 11.7125 5.99577L12.3583 5.5291L12.6583 6.2666L14.8125 11.6249C15.0333 12.1749 15.5625 12.5333 16.1583 12.5333H16.2292C17.4625 12.5333 18.4667 13.5374 18.4667 14.7708V15.6666C18.4667 16.8999 17.4625 17.9041 16.2292 17.9041L16.2333 17.8999ZM7.22501 3.75827L4.08751 6.89577C3.89167 7.0916 3.77917 7.3541 3.77917 7.63327C3.77917 7.91243 3.88751 8.17493 4.08751 8.37077L11.3958 15.6791C12.0208 16.3041 12.85 16.6458 13.7292 16.6458H16.2333C16.7792 16.6458 17.2208 16.2041 17.2208 15.6583V14.7624C17.2208 14.2166 16.7792 13.7749 16.2333 13.7749H16.1625C15.0542 13.7749 14.0708 13.1124 13.6583 12.0833L11.7625 7.37494C10.8042 7.7666 9.35001 7.8666 8.09167 6.5166C7.07917 5.42494 7.09167 4.8166 7.20834 4.18743C7.23751 4.03744 7.25834 3.90827 7.22501 3.74993V3.75827Z"
        fill={fill}
      />
      <path
        d="M12.4731 7.97349L11.0093 8.49561L11.4292 9.67296L12.893 9.15084L12.4731 7.97349Z"
        fill={fill}
      />
      <path
        d="M13.1372 10.0515L11.7401 10.5498L12.16 11.7272L13.5572 11.2288L13.1372 10.0515Z"
        fill={fill}
      />
      <path
        d="M17.8334 15.8373L13.9292 15.7623C13.2084 15.7498 12.5292 15.4623 12.0209 14.9498L3.65002 6.61234L4.53336 5.729L12.9042 14.0665C13.1834 14.3457 13.5584 14.504 13.9542 14.5123L17.8584 14.5873L17.8334 15.8373Z"
        fill={fill}
      />
    </svg>
  );
}

export function StarIcon({
  size = 12,
  fill = '#EFAF00',
  stroke = 'none',
}: {
  size?: number;
  fill?: string;
  stroke?: string;
}) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill={fill}
      stroke={stroke}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M6.0001 0.484375L7.7851 4.10104L11.7751 4.68146L8.8876 7.49604L9.5701 11.4715L6.0001 9.59313L2.4301 11.4715L3.1126 7.49604L0.225098 4.68146L4.2151 4.10104L6.0001 0.484375Z" />
    </svg>
  );
}

export function FilterIcon({ size = 20, fill = 'black' }: { size?: number; fill?: string }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.97994 5.26961C9.64994 4.08961 8.56994 3.22461 7.28994 3.22461C6.00994 3.22461 4.92994 4.09461 4.59994 5.26961H1.56494V6.76961H4.59994C4.92994 7.94961 6.00994 8.81461 7.28994 8.81461C8.56994 8.81461 9.64994 7.94461 9.97994 6.76961H22.3199V5.26961H9.97994ZM7.28994 7.31461C6.57494 7.31461 5.99494 6.73461 5.99494 6.01961C5.99494 5.30461 6.57494 4.72461 7.28994 4.72461C8.00494 4.72461 8.58494 5.30461 8.58494 6.01961C8.58494 6.73461 8.00494 7.31461 7.28994 7.31461Z"
        fill={fill}
      />
      <path
        d="M17.0049 9.70508C15.7249 9.70508 14.6449 10.5751 14.3149 11.7501H1.56494V13.2501H14.3149C14.6449 14.4301 15.7249 15.2951 17.0049 15.2951C18.2849 15.2951 19.3649 14.4251 19.6949 13.2501H22.3199V11.7501H19.6949C19.3649 10.5701 18.2849 9.70508 17.0049 9.70508ZM17.0049 13.7951C16.2899 13.7951 15.7099 13.2151 15.7099 12.5001C15.7099 11.7851 16.2899 11.2051 17.0049 11.2051C17.7199 11.2051 18.2999 11.7851 18.2999 12.5001C18.2999 13.2151 17.7199 13.7951 17.0049 13.7951Z"
        fill={fill}
      />
      <path
        d="M10.2099 16.1846C8.92994 16.1846 7.84994 17.0546 7.51994 18.2296H1.56494V19.7296H7.51994C7.84994 20.9096 8.92994 21.7746 10.2099 21.7746C11.4899 21.7746 12.5699 20.9046 12.8999 19.7296H22.3149V18.2296H12.8999C12.5699 17.0496 11.4899 16.1846 10.2099 16.1846ZM10.2099 20.2796C9.49494 20.2796 8.91494 19.6996 8.91494 18.9846C8.91494 18.2696 9.49494 17.6896 10.2099 17.6896C10.9249 17.6896 11.5049 18.2696 11.5049 18.9846C11.5049 19.6996 10.9249 20.2796 10.2099 20.2796Z"
        fill={fill}
      />
    </svg>
  );
}

export function EventIcon({
  size = 24,
  fill = '#15B47F',
}: { size?: number; fill?: string; stroke?: string }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.7218 6.76953C11.7218 7.6387 11.0159 8.34453 10.1468 8.34453C9.27761 8.34453 8.57178 7.6387 8.57178 6.76953"
        fill={fill}
      />
      <path
        d="M5.42179 6.76953C5.42179 7.6387 4.71596 8.34453 3.84679 8.34453C2.97762 8.34453 2.27179 7.6387 2.27179 6.76953"
        fill={fill}
      />
      <path
        d="M8.57181 6.76953C8.57181 7.6387 7.86598 8.34453 6.99681 8.34453C6.12765 8.34453 5.42181 7.6387 5.42181 6.76953"
        fill={fill}
      />
      <path
        d="M6.99973 3.74219C5.41598 6.00844 2.27765 6.76677 2.27765 6.76677H11.7247C11.7247 6.76677 8.5864 6.00844 7.00265 3.74219H6.99973Z"
        fill={fill}
      />
      <path d="M6.99969 3.74212V0.991699L9.96886 2.36545L6.99969 3.74212Z" fill={fill} />
      <path
        d="M6.96206 9.41779C5.78664 9.41779 4.68997 9.07945 3.75956 8.50195L2.57831 12.8595H11.3458L10.1646 8.50195C9.23122 9.07945 8.13748 9.41779 6.96206 9.41779Z"
        fill={fill}
      />
    </svg>
  );
}
