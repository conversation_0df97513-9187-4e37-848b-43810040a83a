let userAgent = '';

if (typeof window !== 'undefined') {
  userAgent = window.navigator.userAgent;
}

export function isMobile() {
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
    userAgent.toLowerCase(),
  );
}

export function isIOS() {
  return /iPad|iPhone|iPod/.test(userAgent);
}

export function isAndroid() {
  return /android/i.test(userAgent);
}

export function isAndroidByHeader(headerUserAgent: string | null | undefined) {
  return /android/i.test(headerUserAgent || '');
}

export function isIOSByHeader(headerUserAgent: string | null | undefined) {
  return /iPad|iPhone|iPod/.test(headerUserAgent || '');
}

/**
 * より信頼性の高いモバイルデバイス検出
 * ユーザーエージェント、画面サイズ、タッチサポートを組み合わせて判断
 */
export function isMobileReliable() {
  // サーバーサイドレンダリング時の処理
  if (typeof window === 'undefined') {
    return false;
  }

  // ユーザーエージェントによる検出
  const userAgentMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
    userAgent.toLowerCase(),
  );

  // 画面サイズによる検出（768px以下をモバイルとみなす）
  const screenWidth = window.screen.width || window.innerWidth;
  const isSmallScreen = screenWidth <= 768;

  // タッチサポートの確認
  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  // デスクトップブラウザのタッチデバイスを除外するための追加チェック
  const isDesktopTouch = hasTouchSupport && screenWidth > 1024;

  // モバイルデバイスの判定
  // ユーザーエージェントがモバイル OR (小画面かつタッチサポートあり) AND デスクトップタッチでない
  return userAgentMobile || (isSmallScreen && hasTouchSupport && !isDesktopTouch);
}
