import { z } from 'zod';
import { nlog } from './logger';
//TODO: 暂时不用 等后续 8.15后 整理 通知共通类

/**
 * 推送通知参数类型
 */
export const pushNotificationSchema = z.record(z.unknown());

export type PushNotification = z.infer<typeof pushNotificationSchema>;

interface AlertData {
  title: string;
  body: string;
}

interface APSData {
  alert: AlertData;
}

interface NotificationData {
  aps?: APSData;
  page_msg?: string;
  target_url?: string;
}

/**
 * 处理推送通知
 * @param notification 推送通知数据（字符串或 map 对象）
 */
export const handlePushNotification = (notification: string | PushNotification) => {
  try {
    nlog(`收到推送通知: ${JSON.stringify(notification)}`);
    let parsedNotification: NotificationData;

    // 如果是字符串，尝试解析 JSON
    if (typeof notification === 'string') {
      try {
        parsedNotification = JSON.parse(notification);
      } catch (parseError) {
        nlog(`推送通知 JSON 解析失败: ${parseError}`);
        return;
      }
    } else {
      parsedNotification = notification as NotificationData;
    }

    // 解析推送数据
    const parseResult = pushNotificationSchema.safeParse(parsedNotification);
    if (!parseResult.success) {
      nlog(`推送通知数据格式不正确: ${parseResult.error}`);
      return;
    }

    // 处理 map 数据
    const { aps, page_msg, target_url } = parsedNotification;

    // 处理通知内容
    if (aps?.alert) {
      const { title, body } = aps.alert;
      nlog(`通知标题: ${title}`);
      nlog(`通知内容: ${body}`);
    }

    // 处理页面消息
    if (page_msg) {
      nlog(`页面消息: ${page_msg}`);
    }

    // 处理目标 URL
    if (target_url) {
      nlog(`目标 URL: ${target_url}`);
    }
  } catch (error) {
    nlog(`处理推送通知时出错: ${error}`);
  }
};

// 在 window 对象上声明全局函数
declare global {
  interface Window {
    handlePushNotification: (notification: string | PushNotification) => void;
  }
}

// 注册全局函数
if (typeof window !== 'undefined') {
  window.handlePushNotification = handlePushNotification;
}
