export const utf8ArrayToBase64 = (utf8Bytes: Uint8Array): string => {
  return btoa(String.fromCharCode(...utf8Bytes));
};

export const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
};

// Convert numbers to comma separated strings
export const formatNumberWithCommas = (num: number | undefined) => {
  if (!num) return;
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// judge is it empty
export const isEmptyToData = (v: any) => {
  if (v === '' || v === undefined || v === null || v === 'null' || v?.length === 0) {
    return '-';
  }
  return v;
};
