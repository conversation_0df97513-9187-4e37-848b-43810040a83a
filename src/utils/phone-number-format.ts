export function phoneNumberFormat(phoneNumber: string): string {
  // すべての非数字文字を削除
  const cleaned = phoneNumber.replace(/\D/g, '');

  // 携帯電話番号の形式 (11桁: 090/080/070で始まる)
  if (cleaned.length === 11 && /^(090|080|070)/.test(cleaned)) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
  }

  // 固定電話番号の形式 (10桁)
  if (cleaned.length === 10) {
    // 東京 (03) や大阪 (06) など2桁の市外局番
    if (/^(03|06)/.test(cleaned)) {
      return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '$1-$2-$3');
    }
    // その他の3桁市外局番
    return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
  }

  // 固定電話番号の形式 (11桁、4桁市外局番)
  if (cleaned.length === 11 && /^0\d{3}/.test(cleaned)) {
    return cleaned.replace(/(\d{4})(\d{3})(\d{4})/, '$1-$2-$3');
  }

  // どの形式にも一致しない場合、クリーンアップされた数字をそのまま返す
  return cleaned;
}
