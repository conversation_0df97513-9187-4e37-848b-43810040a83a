import { deobfuscate, obfuscate } from '@/utils/obfuscate';

function getItem(name: string) {
  const item = localStorage.getItem(name);
  return item ? deobfuscate(item, 'secret') : null;
}

function setItem(name: string, value: string) {
  const obfuscatedValue = obfuscate(value, 'secret');
  localStorage.setItem(name, obfuscatedValue);
}

function removeItem(name: string) {
  localStorage.removeItem(name);
}

export default {
  getItem,
  setItem,
  removeItem,
};
