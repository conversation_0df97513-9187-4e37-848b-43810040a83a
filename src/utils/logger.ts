/**
 * グローバルログツール
 */

import { sendMessageToNative } from './native-bridge';

// 本番環境かどうかを判断
const isProduction = process.env.NODE_ENV === 'production';

/**
 * ネイティブ端末にログを送信
 * @param message ログメッセージ
 */
const sendToNative = (message: string) => {
  sendMessageToNative({
    type: 'log',
    data: { info: message },
  });
};

/**
 * グローバルログメソッド
 * @param message ログメッセージ
 */
export const nlog = (message: string) => {
  // 本番環境では実行しない
  //TODO: 本番環境でも実行する
  // if (isProduction) {
  //   return;
  // }

  // コンソールに出力
  console.log(message);

  // ネイティブ端末に送信
  sendToNative(message);
};

// window オブジェクトにグローバル関数を宣言
declare global {
  interface Window {
    log: (message: string) => void;
  }
}

// グローバル関数を登録
if (typeof window !== 'undefined') {
  window.log = nlog;
}
