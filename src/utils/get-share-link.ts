import { getOrigin } from './get-host';

export const getShareLink = (query: Record<string, string>) => {
  const queryString = new URLSearchParams(query).toString();
  if (process.env.NEXT_PUBLIC_APP_ENV === 'dev') {
    const origin = 'https://dev-app.kenkomileage-renewal.net';
    return `${origin}/shinkenko?${queryString}`;
  }
  if (process.env.NEXT_PUBLIC_APP_ENV === 'stg') {
    const origin = 'https://app.kmileage-stg.sdpf4hc-ntt.com';
    return `${origin}/shinkenko?${queryString}`;
  }
  return `${getOrigin()}/shinkenko?${queryString}`;
};
