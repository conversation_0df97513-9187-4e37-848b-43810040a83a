import { z } from 'zod';

/**
 * 推送通知参数类型
 */
export const pushNotificationParamsSchema = z.object({
  map: z.record(z.unknown()),
  target_url: z.string().url(),
  page_msg: z.string(),
});

export type PushNotificationParams = z.infer<typeof pushNotificationParamsSchema>;

/**
 * 处理全局 push 通知（需结合全局 Modal 使用）
 * @param params 推送参数
 * @param showModal 显示 Modal 的回调，返回 Promise<boolean>，true 表示确认
 * @param router Next.js 路由对象
 */
export const handleGlobalPushNotification = async (
  params: PushNotificationParams,
  showModal: (msg: string) => Promise<boolean>,
  router: { push: (url: string) => void },
) => {
  // 参数校验
  const parseResult = pushNotificationParamsSchema.safeParse(params);
  //   if (!parseResult.success) {
  //     throw new Error('推送通知参数不合法');
  //   }
  const { map, target_url, page_msg } = params;

  // 显示弹窗，等待用户确认
  const confirmed = await showModal(page_msg);
  if (!confirmed) return;

  // 拼接 query string
  const url = new URL(target_url, window.location.origin);
  for (const [key, value] of Object.entries(map)) {
    url.searchParams.append(key, String(value));
  }

  // 跳转
  router.push(url.toString());
};
