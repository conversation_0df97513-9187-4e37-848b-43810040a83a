export const obfuscate = (str: string, key: string): string => {
  let result = '';
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return btoa(encodeURIComponent(result));
};

export const deobfuscate = (obfuscatedStr: string, key: string): string => {
  try {
    const decoded = decodeURIComponent(atob(obfuscatedStr));
    let result = '';
    for (let i = 0; i < decoded.length; i++) {
      const charCode = decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length);
      result += String.fromCharCode(charCode);
    }
    return result;
  } catch (error) {
    return '';
  }
};
