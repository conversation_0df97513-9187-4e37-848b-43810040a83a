export const distanceFormat = (distance: string | number): string => {
  // 处理空值
  if (!distance && distance !== 0) return '0m';

  // 转换为数字
  const num = Number(distance);

  // 检查有效性
  if (Number.isNaN(num) || num < 0) return '0m';

  // 格式化
  if (num < 1000) {
    return `${Math.round(num)}m`;
  }

  if (num < 10000) {
    return `${(num / 1000).toFixed(1)}km`;
  }

  return `${Math.round(num / 1000)}km`;
};
