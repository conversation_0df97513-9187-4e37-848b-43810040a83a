'use client';

import {
  QueryClient,
  QueryClientProvider as TanstackQueryClientProvider,
} from '@tanstack/react-query';

// グローバルキャッシュ設定
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // キャッシュ時間: 5分 (5 * 60 * 1000 = 300000ms)
      staleTime: 5 * 60 * 1000,
      // ガベージコレクション時間: 10分 (10 * 60 * 1000 = 600000ms)
      gcTime: 10 * 60 * 1000,
      // リトライ回数
      retry: 3,
      // リトライ遅延時間
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // バックグラウンドでの再取得
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      refetchOnMount: true,
    },
    mutations: {
      // ミューテーションのリトライ回数
      retry: 1,
    },
  },
});

export function QueryClientProvider({ children }: { children: React.ReactNode }) {
  return <TanstackQueryClientProvider client={queryClient}>{children}</TanstackQueryClientProvider>;
}
