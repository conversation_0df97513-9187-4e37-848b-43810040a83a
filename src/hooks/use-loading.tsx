'use client';

import { Loading, TextLoading } from '@/components/layout/loading';
import React, { useCallback, useState } from 'react';

interface LoadingData {
  text: string;
}

const LoadingContext = React.createContext<{
  isLoading: boolean;
  data?: LoadingData;
  setIsLoading: (isLoading: boolean) => void;
  setLoading: (open: boolean, data?: LoadingData) => void;
}>({
  isLoading: false,
  setIsLoading: () => {},
  setLoading: () => {},
});

export function LoaderProvider({ children }: { children: React.ReactNode }) {
  const [isLoadingState, setIsLoadingState] = useState(false);
  const [data, setData] = useState<LoadingData>();

  const setLoading = useCallback((open: boolean, data?: LoadingData) => {
    // const stackTrace = new Error().stack;
    // console.log(`loading open: ${open} 呼び出し元: ${stackTrace}`);
    setIsLoadingState(open);
    setData(data);
  }, []);

  const setIsLoading = useCallback((open: boolean) => {
    // const stackTrace = new Error().stack;
    // console.log(`setIsLoading open: ${open} 呼び出し元: ${stackTrace}`);
    setIsLoadingState(open);
    setData(undefined);
  }, []);

  return (
    <LoadingContext.Provider value={{ isLoading: isLoadingState, setIsLoading, setLoading }}>
      {children}
      {isLoadingState && !data?.text && <Loading />}
      {isLoadingState && data?.text && <TextLoading>{data.text}</TextLoading>}
      {isLoadingState && <div className="fixed z-[99] inset-0 bg-white/1" />}
    </LoadingContext.Provider>
  );
}

export const useLoading = () => React.useContext(LoadingContext);
