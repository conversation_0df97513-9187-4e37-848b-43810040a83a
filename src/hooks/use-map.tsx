'use client';

import type { LatLng, MapBounds, Marker } from '@/types/map';
import { APIProvider, useMap as useMapVisGl, useMapsLibrary } from '@vis.gl/react-google-maps';
import { type MutableRefObject, createContext, useContext, useMemo, useRef } from 'react';

const MapContext = createContext<{
  mapRef: MutableRefObject<google.maps.Map | null>;
}>({
  mapRef: { current: null },
});

export function MapProvider({ children }: { children: React.ReactNode }) {
  const mapRef = useRef<google.maps.Map | null>(null);
  return (
    <MapContext.Provider value={{ mapRef }}>
      <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
        <MapInit mapRef={mapRef}>{children}</MapInit>
      </APIProvider>
    </MapContext.Provider>
  );
}

export function MapInit({
  children,
  mapRef,
}: { children: React.ReactNode; mapRef: MutableRefObject<google.maps.Map | null> }) {
  const map = useMapVisGl();
  if (!mapRef.current && map) {
    mapRef.current = map;
  }
  return <>{children}</>;
}

export const useMap = () => useContext(MapContext);

/**
 * 複数のマーカーに基づいてマップのズームレベルと中心点を計算するカスタムフック
 * @param markers マーカーの配列
 * @returns マップの境界情報（中心点とズームレベル）
 */
export function useMapBounds(markers: Marker[]): MapBounds {
  const mapsLibrary = useMapsLibrary('maps');

  return useMemo(() => {
    if (!mapsLibrary) {
      // MapsLibrary が利用できない場合はデフォルト値を返す
      return {
        center: { lat: 35.6762, lng: 139.6503 },
        zoom: 15,
      };
    }

    if (markers.length === 0) {
      // マーカーがない場合はデフォルト値を返す
      return {
        center: { lat: 0, lng: 0 },
        zoom: 15,
      };
    }

    if (markers.length === 1) {
      // マーカーが1つの場合はその位置を中心に、適度なズームレベルを設定
      return {
        center: { lat: Number(markers[0].latitude), lng: Number(markers[0].longitude) },
        zoom: 16,
      };
    }

    // 複数のマーカーがある場合は、境界を計算して適切なズームレベルを決定
    const bounds = new google.maps.LatLngBounds();

    // すべてのマーカーの位置を境界に追加
    for (const marker of markers) {
      bounds.extend({ lat: Number(marker.latitude), lng: Number(marker.longitude) });
    }

    // 境界の中心点を取得
    const center = {
      lat: bounds.getCenter().lat(),
      lng: bounds.getCenter().lng(),
    };

    // 境界の北東と南西の座標を取得
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();

    // 緯度と経度の差分を計算
    const latDiff = Math.abs(ne.lat() - sw.lat());
    const lngDiff = Math.abs(ne.lng() - sw.lng());

    // 最大の差分を使用してズームレベルを計算
    const maxDiff = Math.max(latDiff, lngDiff);

    // より精密なズームレベル計算
    let zoom: number;

    if (maxDiff > 0.1) {
      zoom = 10; // 非常に広い範囲
    } else if (maxDiff > 0.05) {
      zoom = 11; // 広い範囲
    } else if (maxDiff > 0.02) {
      zoom = 12; // 中程度の範囲
    } else if (maxDiff > 0.01) {
      zoom = 13; // やや狭い範囲
    } else if (maxDiff > 0.005) {
      zoom = 14; // 狭い範囲
    } else if (maxDiff > 0.002) {
      zoom = 15; // 非常に狭い範囲
    } else if (maxDiff > 0.001) {
      zoom = 16; // 極めて狭い範囲
    } else if (maxDiff > 0.0005) {
      zoom = 17; // 非常に近い
    } else if (maxDiff > 0.0001) {
      // 0.0001 は 10m の差
      zoom = 18; // 極めて近い
    } else {
      zoom = 19; // 最小範囲
    }
    const offset = 1;
    return { center, zoom: zoom + offset };
  }, [markers, mapsLibrary]);
}

export function getMarkerPosition(marker: Marker, defaultLatLng?: LatLng): LatLng {
  if (!marker.latitude || !marker.longitude) {
    // デフォルト値を返す
    return defaultLatLng || { lat: 35.6762, lng: 139.6503 };
  }
  return { lat: Number(marker.latitude), lng: Number(marker.longitude) };
}
