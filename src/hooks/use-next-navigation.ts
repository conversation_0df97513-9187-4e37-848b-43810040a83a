'use client';
import {
  type ReadonlyURLSearchParams as NextReadonlyURLSearchParams,
  redirect as nextRedirect,
  useParams as nextUseParams,
  usePathname as nextUsePathname,
  useRouter as nextUseRouter,
  useSearchParams as nextUseSearchParams,
} from 'next/navigation';
import { isPathEqual, useNavigation } from './use-navigation';

export const useRouter = () => {
  const navigation = useNavigation();
  const nextRouter = nextUseRouter();

  return {
    ...nextRouter,
    createHistory: navigation.createHistory,
    push: navigation.push,
    back: navigation.back,
    replace: navigation.replace,
    backTo: navigation.backTo,
    getPreviousPath: navigation.getPreviousPath,
    isPathEqual: isPathEqual,
    getOnceMetadata: navigation.getOnceMetadata,
    includePathInHistory: navigation.includePathInHistory,
    getBackPath: navigation.getBackPath,
  };
};

export const usePathname = nextUsePathname;
export const useSearchParams = nextUseSearchParams;
export const useParams = nextUseParams;
export const redirect = nextRedirect;
export type ReadonlyURLSearchParams = NextReadonlyURLSearchParams;
