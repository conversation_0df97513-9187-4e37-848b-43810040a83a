'use client';

import { useEffect, useState } from 'react';

export interface VirtualKeyboardInfo {
  isKeyboardVisible: boolean; // キーボードが表示されているかどうか
  originalHeight: number; // キーボードが表示されていないときのウィンドウの高さ
  currentHeight: number; // キーボードが表示されているときのウィンドウの高さ
}

export const useVirtualKeyboardEvent = () => {
  const [virtualKeyboardInfo, setVirtualKeyboardInfo] = useState<VirtualKeyboardInfo>({
    isKeyboardVisible: false,
    originalHeight: 0,
    currentHeight: 0,
  });

  useEffect(() => {
    const originalHeight = window.innerHeight;

    const handleResize = () => {
      const currentHeight = window.innerHeight;
      const heightChange = originalHeight - currentHeight;
      const isKeyboardVisible = heightChange > 150; // 150px以上の変化をキーボード表示と判定

      setVirtualKeyboardInfo({
        isKeyboardVisible,
        originalHeight,
        currentHeight,
      });
    };

    const handleVisualViewportResize = () => {
      const viewport = window.visualViewport;
      if (!viewport) return;
      const currentHeight = viewport.height;
      const heightChange = originalHeight - currentHeight;
      const isKeyboardVisible = heightChange > 150;

      setVirtualKeyboardInfo({
        isKeyboardVisible,
        originalHeight,
        currentHeight,
      });
    };

    // Visual Viewport APIを使用（より正確）
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportResize);
      handleVisualViewportResize(); // 初期呼び出し
    } else {
      window.addEventListener('resize', handleResize);
      handleResize(); // 初期呼び出し
    }

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleVisualViewportResize);
      } else {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  return virtualKeyboardInfo;
};

export function VirtualKeyboardInfo({
  virtualKeyboardInfo,
}: { virtualKeyboardInfo: VirtualKeyboardInfo }) {
  return (
    <div className="fixed bottom-0 left-0 right-0 h-[100px] bg-red-500">
      {JSON.stringify(virtualKeyboardInfo, null, 2)}
    </div>
  );
}
