'use client';

import { MessageDialog, type MessageDialogProps } from '@/components/layout/message-dialog';
import React, { useCallback, useState } from 'react';

interface MessageDialogData {
  title?: string;
  header?: React.ReactNode;
  content: React.ReactNode;
  footer?: React.ReactNode;
  outSideClickClose?: boolean;
}

const MessageDialogContext = React.createContext<{
  isShow: boolean;
  data?: MessageDialogData;
  setDialog: (isShow: boolean, data?: MessageDialogData) => void;
}>({
  isShow: false,
  setDialog: () => {},
});

export function MessageDialogProvider({ children }: { children: React.ReactNode }) {
  const [isShowState, setIsShowState] = useState(false);
  const [data, setData] = useState<MessageDialogData>();

  const setDialog = useCallback((isShow: boolean, data?: MessageDialogData) => {
    setIsShowState(isShow);
    if (isShow) {
      setData(data);
    }
  }, []);

  return (
    <MessageDialogContext.Provider value={{ isShow: isShowState, setDialog }}>
      {children}
      <MessageDialog
        isOpen={!!(isShowState && data)}
        title={data?.title}
        header={data?.header}
        content={data?.content}
        footer={data?.footer}
        outSideClickClose={data?.outSideClickClose}
        onOpenChange={(isOpen) => setDialog(isOpen)}
      />
    </MessageDialogContext.Provider>
  );
}

export const useMessageDialog = () => React.useContext(MessageDialogContext);
