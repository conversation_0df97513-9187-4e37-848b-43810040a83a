import { riskAPI } from '@/api/modules/health-score-init';
import { type RiskTypeNum, SUPPORT_RISK } from '@/const/app';
import { useLoading } from '@/hooks/use-loading';
import { useDevModeStore } from '@/store/dev-mode';
import { useGlobalStore } from '@/store/global';
import { nlog } from '@/utils/logger';

interface RiskState {
  isRiskInited: boolean;
  isRiskEnabled: boolean;
  agreedAsset: string;
}

export const useRiskData = () => {
  const { isRiskInited, isRiskEnabled, setIsRiskEnabled, setIsRiskInited, setAgreedAsset } =
    useGlobalStore();
  const { setIsLoading } = useLoading();
  const { log } = useDevModeStore();

  //AI利用スイッチ取得API
  const fetchHealthScoreOnOffList = (callback: (riskState?: RiskState) => void) => {
    riskAPI
      .getOnOffList()
      .then((response) => {
        log(`useRiskData-fetchHealthScoreOnOffList:${JSON.stringify(response)}`);
        nlog(`useRiskData-fetchHealthScoreOnOffList:${JSON.stringify(response)}`);
        const riskState = {
          isRiskInited: false,
          isRiskEnabled: false,
          agreedAsset: '',
        };
        if (response?.on_of_list) {
          const unsetAssetTypes = response.on_of_list.filter(
            (element) =>
              element.op_state == null &&
              element.asset_type != null &&
              SUPPORT_RISK.includes(element.asset_type as unknown as RiskTypeNum),
          );
          setIsRiskInited(unsetAssetTypes.length === 0);
          riskState.isRiskInited = unsetAssetTypes.length === 0;
          const agreedAssetTypes = response.on_of_list.filter(
            (element) =>
              (element.op_state === '1' || element.op_state === '2') &&
              element.asset_type != null &&
              SUPPORT_RISK.includes(element.asset_type as unknown as RiskTypeNum),
          );
          if (agreedAssetTypes.length > 0) {
            const agreedAsset = agreedAssetTypes.map((element) => element.asset_type).join(',');
            setAgreedAsset(agreedAsset);
            riskState.agreedAsset = agreedAsset;
          }

          setIsRiskEnabled(agreedAssetTypes.length > 0);
          riskState.isRiskEnabled = agreedAssetTypes.length > 0;
        } else {
          setIsRiskInited(true);
          riskState.isRiskInited = true;
          setIsRiskEnabled(false);
          riskState.isRiskEnabled = false;
        }

        callback(riskState);
      })
      .catch((error) => {
        const riskState = {
          isRiskInited: true,
          isRiskEnabled: false,
          agreedAsset: '',
        };
        setIsRiskInited(true);
        setIsRiskEnabled(false);
        setIsLoading(false);
        callback(riskState);
      });
  };

  const refreshRiskData = (callback: (riskState?: RiskState) => void = () => {}) => {
    fetchHealthScoreOnOffList(callback);
  };

  return {
    isRiskInited,
    isRiskEnabled,
    refreshRiskData,
  };
};
