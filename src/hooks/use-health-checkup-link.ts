'use client';

import { miniappAPI } from '@/api/modules/miniapp';
import { APP_TEXT } from '@/const/text/app';
import { useAuthStore } from '@/store/auth';
import { useDevModeStore } from '@/store/dev-mode';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useQuery } from '@tanstack/react-query';
import { useTheme } from 'next-themes';
import { useEffect, useRef } from 'react';
import { useGeolocation } from './use-geolocation';

export function useMiniAppHealthCheckupLink() {
  const HealthCheckupMiniAppId = '1';
  const { user, token, isAuthenticated, isRehydrated } = useAuthStore.getState();
  const { log } = useDevModeStore.getState();
  const { theme } = useTheme();
  const { location } = useGeolocation();
  const { data: miniAppUrlData } = useQuery({
    queryKey: ['miniAppUrl'],
    queryFn: () => {
      return miniappAPI.getMiniAppUrl(HealthCheckupMiniAppId);
    },
    enabled: isAuthenticated && isRehydrated,
  });
  const link = useRef('');

  useEffect(() => {
    log(`openMiniApp: useEffect: ${JSON.stringify(miniAppUrlData)}`);
    const query = {
      miniappid: HealthCheckupMiniAppId,
      token: token || '',
      theme: theme || 'theme-blue',
      birthdt: user?.birthday || '',
      useorganizerid: user?.useOrganizerID || '',
      organizerid: user?.organizerID || '',
      userid: user?.id || '',
      sex: user?.sex || '1',
      lat: location.lat.toString(),
      lng: location.lng.toString(),
    };
    link.current = `${miniAppUrlData?.pageUrl}?${getQuery(query)}`;
    log(`openMiniApp: useEffect: link: ${link.current}`);
  }, [miniAppUrlData, location]);

  const openMiniApp = (code?: string) => {
    log(`openMiniApp: link: ${link.current}${code ? `&code=${code}` : ''}`);
    sendMessageToNative({
      type: 'start-other-link',
      data: {
        title: APP_TEXT.HEALTH_RECORD_PAGE.HEALTH_CHECKUP_TITLE,
        link: `${link.current}${code ? `&code=${code}` : ''}`,
        callbackLink: '',
        returnLink: '',
        enableClose: '1',
      },
      callback: (data) => {},
    });
  };

  return {
    openLink: openMiniApp,
  };
}

function getQuery(query: Record<string, string>) {
  return Object.entries(query)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');
}
