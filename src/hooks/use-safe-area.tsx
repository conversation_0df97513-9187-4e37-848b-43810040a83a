'use client';

import { remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sendMessageToNative } from '@/utils/native-bridge';
import { createContext, useContext, useEffect, useState } from 'react';

type SafeAreaInsets = {
  top: number;
  bottom: number;
  left: number;
  right: number;
};

function defaultSafeArea(): SafeAreaInsets {
  return {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  };
  // return {
  //   top: 30,
  //   bottom: 20,
  //   left: 0,
  //   right: 0,
  // };
}

// セーフエリアのコンテキスト
const SafeAreaContext = createContext<SafeAreaInsets>(defaultSafeArea());

// セーフエリアのプロバイダー
export const SafeAreaProvider = ({ children }: { children: React.ReactNode }) => {
  const [safeArea, setSafeArea] = useState<SafeAreaInsets>(defaultSafeArea());

  useEffect(() => {
    sendMessageToNative({
      type: 'safe-area-insets',
      callback: (data) => {
        if (data && (data.top || data.bottom || data.left || data.right)) {
          setSafeArea({
            ...defaultSafeArea(),
            ...data,
          });
        }
      },
    });
    return () => {
      removeCallbackHandler('safe-area-insets');
    };
  }, []);

  return <SafeAreaContext.Provider value={safeArea}>{children}</SafeAreaContext.Provider>;
};

// セーフエリアのフック
export const useSafeArea = () => {
  const safeArea = useContext(SafeAreaContext);
  return safeArea;
};
