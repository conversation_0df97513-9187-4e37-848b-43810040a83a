'use client';
import { useLoading } from '@/hooks/use-loading';
import <PERSON><PERSON> from 'lottie-react';
import { useEffect, useState } from 'react';

// アニメーション設定の型
interface AnimationConfig {
  path: string;
  loop?: boolean;
  autoplay?: boolean;
}

// 事前定義されたアニメーション設定
const ANIMATION_CONFIGS: Record<string, AnimationConfig> = {
  jumpLoading: {
    path: '/animations/Jump-loading.json',
    loop: true,
    autoplay: true,
  },
  stampComplete: {
    path: '/animations/stamp-complete-bg.json',
    loop: true,
    autoplay: true,
  },
};

// アニメーションキャッシュマネージャー
class AnimationCacheManager {
  private cache = new Map<string, any>();
  private loadingPromises = new Map<string, Promise<any>>();
  private preloadStatus = new Map<string, 'loading' | 'loaded' | 'error'>();

  // 単一アニメーションの事前読み込み
  async preloadAnimation(path: string): Promise<any> {
    // 既にキャッシュされている場合は、直接返す
    if (this.cache.has(path)) {
      return this.cache.get(path);
    }

    // 読み込み中の場合は、既存のPromiseを返す
    if (this.loadingPromises.has(path)) {
      return this.loadingPromises.get(path);
    }

    // 読み込みPromiseを作成
    const loadPromise = this.loadAnimationData(path);
    this.loadingPromises.set(path, loadPromise);
    this.preloadStatus.set(path, 'loading');

    try {
      const data = await loadPromise;
      this.cache.set(path, data);
      this.preloadStatus.set(path, 'loaded');
      this.loadingPromises.delete(path);
      return data;
    } catch (error) {
      this.preloadStatus.set(path, 'error');
      this.loadingPromises.delete(path);
      throw error;
    }
  }

  // アニメーションデータの読み込み
  private async loadAnimationData(path: string): Promise<any> {
    const response = await fetch(path);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // 全ての事前定義アニメーションの事前読み込み
  async preloadAllAnimations(): Promise<void> {
    const preloadPromises = Object.values(ANIMATION_CONFIGS).map((config) =>
      this.preloadAnimation(config.path).catch((error) => {
        console.log(`アニメーション ${config.path} の事前読み込みに失敗:`, error);
        return null;
      }),
    );

    await Promise.allSettled(preloadPromises);
  }

  // キャッシュされたアニメーションデータの取得
  getCachedAnimation(path: string): any | null {
    return this.cache.get(path) || null;
  }

  // アニメーションが事前読み込み済みかチェック
  isAnimationPreloaded(path: string): boolean {
    return this.cache.has(path);
  }

  // 事前読み込み状態の取得
  getPreloadStatus(path: string): 'loading' | 'loaded' | 'error' | 'not-started' {
    return this.preloadStatus.get(path) || 'not-started';
  }

  // キャッシュのクリア
  clearCache(): void {
    this.cache.clear();
    this.loadingPromises.clear();
    this.preloadStatus.clear();
  }

  // キャッシュ統計情報の取得
  getCacheStats() {
    return {
      totalCached: this.cache.size,
      loadingCount: this.loadingPromises.size,
      statusMap: Object.fromEntries(this.preloadStatus),
    };
  }
}

// グローバルアニメーションキャッシュマネージャーインスタンス
const animationCacheManager = new AnimationCacheManager();

// 全アニメーション事前読み込み関数
export const preloadAllAnimations = async (): Promise<void> => {
  try {
    await animationCacheManager.preloadAllAnimations();
    console.log('全アニメーションの事前読み込み完了');
  } catch (error) {
    console.log('アニメーション事前読み込み失敗:', error);
  }
};

// 特定アニメーション事前読み込み関数
export const preloadAnimation = async (path: string): Promise<void> => {
  try {
    await animationCacheManager.preloadAnimation(path);
    console.log(`アニメーション ${path} の事前読み込み完了`);
  } catch (error) {
    console.log(`アニメーション ${path} の事前読み込み失敗:`, error);
  }
};

// 基本hook：単一アニメーションの読み込み（キャッシュ対応）
export const useLottieAnimation = (animationPath: string) => {
  const [animationData, setAnimationData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { setIsLoading: setGlobalLoading } = useLoading();

  useEffect(() => {
    const loadAnimation = async () => {
      try {
        setError(null);

        // まずキャッシュをチェック
        const cachedData = animationCacheManager.getCachedAnimation(animationPath);
        if (cachedData) {
          setAnimationData(cachedData);
          return;
        }

        // キャッシュがない場合は、読み込み状態を表示してアニメーションを読み込み
        setIsLoading(true);
        setGlobalLoading(true);

        const data = await animationCacheManager.preloadAnimation(animationPath);
        setAnimationData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        console.log('アニメーション読み込み失敗:', err);
      } finally {
        setIsLoading(false);
        setGlobalLoading(false);
      }
    };

    loadAnimation();
  }, [animationPath, setGlobalLoading]);

  return {
    animationData,
    error,
    isLoading,
    isPreloaded: animationCacheManager.isAnimationPreloaded(animationPath),
  };
};

// 事前定義アニメーションhook：アニメーション名による読み込み（事前読み込み対応）
export const usePresetAnimation = (animationName: keyof typeof ANIMATION_CONFIGS) => {
  const config = ANIMATION_CONFIGS[animationName];
  const { animationData, error, isLoading, isPreloaded } = useLottieAnimation(config.path);

  return {
    animationData,
    error,
    config,
    isLoading,
    isPreloaded,
  };
};

// 汎用アニメーションコンポーネントのプロパティ
interface BaseAnimationProps {
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  loop?: boolean;
  autoplay?: boolean;
  showLoadingText?: boolean; // 読み込みテキストを表示するか
  loadingText?: string; // カスタム読み込みテキスト
  errorText?: string; // カスタムエラーテキスト
  fallbackComponent?: React.ReactNode; // カスタムフォールバックコンポーネント
}

// 汎用アニメーションコンポーネント
const BaseAnimationComponent = ({
  animationName,
  width,
  height,
  className,
  style,
  loop,
  autoplay,
  showLoadingText = true,
  loadingText = 'アニメーション読み込み中...',
  errorText = 'アニメーション読み込み失敗',
  fallbackComponent,
}: BaseAnimationProps & { animationName: keyof typeof ANIMATION_CONFIGS }) => {
  const { animationData, error, config, isLoading, isPreloaded } =
    usePresetAnimation(animationName);

  // エラーがある場合、エラー状態を表示
  if (error) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <div
        className={className}
        style={{
          width,
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...style,
        }}
      >
        <div>{errorText}</div>
      </div>
    );
  }

  // 読み込み中でアニメーションデータがない場合、読み込み状態を表示
  if (isLoading && !animationData) {
    if (!showLoadingText) {
      return null; // 何も表示せず、ちらつきを避ける
    }

    return (
      <div
        className={className}
        style={{
          width,
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...style,
        }}
      >
        <div>{loadingText}</div>
      </div>
    );
  }

  // アニメーションデータがない場合、空を返す
  if (!animationData) {
    return null;
  }

  return (
    <Lottie
      animationData={animationData}
      loop={loop ?? config.loop ?? true}
      autoplay={autoplay ?? config.autoplay ?? true}
      style={{ width, height, ...style }}
      className={className}
    />
  );
};

// ジャンプ読み込みアニメーションコンポーネント
export const AnimationJumpLoading = (props: BaseAnimationProps) => {
  return <BaseAnimationComponent animationName="jumpLoading" {...props} />;
};

// スタンプ完了アニメーションコンポーネント
export const AnimationStampComplete = (props: BaseAnimationProps) => {
  return <BaseAnimationComponent animationName="stampComplete" {...props} />;
};

// 全アニメーションコンポーネントのエクスポート
export const LottieAnimations = {
  JumpLoading: AnimationJumpLoading,
  StampComplete: AnimationStampComplete,
};

// アニメーション設定のエクスポート（他の場所で使用するため）
export { ANIMATION_CONFIGS };

// キャッシュマネージャーの実用関数のエクスポート
export const getAnimationCacheStats = () => animationCacheManager.getCacheStats();
export const clearAnimationCache = () => animationCacheManager.clearCache();
export const isAnimationPreloaded = (path: string) =>
  animationCacheManager.isAnimationPreloaded(path);
export const getAnimationPreloadStatus = (path: string) =>
  animationCacheManager.getPreloadStatus(path);

// 特定の事前設定アニメーション事前読み込みの便利関数
export const preloadPresetAnimation = async (animationName: keyof typeof ANIMATION_CONFIGS) => {
  const config = ANIMATION_CONFIGS[animationName];
  return preloadAnimation(config.path);
};

// 全事前設定アニメーションが事前読み込み済みかチェック
export const areAllAnimationsPreloaded = (): boolean => {
  return Object.values(ANIMATION_CONFIGS).every((config) =>
    animationCacheManager.isAnimationPreloaded(config.path),
  );
};

// 事前設定アニメーションの事前読み込み状態を取得
export const getPresetAnimationStatus = () => {
  const status: Record<string, string> = {};
  for (const [name, config] of Object.entries(ANIMATION_CONFIGS)) {
    status[name] = animationCacheManager.getPreloadStatus(config.path);
  }
  return status;
};
