import { healthCheckupAPI } from '@/api/modules/health-checkup';
import { useLoading } from '@/hooks/use-loading';
import type {
  ExamData,
  ExamGraphData,
  ExamResultData,
  ExamResultQuery,
  HealthExamResultData,
  HealthExamResultRequest,
  InquiryData,
  Question1Data,
  Question2Data,
  Question3Data,
  Question4Data,
} from '@/types/health-checkup-input';
import { useCallback, useEffect, useState } from 'react';

interface UseHealthExamDataProps {
  examDay: string;
  categories: string;
}

export function useHealthExamData({ examDay, categories }: UseHealthExamDataProps) {
  const { isLoading, setIsLoading } = useLoading();
  const [examData, setExamData] = useState<ExamData>();
  const [examGraphData, setExamGraphData] = useState<ExamGraphData>();
  const [question1Data, setQuestion1Data] = useState<Question1Data>();
  const [question2Data, setQuestion2Data] = useState<Question2Data>();
  const [question3Data, setQuestion3Data] = useState<Question3Data>();
  const [question4Data, setQuestion4Data] = useState<Question4Data>();
  const [inquiryData, setInquiryData] = useState<InquiryData>();

  const [error, setError] = useState<string | null>(null);

  const fetchExamData = useCallback(
    async (force = false) => {
      // 防止重复调用
      if (isLoading && !force) return;

      setIsLoading(true);
      setError(null);
      try {
        const response: HealthExamResultData = await healthCheckupAPI.getHealthExamResultData({
          examDay,
          categories,
        });

        const {
          examData,
          examGraphData,
          question1Data,
          question2Data,
          question3Data,
          question4Data,
          inquiryData,
        } = response;

        setExamData(examData);
        setExamGraphData(examGraphData);
        setQuestion1Data(question1Data);
        setQuestion2Data(question2Data);
        setQuestion3Data(question3Data);
        setQuestion4Data(question4Data);
        setInquiryData(inquiryData);
      } catch (error) {
        console.log('Error fetching exam data:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    },
    [examDay, categories, isLoading],
  );

  useEffect(() => {
    fetchExamData();
  }, [examDay, categories]); // 移除 fetchExamData 依赖，避免无限循环

  return {
    examData,
    examGraphData,
    question1Data,
    question2Data,
    question3Data,
    question4Data,
    inquiryData,
    isLoading,
    error,
    refetch: () => fetchExamData(true), // 强制刷新
  };
}

/**
 * 検査結果データを取得するhook
 */
export function useExamResultData() {
  const { isLoading, setIsLoading } = useLoading();
  const [error, setError] = useState<string | null>(null);

  const getExamResultData = useCallback(
    async (query: ExamResultQuery): Promise<ExamResultData | null> => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await healthCheckupAPI.getExamResultData(query);
        return response;
      } catch (error) {
        console.log('Error fetching exam result data:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  return {
    getExamResultData,
    isLoading,
    error,
  };
}

/**
 * 健診結果データを更新するhook
 */
export function useUpdateHealthExamResult() {
  const { isLoading, setIsLoading } = useLoading();
  const [error, setError] = useState<string | null>(null);

  const updateHealthExamResultData = useCallback(
    async (request: HealthExamResultRequest): Promise<HealthExamResultData | null> => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await healthCheckupAPI.updateHealthExamResultData(request);
        return response;
      } catch (error) {
        console.log('Error updating health exam result data:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  return {
    updateHealthExamResultData,
    isLoading,
    error,
  };
}
