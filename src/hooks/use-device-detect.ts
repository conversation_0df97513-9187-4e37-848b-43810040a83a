import {
  isAndroid as isAndroidFn,
  isIOS as isIOSFn,
  isMobile as isMobileFn,
  isMobileReliable as isMobileReliableFn,
} from '@/utils/device-detect';
import { useEffect, useState } from 'react';

export function useDeviceDetect() {
  const [isMobile, setIsMobile] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);

  useEffect(() => {
    setIsMobile(isMobileFn());
    setIsIOS(isIOSFn());
    setIsAndroid(isAndroidFn());
  }, []);

  return {
    isMobile,
    isIOS,
    isAndroid,
    isDesktop: !isMobile,
  };
}

/**
 * より信頼性の高いデバイス検出フック
 * 画面サイズ変更にも対応
 */
export function useDeviceDetectReliable() {
  const [isMobile, setIsMobile] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);

  useEffect(() => {
    // 初期検出
    const detectDevice = () => {
      setIsMobile(isMobileReliableFn());
      setIsIOS(isIOSFn());
      setIsAndroid(isAndroidFn());
    };

    // 初回実行
    detectDevice();

    // ウィンドウサイズ変更時の再検出
    const handleResize = () => {
      detectDevice();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return {
    isMobile,
    isIOS,
    isAndroid,
    isDesktop: !isMobile,
  };
}
