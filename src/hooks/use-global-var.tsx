'use client';

import React, { useEffect, useState } from 'react';
import { usePathname } from './use-next-navigation';

export enum FooterMenuName {
  HOME = 'home',
  MAP = 'map',
  POST = 'post',
  FRIEND = 'friend',
  MENU = 'menu',
}

export interface FooterMenuSetting {
  isShow: boolean;
  layerIndex: number;
  defaultLayerIndex: number;
  activeMenuName: FooterMenuName;
}

export interface FooterMenuSettingOptions {
  isShow?: boolean;
  layerIndex?: number;
  activeMenuName?: FooterMenuName;
}

const defaultFooterMenuSetting: FooterMenuSetting = {
  isShow: true,
  layerIndex: 50,
  defaultLayerIndex: 50,
  activeMenuName: FooterMenuName.HOME,
};

const GlobalVarContext = React.createContext<{
  footerMenuSetting: FooterMenuSetting;
  setFooterMenuSettingOptions: (options: FooterMenuSettingOptions) => void;
  resetFooterMenuSetting: () => void;
  setCurrentActiveFooterMenuName: (menuName: FooterMenuName) => void;
}>({
  footerMenuSetting: defaultFooterMenuSetting,
  setFooterMenuSettingOptions: () => {},
  resetFooterMenuSetting: () => {},
  setCurrentActiveFooterMenuName: () => {},
});

export function GlobalVarProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [footerMenuSetting, setFooterMenuSetting] =
    useState<FooterMenuSetting>(defaultFooterMenuSetting);

  const resetFooterMenuSetting = () => {
    setFooterMenuSetting({ ...defaultFooterMenuSetting });
  };

  const setCurrentActiveFooterMenuName = (menuName: FooterMenuName) => {
    setFooterMenuSetting({ ...footerMenuSetting, activeMenuName: menuName });
  };

  const setFooterMenuSettingOptions = (options: FooterMenuSettingOptions) => {
    setFooterMenuSetting({ ...footerMenuSetting, ...options });
  };

  useEffect(() => {
    if (pathname === '/home' && footerMenuSetting.activeMenuName !== FooterMenuName.HOME) {
      setCurrentActiveFooterMenuName(FooterMenuName.HOME);
    } else if (pathname === '/map' && footerMenuSetting.activeMenuName !== FooterMenuName.MAP) {
      setCurrentActiveFooterMenuName(FooterMenuName.MAP);
    } else if (pathname === '/post' && footerMenuSetting.activeMenuName !== FooterMenuName.POST) {
      setCurrentActiveFooterMenuName(FooterMenuName.POST);
    } else if (
      pathname === '/friend' &&
      footerMenuSetting.activeMenuName !== FooterMenuName.FRIEND
    ) {
      setCurrentActiveFooterMenuName(FooterMenuName.FRIEND);
    } else if (pathname === '/menu' && footerMenuSetting.activeMenuName !== FooterMenuName.MENU) {
      setCurrentActiveFooterMenuName(FooterMenuName.MENU);
    }
  }, [pathname, footerMenuSetting]);

  return (
    <GlobalVarContext.Provider
      value={{
        footerMenuSetting,
        setFooterMenuSettingOptions,
        resetFooterMenuSetting,
        setCurrentActiveFooterMenuName,
      }}
    >
      {children}
    </GlobalVarContext.Provider>
  );
}

export const useGlobalVar = () => React.useContext(GlobalVarContext);
