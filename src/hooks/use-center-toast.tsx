import { FadeInOut } from '@/components/shared/animation';
import { cn } from '@/lib/utils';
import toast from 'react-hot-toast';

type ToastType = 'success' | 'error' | 'warning' | 'info';

interface UseCenterToastProps {
  message: string | React.ReactNode;
  duration?: number;
  type?: ToastType;
}

export default function useCenterToast() {
  return {
    toast: ({ message, duration = 1000, type = 'success' }: UseCenterToastProps) => {
      toast.custom(
        (t) => (
          <FadeInOut show={t.visible}>
            <div
              className={cn(
                'absolute w-[160px] h-[160px] shadow-lg flex flex-col items-center justify-center bg-black/70 rounded-3xl',
              )}
              style={{
                boxShadow: '4px 8px 16px 0px rgba(0, 0, 0, 0.25)',
                top: 'calc(-80px - 50vh)',
              }}
            >
              <img className="h-14 w-14" src="/images/common/done.png" alt="" />
              <p className="text-sm font-bold text-white mt-2 text-center">{message}</p>
            </div>
          </FadeInOut>
        ),
        {
          duration: duration,
        },
      );
    },
  };
}
