import { useCodeStore } from '@/store/code-store';
import type { BaseType, CodeOption } from '@/types/code-types';

/**
 * カスタムHook：Code Optionsの使用を簡素化
 * 様々なタイプのオプションデータを取得するための、よりシンプルなAPIを提供
 *

 */
export const useCommonCode = () => {
  const store = useCodeStore();

  return {
    // 汎用メソッド
    getOptions: (typeKey: string): CodeOption[] => store.getOptionsByTypeKey(typeKey),
    getType: (typeKey: string): BaseType | undefined => store.getTypeByKey(typeKey),
    getRealValue: (typeKey: string, codeKey: string): number | string | null | boolean => {
      const type = store.getTypeByKey(typeKey);
      if (!type) return codeKey;
      const code = type.codeList.find((code) => code.codeKey === codeKey);
      if (code?.metaData?.val !== undefined) {
        return code.metaData.val;
      }
      return codeKey;
    },
  };
};

/**
 * 特定タイプのオプションを取得するHook
 * @param typeKey - タイプキー値（例：'event-type', 'point-type', 'prefecture' など）
 * @returns 対応するオプション配列、形式は { value: string, label: string }[]
 *
 * 使用例：
 * const pointTypeOptions = useCodeOptions(codeTypeList.POINT_TYPE);
 * const prefectureOptions = useCodeOptions(codeTypeList.PREFECTURE);
 */
export const useCodeOptions = (typeKey: string): CodeOption[] => {
  const { getOptions } = useCommonCode();
  return getOptions(typeKey);
};

/**
 * 特定タイプの元データを取得するHook
 * @param typeKey - タイプキー値（例：'event-type', 'point-type', 'prefecture' など）
 * @returns 対応する元のタイプデータ、完全なcodeListを含む
 *
 * 使用例：
 * const eventType = useCodeType(codeTypeList.EVENT_TYPE);
 * const pointType = useCodeType(codeTypeList.POINT_TYPE);
 * const prefecture = useCodeType(codeTypeList.PREFECTURE);
 */
export const useCodeType = (typeKey: string): BaseType | undefined => {
  const { getType } = useCommonCode();
  return getType(typeKey);
};

export const codeTypeList = {
  // organizerTypeList
  ACCESS_PERMISSIONS: 'access-permissions',

  // typeList
  EVENT_SEARCH_POINT: 'event-search-point',
  MINIAPP_PERMISSIONS: 'miniapp-permissions',
  POINT_TYPE: 'point-type',
  POINT_TYPE_EVENT: 'point-type-event',
  MINIAPP_CATEGORY: 'miniapp-category',
  POINT_TYPE_LOTTERY: 'point-type-lottery',
  POINT_TYPE_MISSION: 'point-type-mission',
  POINT_TYPE_OTHER: 'point-type-other',
  POINT_TYPE_RECORD: 'point-type-record',
  POINT_TYPE_STEP: 'point-type-step',
  POINT_TYPE_SURVEY: 'point-type-survey',
  EVENT_SEARCH_DISTANCE: 'event-search-distance',
  EVENT_TYPE: 'event-type',
  MINIAPP_PUBLISHED_STATUS: 'miniapp-published-status',
  POINT_TYPE_EXCHANGE: 'point-type-exchange',
  PREFECTURE: 'prefecture',
  EVENT_FREQUENCY_COUNT_TIMEUNIT: 'event-frequency-count-timeunit',
  EVENT_FREQUENCY_PATTERN: 'event-frequency-pattern',
  EVENT_POINT_PATTERN: 'event-point-pattern',
  EVENT_SEARCH_FEE: 'event-search-fee',
  WALKING_COURSE_SEARCH_DISTANCE: 'walking-course-search-distance',
  WALKING_COURSE_SEARCH_POINT: 'walking-course-search-point',
  WALKING_COURSE_SEARCH_CHALLENGE: 'walking-course-search-challenge',
  WALKING_COURSE_SEARCH_SORT: 'walking-course-search-sort',
  COUPON_SEARCH_CATEGORY: 'coupon-search-category',
  SHOP_TYPES: 'shop-types',
} as const;
