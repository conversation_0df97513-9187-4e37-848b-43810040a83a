'use client';
import { DeviceDataSyncService } from '@/app/other-connect/sycn-device-data';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import { Sex, useAuthStore } from '@/store/auth';
import { useCodeStore } from '@/store/code-store';
import { useDevModeStore } from '@/store/dev-mode';
import { useGlobalStore } from '@/store/global';
import { nlog } from '@/utils/logger';
import { registerMessageHandler, sendMessageToNative } from '@/utils/native-bridge';
import router from 'next/router';
import { useCallback, useEffect } from 'react';
import { preloadAllAnimations } from './use-lottie-animation';
import { useMessageDialog } from './use-message-dialog';
import { useRiskData } from './use-risk-data';

export function useGlobalInit() {
  let isneedAddOrg = false;
  const { isGlobalInited, setIsGlobalInited, isRiskShowed, setIsRiskShowed, setFromPage } =
    useGlobalStore();
  const { refreshRiskData, isRiskInited } = useRiskData();
  const { isShow, setDialog } = useMessageDialog();
  const router = useRouter();
  const pathname = usePathname();
  const syncService = new DeviceDataSyncService();
  const { setUser, setAppData, user } = useAuthStore.getState();
  const { initCommonCodes } = useCodeStore();
  const { log, setDevMode } = useDevModeStore();
  const addUsersOrganizer = () => {
    sendMessageToNative({
      type: 'get-activity-info',
      data: {},
      callback: (data) => {
        nlog(`zzz06 ${JSON.stringify(data)}`);
        if (data?.code) {
          const organizerUseCodes = user?.organizerCode ? user?.organizerCode.split(',') : [];
          nlog(`zzz07 ${JSON.stringify(organizerUseCodes)}`);
          nlog(`zzz08 ${organizerUseCodes.includes(data?.code)}`);
          if (!organizerUseCodes.includes(data?.code)) {
            isneedAddOrg = true;
            nlog(`zzz09 ${organizerUseCodes.includes(data?.code)}`);
            router.push(`/registration?organizerCode=${data?.code}`);
          }
        }
        if (data?.gcode) {
          router.push(`/registration?organizerCode=${data?.code}&groupCode=${data?.gcode}`);
        }
        if (data?.fcode) {
          router.push(`/registration?friendCode=${data?.fcode}`);
        }
        if (
          (process.env.NEXT_PUBLIC_DEV_CODE || '').length > 0 &&
          data?.devcode === process.env.NEXT_PUBLIC_DEV_CODE
        ) {
          setDevMode(true);
        }
      },
    });
  };
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    nlog('zzz06 registerMessageHandler');
    registerMessageHandler('activity-notification', (data) => {
      // sendMessageToNative({
      //   type: 'log',
      //   data: { info: addUsersOrganizer },
      // });
      log('addUsersOrganizer');
      addUsersOrganizer();
    });
  }, []);

  const init = () => {
    if (isRiskInited) {
      log('useGlobalInit: isRiskInited is true');
      setIsGlobalInited(true);
    } else {
      log('useGlobalInit: isRiskInited is false');
      nlog('zzz13 addUsersOrganizer 0');
      addUsersOrganizer();
      //1. get user api
      // const user = {
      //   id: values.email,
      //   name: 'userid',
      //   email: '<EMAIL>',
      //   useOrganizerID: '102',
      //   organizerID: '101,102',
      // };
      // setUser(user);
      //2. get risk data api
      //3. get device data

      // sendMessageToNative({
      //   type: 'user-info',
      //   data: {
      //     userID: String(res?.userId ?? 0),
      //     userName: user?.name ?? '',
      //     organizerID: user?.organizerID ?? '',
      //     userOrganizerID: user?.useOrganizerID ?? '',
      //   },
      //   callback: (data) => {
      //     log('callbacklogin');
      //   },
      // });
      sendMessageToNative({
        type: 'get-user-info',
        data: {},
        callback: (data) => {
          setAppData({
            appVersion: data?.appVersion,
            userAgent: data?.userAgent,
          });

          const userAgent = navigator.userAgent;
          setUser({
            ...(user || { lat: 0, lng: 0 }),
            id: data?.userID,
            name: data?.nickName,
            email: data?.email,
            userAgent: userAgent,
            birthday: data?.birthDt,
            sex: Sex[String(data?.sex) as keyof typeof Sex],
            organizerID: data?.organizerID,
            useOrganizerID: data?.userOrganizerID,
          });
        },
      });
      registerMessageHandler('activity-notification', (data) => {
        log('addUsersOrganizer');
        addUsersOrganizer();
      });
      // init common codes
      initCommonCodes();

      // 预加载所有动画
      preloadAllAnimations()
        .then(() => console.log('动画预加载成功'))
        .catch((err) => console.log('动画预加载失败:', err));

      // sync sleep weight blood
      syncService
        .syncAllData(0)
        .then(() => console.log('device sync success'))
        .catch((err) => console.log('device sync fail', err));
      log(`useGlobalInit: isRiskShowed: ${isRiskShowed}`);
      if (!isRiskShowed) {
        log('useGlobalInit: isRiskShowed if false');
        refreshRiskData((riskState) => {
          log(`useGlobalInit: refreshRiskData: ${JSON.stringify(riskState)}`);
          if (riskState?.isRiskInited) {
            if (pathname === '/' || pathname === '/auth/login' || pathname === '/home') {
              if (riskState.agreedAsset !== '') {
                sendMessageToNative({
                  type: 'start-ai',
                  data: { info: riskState.agreedAsset },
                });
              }
              nlog(`zzz10 ${isneedAddOrg}`);
              if (pathname !== '/home' && !isneedAddOrg) {
                // router.replace('/registration');
                router.replace('/home');
              }
            }
            setIsGlobalInited(true);
          } else {
            log('useGlobalInit: riskState?.isRiskInited if false');
            setIsRiskShowed(true);
            nlog(`zzz11 ${isneedAddOrg}`);
            if (!isneedAddOrg) {
              setFromPage('home');
              router.replace('/health-score-init/riskusage');
            }
          }
        });
      } else {
        log('useGlobalInit: isRiskShowed if true');
        log(`useGlobalInit: pathname: ${pathname}`);
      }
      // TODO: 和扫码添可能冲突
      sendMessageToNative({
        type: 'query-push',
        callback: (data) => {
          nlog(JSON.stringify(data));
          handlePushData(JSON.stringify(data));
        },
      });
      window.pushDataFunction = handlePushData;
      window.addGroupFunction = addUsersOrganizer;
    }
  };

  // プッシュデータを処理する関数
  const handlePushData = (jsonStr: string) => {
    try {
      const data = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;

      const message = data?.pushMsg || '';
      const url = data?.pushUrl || '/home';
      const title = data?.pushTitle || '';

      if (data?.pushUrl) {
        nlog('zzz12 push');
        router.push(url);
      }
      if (message) {
        // 0.5秒後にダイアログを表示
        setTimeout(() => {
          setDialog(true, {
            title: title,
            content: message,
          });
        }, 500);
      }
    } catch (e) {
      console.log('push json fail:', e);
    }
  };
  const checkRiskForAddOrg = () => {
    refreshRiskData((riskState) => {
      log(`useGlobalInit: checkRiskForAddOrg: ${JSON.stringify(riskState)}`);
      nlog(`useGlobalInit: checkRiskForAddOrg: ${JSON.stringify(riskState)}`);
      nlog('zzz12 checkRiskForAddOrg');
      if (riskState?.isRiskInited) {
        nlog('useGlobalInit: checkRiskForAddOrg:go home');
        router.replace('/home');
      } else {
        nlog('useGlobalInit: checkRiskForAddOrg:go riskusage');
        router.replace('/health-score-init/riskusage');
      }
    });
  };

  return {
    isGlobalInited,
    checkRiskForAddOrg,
    init,
  };
}
