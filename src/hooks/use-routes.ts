import { ROUTES } from '@/const/routes';
import { useRouter } from '@/hooks/use-next-navigation';
import { useAuthStore } from '@/store/auth';

export function useRoutes() {
  const router = useRouter();
  const { logout } = useAuthStore();
  return {
    // route object
    routes: ROUTES,

    // common navigation methods
    push: (path: string) => router.push(path),
    replace: (path: string) => router.replace(path),
    back: () => router.back(),

    // push to specific routes
    goLogin: () => router.push(ROUTES.AUTH.LOGIN),
    goLogout: () => {
      logout();
      router.push(ROUTES.AUTH.LOGIN);
    },
    goRegister: () => router.push(ROUTES.AUTH.REGISTER),
    goForgotPassword: () => router.push(ROUTES.AUTH.FORGOT_PASSWORD),
    goMenu: () => router.push(ROUTES.MENU.MENU),
    goPost: () => router.push(ROUTES.POST),
    goHealthScore: () => router.push(ROUTES.HEALTH_SCORE),
  };
}
