'use client';

import { eventAPI } from '@/api/modules/event-api';
import { useMiniAppHealthCheckupLink } from '@/hooks/use-health-checkup-link';
import { sendMessageToNative } from '@/utils/native-bridge';
import { toast } from 'react-hot-toast';
/**
 * QRコードのタイプを定義する列挙型
 */
export enum QRCodeType {
  EVENT = 'event',
  HEALTH_CHECKUP = 'health-checkup',
}

/**
 * イベント用のQRコードデータインターフェース
 */
export interface EventData {
  id: string;
  code: string;
}

/**
 * 健康診断用のQRコードデータインターフェース
 */
export interface HealthCheckupData {
  code: string;
}

/**
 * QRコードデータの型マッピング
 */
export interface QRCodeData {
  [QRCodeType.EVENT]: EventData;
  [QRCodeType.HEALTH_CHECKUP]: HealthCheckupData;
}

/**
 * QRスキャン時のペイロードインターフェース
 */
export interface QRScanPayload {
  [key: string]: unknown;
}

/**
 * 解析済みQRコードデータのインターフェース
 */
export interface ParsedQRData {
  type: QRCodeType;
  data: EventData | HealthCheckupData;
}

/**
 * QRコードデータを解析するユーティリティ関数
 * @param data - Base64エンコードされたQRコードデータ
 * @returns 解析済みのQRコードデータまたはnull
 */
export const parseQrCode = (data: string): ParsedQRData | null => {
  try {
    // Base64デコードを実行
    const decodedData = base64Decode(data);
    const qrData = JSON.parse(decodedData);

    // データの基本構造を検証
    if (!qrData.type || !qrData.data) {
      throw new Error('QRコードの形式が正しくありません');
    }

    // QRコードタイプの妥当性を検証
    if (!Object.values(QRCodeType).includes(qrData.type)) {
      throw new Error(`未対応のQRコードタイプ: ${qrData.type}`);
    }

    return qrData as ParsedQRData;
  } catch (error) {
    console.error('QRコード解析エラー:', error);
    return null;
  }
};

/**
 * Base64文字列をデコードするユーティリティ関数
 * @param qrCodeData - Base64エンコードされた文字列
 * @returns デコードされた文字列
 * @throws Base64デコードに失敗した場合はエラーをスロー
 */
const base64Decode = (qrCodeData: string): string => {
  try {
    // ブラウザのatob関数を使用してBase64デコード
    return atob(qrCodeData);
  } catch (error) {
    throw new Error('Base64デコードに失敗しました');
  }
};

export const useScanInapp = () => {
  const { openLink } = useMiniAppHealthCheckupLink();

  /**
   * イベント用QRコードの処理を行うコールバック関数
   * @param eventData - イベントデータ
   * @param payload - オプションのペイロード
   * @param router - Next.jsのルーター（コンポーネントから渡される）
   */
  const eventCallback = async (eventData: EventData, payload?: QRScanPayload, router?: any) => {
    const targetEventId = eventData.id;
    const currentEventId = payload?.eventId;

    // イベントIDの整合性を検証
    if (currentEventId && targetEventId !== currentEventId) {
      toast.error(
        'このQRコードは別のイベントのものです。正しいイベントのQRコードをスキャンしてください。',
      );
      return;
    }

    try {
      // イベント参加APIを呼び出し
      await eventAPI.attendEvent({ qrString: eventData.code });
      toast.success('イベント参加が完了しました');
      // 完了ページへリダイレクト
      setTimeout(() => {
        if (router) {
          router.push(`/event/${targetEventId}/stamp-point-done`);
        }
      }, 2000);
    } catch (error) {
      console.error('イベント参加エラー:', error);

      // エラーメッセージの表示
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        console.error('イベント参加エラー:', error);
      }
    }
  };

  const healthCheckupCallback = async (
    healthCheckupData: HealthCheckupData,
    payload?: QRScanPayload,
    router?: any,
  ) => {
    const code = healthCheckupData.code;
    openLink(code);
  };

  /**
   * QRコードのタイプに応じて適切な処理を振り分ける関数
   * @param qrData - 解析済みのQRコードデータ
   * @param payload - オプションのペイロード
   * @param router - Next.jsのルーター（コンポーネントから渡される）
   */
  const handleQRCodeByType = async (
    qrData: ParsedQRData,
    payload?: QRScanPayload,
    router?: any,
  ) => {
    switch (qrData.type) {
      case QRCodeType.EVENT:
        // イベント用の処理を実行
        await eventCallback(qrData.data as EventData, payload, router);
        break;

      case QRCodeType.HEALTH_CHECKUP:
        await healthCheckupCallback(qrData.data as HealthCheckupData, payload, router);
        break;

      default:
        // 未対応のQRコードタイプの場合
        toast.error('未対応のQRコードタイプです');
        console.warn('未対応のQRコードタイプ:', qrData.type);
    }
  };

  /**
   * QRコードスキャンを開始する関数
   * @param payload - スキャン時に渡すオプションのペイロード
   * @param router - Next.jsのルーター（コンポーネントから渡される）
   */
  const handleQrScan = (payload?: QRScanPayload, router?: any) => {
    sendMessageToNative({
      type: 'scanner',
      data: {},
      callback: async (data) => {
        try {
          // QRコードデータの存在確認
          if (!data?.code) {
            toast.error('QRコードの読み取りに失敗しました');
            return;
          }

          console.log('QRコードを読み取りました:', data.code);

          // QRコードデータの解析
          const qrData = parseQrCode(data.code);

          if (!qrData) {
            toast.error('QRコードの解析に失敗しました');
            return;
          }

          // QRコードタイプに応じた処理を実行
          await handleQRCodeByType(qrData, payload, router);
        } catch (error) {
          if (error instanceof Error) {
            toast.error(error.message);
          } else {
            toast.error('QRコードの処理中にエラーが発生しました');
            toast.error(error as string);
          }
        }
      },
    });
  };
  return {
    handleQrScan,
  };
};
