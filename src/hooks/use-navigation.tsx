'use client';

import { useDevModeStore } from '@/store/dev-mode';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';

interface PageNavigationConfig {
  path: string;
  timestamp: number;
  metadata: metadata;
}

interface metadata {
  hasUnsavedChanges?: boolean;
  isErrorPage?: boolean;
  isRootPage?: boolean;
  query?: Record<string, string>;
}

interface SetPageConfigConfig {
  path: string;
  metadata?: metadata;
}

interface Options {
  scroll?: boolean;
  onceMetadata?: Record<string, string>;
}

function getDefaultPageNavigationConfig(path: string): PageNavigationConfig {
  return {
    path: path,
    timestamp: Date.now(),
    metadata: {},
  };
}

export const NavigationContext = createContext<{
  currentPageConfig: PageNavigationConfig;
  history: PageNavigationConfig[]; // 履歴配列を追加
  setPageConfig: (config: SetPageConfigConfig) => void;
  back: (options?: Options) => void;
  backTo: (path: string, options?: Options) => void;
  replace: (path: string, options?: Options) => void;
  push: (path: string, options?: Options) => void;
  canGoBack: () => boolean; // 戻れるかどうかをチェック
  getPreviousPath: () => string | null; // 前のパスを取得
  createHistory: (newHistory: string[]) => void;
  getOnceMetadata: () => Record<string, string> | undefined;
  includePathInHistory: (path: string) => boolean;
  getBackPath: (paths: string[]) => string;
}>({
  currentPageConfig: getDefaultPageNavigationConfig(''),
  history: [],
  setPageConfig: () => {},
  back: () => {},
  backTo: (path: string) => {},
  replace: (path: string) => {},
  push: (path: string) => {},
  canGoBack: () => false,
  getPreviousPath: () => null,
  createHistory: () => {},
  getOnceMetadata: () => undefined,
  includePathInHistory: () => false,
  getBackPath: (paths: string[]) => '',
});

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  // 一度だけメタデータを設定する
  const [onceMetadata, setOnceMetadata] = useState<Record<string, string>>();
  const [currentPageConfig, setCurrentPageConfig] = useState<PageNavigationConfig>(
    getDefaultPageNavigationConfig(pathname),
  );
  const [history, setHistory] = useState<PageNavigationConfig[]>([]); // 履歴配列
  const { log: logDevMode } = useDevModeStore();
  const log = (string: string) => {
    setTimeout(() => {
      logDevMode(`route hook: ${string}`);
    }, 0);
    // console.log(string);
    // return alert(string);
  };
  // パスが変更された時に履歴を更新
  useEffect(() => {
    if (pathname && history.length === 0) {
      setHistory([getDefaultPageNavigationConfig(pathname)]);
    }
  }, [pathname, history]);

  useEffect(() => {
    log(`history:${JSON.stringify(history)}`);
  }, [history]);

  // 戻れるかどうかをチェック
  const canGoBack = useCallback(() => {
    return history.length > 1;
  }, [history]);

  // 前のパスを取得
  const getPreviousPath = () => {
    if (history.length > 1) {
      return history[history.length - 2].path;
    }
    return null;
  };

  const createHistory = (newHistory: string[]) => {
    log(`createHistory:${newHistory.join(',')}`);
    const newHistoryConfig = newHistory.map((path) => getDefaultPageNavigationConfig(path));
    if (!isPathEqual(currentPageConfig.path, newHistoryConfig[newHistoryConfig.length - 1].path)) {
      newHistoryConfig.push(currentPageConfig);
    }
    setHistory(newHistoryConfig);
  };

  // 履歴の末尾に新しいエントリを更新
  // パラメタconfig.path が history の末尾のpathと一致する場合は履歴の先頭に追加
  const updateLastOfHistory = (config: PageNavigationConfig, isForceUpdate?: boolean) => {
    if (isForceUpdate) {
      setHistory((prev) => [...prev.slice(0, -1), config]);
      return;
    }

    if (history.length > 0 && isPathEqual(config.path, history[history.length - 1].path)) {
      setHistory((prev) => [...prev.slice(0, -1), config]);
      return;
    }

    if (history.length === 0) {
      setHistory([config]);
    }
  };

  // 論理的回退の処理
  const handleLogicBack = (
    currentConfig: PageNavigationConfig,
    previousConfig: PageNavigationConfig,
  ) => {
    const { metadata } = currentConfig;
    const { path } = previousConfig;

    // 条件分岐の例
    if (metadata.hasUnsavedChanges) {
      // 未保存の変更がある場合は確認ダイアログ
      if (confirm('変更内容が失われますが、戻りますか？')) {
        router.push(path);
      }
    } else if (metadata.isErrorPage && history.length === 1) {
      // エラーページの場合はホームに戻る
      router.push('/home');
    } else {
      // デフォルトは指定されたパスに戻る
      router.push(path);
    }
  };

  const back = (options?: Options) => {
    log('back');
    setOnceMetadata(options?.onceMetadata);
    if (history.length > 1) {
      const lastConfig = history[history.length - 1];
      const previousConfig = history[history.length - 2];
      handleLogicBack(lastConfig, previousConfig);
      setHistory((prev) => {
        // prev配列が空の場合の安全なチェック
        if (prev.length === 0) {
          return [];
        }
        return prev.slice(0, -1);
      });
    } else {
      replace('/home');
    }
  };

  const backTo = (path: string, options?: Options) => {
    setOnceMetadata(options?.onceMetadata);
    log(`backTo:${path}`);
    if (history.length > 1) {
      const index = history.findIndex((config) => isPathEqual(config.path, path));
      if (index !== -1) {
        setHistory((prev) => prev.slice(0, index + 1));
      }
      router.push(path);
    } else {
      push('/home');
    }
  };

  const replace = (path: string, options?: Options) => {
    log(`replace:${path}`);
    setOnceMetadata(options?.onceMetadata);
    router.replace(path, options);
    setHistory((prev) => {
      // prev配列が空の場合の安全なチェック
      if (prev.length === 0) {
        return [getDefaultPageNavigationConfig(path)];
      }
      return [...prev.slice(0, -1), getDefaultPageNavigationConfig(path)];
    });
  };

  const push = (path: string, options?: Options) => {
    // const stackTrace = new Error().stack;
    // log(`push:${path} - 呼び出し元: ${stackTrace}`);
    log(`push:${path}`);
    setOnceMetadata(options?.onceMetadata);
    setHistory((prev) => {
      // prev配列が空の場合の安全なチェック
      if (prev.length === 0) {
        log(`履歴が空の場合 push:${path}`);
        return [getDefaultPageNavigationConfig(path)];
      }

      const lastRouter = prev[prev.length - 1];
      if (isPathEqual(lastRouter.path, path)) {
        return [...prev.slice(0, -1), getDefaultPageNavigationConfig(path)];
      }
      return [...prev, getDefaultPageNavigationConfig(path)];
    });
    router.push(path);
  };

  const setPageConfig = (config: SetPageConfigConfig) => {
    const pageConfig: PageNavigationConfig = {
      path: config.path,
      timestamp: Date.now(),
      metadata: {
        hasUnsavedChanges: config.metadata?.hasUnsavedChanges,
        isErrorPage: config.metadata?.isErrorPage,
        isRootPage: config.metadata?.isRootPage,
        query: Object.fromEntries(searchParams.entries()),
      },
    };
    setCurrentPageConfig(pageConfig);
    updateLastOfHistory(pageConfig);
  };

  const getOnceMetadata = () => {
    const metadata = onceMetadata;
    setOnceMetadata(undefined);
    return metadata;
  };

  const includePathInHistory = (path: string) => {
    return history.some((item) => isPathEqual(item.path, path));
  };

  const getBackPath = (paths: string[]) => {
    let targetPath = '';
    history.some((item) => {
      paths.map((path) => {
        if (isPathEqual(item.path, path)) {
          targetPath = path;
        }
      });
    });

    return targetPath;
  };

  return (
    <NavigationContext.Provider
      value={{
        currentPageConfig,
        history,
        setPageConfig,
        back,
        backTo,
        replace,
        push,
        canGoBack,
        getPreviousPath,
        createHistory,
        getOnceMetadata,
        includePathInHistory,
        getBackPath,
      }}
    >
      {children}
    </NavigationContext.Provider>
  );
}

export const useNavigation = () => {
  return useContext(NavigationContext);
};

// パスが一致するかどうかをチェック
// /health-score-init/risksel と /health-score-init/risksel?param=value は一致する
export const isPathEqual = (path1: string, path2: string) => {
  const url1 = new URL(path1, window.location.origin);
  const url2 = new URL(path2, window.location.origin);
  return url1.pathname === url2.pathname;
};
