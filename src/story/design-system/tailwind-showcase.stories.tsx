import { Button } from '@/components/shared/button';
import ColorsStory from './colors';

export default {
  title: 'Design System/Tailwind CSS Showcase',
};

export const Colors = ColorsStory;

export const Typography = () => (
  <div className="p-6 space-y-4">
    <h1 className="text-4xl font-bold text-foreground">Heading 1</h1>
    <h2 className="text-3xl font-semibold text-foreground">Heading 2</h2>
    <h3 className="text-2xl font-medium text-foreground">Heading 3</h3>
    <h4 className="text-xl font-medium text-foreground">Heading 4</h4>
    <p className="text-base text-foreground">
      This is a regular paragraph with normal text styling.
    </p>
    <p className="text-sm text-muted-foreground">This is smaller text with muted color.</p>
    <p className="text-xs text-muted-foreground">This is extra small text.</p>
  </div>
);

export const Layout = () => (
  <div className="p-6 space-y-6">
    <h2 className="text-2xl font-bold text-primary">Layout Examples</h2>

    {/* Flexbox Layout */}
    <div className="bg-card p-4 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Flexbox Layout</h3>
      <div className="flex gap-4 flex-wrap">
        <div className="flex-1 bg-primary/10 p-3 rounded text-center min-w-[100px]">
          Flex Item 1
        </div>
        <div className="flex-1 bg-primary/10 p-3 rounded text-center min-w-[100px]">
          Flex Item 2
        </div>
        <div className="flex-1 bg-primary/10 p-3 rounded text-center min-w-[100px]">
          Flex Item 3
        </div>
      </div>
    </div>

    {/* Grid Layout */}
    <div className="bg-card p-4 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Grid Layout</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="bg-secondary/50 p-4 rounded text-center">Grid Item 1</div>
        <div className="bg-secondary/50 p-4 rounded text-center">Grid Item 2</div>
        <div className="bg-secondary/50 p-4 rounded text-center">Grid Item 3</div>
        <div className="bg-secondary/50 p-4 rounded text-center">Grid Item 4</div>
        <div className="bg-secondary/50 p-4 rounded text-center">Grid Item 5</div>
        <div className="bg-secondary/50 p-4 rounded text-center">Grid Item 6</div>
      </div>
    </div>
  </div>
);

export const Spacing = () => (
  <div className="p-6 space-y-6">
    <h2 className="text-2xl font-bold text-primary">Spacing System</h2>

    <div className="space-y-4">
      <div className="bg-card p-2 rounded">Padding: p-2</div>
      <div className="bg-card p-4 rounded">Padding: p-4</div>
      <div className="bg-card p-6 rounded">Padding: p-6</div>
      <div className="bg-card p-8 rounded">Padding: p-8</div>
    </div>

    <div className="flex gap-2">
      <div className="bg-primary/20 p-4 rounded">Gap: 2</div>
      <div className="bg-primary/20 p-4 rounded">Gap: 2</div>
    </div>

    <div className="flex gap-4">
      <div className="bg-primary/20 p-4 rounded">Gap: 4</div>
      <div className="bg-primary/20 p-4 rounded">Gap: 4</div>
    </div>
  </div>
);

export const Interactive = () => (
  <div className="p-6 space-y-6">
    <h2 className="text-2xl font-bold text-primary">Interactive Elements</h2>

    <div className="space-y-4">
      <Button className="transition-all duration-300 hover:scale-105 hover:shadow-lg">
        Hover to Scale
      </Button>

      <div className="bg-card p-4 rounded-lg hover:bg-card/80 transition-colors cursor-pointer">
        Hover to Change Background
      </div>

      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 cursor-pointer">
        Gradient with Hover Effect
      </div>

      <div className="group bg-card p-4 rounded-lg cursor-pointer">
        <div className="group-hover:translate-x-2 transition-transform duration-300">
          Group Hover Effect →
        </div>
      </div>
    </div>
  </div>
);

export const ResponsiveDesign = () => (
  <div className="p-6 space-y-6">
    <h2 className="text-2xl font-bold text-primary">Responsive Design</h2>

    <div className="bg-card p-4 rounded-lg">
      <p className="mb-4 text-sm text-muted-foreground">
        Resize your browser to see responsive behavior
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div className="bg-primary/10 p-4 rounded text-center">
          <div className="block sm:hidden">Mobile</div>
          <div className="hidden sm:block md:hidden">Tablet</div>
          <div className="hidden md:block lg:hidden">Desktop</div>
          <div className="hidden lg:block">Large</div>
        </div>
        <div className="bg-secondary/50 p-4 rounded text-center">Responsive Item</div>
        <div className="bg-muted/50 p-4 rounded text-center">Responsive Item</div>
        <div className="bg-primary/20 p-4 rounded text-center">Responsive Item</div>
      </div>
    </div>
  </div>
);
