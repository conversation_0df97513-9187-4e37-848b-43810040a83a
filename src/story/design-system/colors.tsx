import { COLORS } from '@/const/colors';

function ThemeColors() {
  const colors = Object.entries(COLORS)
    .map(([key, value]) => {
      if (typeof value === 'string') {
        return {
          name: key,
          list: [{ name: 'DEFAULT', value }],
        };
      }

      return {
        name: key,
        list: Object.entries(value).map(([subKey, value]) => ({
          name: `${subKey}`,
          value: value,
        })),
      };
    })
    .sort((a, b) => a.name.localeCompare(b.name));

  return (
    <>
      {colors.map((item) => (
        <div key={item.name} className="mb-4">
          <div className="flex flex-wrap gap-4">
            {item.list.map((color) => (
              <div key={color.name} className="p-4 rounded-lg shadow w-[240px] bg-card">
                <div className="font-semibold">
                  {color.name === 'DEFAULT' ? item.name : `${item.name}-${color.name}`}
                </div>
                <div className="flex items-center gap-2">
                  <div className="relative w-10 h-10 rounded-sm overflow-hidden">
                    <div className="absolute inset-0 bg-[linear-gradient(45deg,#e5e7eb_25%,transparent_25%),linear-gradient(-45deg,#e5e7eb_25%,transparent_25%),linear-gradient(45deg,transparent_75%,#e5e7eb_75%),linear-gradient(-45deg,transparent_75%,#e5e7eb_75%)] bg-[length:8px_8px]" />
                    <div className="absolute w-6 h-6 shadow-md overflow-hidden rounded-full left-2 top-2 before:content-['x'] before:text-lg before:text-red-500 before:font-bold before:absolute before:left-1/2 before:top-1/2 before:-translate-x-1/2 before:-translate-y-1/2">
                      <div
                        className="absolute inset-0"
                        style={{ backgroundColor: `${color.value}` }}
                      />
                    </div>
                  </div>
                  <div className="text-xs">{color.value}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </>
  );
}

export default ThemeColors;
