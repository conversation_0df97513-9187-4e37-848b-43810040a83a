import { But<PERSON> } from '@/components/shared/button';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';

import { fn } from 'storybook/test';

const meta = {
  title: 'Components/Button',
  component: Button,
  tags: ['autodocs'],
  args: { onClick: fn() },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Default Button',
  },
};

export const Variants: Story = {
  args: {
    children: 'Variants',
    variant: 'default',
  },
};

export const Sizes: Story = {
  args: {
    children: 'Sizes',
    size: 'xs',
  },
};
