import { DateTimePicker } from '@/components/shared/datetime-picker';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { fn } from 'storybook/test';

const meta = {
  title: 'Components/DateTimePicker',
  component: DateTimePicker,
  tags: ['autodocs'],
  args: {
    onChange: fn(),
  },
  argTypes: {
    onChange: { action: 'changed' },
    value: { control: 'text' },
    defaultValue: { control: 'text' },
    max: { control: 'text' },
    min: { control: 'text' },
    disabled: { control: 'boolean' },
    'aria-invalid': { control: 'boolean' },
  },
} satisfies Meta<typeof DateTimePicker>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onChange: fn(),
  },
};

const max = new Date().toISOString().split('T')[0];
export const Max: Story = {
  args: {
    max,
  },
};

const min = new Date().toISOString().split('T')[0];
export const Min: Story = {
  args: {
    min,
  },
};

const defaultValue = '2025-06-01T14:00';
export const DefaultValue: Story = {
  args: {
    defaultValue,
  },
};

export const OnChange = () => {
  const [value, setValue] = useState<string>('');
  return (
    <div>
      <DateTimePicker onChange={setValue} />
      <p>onChange: {value}</p>
    </div>
  );
};
