import { Checkbox } from '@/components/shared/checkbox';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';

import { fn } from 'storybook/test';

const meta = {
  title: 'Components/Checkbox',
  component: Checkbox,
  tags: ['autodocs'],
  args: { onChange: fn() },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const OnChange = () => {
  const [value, setValue] = useState<boolean>(false);
  return (
    <div>
      <Checkbox onCheckedChange={() => setValue(!value)} checked={value} />
      <p>onChange: {String(value)}</p>
    </div>
  );
};
