import { NumberInput } from '@/components/shared/number-input';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { fn } from 'storybook/test';

const meta = {
  title: 'Components/NumberInput',
  component: NumberInput,
  tags: ['autodocs'],
  args: { onChange: fn() },
} satisfies Meta<typeof NumberInput>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: 'number-input',
    unit: 'kg',
    value: '100',
    maxIntLen: 3,
    maxDecLen: 2,
    className: 'w-full',
  },
};

export const Disabled: Story = {
  args: {
    name: 'number-input',
    unit: 'kg',
    value: '',
    disabled: true,
  },
};

export const ErrorValidation: Story = {
  args: {
    name: 'number-input',
    unit: 'kg',
    value: '100',
    'aria-invalid': true,
  },
};

export const OnChange = () => {
  const [value, setValue] = useState<string>('');
  return (
    <div>
      <NumberInput
        name="number-input"
        maxIntLen={3}
        maxDecLen={2}
        unit="kg"
        value={value}
        onChange={setValue}
      />
      <p>onChange: {String(value)}</p>
    </div>
  );
};
