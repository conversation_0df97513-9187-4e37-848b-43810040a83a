import { ENDPOINTS } from '@/api/endpoints';
import { get, post } from '@/api/request';
import type {
  CheckupDateRecordResponse,
  CheckupDateRecordResquest,
  CheckupDateRecordTypeResponse,
} from '@/types/checkup-date-record';

export const checkupDateRecordAPI = {
  getCheckupDateRecordTypeList: () =>
    post<CheckupDateRecordTypeResponse>(`${ENDPOINTS.CHECKUP_DATE_RECORD.TYPE_LIST}`),

  getCheckupDateRecordList: (query: CheckupDateRecordResquest) =>
    get<CheckupDateRecordResponse>(`${ENDPOINTS.CHECKUP_DATE_RECORD.LIST}`, query),
};
