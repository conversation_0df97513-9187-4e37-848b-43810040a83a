import type { Asset, PutAsset, SendAsset } from '@/types/health-core-init';
import { ENDPOINTS } from '../endpoints';
import { type ApiResponse, get, post, put } from '../request';

export interface RiskOnOffGetResponse {
  on_of_list: Asset[];
}

export class CalcJudgementRequest {
  asset_list: SendAsset[] = [];
  on_assets: number[] = [];
}

export class RiskOnOffPutRequest {
  assets: PutAsset[] = [];
}

const ONOFFSTATE_URL = ENDPOINTS.RISK.BASE + ENDPOINTS.RISK.ONOFFSTATE;
const CALC_JUDGEMENT_URL = ENDPOINTS.RISK.BASE + ENDPOINTS.RISK.JUDGEMENTDATECALC;
export const riskAPI = {
  getOnOffList: () => get<RiskOnOffGetResponse>(ONOFFSTATE_URL),
  putOnOffList: (request: RiskOnOffPutRequest) => put<ApiResponse>(ONOFFSTATE_URL, request),
  calcJudgementDate: (request: CalcJudgementRequest) =>
    post<ApiResponse>(CALC_JUDGEMENT_URL, request),
};
