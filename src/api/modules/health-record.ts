import { ENDPOINTS } from '@/api/endpoints';
import { get, post } from '@/api/request';
import type {
  DailyVitalDataRequest,
  DailyVitalDataResponse,
  ThumbnailDataRequest,
  ThumbnailDataResponse,
  VitalDataRequest,
  VitalDataResponse,
} from '@/types/health-record';

export const healthRecordAPI = {
  vitalData: (data: VitalDataRequest) => post<VitalDataResponse>(ENDPOINTS.RECORD.VITAL_DATA, data),
  dailyVitalData: (query: DailyVitalDataRequest) =>
    get<DailyVitalDataResponse>(ENDPOINTS.RECORD.DAILY_VITAL_DATA, query),
  thumbnailData: (query: ThumbnailDataRequest) =>
    get<ThumbnailDataResponse>(ENDPOINTS.RECORD.THUMBNAIL_DATA, query),
};
