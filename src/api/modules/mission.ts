import { ENDPOINTS } from '@/api/endpoints';
import { get, post, put } from '@/api/request';

import type {
  ContentInterface,
  MissionAchieveUpdateRequest,
  MissionDetailRequest,
  MissionListResponse,
  MissionPopupDetailResponse,
  MissionSectionRequest,
  MissionSectionResponse,
  QuizAchieveUpdatelRequest,
  QuizAchieveUpdatelResponse,
  QuizMissionDetailRequest,
  QuizMissionDetailResponse,
  missionReadDetailRequest,
} from '@/types/mission';

export const missionAPI = {
  missionList: () => get<MissionListResponse>(ENDPOINTS.MISSION.MISSION_LIST),
  missionDetail: (data: MissionDetailRequest) =>
    post<ContentInterface>(ENDPOINTS.MISSION.MISSION_DETAIL, data),
  missionPopupDetail: () =>
    post<MissionPopupDetailResponse>(ENDPOINTS.MISSION.MISSION_POPUP_DETAIL),
  achieveUpdate: (query: MissionAchieveUpdateRequest) =>
    put<MissionPopupDetailResponse>(ENDPOINTS.MISSION.ACHIEVE_UPDATE, query),
  missionReadDetail: (query: missionReadDetailRequest) =>
    post(ENDPOINTS.MISSION.MISSION_READ_DETAIL, query),
  missionSectionList: (query: MissionSectionRequest) =>
    get<MissionSectionResponse>(ENDPOINTS.MISSION.MISSION_SECTION_LIST, query),

  quizMissionDetail: (query: QuizMissionDetailRequest) =>
    post<QuizMissionDetailResponse>(ENDPOINTS.MISSION.QUIZ_MISSION_DETAIL, query),

  quizAchieveUpdate: (query: QuizAchieveUpdatelRequest) =>
    post<QuizAchieveUpdatelResponse>(ENDPOINTS.MISSION.QUIZ_ACHIEVE_UPDATE, query),
};
