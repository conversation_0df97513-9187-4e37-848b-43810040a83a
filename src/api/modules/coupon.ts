import { ENDPOINTS } from '@/api/endpoints';
import { get, post } from '@/api/request';
import type {
  CouponDetailResponse,
  CouponListResponse,
  SearchCouponRequest,
  SearchCouponResponse,
  ShopDetailResponse,
  SuggestedCouponResponse,
} from '@/types/coupon-types';

export const couponAPI = {
  searchCoupon: (data: SearchCouponRequest) =>
    post<SearchCouponResponse>(ENDPOINTS.COUPON.SEARCH, data),
  searchCouponByKeyword: (keyword: string) =>
    get<SuggestedCouponResponse>(ENDPOINTS.COUPON.KEYWORD_SEARCH, { keyword }),
  getHomeCoupon: () => get<CouponListResponse>(ENDPOINTS.COUPON.HOME_COUPON),
  getRecommendations: () => get<CouponListResponse>(ENDPOINTS.COUPON.RECOMMEND),
  getPopularCoupon: () => get<CouponListResponse>(ENDPOINTS.COUPON.POPULAR),
  couponDetail: (id: string) =>
    get<CouponDetailResponse>(ENDPOINTS.COUPON.COUPON_DETAIL.replace('[id]', id)),
  shopDetail: (id: string) =>
    get<ShopDetailResponse>(ENDPOINTS.COUPON.SHOP_DETAIL.replace('[id]', id)),
  useCoupon: (id: string) => post(ENDPOINTS.COUPON.USE_COUPON.replace('[id]', id)),
};
