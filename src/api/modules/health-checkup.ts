import type {
  DailyExamData,
  ExamDaysData,
  ExamDaysRequest,
  ExamResultData,
  ExamResultQuery,
  HealthExamResultData,
  HealthExamResultRequest,
} from '@/types/health-checkup-input';
import { ENDPOINTS } from '../endpoints';
import { del, get, patch, post } from '../request';

const HEALTH_CHECKUP_URL = ENDPOINTS.HEALTH_CHECKUP_INPUT;

// 受診日一覧取得	exam-days	GET
// 受診日変更	exam-day	POST
// 日別健診判定結果取得	daily-result	GET
// 日別健診結果削除	daily-results/{date}	DELETE
// カテゴリ別検査結果取得	exam-result	GET
// カテゴリ別健診結果取得	health-exam-result	GET
// カテゴリ別健診結果登録	health-exam-result	PATCH
// 健康診断情報取得(連携)	medical-exam	GET
// 健康診断情報登録(連携)	medical-exam	POST
export const healthCheckupAPI = {
  // 受診日一覧取得
  getExamDays: () => get<ExamDaysData>(`${HEALTH_CHECKUP_URL}/exam-days`),
  // 受診日変更
  updateExamDay: (request: ExamDaysRequest) =>
    post<ExamDaysData>(`${HEALTH_CHECKUP_URL}/exam-day`, request),
  // 日別健診判定結果取得
  getDailyExamData: (query: { examDay: string }) =>
    get<DailyExamData>(`${HEALTH_CHECKUP_URL}/daily-result`, query),
  // 日別健診結果削除
  deleteDailyExamData: (date: string) =>
    del<DailyExamData>(`${HEALTH_CHECKUP_URL}/daily-results/${date}`),
  // カテゴリ別健診結果取得
  getHealthExamResultData: (query: ExamResultQuery) =>
    get<HealthExamResultData>(`${HEALTH_CHECKUP_URL}/health-exam-result`, query),
  // カテゴリ別健診結果登録
  updateHealthExamResultData: (request: HealthExamResultRequest) =>
    patch<HealthExamResultData>(`${HEALTH_CHECKUP_URL}/health-exam-result`, request),
  // カテゴリ別検査結果取得
  getExamResultData: (query: ExamResultQuery) =>
    get<ExamResultData>(`${HEALTH_CHECKUP_URL}/exam-result`, query),
};
