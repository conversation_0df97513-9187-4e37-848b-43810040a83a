import type {
  GifteeInfo,
  LotteryDetailResponse,
  LotteryDrawingResponse,
  LotteryHistoryResponse,
  LotteryResponse,
  LotteryResultResponse,
  UserLotteryChoiceListResponse,
} from '@/types/lottery-select';
import { ENDPOINTS } from '../endpoints';
import { type ApiResponse, get, post } from '../request';
export const LorreryPrizeAPI = {
  //抽選開催情報
  getLotteryInfo: () => get<LotteryDrawingResponse>(ENDPOINTS.LOTTERY_PRIZE.LOTTERY_OPEN),
  getLotteryHistory: (yyyyMmStr: string) =>
    get<LotteryHistoryResponse>(ENDPOINTS.LOTTERY_PRIZE.LOTTERY_HISTORY, { yyyyMm: yyyyMmStr }),
  //ユーザ抽選結果取得API
  getLotteryResult: (lotteryIdStr: string) =>
    get<LotteryResultResponse>(ENDPOINTS.LOTTERY_PRIZE.LOTTERY_USER_RESULT, {
      lotteryId: lotteryIdStr,
    }),
  //抽選詳細取得
  getLotteryResultDetail: (lotteryIdStr: string) =>
    get<LotteryDetailResponse>(ENDPOINTS.LOTTERY_PRIZE.LOTTERY_DETAIL, {
      lotteryId: lotteryIdStr,
    }),
  //ユーザ抽選景品選択取得
  getLotteryUserChoice: (lotteryIdStr: string) =>
    get<UserLotteryChoiceListResponse>(ENDPOINTS.LOTTERY_PRIZE.LOTTERY_USER_CHOICE, {
      lotteryId: lotteryIdStr,
    }),
  //景品一覧取得
  getLotteryPrizeList: (lotteryIdStr: string) =>
    get<LotteryResponse>(ENDPOINTS.LOTTERY_PRIZE.LOTTERY_PRIZE_LIST, {
      lotteryId: lotteryIdStr,
    }),
  //ギフティボックス発行API
  getGifteeUrl: (lotteryIdStr: string, prizeId: number) =>
    get<GifteeInfo>(ENDPOINTS.LOTTERY_PRIZE.LOTTERY_GIFTEE, {
      lotteryId: lotteryIdStr,
      prizeId: prizeId,
    }),
};
