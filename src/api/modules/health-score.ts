import { ENDPOINTS } from '@/api/endpoints';
import { get, post } from '@/api/request';
import type { QueryParams } from '@/types/api-common';
import type {
  HealthScoreHistoryListResponse,
  HealthScoreJudgmentResponse,
  HealthScoreMissionResponse,
} from '@/types/risk-score';

export const healthScoreAPI = {
  assetGraph: (queryParams: { month: number; asset_type: string[] }) =>
    post<HealthScoreHistoryListResponse>(`${ENDPOINTS.HEALTHY.ASSET_GRAPH}`, {
      ...queryParams,
    }),

  judgmentResult: (queryParams: QueryParams = {}) =>
    get<HealthScoreJudgmentResponse>(`${ENDPOINTS.HEALTHY.JUDGMENT_RESULT}`, {
      ...queryParams,
    }),

  mission: (queryParams: QueryParams = {}) =>
    get<HealthScoreMissionResponse>(`${ENDPOINTS.HEALTHY.MISSION}`, {
      ...queryParams,
    }),
};
