import type {
  DataConnectSettingResponse,
  FitBitRedirectResponse,
  FitbitTokenResponse,
  OmronTokenResponse,
  VitalFromResponse,
  VitalHistoryData,
  VitalTypeResponse,
} from '@/types/data-connect';
import { ENDPOINTS } from '../endpoints';
import { type ApiResponse, get, post, put } from '../request';

const FITBIT_RECRECT_URL = ENDPOINTS.DATA_CONNECT.FITBIT_RECRECT;

export class VitalSettingRequest {
  vitalPostChange: number | undefined;
  vitalType: number | undefined;
}

export class ServiceSettingRequest {
  deviceType: number | undefined;
  connectFlg: number | undefined;
  scope: number[] | undefined;
  accountId: string | undefined;
  changeUserId: string | undefined;
}

export class FitBitRequest {
  scope: string | undefined;
  code: string | undefined;
}

export class SyncVitalDataRequest {
  weightData: WeightData[] | undefined;
  sleepData: SleepData[] | undefined;
  bloodPressureData: BloodPressureData[] | undefined;
}
export class WeightData {
  measureDate: string | undefined;
  weight: string | undefined;
  fatPercentage: string | undefined;
  bodyFrom: number | undefined;
}

export class SleepData {
  measureDate: string | undefined;
  sleepAt: string | undefined;
  wakeAt: string | undefined;
  sleepFrom: number | undefined;
  sleepTime: number | undefined;
}

export class BloodPressureData {
  measureDate: string | undefined;
  bpAmMeasureAt: string | undefined;
  highBpAm: number | undefined;
  lowBpAm: number | undefined;
  bpPmMeasureAt: string | undefined;
  highBpPm: number | undefined;
  lowBpPm: number | undefined;
  bloodPressureFrom: number | undefined;
}

export class OmronRequest {
  accountId: string | undefined;
}

export const dataConnectAPI = {
  fitbitRedirect: (request: FitBitRequest) =>
    post<FitBitRedirectResponse>(FITBIT_RECRECT_URL, request),
  deviceSetting: () => get<DataConnectSettingResponse>(ENDPOINTS.DATA_CONNECT.DEVICE_SETTING),
  vitalHistory: (vitalType: number) =>
    get<VitalHistoryData>(`${ENDPOINTS.DATA_CONNECT.VITAL_DATA_FROM}vitalType=${vitalType}`),
  vitalSetting: (request: VitalSettingRequest) =>
    put<ApiResponse>(ENDPOINTS.DATA_CONNECT.VITAL_SETTING, request),
  serviceSetting: (request: ServiceSettingRequest) =>
    post<ApiResponse>(ENDPOINTS.DATA_CONNECT.SERVICE_SETTING, request),
  getFitbitToken: () => get<FitbitTokenResponse>(ENDPOINTS.DATA_CONNECT.FITBIT_TOKEN),
  getVitalFrom: (isToast?: boolean) =>
    get<VitalFromResponse>(
      ENDPOINTS.DATA_CONNECT.VITAL_FROM,
      isToast !== undefined ? { showErrorToast: isToast } : undefined,
    ),
  syncVitalData: (request: SyncVitalDataRequest, isToast?: boolean) =>
    post<ApiResponse>(
      ENDPOINTS.DATA_CONNECT.SYNC_VITAL,
      request,
      isToast !== undefined ? { showErrorToast: isToast } : undefined,
    ),
  omronRedirect: (request: OmronRequest) =>
    post<ApiResponse>(ENDPOINTS.DATA_CONNECT.OMRON_RECRECT, request),
  getOmronToken: () => get<OmronTokenResponse>(ENDPOINTS.DATA_CONNECT.OMRON_TOKEN),
  getVitaType: (uesrequest: ServiceSettingRequest) =>
    get<VitalTypeResponse>(ENDPOINTS.DATA_CONNECT.VITAL_TYPE, uesrequest),
};
import { omron } from '@/const/app';
import axios from 'axios';
//TYPE_WEIGHT :1
// TYPE_BLOOD:2

export async function getOneUserVitalData(accessToken: string, omron_connect_id: string) {
  const fromTime = new Date();
  fromTime.setDate(fromTime.getDate() - 1);
  fromTime.setHours(0, 0, 0, 0);

  const url = `${omron.resource.domain}${omron.resource.wp_id}/partners/${omron.partner.id}/groups/${omron.group.id}/users/${omron_connect_id}/vital-signs`;

  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${accessToken}`,
    MyLoginName: omron.auth.username,
    IncludeDelete: 'False',
    SearchDateFrom: `${Math.floor(fromTime.getTime() / 1000)}000`,
  };

  try {
    const response = await axios.get(url, { headers });
    return response.data;
  } catch (error) {
    console.error('Error fetching vital data:', error);
    throw error;
  }
}
