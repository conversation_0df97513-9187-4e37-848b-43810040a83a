import { ENDPOINTS } from '@/api/endpoints';
import { get, put } from '@/api/request';

import type {
  LevelInfoResponse,
  PushFlagData,
  PushSendFlagRequest,
  TermsResponse,
} from '@/types/menu';

export const menuAPI = {
  levelInfo: () => get<LevelInfoResponse>(ENDPOINTS.MENU.LEVEL_INFO),
  pushSendFlg: (query: PushSendFlagRequest) => put(ENDPOINTS.MENU.SEND_PUSH_FLG, query),
  getPushFlg: () => get<PushFlagData>(ENDPOINTS.MENU.GET_PUSH_FLG),
  getTerms: () => get<TermsResponse>(ENDPOINTS.MENU.MENU_TERMS),
};
