import type {
  GetHomePointCardInfoResponse,
  GetLotteryInfoResponse,
  GetPointCardInfoResponse,
  GetPointHistoryListRequest,
  GetPointHistoryListResponse,
  GetResetInfoResponse,
} from '@/types/point';
import { ENDPOINTS } from '../endpoints';
import { get } from '../request';

export const pointAPI = {
  getHomePointCardInfo: () =>
    get<GetHomePointCardInfoResponse>(`${ENDPOINTS.POINT.HOME_POINT_CARD_INFO}`),

  getResetInfo: () => get<GetResetInfoResponse>(`${ENDPOINTS.POINT.GET_RESET_INFO}`),

  getPointCardInfo: () => get<GetPointCardInfoResponse>(`${ENDPOINTS.POINT.GET_POINT_CARD_INFO}`),

  getLotteryInfo: () => get<GetLotteryInfoResponse>(`${ENDPOINTS.POINT.GET_LOTTERY_INFO}`),

  getPointHistoryList: (queryParams: GetPointHistoryListRequest) =>
    get<GetPointHistoryListResponse>(`${ENDPOINTS.POINT.GET_POINT_HISTORY_LIST}`, {
      ...queryParams,
    }),
};
