import { ENDPOINTS } from '@/api/endpoints';
import { type ApiResponse, del, get, post, put } from '@/api/request';
import type {
  AppLotteryResponse,
  LotteryChoiceParam,
  PrizeDetailResponse,
  PrizeListResponse,
  UserLotteryChoiceResponse,
} from '@/types/prize-select';

export const prizeSelectPageAPI = {
  getPrizeList: (queryParams: { lotteryId: string }) =>
    get<PrizeListResponse>(`${ENDPOINTS.PRIZE_SELECT.PRIZE_LIST}`, {
      ...queryParams,
    }),
  getPrizeDetail: (queryParams: { prizeId: number }) =>
    get<PrizeDetailResponse>(`${ENDPOINTS.PRIZE_SELECT.PRIZE_DETAIL}`, {
      ...queryParams,
    }),
  getUserLotteryChoice: (queryParams: { lotteryId: string }) =>
    get<UserLotteryChoiceResponse>(`${ENDPOINTS.PRIZE_SELECT.USER_LOTTERY_CHOICE}`, {
      ...queryParams,
    }),
  setRegLotteryChoice: (request: LotteryChoiceParam) =>
    post<ApiResponse>(`${ENDPOINTS.PRIZE_SELECT.REG_LOTTERY_CHOICE}`, request),

  setUpdLotteryChoice: (request: LotteryChoiceParam) =>
    post<ApiResponse>(`${ENDPOINTS.PRIZE_SELECT.UPD_LOTTERY_CHOICE}`, request),

  setDelLotteryChoice: (lotteryId: string | undefined) =>
    del<ApiResponse>(`${ENDPOINTS.PRIZE_SELECT.DEL_LOTTERY_CHOICE}/${lotteryId}`),

  getAppLottery: () => get<AppLotteryResponse>(`${ENDPOINTS.PRIZE_SELECT.GET_APP_LOTTERY}`),
};
