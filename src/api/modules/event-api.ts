import type {
  EventAttendRequest,
  EventDetailResponse,
  EventKeywordSearchResponse,
  HomeEventListResponse,
  InitOptionsResponse,
  PopularEventListResponse,
  RecommendedEventListResponse,
  SearchEventListRequest,
  SearchEventListResponse,
} from '@/types/event-types';

import { ENDPOINTS } from '../endpoints';
import { get, post } from '../request';

const BASE_URL = ENDPOINTS.EVENT;

// イベント詳細	GET	/api/v1/app/events/{eventId}
// ホームイベント	GET	/api/v1/app/events/home
// おすすめイベント	GET	/api/v1/app/events/recommended
// 人気イベント	GET	/api/v1/app/events/popular
// イベントOptions初期化	GET	/api/v1/app/events/init-options
// イベント一覧	POST	/api/v1/app/events/searde
// イベントキーワード検索	POST	/api/v1/app/events/keyword-search
// イベント参加	POST	/api/v1/app/events/attend
export const eventAPI = {
  // イベント詳細
  getEventDetail: (eventId: string, latitude: string, longitude: string) =>
    get<EventDetailResponse>(`${BASE_URL}/${eventId}?latitude=${latitude}&longitude=${longitude}`),

  // ホームイベント
  getHomeEvents: () => get<HomeEventListResponse>(`${BASE_URL}/home`),

  // おすすめイベント
  getRecommendedEvents: () => get<RecommendedEventListResponse>(`${BASE_URL}/recommended`),

  // 人気イベント
  getPopularEvents: () => get<PopularEventListResponse>(`${BASE_URL}/popular`),

  // イベントOptions初期化
  initOptions: () => get<InitOptionsResponse>(`${BASE_URL}/init-options`),

  // イベント一覧
  searchEvents: (queryParams: SearchEventListRequest) =>
    post<SearchEventListResponse>(`${BASE_URL}/search`, queryParams),

  // イベントキーワード検索
  keywordSearch: (keyword: string) =>
    get<EventKeywordSearchResponse>(`${BASE_URL}/keyword-search`, { keyword }),

  // イベント参加
  attendEvent: (queryParams: EventAttendRequest) => post(`${BASE_URL}/attend`, queryParams),
};
