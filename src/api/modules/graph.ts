import type {
  BloodGlucoseGraphData,
  BloodPressureGraphData,
  EnergyGraphData,
  FatPercentageGraphData,
  GraphSearchParam,
  SleepGraphData,
  StepDistanceTimeGraphData,
  StepGraphData,
  WeightBmiGraphData,
} from '@/types/graph';
import { ENDPOINTS } from '../endpoints';
import { get } from '../request';

const GRAPHS_URL = ENDPOINTS.GRAPH;

export const graphAPI = {
  // 歩数グラフ取得
  getStepGraph: (queryParams?: GraphSearchParam) =>
    get<StepGraphData>(`${GRAPHS_URL}/step`, queryParams),

  // 歩行距離・時間グラフ取得
  getStepDistanceTimeGraph: (queryParams?: GraphSearchParam) =>
    get<StepDistanceTimeGraphData>(`${GRAPHS_URL}/step-distance-time`, queryParams),

  // 睡眠時間グラフ取得
  getSleepGraph: (queryParams?: GraphSearchParam) =>
    get<SleepGraphData>(`${GRAPHS_URL}/sleep`, queryParams),

  // 体脂肪率グラフ取得
  getFatPercentageGraph: (queryParams?: GraphSearchParam) =>
    get<FatPercentageGraphData>(`${GRAPHS_URL}/fat-percentage`, queryParams),

  // 体重・BMIグラフ取得
  getWeightBmiGraph: (queryParams?: GraphSearchParam) =>
    get<WeightBmiGraphData>(`${GRAPHS_URL}/weight-bmi`, queryParams),

  // エネルギー消費グラフ取得
  getEnergyGraph: (queryParams?: GraphSearchParam) =>
    get<EnergyGraphData>(`${GRAPHS_URL}/energy`, queryParams),

  // 血圧グラフ取得
  getBloodPressureGraph: (queryParams?: GraphSearchParam) =>
    get<BloodPressureGraphData>(`${GRAPHS_URL}/blood-pressure`, queryParams),

  // 血糖値グラフ取得
  getBloodGlucoseGraph: (queryParams?: GraphSearchParam) =>
    get<BloodGlucoseGraphData>(`${GRAPHS_URL}/blood-glucose`, queryParams),
};
