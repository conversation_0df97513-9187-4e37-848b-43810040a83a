import type {
  CommonRequest,
  CommonResponse,
  RankingExclUserResponse,
  UserRankingInfoListResponse,
} from '@/types/ranking';
import { ENDPOINTS } from '../endpoints';
import { get } from '../request';

export const rankingAPI = {
  // 利用者のランキング情報取得API
  getUserRankingInfoList: () =>
    get<UserRankingInfoListResponse>(`${ENDPOINTS.RANKING.USER_RANKING_INFO_LIST}`),
  // 歩数ランキング日別グループ別取得API
  getStepGroupDailyRankingList: (queryParams: CommonRequest) =>
    get<CommonResponse>(`${ENDPOINTS.RANKING.STEP_GROUP_DAILY_RANKING_LIST}`, {
      ...queryParams,
    }),
  // 歩数ランキング日別全体別取得API
  getStepDailyRankingList: (queryParams: CommonRequest) =>
    get<CommonResponse>(`${ENDPOINTS.RANKING.STEP_DAILY_RANKING_LIST}`, {
      ...queryParams,
    }),
  // 歩数ランキング週別グループ別取得API
  getStepGroupWeeklyRankingList: (queryParams: CommonRequest) =>
    get<CommonResponse>(`${ENDPOINTS.RANKING.STEP_GROUP_WEEKLY_RANKING_LIST}`, {
      ...queryParams,
    }),
  // 歩数ランキング週別全体別取得API
  getStepWeeklyRankingList: (queryParams: CommonRequest) =>
    get<CommonResponse>(`${ENDPOINTS.RANKING.STEP_WEEKLY_RANKING_LIST}`, {
      ...queryParams,
    }),
  // ミッションランキング日別取得API
  getMissionDailyRankingList: (queryParams: CommonRequest) =>
    get<CommonResponse>(`${ENDPOINTS.RANKING.MISSION_DAILY_RANKING_LIST}`, {
      ...queryParams,
    }),
  // ミッションランキング週別取得API
  getMissionWeeklyRankingList: (queryParams: CommonRequest) =>
    get<CommonResponse>(`${ENDPOINTS.RANKING.MISSION_WEEKLY_RANKING_LIST}`, {
      ...queryParams,
    }),
  // 団体ランキング取得API
  getOrganizerRankingList: (queryParams: CommonRequest) =>
    get<CommonResponse>(`${ENDPOINTS.RANKING.ORGANIZER_RANKING_LIST}`, {
      ...queryParams,
    }),
  // ランキング対象外ユーザーを取得
  getRankingExclUser: (queryParams: { organizerId: number | undefined }) =>
    get<RankingExclUserResponse>(`${ENDPOINTS.RANKING.RANKING_EXCL_USER}`, {
      ...queryParams,
    }),
};
