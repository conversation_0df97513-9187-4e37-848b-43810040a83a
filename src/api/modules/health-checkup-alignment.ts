import type {
  ConnectionServiceResponse,
  GetMedicalExamsResponse,
  HealthInfoRequest,
  HealthInfoResponse,
  MycardRequest,
  MycardResponseSendRequest,
  WellcleUploadRequest,
  WellcleUploadResponse,
} from '@/types/health-checkup';
import { ENDPOINTS } from '../endpoints';
import { get, post } from '../request';

const HEALTH_INFO = ENDPOINTS.HEALTH_CHECKUP.HEALTH_INFO;
const CONNECTION_SERVICE = ENDPOINTS.HEALTH_CHECKUP.CONNECTION_SERVICE;
const MEDICAL_EXAM = ENDPOINTS.HEALTH_CHECKUP.MEDICAL_EXAM;
const WELLCLE_UPLOAD = ENDPOINTS.HEALTH_CHECKUP.WELLCLE_UPLOAD;
const HEALTH_INFO_REQUEST = ENDPOINTS.HEALTH_CHECKUP.HEALTH_INFO_REQUEST;

export const healthCheckupAPI = {
  // 接続サービス設定取得
  getConnectionService: () => get<ConnectionServiceResponse>(`${CONNECTION_SERVICE}`),
  // 健康診断API
  getMedicalExam: () => get<GetMedicalExamsResponse>(`${MEDICAL_EXAM}`),
  postMedicalExam: (queryParams?: MycardResponseSendRequest) =>
    post(`${MEDICAL_EXAM}`, queryParams),
  // 生活習慣病チェック推定(連携)
  getWellcleUpload: (queryParams?: WellcleUploadRequest) =>
    post<WellcleUploadResponse>(`${WELLCLE_UPLOAD}`, queryParams),
  // 本人複数同意・本人確認要求(連携)
  postHealthInfoRequest: (queryParams?: HealthInfoRequest) =>
    post<HealthInfoResponse>(`${HEALTH_INFO_REQUEST}`, queryParams),
  // マイナポータル確認完了(連携)
  postMycard: (queryParams?: MycardRequest) =>
    post<MycardResponseSendRequest>(`${HEALTH_INFO}`, queryParams),
};
