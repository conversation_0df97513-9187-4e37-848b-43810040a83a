import { ENDPOINTS } from '@/api/endpoints';
import { get, post } from '@/api/request';
import type {
  CancelFriendRequest,
  FriendsResponse,
  InviteLinkResponse,
  SendCancelYellRequest,
  StepRankingRequest,
  StepRankingResponse,
} from '@/types/friend';

export const friendAPI = {
  setpRanking: (query: StepRankingRequest) =>
    get<StepRankingResponse>(ENDPOINTS.FRIEND.STEP_RANKING, query),
  cancelFriend: (data: CancelFriendRequest) => post(ENDPOINTS.FRIEND.CANCEL_FRIEND, data),
  inviteLink: () => get<InviteLinkResponse>(ENDPOINTS.FRIEND.INVITE_LINK),
  sendCancelYell: (query: SendCancelYellRequest) => get(ENDPOINTS.FRIEND.SEND_CANCEL_YELL, query),
  friends: () => get<FriendsResponse>(ENDPOINTS.FRIEND.FRIENDS),
  addFriendByUUID: (uuid: string) => post(ENDPOINTS.FRIEND.ADD_FRIEND, { uuid }),
};
