import type { StepTarget } from '@/types/step-goal';
import { ENDPOINTS } from '../endpoints';
import { type ApiResponse, get, post } from '../request';

// export class StepTargetResponse {
//   data: StepTarget | undefined;
// }

// export class PeriodComputeResponse {
//   data: StepTarget | undefined;
// }

export class StepTargetRequest {
  targetPlan: number | undefined;
  stepTarget: number | undefined;
}

const STEP_TARGET_URL = ENDPOINTS.GOAL.STEP_TARGET;
const PERIOD_COMPUTE_URL = ENDPOINTS.GOAL.PERIOD_COMPUTE;
export const stepGoalAPI = {
  getStepTargetInfo: () => get<StepTarget>(STEP_TARGET_URL),
  getPeriodStep: () => get<StepTarget>(PERIOD_COMPUTE_URL),
  setStepTargetInfo: (request: StepTargetRequest) => post<ApiResponse>(STEP_TARGET_URL, request),
};
