import type {
  FavoriteRequest,
  MapSearchRequest,
  MapSearchResponse,
  PinRequest,
  PinResponse,
  PostRequest,
  PostResponse,
  SearchCandidateResponse,
} from '@/types/map-types';
import { ENDPOINTS } from '../endpoints';
import { del, get, post } from '../request';

export const mapAPI = {
  // お気に入り登録API
  toggleFavorite: (favoriteRequest: FavoriteRequest) =>
    post(ENDPOINTS.MAP.FAVORITE, favoriteRequest),
  // お気に入り解除API
  //   removeFromFavorite: (favoriteRequest: FavoriteRequest) =>
  //     del(`${BASE_URL}/favorite`, favoriteRequest),

  listPins: (pinRequest: PinRequest) => post<PinResponse>(ENDPOINTS.MAP.PINGS, pinRequest),
  listResults: (mapSearchRequest: MapSearchRequest) =>
    post<MapSearchResponse>(ENDPOINTS.MAP.RESULTS, mapSearchRequest),
  searchCandidates: (inputContent: string) =>
    get<SearchCandidateResponse>(ENDPOINTS.MAP.SEARCH_CANDIDATES, inputContent),
  listPosts: (postRequest: PostRequest) => post<PostResponse>(ENDPOINTS.MAP.POSTS, postRequest),
};
