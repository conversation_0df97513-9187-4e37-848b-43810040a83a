import { ENDPOINTS } from '@/api/endpoints';
import { get, post } from '@/api/request';
import type {
  DoTerminateRequest,
  OrganizerResponse,
  StartupResponse,
} from '@/types/out-login-types';

export const outLoginAPI = {
  doTerminate: (data: DoTerminateRequest) => post(ENDPOINTS.MENU.DO_TERMINATE, data),
  getOrganizers: () => get<OrganizerResponse>(ENDPOINTS.MENU.GET_ORGANIZERS),
  startup: () => post<StartupResponse>(ENDPOINTS.MENU.STARTUP),
};
