import type {
  MissionDetailResponse,
  MissionListResponse,
  MyPageResponse,
  UpdateMissionRequest,
  UpdateMissionResponse,
} from '@/types/my-page';
import { ENDPOINTS } from '../endpoints';
import { get, post } from '../request';

const GAMIFICATION_URL = ENDPOINTS.GAMIFICATION;

export const gamificationAPI = {
  // マイページ情報取得API
  getMyPage: () => get<MyPageResponse>(`${GAMIFICATION_URL}/mypage`),
  // レベルアップミッション一覧取得API
  missionList: (request: { homeUseFlg: number }) =>
    post<MissionListResponse>(`${GAMIFICATION_URL}/level-up-mission`, request),
  // レベルアップミッション更新API
  updateMission: (request: UpdateMissionRequest) =>
    post<UpdateMissionResponse>(`${GAMIFICATION_URL}/level-up-mission`, request),
  // レベルアップミッション詳細取得API
  getMissionDetail: (request: { missionId: string }) =>
    get<MissionDetailResponse>(`${GAMIFICATION_URL}/level-up-mission-detail`, request),
};
