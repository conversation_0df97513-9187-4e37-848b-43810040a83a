import type {
  AccountProfilePatchRequest,
  AccountProfileResponse,
  AuthLoginRequest,
  AuthLoginResponse,
  FriendOrganizerResponse,
  LitaTokenResponse,
  LitaUserInfoResponse,
  OrganizerInfoResponse,
  OrganizerSetupResponse,
  RegisterAnonymousRequest,
  RegisterAnonymousResponse,
  UserOrganizersResponse,
  UserProfileRequest,
  organizerTermsResponse,
  registerWithAuthRequest,
  registerWithAuthResponse,
} from '@/types/register';
import { ENDPOINTS } from '../endpoints';
import { get, patch, post, put } from '../request';

export const registerAPI = {
  // 一時トークン取得API
  tempToken: (query: { deviceId: string }) =>
    post<{ tempToken: string }>(ENDPOINTS.REGISTER.TEMP_TOKEN, query),
  // 団体情報取得API
  organizerInfo: (query: { organizerCode: string[] }) =>
    post<OrganizerInfoResponse>(ENDPOINTS.REGISTER.ORGANIZER_INFO, query),
  // フレンド情報取得
  friendInfo: (query: { friendUuid: string }) =>
    get<FriendOrganizerResponse>(ENDPOINTS.REGISTER.FRIEND_INFO, query),
  // 団体セットアップ情報取得API
  organizerSetup: (query: { organizerId: number; fcnId: number }) =>
    get<OrganizerSetupResponse>(ENDPOINTS.REGISTER.ORGANIZER_SETUP, query),
  // アカウント情報取得
  accountProfile: (query: { deviceId: string }) =>
    get<AccountProfileResponse>(ENDPOINTS.REGISTER.ACCOUNT_PROFILE, query),
  // アカウント情報更新API
  accountProfilePatch: (query: AccountProfilePatchRequest) =>
    patch(ENDPOINTS.REGISTER.ACCOUNT_PROFILE, query),
  // Lita認証トークン取得API
  litaToken: (query: { code: string }) =>
    post<LitaUserInfoResponse>(ENDPOINTS.REGISTER.LITA_TOKEN, query),
  // ログインAPI
  authLogin: (query: AuthLoginRequest) =>
    post<AuthLoginResponse>(ENDPOINTS.REGISTER.AUTH_LOGIN, query),
  // 匿名ユーザー新規作成API
  registerAnonymous: (query: RegisterAnonymousRequest) =>
    post<RegisterAnonymousResponse>(ENDPOINTS.REGISTER.REGISTER_ANONYMOUS, query),
  // ユーザー所属団体追加
  userOrganizers: (query: { organizerId: number; groupId: number }) =>
    post<UserOrganizersResponse>(ENDPOINTS.REGISTER.USER_ORGANIZERS, query),
  // ユーザー情報更新
  userProfile: (query: UserProfileRequest) => put(ENDPOINTS.REGISTER.USER_PROFILE, query),
  // トークンリフレッシュAPI
  refreshToken: (query: { refreshToken: string }) => post(ENDPOINTS.REGISTER.REFRESH_TOKEN, query),
  // SMS認証コード送信API
  smsCode: () => post(ENDPOINTS.REGISTER.USER_SMS_CODE),
  // SMS認証コード検証
  smsCodeVerify: (query: { code: string }) => post(ENDPOINTS.REGISTER.USER_SMS_CODE_VERIFY, query),
  // 新規ユーザー作成
  registerWithAuth: (query: registerWithAuthRequest) =>
    post<registerWithAuthResponse>(ENDPOINTS.REGISTER.REGISTER_WITH_AUTH, query),
  // 利用規約取得API
  organizerTerms: (query: { organizerId: number }) =>
    get<organizerTermsResponse>(ENDPOINTS.REGISTER.ORGANIZER_TERMS, query),
  // フレンド追加API
  addFriend: (query: { uuid: string }) => post(ENDPOINTS.REGISTER.ADD_FRIEND, query),
};
