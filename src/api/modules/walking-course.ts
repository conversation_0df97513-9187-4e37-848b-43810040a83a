import { ENDPOINTS } from '@/api/endpoints';
import { get, post } from '@/api/request';
import type {
  ListWalkingCourseRequest,
  ListWalkingCourseResponse,
  MapWalkingCourseRequest,
  MapWalkingCourseResponse,
  RecordStampRequest,
  RecordStampResponse,
  SearchWalkingCourseNameRequest,
  SearchWalkingCourseNameResponse,
  WalkingCourseDetailResponse,
  WalkingCourseListRequest,
  WalkingCourseListResponse,
} from '@/types/walking-course';

export const walkingCourseAPI = {
  walkingCourseList: (data: WalkingCourseListRequest) =>
    post<WalkingCourseListResponse>(ENDPOINTS.WALKING_COURSE.LIST, data),
  walkingCourseDetail: (id: string) =>
    get<WalkingCourseDetailResponse>(ENDPOINTS.WALKING_COURSE.DETAIL.replace('[id]', id)),
  listWalkingCourse: (data: ListWalkingCourseRequest) =>
    post<ListWalkingCourseResponse>(ENDPOINTS.WALKING_COURSE.WALKING_COURSE_LIST, data),
  searchNameWalkingCourse: (data: SearchWalkingCourseNameRequest) =>
    post<SearchWalkingCourseNameResponse>(ENDPOINTS.WALKING_COURSE.WALKING_COURSE_NAME, data),
  mapWalkingCourse: (data: MapWalkingCourseRequest) =>
    post<MapWalkingCourseResponse>(ENDPOINTS.WALKING_COURSE.WALKING_COURSE_NAME, data),
  recordStamp: (data: RecordStampRequest) =>
    post<RecordStampResponse>(ENDPOINTS.WALKING_COURSE.RECORD_STAMP, data),
};
