import type { CodeOption } from '@/types/code-types';
import type { LatLng } from '@/types/map';
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getOptionLabel = (options: CodeOption[] | undefined, value: any): string => {
  if (!value || !options?.length) return '-';
  const option = options.find((opt) => String(opt.value) === String(value));
  return option?.label || '-';
};

export function calculateDistance(latLng1: LatLng, latLng2: LatLng) {
  const lat1 = latLng1.lat;
  const lng1 = latLng1.lng;
  const lat2 = latLng2.lat;
  const lng2 = latLng2.lng;
  const radLat1 = (lat1 * Math.PI) / 180.0;
  const radLat2 = (lat2 * Math.PI) / 180.0;
  const a = radLat1 - radLat2;
  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  const s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.sin(a / 2) ** 2 + Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(b / 2) ** 2,
      ),
    );
  const distance = s * 6378.137; // EARTH_RADIUS;
  return Math.round(distance * 1000);
}
