'use client';

/**
 * Polyfills for older browsers (Android 9 + Chrome 74)
 * 古いブラウザ向けのポリフィル (Android 9 + Chrome 74)
 */
import 'core-js/stable/promise/all-settled';
import 'core-js/stable/string/replace-all';
import 'core-js/stable/array/at';
import 'core-js/stable/object/has-own';
import 'core-js/stable/object/from-entries';
import 'core-js/stable/array/flat';
import 'core-js/stable/array/flat-map';
import 'core-js/stable/string/trim-start';
import 'core-js/stable/string/trim-end';

// Manual polyfills for features not covered by core-js
if (typeof window !== 'undefined' && typeof globalThis === 'undefined') {
  const globalObject = (() => {
    if (typeof self !== 'undefined') return self;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    throw new Error('Unable to locate global object');
  })();

  // @ts-ignore - globalThis polyfill is necessary for older browsers
  window.globalThis = globalObject;
}

// ResizeObserver polyfill (Chrome 64+が必要だが、完全対応はChrome 88+)
// if (typeof window !== 'undefined' && typeof ResizeObserver === 'undefined') {
//   let resizeObserverLoaded = false;

//   const loadResizeObserver = async (): Promise<void> => {
//     if (!resizeObserverLoaded) {
//       try {
//         const module = await import('resize-observer-polyfill');
//         (window as unknown as Record<string, unknown>).ResizeObserver = module.default;
//         resizeObserverLoaded = true;
//       } catch (error) {
//         console.warn('Failed to load ResizeObserver polyfill:', error);
//       }
//     }
//   };

//   // Load polyfill when needed
//   loadResizeObserver();
// }

// IntersectionObserver improvements for older versions
if (typeof window !== 'undefined' && typeof IntersectionObserver !== 'undefined') {
  // Chrome 74にはあるが、一部のオプションが不完全
  const originalIntersectionObserver = window.IntersectionObserver;

  // Ensure rootMargin parsing is more robust
  window.IntersectionObserver = class extends originalIntersectionObserver {
    constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
      // Normalize rootMargin format for older browsers
      let processedOptions = options;
      if (options?.rootMargin) {
        const rootMargin = options.rootMargin.replace(/px/g, '').replace(/\s+/g, ' ').trim();
        processedOptions = {
          ...options,
          rootMargin: rootMargin
            .split(' ')
            .map((value) => `${value}px`)
            .join(' '),
        };
      }
      super(callback, processedOptions);
    }
  };
}

// Fetch improvements (Chrome 74は基本的にサポートしているが、一部のオプションが不完全)
// if (typeof window !== 'undefined' && typeof fetch !== 'undefined') {
//   const originalFetch = window.fetch;

//   window.fetch = (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
//     // AbortController support check
//     let processedInit = init;
//     if (init?.signal && typeof AbortController === 'undefined') {
//       console.warn('AbortController not supported in this browser');
//       const { signal, ...restInit } = init;
//       processedInit = restInit;
//     }

//     return originalFetch.call(window, input, processedInit);
//   };
// }

// Smooth scrolling polyfill
// if (
//   typeof window !== 'undefined' &&
//   typeof Element !== 'undefined' &&
//   !Element.prototype.scrollTo
// ) {
//   // @ts-ignore - scrollTo polyfill for older browsers
//   Element.prototype.scrollTo = function (
//     this: Element,
//     optionsOrX?: ScrollToOptions | number,
//     y?: number,
//   ): void {
//     if (typeof optionsOrX === 'object' && optionsOrX !== null) {
//       this.scrollLeft = optionsOrX.left || 0;
//       this.scrollTop = optionsOrX.top || 0;
//     } else if (typeof optionsOrX === 'number') {
//       this.scrollLeft = optionsOrX;
//       this.scrollTop = y || 0;
//     }
//   };
// }

// CSS.supports polyfill
if (typeof window !== 'undefined' && (typeof CSS === 'undefined' || !CSS.supports)) {
  if (typeof CSS === 'undefined') {
    (window as unknown as Record<string, unknown>).CSS = {};
  }

  CSS.supports = (property: string, value?: string): boolean => {
    try {
      const testElement = document.createElement('div');
      if (value !== undefined) {
        testElement.style.setProperty(property, value);
        return testElement.style.getPropertyValue(property) === value;
      }

      // Property:value format
      const [prop, val] = property.split(':').map((s) => s.trim());
      testElement.style.setProperty(prop, val);
      return testElement.style.getPropertyValue(prop) === val;
    } catch {
      return false;
    }
  };
}

// Performance improvements for older browsers
if (typeof window !== 'undefined' && typeof performance === 'undefined') {
  (window as unknown as Record<string, unknown>).performance = {
    now: (): number => Date.now(),
    timing: {
      navigationStart: Date.now(),
    },
  };
}

if (typeof window !== 'undefined') {
  console.log('Polyfills loaded for Chrome 74+ compatibility');
}
