'use client';

import { checkupDateRecordAPI } from '@/api/modules/checkup-date-record';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import CalendarIcon from '@/components/shared/calendar-icon';
import DeleteIcon from '@/components/shared/delete-icon';
import SectionTitle from '@/components/shared/section-title';
import { TextButton } from '@/components/shared/text-button';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import type { CheckupDateRecord } from '@/types/checkup-date-record';
import { formatDate } from '@/utils/date-format';
import { useEffect, useState } from 'react';
import AddRecordButton from './_components/add-record-button';
import DeleteMessageContent from './_components/delete-confirm-content';
import Empty from './_components/empty';

type CheckupDateRecordDetailPageProps = {
  searchParams: {
    id: string;
    title: string;
  };
};

const mockData: CheckupDateRecord[] = [
  { date: '2025-01-01', id: '1', typeId: '1' },
  { date: '2025-01-02', id: '2', typeId: '1' },
  { date: '2025-01-03', id: '3', typeId: '1' },
];

export default function CheckupDateRecordDetailPage({
  searchParams,
}: CheckupDateRecordDetailPageProps) {
  const [data, setData] = useState<CheckupDateRecord[]>([]);
  const [isEdit, setIsEdit] = useState(false);
  const { setDialog } = useMessageDialog();
  const id = searchParams.id;
  const fetchData = (typeId: string) => {
    // checkupDateRecordAPI.getCheckupDateRecordList({ id: typeId }).then((res) => {
    //   if (res.list.length > 0) {
    //     setData(res.list);
    //   }
    // });
    const filteredData = mockData.filter((item) => item.typeId === typeId);
    setData(filteredData);
  };
  useEffect(() => {
    fetchData(id);
  }, [id]);
  const handleSwitchEdit = () => {
    setIsEdit((prev) => !prev);
  };
  const handleEdit = (event: React.ChangeEvent<HTMLInputElement>, item: CheckupDateRecord) => {
    console.log(item.id, event.target.value);
    const newRecord = {
      date: event.target.value,
      id: item.id,
      typeId: item.typeId,
    };
    setData(data.map((item) => (item.id === newRecord.id ? newRecord : item)));
  };
  const handleDelete = (record: CheckupDateRecord) => {
    setDialog(false);
    setData(data.filter((item) => item.date !== record.date));
  };
  const handleDeleteConfirm = (record: CheckupDateRecord) => {
    setDialog(true, {
      content: <DeleteMessageContent record={record} />,
      footer: (
        <>
          <Button variant="destructive" onClick={() => handleDelete(record)}>
            削除
          </Button>
          <TextButton variant="muted" onClick={() => setDialog(false)}>
            キャンセル
          </TextButton>
        </>
      ),
    });
  };
  const handleAdd = (event: React.ChangeEvent<HTMLInputElement>) => {
    const date = event.target.value;
    const newRecord = {
      date,
      id: '',
      typeId: id,
    };
    setData([...data, newRecord]);
  };
  const handleSave = () => {
    console.log(data);
  };
  return (
    <>
      <TopBar title={searchParams.title} />
      <SectionTitle className="flex justify-between">
        受診日一覧
        {data.length > 0 && (
          <TextButton aria-label="編集する" onClick={handleSwitchEdit}>
            編集する
          </TextButton>
        )}
      </SectionTitle>
      {data.length > 0 && (
        <>
          <div className="bg-card mx-6 rounded-2xl px-6 py-2">
            {data.map((item, index) => (
              <div
                key={index}
                className="flex items-center border-b border-text-muted last:border-b-0"
              >
                <div className="text-base text-left py-4">
                  {formatDate(item.date, 'yyyy年MM月dd日(d)')}
                </div>
                {isEdit && (
                  <>
                    <span className="flex-1" />
                    <Button aria-label="edit" className="mr-4 relative" variant="icon">
                      <CalendarIcon />
                      <input
                        type="date"
                        onChange={(event) => handleEdit(event, item)}
                        className="opacity-0 absolute inset-0"
                        value={item.date}
                      />
                    </Button>
                    <Button
                      aria-label="delete"
                      variant="icon"
                      onClick={() => handleDeleteConfirm(item)}
                    >
                      <DeleteIcon />
                    </Button>
                  </>
                )}
              </div>
            ))}
          </div>
          {isEdit && (
            <div className="flex justify-center items-center mt-4">
              <AddRecordButton handleAdd={handleAdd} />
            </div>
          )}
        </>
      )}
      {data.length === 0 && <Empty handleAdd={handleAdd} />}
      {isEdit && (
        <div className="flex justify-center items-center mt-4 mx-6">
          <Button className="w-full" onClick={handleSave}>
            保存
          </Button>
        </div>
      )}
    </>
  );
}
