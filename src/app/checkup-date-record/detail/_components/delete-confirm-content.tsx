import type { CheckupDateRecord } from '@/types/checkup-date-record';
import { formatDate } from '@/utils/date-format';

export default function DeleteMessageContent({ record }: { record: CheckupDateRecord }) {
  return (
    <div>
      <div>
        「{formatDate(record.date, 'yyyy年MM月dd日(d)')}」の受診記録を削除してよろしいですか？
      </div>
      <div className="text-sm text-text-muted mt-2">※一度削除すると元に戻すことはできません</div>
    </div>
  );
}
