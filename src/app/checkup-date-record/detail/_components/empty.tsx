import AddRecordButton from './add-record-button';

export default function Empty({
  handleAdd,
}: { handleAdd: (event: React.ChangeEvent<HTMLInputElement>) => void }) {
  return (
    <div className="bg-card flex flex-col items-center mx-6 rounded-2xl px-6 pb-4 pt-6">
      <div className="w-[120px] h-[120px] bg-gray-200" aria-label="画像のプレースホルダー" />
      <div className="text-base text-center mb-4 mt-2">登録データがありません</div>
      <div className="flex justify-center items-center">
        <AddRecordButton handleAdd={handleAdd} />
      </div>
    </div>
  );
}
