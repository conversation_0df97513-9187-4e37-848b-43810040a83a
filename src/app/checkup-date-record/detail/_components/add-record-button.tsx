import { TextButton } from '@/components/shared/text-button';
import { Plus } from 'lucide-react';

export default function AddRecordButton({
  handleAdd,
}: { handleAdd: (event: React.ChangeEvent<HTMLInputElement>) => void }) {
  return (
    <TextButton aria-label="受診日を追加" className="h-6 relative">
      <Plus className="w-5 h-5" />
      受診日を追加
      <input
        type="date"
        className="absolute inset-0 opacity-0"
        onChange={(event) => handleAdd(event)}
      />
    </TextButton>
  );
}
