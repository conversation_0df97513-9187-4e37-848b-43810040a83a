import TopBar from '@/components/layout/top-bar';
import MenuPanel from '@/components/shared/menu-panel';
import { APP_TEXT } from '@/const/text/app';

export default function CheckupDateRecordPage() {
  const menuItems = [
    {
      label: '胃がん検診',
      href: '/checkup-date-record/detail?id=1&title=胃がん検診',
      subLabel: <SubLabel date="2025年1月1日" />,
    },
    {
      label: '胃がん検診2',
      href: '/checkup-date-record/detail?id=2&title=胃がん検診2',
      subLabel: <SubLabel date="2025年1月1日" />,
    },
  ];
  return (
    <>
      <TopBar title={APP_TEXT.CHECKUP_DATE_RECORD.TITLE} />
      <div className="p-6 pt-4">{APP_TEXT.CHECKUP_DATE_RECORD.SUBTITLE}</div>
      <MenuPanel menuItems={menuItems} className="rounded-lg" />
    </>
  );
}

function SubLabel({ date }: { date: string }): React.ReactNode {
  return <div className="text-sm text-text-secondary">{date}</div>;
}
