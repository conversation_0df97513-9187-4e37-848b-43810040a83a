'use client';

import { dataConnectAPI } from '@/api/modules/data-connect';
import { LorreryPrizeAPI } from '@/api/modules/lottery-prize';
import TopBar from '@/components/layout/top-bar';
import SectionTitle from '@/components/shared/section-title';
import { Button } from '@/components/ui/button';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { LotteryDetail, LotteryDrawingResponse } from '@/types/lottery-select';
import { sendMessageToNative } from '@/utils/native-bridge';
import { DialogClose } from '@radix-ui/react-dialog';
import { useEffect, useState } from 'react';
import { SelectDateDrawer } from './_components/date-drawer';
import LotteryHistoryList from './_components/lottery-list';
import LotteryPage from './_components/lottery-page';
import { LotteryAlertDialogComponent } from './_components/lottery-push-dialog';
import NoLotteryPage from './_components/no-lottery-page';

export default function LotteryResultPage() {
  const { setLoading } = useLoading();
  const [queryDate, setQueryDate] = useState<Date>();
  const searchParams = useSearchParams();
  const status = searchParams.get('status');
  const [lotteryInfo, setLotteryInfo] = useState<LotteryDrawingResponse>();
  const [historyLottery, setHistoryLottery] = useState<LotteryDetail[]>();

  // const { isShow, setDialog } = useMessageDialog();
  // function showLotteryDialog() {
  //   setDialog(true, {
  //     content: (
  //       <div className="flex flex-col gap-2 text-center font-normal text-black text-base">
  //         <p>{APP_TEXT.CONNECT.GOOGLE_PLAY_CONTEXT}</p>
  //       </div>
  //     ),
  //     outSideClickClose: false,
  //     footer: (
  //       <div className="flex-col font-bold">
  //         <Button
  //           className="w-full"
  //           onClick={() => {
  //             setDialog(false);
  //           }}
  //         >
  //           <div className="font-semibold text-base">{APP_TEXT.CONNECT.OPEN_GOOGLE_PLAY}</div>
  //         </Button>
  //         <DialogClose asChild>
  //           <div className="mt-4 w-full text-gray-600 text-center font-bold  text-base">
  //             {COMMON_TEXT.BUTTON.CLOSE}
  //           </div>
  //         </DialogClose>
  //       </div>
  //     ),
  //   });
  // }

  const getLotteryInfo = async () => {
    setLoading(true);
    LorreryPrizeAPI.getLotteryInfo()
      .then((response) => {
        setLoading(false);
        if (response.lotteryDetail) {
          setLotteryInfo(response);
        }
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };
  useEffect(() => {
    //1：抽選ミッションがありません
    if (status !== '1') {
      getLotteryInfo();
    }
  }, [status]);
  useEffect(() => {
    getLotteryHistoryList('');
  }, []);
  const getLotteryHistoryList = async (date: string) => {
    setLoading(true);
    LorreryPrizeAPI.getLotteryHistory(date)
      .then((response) => {
        setLoading(false);
        setHistoryLottery(response.historyLottery);
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };
  useEffect(() => {
    console.log(status);
  }, [status]);

  // const isDrawing = params.status === 'drawing';
  function handleSelect(value: { year: string; month: string }): void {
    const date = `${value.year}-${value.month}`;
    getLotteryHistoryList(date);

    const year = Number.parseInt(value.year);
    const month = Number.parseInt(value.month);
    setQueryDate(new Date(year, month));

    // setQueryDateStr(date);
  }

  return (
    <>
      <TopBar title={APP_TEXT.LOTTERY.TITLE} />
      {status === '1' ? (
        <NoLotteryPage />
      ) : (
        lotteryInfo && <LotteryPage lotteryDrawingData={lotteryInfo} />
      )}

      <SectionTitle className="py-0">{APP_TEXT.LOTTERY.ENDED}</SectionTitle>
      <div className="bg-white p-6">
        <SelectDateDrawer
          title={APP_TEXT.LOTTERY.TITLE}
          minYear={2025}
          maxYear={new Date().getFullYear()}
          value={{
            year: queryDate?.getFullYear() ?? 0,
            month: queryDate?.getMonth() ?? 0,
          }}
          onSelect={handleSelect}
        />
        {historyLottery && <LotteryHistoryList historyLottery={historyLottery} />}
      </div>
    </>
  );
}
