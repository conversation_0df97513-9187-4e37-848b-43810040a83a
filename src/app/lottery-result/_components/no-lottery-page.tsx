import SectionTitle from '@/components/shared/section-title';
import { Button } from '@/components/ui/button';
import { APP_TEXT } from '@/const/text/app';
import type { LotteryDrawingResponse } from '@/types/lottery-select';
import AppliedPrizesContent from './applied-prizes-content';
import PointsList from './point-list';
import SectionTitleTwoLines from './title-context';

//抽選ページはありません
const NoLotteryPage = () => {
  return (
    <div className="p-4 rounded-lg ">
      <h2 className="text-lg font-semibold mb-4">{APP_TEXT.LOTTERY.DURING}</h2>
      <div className="bg-white rounded-2xl shadow-md h-[104px] flex items-center justify-center">
        <p className="text-sm text-gray-600">{APP_TEXT.LOTTERY.NO_LOTTERY}</p>
      </div>
    </div>
  );
};

export default NoLotteryPage;
