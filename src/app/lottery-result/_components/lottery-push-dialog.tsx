import { LorreryPrizeAPI } from '@/api/modules/lottery-prize';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog'; // 根据你的实际路径调整
import { Button } from '@/components/ui/button';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import type { UsrLottoRes } from '@/types/lottery-select';
import { DateFormatEnum, formatDate } from '@/utils/date-format';
import { DialogClose } from '@radix-ui/react-dialog';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';

interface AlertDialogComponentProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  lotteryId: string | React.ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
  navigateTo?: string; // 跳转路径
  className?: string;
}

export const LotteryAlertDialogComponent: React.FC<AlertDialogComponentProps> = ({
  open,
  onOpenChange,
  lotteryId,
  onConfirm,
  onCancel,
  className = '',
}) => {
  const router = useRouter();
  const { setLoading } = useLoading();

  // からAPI获取实际抽奖结果
  const [isWinning, setIsWinning] = useState(-1);
  const [prizeResult, setPrizeResult] = useState<UsrLottoRes>();

  const getLotteryResult = useCallback(() => {
    setLoading(true);
    LorreryPrizeAPI.getLotteryResult(lotteryId as string)
      .then((response) => {
        setLoading(false);
        const list = response.UsrLottoRes.winPrizes;
        setPrizeResult(response.UsrLottoRes);
        //当たりの判定
        if (list && list.length > 0) {
          setIsWinning(1);
        } else {
          setIsWinning(1);
        }
      })
      .catch((error) => {
        setLoading(false);
        setIsWinning(0);
      });
  }, []);
  useEffect(() => {
    getLotteryResult();
  }, []);
  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  const handleConfirm = () => {
    if (isWinning === 2) {
      router.push(`/lottery-result/result?lotteryId=${lotteryId}`);
    } else if (isWinning === 1) {
      // TODO 实物情况
    }
    onOpenChange(false);
  };
  const blueRiband = '/images/lottery/ic_blue_riband.png';
  const blueBg = '/images/lottery/ic_blue_loser.png';

  const redRiband = '/images/lottery/ic_red_riband.png';
  const redBg = '/images/lottery/ic_red_win.png';
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent
        className={`"sm:max-w-[425px] w-[calc(100vw-48px)] rounded-3xl bg-white p-0 ${className}`}
      >
        <div>
          {/* 背景图層 */}
          <div className="relative w-full h-[327px]">
            <img
              className="absolute top-0 left-0 w-full h-auto object-cover rounded-t-3xl"
              src={isWinning ? redBg : blueBg}
              alt=""
            />
            <div className="relative w-full h-[54px] top-1">
              <img
                src={isWinning ? redRiband : blueRiband}
                alt="Background"
                className="absolute inset-0 w-full object-cover"
              />
              <h2 className="absolute inset-0 flex items-center justify-center text-white text-center font-bold text-xl">
                {prizeResult?.lotteryTitle}
              </h2>
            </div>
            {!isWinning && (
              <div className="absolute p-4 text-center left-6 right-6 bottom-3 h-[52px] bg-gray-100 p-2 rounded-2xl text-gray-800 text-sm">
                {`${APP_TEXT.LOTTERY.LOTTERY_DAY} : ${prizeResult?.lotteryOn && formatDate(prizeResult.lotteryOn, DateFormatEnum.DATE_YMD_JP)}`}
              </div>
            )}
          </div>
          {isWinning === 0 ? (
            <div className="p-6">
              <p className="mt-4 text-black text-base">{APP_TEXT.LOTTERY.LOTTERY_PUSH_LOSE}</p>
              <Button
                onClick={handleCancel}
                className=" w-full text-white bg-primary rounded-[23px] font-bold h-12 text-[16px] mt-4"
              >
                {COMMON_TEXT.BUTTON.CLOSE}
              </Button>
            </div>
          ) : (
            <div className="p-6">
              <p className="mt-2 text-black text-xl font-bold">
                {APP_TEXT.LOTTERY.LOTTERY_PUSH_WIN_TITLE}
              </p>
              <p className="mt-2 text-black text-base">
                {isWinning === 2
                  ? APP_TEXT.LOTTERY.LOTTERY_PUSH_WIN
                  : APP_TEXT.LOTTERY.PRIZE_IS + prizeResult?.lotteryOn + APP_TEXT.LOTTERY.DATE_SEND}
              </p>
              <Button
                onClick={handleConfirm}
                className=" w-full text-white bg-primary rounded-[23px] font-bold h-12 text-[16px] mt-4"
              >
                {isWinning === 2 ? APP_TEXT.LOTTERY.RECEIVE : APP_TEXT.LOTTERY.GO_LOTTERY_DETAIL}
              </Button>
              <div
                onClick={handleCancel}
                onKeyUp={(e) => e.key === 'Enter' && handleCancel()}
                className="mt-4 w-full text-gray-600 text-center font-bold  text-base"
              >
                {COMMON_TEXT.BUTTON.CLOSE}
              </div>
            </div>
          )}
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
};
