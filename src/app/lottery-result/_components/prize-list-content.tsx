import { APP_TEXT } from '@/const/text/app';
import type { PrizeItem } from '@/types/lottery-select';
import { ChevronRight } from 'lucide-react';

// データソースがないのでanyを一時的に使用
interface PrizeListContentProps {
  prizes: PrizeItem[];
}

// 景品リストコンテンツ
const PrizeListContent = ({ prizes }: PrizeListContentProps) => {
  return (
    <div className="pb-4">
      <div className="bg-card-mainLight3 rounded-2xl overflow-hidden">
        {prizes.map((prize, index) => (
          <div key={prize.prizeId}>
            <div className="flex items-center gap-3 py-3 px-6">
              <div className="w-14 h-14 bg-gray-200 rounded-lg flex-shrink-0">
                <img src={prize.prizeImageFile} alt="" className="w-full h-auto rounded-lg" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-text-primary text-base">{prize.prizeNm}</h4>
                <div className="text-sm leading-6 text-text-secondary">
                  {prize.prizeNum + APP_TEXT.LOTTERY.PARTICIPANT_NAME_SUFFIX}
                </div>
                <div className="text-sm font-bold text-primary">
                  {prize.needPoint + APP_TEXT.LOTTERY.PER_ENTRY_UNIT}
                </div>
              </div>

              <ChevronRight className="w-4 h-4 text-text-primary flex-shrink-0" />
            </div>
            {index < prizes.length - 1 && (
              <div className="px-6">
                <div className="h-px bg-border" />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PrizeListContent;
