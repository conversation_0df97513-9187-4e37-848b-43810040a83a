import { TextButton } from '@/components/shared/text-button';
import { PrizeType } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import type { WinPrize } from '@/types/lottery-select';
import { Check } from 'lucide-react';

interface PrizeListProps {
  prizes: WinPrize[];
  onPrizeReceive?: (prizeId: number, prizeType: number) => void;
}

/**
 * 奨品一覧コンポーネント。ユーザーが抽選で獲得した奨品を表示します。
 *
 * @param {PrizeListProps} props - コンポーネントプロパティ
 * @param {Array} props.prizes - 奨品配列。奨品情報を含みます。
 * @param {Function} props.onPrizeReceive - 奨品受領コールバック関数
 * @returns {JSX.Element} 奨品一覧のReactコンポーネント
 *
 * @description
 * 1. 実物奨品とデジタル奨品の2種類を表示できます。
 * 2. デジタル奨品は受領期限とステータスを表示します。
 * 3. 奨品受領機能を提供します。
 * 4. 空状態の表示を処理します。
 */
export default function WinPrizeList({ prizes, onPrizeReceive }: PrizeListProps) {
  // 处理奖品领取
  const handlePrizeReceive = (prizeId: number, prizeType: number) => {
    onPrizeReceive?.(prizeId, prizeType);
  };

  // 空状态处理
  if (!prizes || prizes.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {APP_TEXT.LOTTERY.NO_SELECTED_PRIZE}
      </div>
    );
  }
  return (
    <div className="space-y-4">
      {prizes.map((prize) => {
        //1:品物、2.電子ギフト　3.dポイント
        //実物と非実物ではUIが一致しません
        const isDigital = prize.prizeType !== PrizeType.ITEM;

        return (
          <div
            key={prize.prizeId}
            className={`bg-card rounded-lg  flex-row mb-4'} flex items-center gap-3 `}
          >
            <img
              className="w-14 h-14 rounded-md flex-shrink-0"
              src={prize.prizeImageFile}
              alt={prize.prizeNm}
            />
            <div className="flex-1">
              <h3 className="text-base font-normal text-foreground">{prize.prizeNm}</h3>

              {isDigital ? (
                <p className="text-sm text-muted-foreground mt-1">
                  {APP_TEXT.LOTTERY.CLAIM_PERIOD}：{prize.pickUpDeadline}{' '}
                  {APP_TEXT.LOTTERY.DATE_END}
                </p>
              ) : (
                <p className="text-sm text-muted-foreground">
                  {APP_TEXT.LOTTERY.PRIZE_IS + prize.prizeSchedDt + APP_TEXT.LOTTERY.DATE_SEND}
                </p>
              )}

              {prize.invalidFlg === 0 && (
                <p className="text-sm text-destructive-foreground mt-1">
                  {APP_TEXT.LOTTERY.CLAIM_EXPIRED}
                </p>
              )}
            </div>

            {isDigital && (
              <div className="flex-shrink-0">
                <TextButton
                  disabled={prize.invalidFlg === 1}
                  onClick={() =>
                    prize.recvDispFlg !== 1 &&
                    prize.invalidFlg !== 1 &&
                    handlePrizeReceive(prize.prizeId, prize.prizeType)
                  }
                >
                  {
                    // 未受取
                    prize.recvDispFlg === 0 ? (
                      //有効
                      prize.invalidFlg === 0 ? (
                        APP_TEXT.LOTTERY.RECEIVE
                      ) : (
                        //無効
                        <span className="chart-2">{APP_TEXT.LOTTERY.RECEIVE}</span>
                      )
                    ) : (
                      // 受取済
                      <div className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-primary" />
                        <span>{APP_TEXT.LOTTERY.RECEIVED}</span>
                      </div>
                    )
                  }
                </TextButton>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
