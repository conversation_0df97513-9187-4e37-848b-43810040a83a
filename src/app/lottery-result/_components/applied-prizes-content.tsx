import SectionTitle from '@/components/shared/section-title';
import { APP_TEXT } from '@/const/text/app';
import type { PrizeData } from '@/types/lottery-select';

interface AppliedPrizesContentProps {
  appliedPrizes: PrizeData[];
  totalCount: number | undefined;
  totalPoints: number | undefined;
  title: string | undefined;
}

const AppliedPrizesContent = ({
  appliedPrizes,
  totalCount,
  totalPoints,
  title,
}: AppliedPrizesContentProps) => {
  return (
    <div className="pb-4 min-h-32 ">
      <div className="bg-card-mainLight3 rounded-2xl overflow-hidden px-6 py-4">
        {title && <div className="text-base  py-0  font-bold ">{title}</div>}
        {appliedPrizes ? (
          appliedPrizes.map((prize, index) => (
            <div key={prize.prizeId}>
              <div className="flex items-center justify-between py-2 ">
                {/* 左側：商品情報 */}
                <div className="flex-1">
                  <h4 className="text-  text-text-primary">{prize.prizeNm}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-text-secondary">
                      {APP_TEXT.LOTTERY.APPLICATION_COUNT}：{prize.prizeAppNum}
                    </span>
                    {prize.prizeDonation === 0 && (
                      <span className="px-1 py-0.5 bg-border text-xs text-text-secondary rounded">
                        {APP_TEXT.LOTTERY.DONATE}
                      </span>
                    )}
                  </div>
                </div>

                {/* 右側：ポイント */}
                <div className="text-right">
                  <div className="text-base font-medium text-text-primary">
                    {prize.chosenPrizePoint}
                    <span className="text-sm font-normal text-text-primary ml-1">p</span>
                  </div>
                </div>
              </div>

              {/* 区切り線 (最後のアイテム以外) */}
              {index < appliedPrizes.length - 1 && (
                <div className="px-4">
                  <div className="h-px bg-border" />
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="flex items-center justify-center text-center h-[86px] text-sm text-gray-500">
            <span>{APP_TEXT.LOTTERY.NO_SELECTED_PRIZE}</span>
          </div>
        )}

        {/* 合計行 */}
        {totalCount && totalCount && (
          <div className="border-t border-border">
            <div className="flex items-center justify-between py-4">
              <div className="flex-1">
                <h4 className="text-sm text-text-primary font-medium">{APP_TEXT.LOTTERY.TOTAL}</h4>
                <div className="text-xs text-text-secondary mt-1">
                  {`${appliedPrizes.length}${APP_TEXT.LOTTERY.PRIZE}・${totalCount}${APP_TEXT.LOTTERY.MOUTH}`}
                </div>
              </div>

              <div className="text-right">
                <div className="text-xl font-bold text-primary">
                  {totalPoints?.toLocaleString()}
                  <span className="text-sm font-normal text-text-primary ml-1">p</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppliedPrizesContent;
