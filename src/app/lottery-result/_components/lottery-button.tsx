import { But<PERSON> } from '@/components/ui/button';
import { SelectType } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';

interface LotteryButtonProps {
  flg: number;
  className?: string;
  clickCallback: () => void;
}

const LotteryButton: React.FC<LotteryButtonProps> = ({ flg, className = '', clickCallback }) => {
  const buttonStyles: Record<string, string> = {
    primary: 'bg-primary text-white rounded-full',
    secondary: 'bg-secondary text-white rounded-lg',
    outline:
      'border border-primary text-primary bg-transparent hover:bg-transparent hover:text-accent-foreground',
    disabled: 'bg-gray-300 text-gray-500 cursor-not-allowed',
  };

  return (
    <Button
      onClick={clickCallback}
      className={`${className} h-12 w-full  text-base  rounded-full ${buttonStyles[flg === SelectType.select ? 'outline' : 'default']}`}
    >
      {flg === SelectType.select ? (
        <div className="font-bold text-base text-primary">{APP_TEXT.LOTTERY.LOTTERY_CHANGE}</div>
      ) : (
        <div className="font-bold text-base">{APP_TEXT.LOTTERY.SELECT_AND_APPLY}</div>
      )}
    </Button>
  );
};

export default LotteryButton;
