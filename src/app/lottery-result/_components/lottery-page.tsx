import SectionTitle from '@/components/shared/section-title';
import { Button } from '@/components/ui/button';
import { SelectType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import type { LotteryDrawingResponse } from '@/types/lottery-select';
import { DateFormatEnum } from '@/utils/date-format';
import { formatDate } from 'date-fns';
import { Router } from 'lucide-react';
import AppliedPrizesContent from './applied-prizes-content';
import LotteryButton from './lottery-button';
import PointsList from './point-list';
import SectionTitleTwoLines from './title-context';

const LotteryPage = ({ lotteryDrawingData }: { lotteryDrawingData: LotteryDrawingResponse }) => {
  const lotteryDetail = lotteryDrawingData.lotteryDetail;
  const currAppInfo = lotteryDrawingData.currAppInfo;
  const lotteryImage = '/images/lottery/lottery-icon.svg';
  const isSelect =
    lotteryDrawingData.currAppInfo.choiceList &&
    lotteryDrawingData.currAppInfo.choiceList.length > 0
      ? SelectType.select
      : SelectType.update;
  // const isLotteryStart:boolean= pointValidFrom>
  const isLotteryStart = (date: string): boolean => {
    try {
      const validFromDate = new Date(date);
      const currentDate = new Date();
      return currentDate > validFromDate;
    } catch (e) {
      console.error('Invalid date format:', date);
      return false;
    }
  };
  const router = useRouter();

  const selectLottery = () => {
    const params = {
      sourceFlag: 1,
      lotteryId: lotteryDetail.lotteryId,
    };
    const paramsStr = btoa(JSON.stringify(params));
    const path = `/prize-selection?data=${paramsStr}`;
    router.push(path);
  };
  //"1：応募期間外
  // 2：景品公開前
  // 3：応募期間中_景品未選択
  // 4：応募期間中_景品選択後"
  const isStart: number = lotteryDetail.lotteryStatus ?? 0;
  return (
    <div className="p-4 rounded-lg">
      <h2 className="text-lg font-semibold mb-4">{APP_TEXT.LOTTERY.SCHEDULED}</h2>
      <div className="bg-white p-6 rounded-2xl shadow-md ">
        <img src={lotteryImage} alt="" className="w-full h-auto" />

        <div className="my-4">
          <p className="text-sm text-gray-600 mb-2">{lotteryDetail.organizerNm}</p>
          <h3 className="text-base font-semibold mb-2">{lotteryDetail.lotteryTitle}</h3>
          <p className="text-sm text-gray-600">{lotteryDetail.lotteryDetail}</p>
        </div>
        {/* //抽選オンの判定 */}
        {isStart > 1 ? (
          <LotteryButton flg={isSelect} clickCallback={selectLottery} />
        ) : (
          <div>
            <p className="text-sm text-blue-500 mt-6 text-center mb-4">
              {APP_TEXT.LOTTERY.FUTURE_RELEASE}
            </p>
            <Button variant="secondary" className="w-full  text-white rounded-full h-12">
              <div className="font-bold text-base">{APP_TEXT.LOTTERY.NOT_STARTED}</div>
            </Button>
          </div>
        )}

        <div className="mt-6 border-t border-gray-200 pt-4 space-y-4">
          <SectionTitleTwoLines title={APP_TEXT.LOTTERY.APPLICATION_PERIOD} content={'未定'} />
          <SectionTitleTwoLines
            title={APP_TEXT.LOTTERY.TARGET_POINTS}
            content={`${lotteryDetail.pointValidFrom && formatDate(lotteryDetail.pointValidFrom, DateFormatEnum.DATE_YMDD_JP)} ~ ${lotteryDetail.pointValidTo && formatDate(lotteryDetail.pointValidTo, DateFormatEnum.DATE_YMDD_JP)} ${APP_TEXT.LOTTERY.GET_POINT}`}
          />
          <SectionTitleTwoLines
            title={APP_TEXT.LOTTERY.ANNOUNCEMENT_DATE}
            content={
              lotteryDetail.prizeApplyTo &&
              formatDate(lotteryDetail.prizeApplyTo, DateFormatEnum.DATE_YMDD_JP)
            }
          />
          <SectionTitleTwoLines
            title={APP_TEXT.LOTTERY.ANNOUNCEMENT_METHOD}
            content={APP_TEXT.LOTTERY.ANNOUNCEMENT_METHOD_CONTENT}
          />
          {isStart > 1 ? (
            <SectionTitleTwoLines
              title={APP_TEXT.LOTTERY.NOTES}
              content={`${APP_TEXT.LOTTERY.NOTES_CONTENT} ${lotteryDetail.prizeApplyTo && formatDate(lotteryDetail.prizeApplyTo, DateFormatEnum.DATE_YMD_JP)}  ${APP_TEXT.LOTTERY.NOTES_CONTENT_END}`}
            />
          ) : (
            <></>
          )}
        </div>
        {isStart ? (
          <div>
            <div className="h-px bg-border mt-6" />
            <SectionTitle className="text-base px-0 mt-4">
              {APP_TEXT.LOTTERY.CURRENT_APPLICATION}
            </SectionTitle>
            <PointsList
              point={currAppInfo.point}
              choicedPoint={currAppInfo.choicedPoint}
              remainPoint={currAppInfo.remainPoint}
            />
            <AppliedPrizesContent
              appliedPrizes={currAppInfo.choiceList}
              totalCount={undefined}
              totalPoints={undefined}
              title={APP_TEXT.LOTTERY.SELECTED_PRIZE}
            />

            <LotteryButton flg={isSelect} className="mt-4" clickCallback={selectLottery} />
          </div>
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default LotteryPage;
