interface SectionTitleProps {
  title: string;
  content: string;
}

const SectionTitleTwoLines: React.FC<SectionTitleProps> = ({ title, content }) => {
  return (
    <div>
      <div className="text-base font-normal text-black flex items-center">
        <span className="bg-primary-60 w-1 h-5 mr-2" />
        {title}
      </div>
      <div className="text-sm font-norma text-black mt-2 whitespace-pre-line">{content}</div>
    </div>
  );
};

export default SectionTitleTwoLines;
