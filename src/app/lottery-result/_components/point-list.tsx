import { APP_TEXT } from '@/const/text/app';
import { type CurrAppInfo, PrizeData } from '@/types/lottery-select';
import { CircleParking } from 'lucide-react';
type PointsListProps = {
  point: number;
  choicedPoint: number;
  remainPoint: number;
};

const PointsList = ({ point, choicedPoint, remainPoint }: PointsListProps) => {
  const data = [
    {
      name: APP_TEXT.LOTTERY.OWNED_POINTS,
      point: point,
    },
    {
      name: APP_TEXT.LOTTERY.SELECTED_POINTS,
      point: choicedPoint,
    },
    {
      name: APP_TEXT.LOTTERY.REMAINING_SELECTABLE_POINTS,
      point: remainPoint,
    },
  ];

  return (
    <div className="bg-card-mainLight3 rounded-2xl px-6 py-4 space-y-2 mb-4">
      {data.map((item, index) => (
        <div key={index}>
          <div className="flex justify-between items-center min-h-10">
            <div className="flex items-center space-x-2">
              <CircleParking className="w-5 h-5 text-primary" />
              <span>{item.name}</span>
            </div>
            <span className="text-base min-w-16 text-right">
              {item.point} <span className="text-sm">ｐ</span>
            </span>
          </div>
          {index !== data.length - 1 && <div className="h-px bg-border" />}
        </div>
      ))}
    </div>
  );
};

export default PointsList;
