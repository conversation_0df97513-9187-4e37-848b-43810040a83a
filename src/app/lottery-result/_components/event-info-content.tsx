import { APP_TEXT } from '@/const/text/app';
import type { LotteryDetail } from '@/types/lottery-select';
import { DateFormatEnum, formatDate } from '@/utils/date-format';
import SectionTitleTwoLines from './title-context';

interface EventInfoContentProps {
  details: LotteryDetail;
}

const EventInfoContent = ({ details }: EventInfoContentProps) => {
  return (
    <div className="pb-4">
      <div className="space-y-4">
        {/* <div className="flex gap-2">
          <div className="w-1 h-5 bg-primary-soft rounded-full flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <div className="font-medium text-text-primary">応募期間</div>
            <div className="text-sm text-text-primary mt-2"></div>
          </div>
        </div> */}
        <SectionTitleTwoLines
          title={APP_TEXT.LOTTERY.APPLICATION_PERIOD}
          content={
            formatDate(details.prizeApplyTo, DateFormatEnum.DATE_YMDD_JP) +
            APP_TEXT.LOTTERY.TIME_END
          }
        />
        <SectionTitleTwoLines
          title={APP_TEXT.LOTTERY.TARGET_POINTS}
          content={`${formatDate(details.pointValidFrom, DateFormatEnum.DATE_YMDD_JP)} ~ ${formatDate(details.pointValidTo, DateFormatEnum.DATE_YMDD_JP)} ${APP_TEXT.LOTTERY.GET_POINT}`}
        />
        <SectionTitleTwoLines
          title={APP_TEXT.LOTTERY.ANNOUNCEMENT_DATE}
          content={formatDate(details.prizeApplyTo, DateFormatEnum.DATE_YMDD_JP)}
        />
        <SectionTitleTwoLines
          title={APP_TEXT.LOTTERY.ANNOUNCEMENT_METHOD}
          content={APP_TEXT.LOTTERY.ANNOUNCEMENT_METHOD_CONTENT}
        />
        <SectionTitleTwoLines
          title={APP_TEXT.LOTTERY.NOTES}
          content={`${APP_TEXT.LOTTERY.NOTES_CONTENT} ${formatDate(details.prizeApplyTo, DateFormatEnum.DATE_YMD_JP)}  ${APP_TEXT.LOTTERY.NOTES_CONTENT_END}`}
        />
      </div>
    </div>
  );
};

export default EventInfoContent;
