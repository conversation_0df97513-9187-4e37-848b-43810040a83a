import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import type { LotteryHistoryResponse } from '@/types/lottery-select';
import { DateFormatEnum, formatDate } from '@/utils/date-format';
import { ChevronRight } from 'lucide-react';

const LotteryHistoryList: React.FC<LotteryHistoryResponse> = ({
  historyLottery,
}: LotteryHistoryResponse) => {
  const router = useRouter();

  const openLotteryDetail = (id: number) => {
    const path = `${ROUTES.LOTTERY.DETAIL}?lotteryId=${id}`;
    router.push(path);
  };
  return (
    <div className="">
      {historyLottery.map((item, index) => (
        <div
          key={index}
          onClick={(e) => {
            openLotteryDetail(item.lotteryId);
          }}
          onKeyUp={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              openLotteryDetail(item.lotteryId);
            }
          }}
        >
          <div
            className={`${index !== historyLottery.length - 1 ? 'border-b border-border' : ''} py-3 flex items-center`}
          >
            <div className="mt-1 w-full">
              {item.status === 1 && (
                <span className="text-xs bg-red-600 text-white px-1.5 py-1 rounded-md">
                  {APP_TEXT.LOTTERY.WINNING}
                </span>
              )}
              <h2 className="text-normal font-base text-gray-700">{item.lotteryTitle}</h2>
              <p className="text-sm text-gray-500">
                {item.lotteryYyyyMm && formatDate(item.lotteryYyyyMm, DateFormatEnum.DATE_YMDD_JP)}
              </p>
            </div>
            <ChevronRight className="w-5 h-5" />
          </div>
        </div>
      ))}
    </div>
  );
};
export default LotteryHistoryList;
