'use client';

import { LorreryPrizeAPI } from '@/api/modules/lottery-prize';
import TopBar from '@/components/layout/top-bar';
import { TextButton } from '@/components/shared/text-button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { PrizeType } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { cn } from '@/lib/utils';
import type {
  LotteryDetail,
  PrizeItem,
  UserLotteryChoice,
  UserLotteryChoiceListResponse,
  WinPrize,
} from '@/types/lottery-select';
import { Check, ChevronRight } from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';
import AppliedPrizesContent from '../_components/applied-prizes-content';
import EventInfoContent from '../_components/event-info-content';
import PrizeListContent from '../_components/prize-list-content';
import WinPrizeList from '../_components/win-prize-list';

const CustomAccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionTrigger>,
  React.ComponentPropsWithoutRef<typeof AccordionTrigger> & {
    children: React.ReactNode;
  }
>(({ className, children, ...props }, ref) => (
  <AccordionTrigger
    ref={ref}
    className={cn(
      'px-0 py-4 text-base group/trigger',
      'hover:no-underline data-[state=open]:no-underline',
      '[&>svg]:hidden',
      className,
    )}
    {...props}
  >
    <div className="flex items-center justify-between w-full">
      <span>{children}</span>
      <div className="relative">
        <ChevronRight className="h-4 w-4 shrink-0 text-text-primary" />
      </div>
    </div>
  </AccordionTrigger>
));
CustomAccordionTrigger.displayName = 'CustomAccordionTrigger';

export default function LotteryResultPage({
  searchParams,
}: {
  searchParams: { lotteryId?: string };
}) {
  const { setLoading } = useLoading();
  let lotteryId = searchParams.lotteryId;

  lotteryId = encodeURIComponent(lotteryId ?? '');

  const SuccessImage = '/images/lottery/lottery-success-icon.svg';
  const FailImage = '/images/lottery/lottery-fail-icon.svg';

  // からAPI获取实际抽奖结果
  const [isWinning, setIsWinning] = useState(-1);
  const [isNotParticipated, setIsNotParticipated] = useState(false);

  const [prizeList, setPrizeList] = useState<PrizeItem[]>();
  const [userPrizeList, setUserPrizeList] = useState<UserLotteryChoiceListResponse>();
  const [lotteryDetail, setLotteryDetail] = useState<LotteryDetail>();
  const [winPrize, setWinPrize] = useState<WinPrize[]>();

  useEffect(() => {
    fetchLotteryResult();
    getUserPrizes();
    getAllPrizes();
    getLotteryDetail();
  }, []);
  //抽選詳細取得
  const getLotteryDetail = useCallback(() => {
    setLoading(true);
    LorreryPrizeAPI.getLotteryResultDetail(lotteryId)
      .then((response) => {
        setLoading(false);
        setLotteryDetail(response.lotteryDetail);
      })
      .catch((error) => {
        setLoading(false);
      });
  }, []);
  //景品一覧取得
  const getAllPrizes = useCallback(() => {
    setLoading(true);
    LorreryPrizeAPI.getLotteryPrizeList(lotteryId)
      .then((response) => {
        setLoading(false);
        setPrizeList(response.prizeList);
      })
      .catch((error) => {
        setLoading(false);
      });
  }, []);
  //ユーザ抽選結果取得API
  const fetchLotteryResult = useCallback(() => {
    setLoading(true);
    LorreryPrizeAPI.getLotteryResult(lotteryId)
      .then((response) => {
        setLoading(false);
        const list = response.UsrLottoRes.winPrizes;
        //当たりの判定
        if (list && list.length > 0) {
          setWinPrize(list);
          setIsWinning(1);
        } else {
          setIsWinning(0);
        }
      })
      .catch((error) => {
        setLoading(false);
        setIsWinning(0);
      });
  }, []);
  //ユーザ抽選景品選択取得
  const getUserPrizes = useCallback(() => {
    setLoading(true);
    LorreryPrizeAPI.getLotteryUserChoice(lotteryId)
      .then((response) => {
        setLoading(false);
        if (response) {
          const list = response.userLotteryChoiceList;
          //立候補の判断をします
          if (list.length > 0) {
            setUserPrizeList(response);

            setIsNotParticipated(false);
          } else {
            setIsNotParticipated(true);
          }
        }
      })
      .catch((error) => {
        setLoading(false);
        setIsNotParticipated(true);
      });
  }, []);
  const getGiftee = useCallback((lotteryId: string, prizeId: number) => {
    setLoading(true);
    LorreryPrizeAPI.getGifteeUrl(lotteryId, prizeId)
      .then((response) => {
        setLoading(false);
      })
      .catch((error) => {
        setLoading(false);
      });
  }, []);
  const handlePrizeReceive = (prizeId: number, type: number) => {
    // 1:品物、2.電子ギフト　3.dポイント
    if (type === PrizeType.GIFTEE) {
      getGiftee(lotteryId, prizeId);
    }
    if (type === PrizeType.DPOINT) {
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <TopBar title={APP_TEXT.LOTTERY.TITLE_RESULT} />
      <div className="pb-12">
        {!isNotParticipated && isWinning > -1 && (
          <img src={isWinning ? SuccessImage : FailImage} alt="" className="w-full h-auto" />
        )}
        {/* 不参加 */}
        {isNotParticipated && (
          <div className="pt-4 px-6 mb-4">
            <div className="w-full bg-muted rounded-2xl p-6">
              <p className="text-base text-text-secondary text-center">
                {APP_TEXT.LOTTERY.NOT_PARTICIPATED}
              </p>
            </div>
          </div>
        )}
        {/* 当たり */}
        {!isNotParticipated && isWinning === 1 && (
          <div className="pt-4 px-6">
            <h3 className="text-[18px] font-bold text-foreground mb-1">
              {APP_TEXT.LOTTERY.WON_PRIZE}
            </h3>

            {winPrize && <WinPrizeList prizes={winPrize} onPrizeReceive={handlePrizeReceive} />}
          </div>
        )}

        {lotteryDetail && (
          <div className="pt-4 px-6 mb-4">
            <h3 className="text-[18px] font-bold text-foreground mb-4">
              {APP_TEXT.LOTTERY.EVENT_INFORMATION}
            </h3>
            <div className="">
              <img className="w-full h-[184px] my-4" src={lotteryDetail.lotteryImageFile} alt="" />
              <div className="space-y-1">
                <div className="text-sm text-foreground">{lotteryDetail.organizerNm}</div>
                <h3 className="text-lg font-bold text-foreground">{lotteryDetail.lotteryTitle}</h3>
                <p className="text-sm text-foreground break-all">{lotteryDetail.lotteryDetail}</p>
              </div>
            </div>
          </div>
        )}

        <div className="mx-6">
          <Accordion type="multiple" defaultValue={['event-info']} className="w-full p-0">
            <AccordionItem value="event-info" className="border-b data-[state=open]:border-b-0">
              <CustomAccordionTrigger>{APP_TEXT.LOTTERY.EVENT_OVERVIEW}</CustomAccordionTrigger>
              {lotteryDetail && (
                <AccordionContent className="p-0">
                  <EventInfoContent details={lotteryDetail} />
                </AccordionContent>
              )}
            </AccordionItem>

            <AccordionItem value="prize-list" className="border-b data-[state=open]:border-b-0">
              <CustomAccordionTrigger>{APP_TEXT.LOTTERY.PRIZE_LIST}</CustomAccordionTrigger>
              {prizeList && (
                <AccordionContent className="p-0">
                  <PrizeListContent prizes={prizeList} />
                </AccordionContent>
              )}
            </AccordionItem>

            {!isNotParticipated && userPrizeList && (
              <AccordionItem
                value="applied-prizes"
                className="border-b data-[state=open]:border-b-0"
              >
                <CustomAccordionTrigger>{APP_TEXT.LOTTERY.APPLIED_PRIZE}</CustomAccordionTrigger>
                <AccordionContent className="p-0">
                  <AppliedPrizesContent
                    appliedPrizes={userPrizeList.userLotteryChoiceList}
                    totalCount={userPrizeList.sumPrizeAppNum}
                    totalPoints={userPrizeList.sumChosenPrizePoint}
                    title={undefined}
                  />
                </AccordionContent>
              </AccordionItem>
            )}
          </Accordion>
        </div>
      </div>
    </div>
  );
}
