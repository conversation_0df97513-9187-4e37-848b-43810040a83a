// 古いブラウザ向けのポリフィルを最初に読み込み
import '@/lib/polyfills';

import NativeBridgeInitializer from '@/components/layout/native-bridge-initializer';
import { LoaderProvider } from '@/hooks/use-loading';
import { MapProvider } from '@/hooks/use-map';
import { MessageDialogProvider } from '@/hooks/use-message-dialog';
import { SafeAreaProvider } from '@/hooks/use-safe-area';
import type { Metadata, Viewport } from 'next';
import { ThemeProvider } from 'next-themes';
import { Toaster } from 'react-hot-toast';
import './globals.css';
import TestEnter from '@/components/layout/test-enter';
import { GeolocationProvider } from '@/hooks/use-geolocation';
import { GlobalVarProvider } from '@/hooks/use-global-var';
import { NavigationProvider } from '@/hooks/use-navigation';
import { QueryClientProvider } from '@/hooks/use-query-client';
import { SlidePageProvider } from '@/hooks/use-slide-page';
import { cn } from '@/lib/utils';
import { isAndroidByHeader, isIOSByHeader } from '@/utils/device-detect';
import { headers } from 'next/headers';

// const roboto = Roboto({
//   subsets: ['latin'],
//   weight: ['400', '500', '700'],
// });

export const metadata: Metadata = {
  title: 'New Health',
  description: 'New Health',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const isAndroidDevice = isAndroidByHeader(headers().get('user-agent'));
  const isIOSDevice = isIOSByHeader(headers().get('user-agent'));
  return (
    <html lang="ja" suppressHydrationWarning>
      <body
        className={cn(
          'min-h-screen relative bg-background',
          isAndroidDevice ? 'android' : isIOSDevice ? 'ios' : 'desktop',
        )}
      >
        <NavigationProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="theme-blue"
            disableTransitionOnChange
            themes={[
              'theme-blue',
              'theme-light-blue',
              'theme-cyan',
              'theme-green',
              'theme-orange',
              'theme-pink',
              'theme-purple',
            ]}
          >
            <NativeBridgeInitializer />
            <GlobalVarProvider>
              <SafeAreaProvider>
                <LoaderProvider>
                  <MessageDialogProvider>
                    <QueryClientProvider>
                      <GeolocationProvider>
                        <SlidePageProvider>
                          <MapProvider>
                            <TestEnter />
                            <main className="min-h-screen">{children}</main>
                          </MapProvider>
                        </SlidePageProvider>
                      </GeolocationProvider>
                    </QueryClientProvider>
                  </MessageDialogProvider>
                </LoaderProvider>
                <Toaster
                  position="bottom-center"
                  toastOptions={{
                    success: {
                      duration: 3000,
                    },
                    error: {
                      duration: 6000,
                    },
                  }}
                />
              </SafeAreaProvider>
            </GlobalVarProvider>
          </ThemeProvider>
        </NavigationProvider>
      </body>
    </html>
  );
}
