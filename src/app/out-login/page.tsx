'use client';
import { outLogin<PERSON>I } from '@/api/modules/out-login';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { Checkbox } from '@/components/shared/checkbox';
import { APP_TEXT } from '@/const/text/app';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { useOutLoginStore } from '@/store/out-login-store';

import type { OrganizerItem } from '@/types/out-login-types';
import React, { useEffect, useState } from 'react';

export default function OutLogin() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const [organizersList, setOrganizersList] = useState<OrganizerItem[]>([]);
  const [choosedOrganizers, setChoosedOrganizers] = useState<OrganizerItem[]>([]);

  // Geolocation hook should be called at the top level
  const { location } = useGeolocation();
  const { setConfirmedOrganizers, setInitOrganizers } = useOutLoginStore();

  const handleNext = () => {
    setConfirmedOrganizers([...choosedOrganizers]);
    router.push('/out-login/confirm');
  };
  const handleSelect = (choosedItem: OrganizerItem) => {
    setChoosedOrganizers([...choosedOrganizers, choosedItem]);
  };
  const handleUnselect = (choosedItem: OrganizerItem) => {
    setChoosedOrganizers(
      choosedOrganizers.filter((f: OrganizerItem) => f.organizerId !== choosedItem.organizerId),
    );
  };

  const searchOrganizersData = async () => {
    setLoading(true);
    try {
      if (!location) return;
      const data = await outLoginAPI.getOrganizers();
      if (data) {
        setOrganizersList(data.organizerList || []);
        setInitOrganizers(data.organizerList || []);
      }
    } catch (error) {
      console.log('Failed to search coupon data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    searchOrganizersData();
  }, []);

  return (
    <div className="flex flex-col bg-white min-h-screen">
      <TopBar
        title={APP_TEXT.MENU.OUT_LOGIN.TITLE}
        enableClose={true}
        enableBack={true}
        onClose={() => {
          router.push('/menu/settings');
        }}
      />
      <div className="flex flex-1 justify-between flex-col px-6 pt-6 pb-6">
        <div>
          <div className="text-l text-black mb-2 ">{APP_TEXT.MENU.OUT_LOGIN.INPUT}</div>
          <div className="text-xs  text-[#697077] mb-3">{APP_TEXT.MENU.OUT_LOGIN.HINT}</div>
          <div className="flex flex-col gap-0 py-0">
            {organizersList.length > 0 &&
              organizersList.map((organizer: OrganizerItem) => (
                <div
                  className="flex items-center"
                  key={organizer.organizerId}
                  onClick={() => {
                    if (choosedOrganizers.includes(organizer)) {
                      handleUnselect(organizer);
                    } else {
                      handleSelect(organizer);
                    }
                  }}
                >
                  <Checkbox checked={choosedOrganizers.includes(organizer)} />
                  <div className="flex items-center ml-4 py-3 flex-1">{organizer.organizerNm}</div>
                </div>
              ))}
          </div>
        </div>
        <Button
          className="w-full h-12 rounded-full mb-3"
          onClick={handleNext}
          disabled={!(choosedOrganizers && choosedOrganizers.length > 0)}
        >
          {APP_TEXT.MENU.OUT_LOGIN.BTN}
        </Button>
      </div>
    </div>
  );
}
