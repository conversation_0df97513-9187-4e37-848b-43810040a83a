'use client';
import { outLoginAPI } from '@/api/modules/out-login';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { useRoutes } from '@/hooks/use-routes';
import { useOutLoginStore } from '@/store/out-login-store';

import type { OrganizerItem } from '@/types/out-login-types';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import React, { useEffect } from 'react';

export default function OutLoginConfirm() {
  const router = useRouter();
  const { setLoading } = useLoading();

  const { setConfirmedOrganizers, getConfirmedOrganizers, getIsMult, getInitOrganizers } =
    useOutLoginStore();
  const isMult = getIsMult();
  const choosedOrganizers = getConfirmedOrganizers();
  // Geolocation hook should be called at the top level
  const { location } = useGeolocation();
  const { setDialog } = useMessageDialog();

  const { goLogout } = useRoutes();
  const handleBack = () => {
    router.push('/menu/settings');
  };
  const { setIsMult } = useOutLoginStore();

  const logoutClick = () => {
    sendMessageToNative({
      type: 'logout',
      data: {},
    });
    goLogout();
  };
  const handleOutLogin = () => {
    // setIsMult(true)
    // router.push(ROUTES.MENU.OUT_LOGIN_LINK);
    setLoading(true, { text: 'データを通信中...' });
    outLoginAPI
      .startup()
      .then((response) => {
        setLoading(false);
        if (response.organizerIdList && response.organizerIdList.length > 0) {
          setIsMult(true);
          router.push(ROUTES.MENU.ONLY_OUT_LOGIN_LINK);
        } else {
          logoutClick();
        }
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        return Promise.reject();
      });
  };
  const handleAgree = async () => {
    setLoading(true);
    try {
      if (!location) return;
      const organizerIdList = choosedOrganizers.map((item) => {
        return Number.parseInt(item.organizerId.toString(), 10); // organizerIdをint型に変換
      });
      nlog(`handleAgree${choosedOrganizers} organizerIdList ${organizerIdList}`);
      const data = await outLoginAPI.doTerminate({ organizerIdList });
      if (data) {
        router.push('/out-login/success');
      }
    } catch (error) {
      console.log('Failed to search coupon data:', error);
    } finally {
      setLoading(false);
    }
  };
  // 確認ダイアログ
  const handleDialogOpen = (open: boolean) => {
    setDialog(open, {
      title: APP_TEXT.MENU.OUT_LOGIN.DIALOG.TITLE,
      content: APP_TEXT.MENU.OUT_LOGIN.DIALOG.CONTENT,
      outSideClickClose: true,
      footer: (
        <div className="flex flex-col">
          <Button
            // className="w-full h-12 rounded-full mb-3 text-[#CE0000] bg-white border border-[#CE0000]"
            className="w-full bg-primary text-card text-[#CE0000] bg-white border border-[#CE0000]"
            // variant="destructive"
            onClick={() => {
              handleDialogOpen(false);
              handleAgree();
            }}
          >
            {APP_TEXT.MENU.OUT_LOGIN.CONFIRM.CONFIRM_BTN}
          </Button>
          <TextButton className="w-full mt-4" variant="muted" onClick={() => setDialog(false)}>
            {APP_TEXT.MENU.OUT_LOGIN.CONFIRM.CANCEL_BTN}
          </TextButton>
        </div>
      ),
    });
  };

  useEffect(() => {}, []);

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <TopBar
        title={APP_TEXT.MENU.OUT_LOGIN.TITLE}
        enableClose={true}
        enableBack={true}
        onClose={() => {
          router.push('/menu/settings');
        }}
      />
      <div className="flex-1 flex justify-between flex-col px-6 pt-6 pb-6">
        <div>
          <div className="rounded-[16px] bg-[#FDF2F2] p-6 mb-2">
            <div className="flex items-center mb-4">
              <div className="flex items-center flex-1">
                <img src="/images/point/warn.svg" alt="warn" width={24} height={24} />
                <span className="text-l font-bold text-[#CE0000] ml-1">
                  {APP_TEXT.MENU.OUT_LOGIN.CONFIRM.TITLE}
                </span>
              </div>
            </div>

            {choosedOrganizers && choosedOrganizers.length > 0 && isMult && (
              <div className="mb-4">
                <div className="text-l text-black mb-2">
                  {APP_TEXT.MENU.OUT_LOGIN.CONFIRM.SUB_TITLE}
                </div>
                {choosedOrganizers.map((item: OrganizerItem) => (
                  <div className="text-l font-bold text-[#CE0000]" key={item.organizerId}>
                    <span className="px-2">·</span>
                    {item.organizerNm}
                  </div>
                ))}
              </div>
            )}
            <div className="text-l  text-black">{APP_TEXT.MENU.OUT_LOGIN.CONFIRM.DESCRIPTION}</div>
          </div>
          <div className="text-xs  text-black mb-4">{APP_TEXT.MENU.OUT_LOGIN.CONFIRM.HINT}</div>
        </div>
        <div>
          <Button
            className="w-full h-12 rounded-full mb-3 text-[#CE0000] bg-white border border-[#CE0000]"
            onClick={() => {
              handleDialogOpen(true);
            }}
          >
            {APP_TEXT.MENU.OUT_LOGIN.CONFIRM.CONFIRM_BTN}
          </Button>
          <TextButton
            className="w-full h-12 rounded-full mb-3"
            variant="muted"
            onClick={handleBack}
          >
            {APP_TEXT.MENU.OUT_LOGIN.CONFIRM.CANCEL_BTN}
          </TextButton>
        </div>
      </div>
    </div>
  );
}
