'use client';
import { outLoginAPI } from '@/api/modules/out-login';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import { useRoutes } from '@/hooks/use-routes';
import success_icon from '@/images/success.png';
import { useAuthStore } from '@/store/auth';
import { useOutLoginStore } from '@/store/out-login-store';
import type { OrganizerItem } from '@/types/out-login-types';
import { sendMessageToNative } from '@/utils/native-bridge';
import React, { useEffect } from 'react';

export default function OutLoginSuccess() {
  const router = useRouter();

  const { getConfirmedOrganizers, getInitOrganizers } = useOutLoginStore();
  const { getIsMult } = useOutLoginStore();
  const { goLogout } = useRoutes();
  const { setLoading } = useLoading();
  const { user } = useAuthStore.getState();
  const { setUser } = useAuthStore();
  const isMult = getIsMult();
  const choosedOrganizers = getConfirmedOrganizers();
  const initOrganizers = getInitOrganizers();

  const logoutClick = () => {
    sendMessageToNative({
      type: 'logout',
      data: {},
    });
    goLogout();
  };

  const searchOrganizersData = async () => {
    setLoading(true);
    try {
      if (!location) return;
      const data = await outLoginAPI.startup();
      if (data && user?.id !== undefined) {
        const updatedUser = {
          ...user,
          useOrganizerID: `${data.organizerIdList[0] ?? ''}`,
          organizerID: data.organizerIdList.toString() ?? '',
        };
        setUser(updatedUser);
        //
        sendMessageToNative({
          type: 'user-info',
          data: {
            ...updatedUser,
            userOrganizerID: data.organizerIdList[0].toString() ?? '',
            organizerID: data.organizerIdList.toString(),
          },
        });
        router.backTo('/home');
      }
    } catch (error) {
      console.log('Failed to search coupon data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!isMult || choosedOrganizers.length === initOrganizers.length) {
      logoutClick();
    } else {
      searchOrganizersData();
    }
  };

  useEffect(() => {}, []);

  return (
    <div className="flex flex-col bg-white min-h-screen ">
      <TopBar
        title={APP_TEXT.MENU.OUT_LOGIN.TITLE}
        enableClose={true}
        enableBack={false}
        onClose={() => {
          handleClose();
        }}
      />
      <div className="flex justify-between flex-1 flex-col px-6 py-6">
        <div className="flex-1 flex flex-col justify-center">
          <div className="flex justify-center items-center flex-col rounded mb-4">
            <img className="h-16 w-16 mx-auto mb-4" alt="desc" src={success_icon.src} />
            <div className="text-[20px] text-center font-bold">
              {APP_TEXT.MENU.OUT_LOGIN.SUCCESS.TITLE}
            </div>
          </div>
          {isMult && (
            <div className="rounded-[16px] bg-[#F6F8FF] p-6 mb-4">
              <div className="mb-2">{APP_TEXT.MENU.OUT_LOGIN.SUCCESS.MULT_TITLE}</div>
              {choosedOrganizers.map((item: OrganizerItem) => (
                <div className="text-l font-bold text-black" key={item.organizerId}>
                  <span className="px-2">·</span>
                  {item.organizerNm}
                </div>
              ))}
            </div>
          )}
          <div className="text-[#121619]">
            {isMult
              ? APP_TEXT.MENU.OUT_LOGIN.SUCCESS.MULT_DESCRIPTION
              : APP_TEXT.MENU.OUT_LOGIN.SUCCESS.ONLY_DESCRIPTION}
          </div>
        </div>
        <Button
          className="w-full bg-primary text-card mt-4"
          // variant="destructive"
          onClick={() => {
            handleClose();
          }}
        >
          {APP_TEXT.MENU.OUT_LOGIN.SUCCESS.CLOSE_BTN}
        </Button>
      </div>
    </div>
  );
}
