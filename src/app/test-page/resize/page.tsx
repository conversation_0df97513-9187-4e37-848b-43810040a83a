'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect, useState } from 'react';

interface ResizeEvent {
  timestamp: number;
  type: 'resize' | 'orientationchange' | 'focus' | 'blur' | 'visibilitychange';
  windowHeight: number;
  windowWidth: number;
  innerHeight: number;
  innerWidth: number;
  outerHeight: number;
  outerWidth: number;
  screenHeight: number;
  screenWidth: number;
  visualViewportHeight?: number;
  visualViewportWidth?: number;
  devicePixelRatio: number;
  userAgent: string;
  platform: string;
}

export default function ResizeTestPage() {
  const [events, setEvents] = useState<ResizeEvent[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [currentDimensions, setCurrentDimensions] = useState({
    windowHeight: 0,
    windowWidth: 0,
    innerHeight: 0,
    innerWidth: 0,
    outerHeight: 0,
    outerWidth: 0,
    screenHeight: 0,
    screenWidth: 0,
    visualViewportHeight: 0,
    visualViewportWidth: 0,
  });

  // 現在のディメンションを取得する関数
  const getCurrentDimensions = () => ({
    windowHeight: window.innerHeight,
    windowWidth: window.innerWidth,
    innerHeight: window.innerHeight,
    innerWidth: window.innerWidth,
    outerHeight: window.outerHeight,
    outerWidth: window.outerWidth,
    screenHeight: window.screen.height,
    screenWidth: window.screen.width,
    visualViewportHeight: window.visualViewport?.height || 0,
    visualViewportWidth: window.visualViewport?.width || 0,
    devicePixelRatio: window.devicePixelRatio,
  });

  // イベントを記録する関数
  const recordEvent = (type: ResizeEvent['type']) => {
    if (!isRecording) return;

    const newEvent: ResizeEvent = {
      timestamp: Date.now(),
      type,
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      ...getCurrentDimensions(),
    };

    setEvents((prev) => [newEvent, ...prev]);
    setCurrentDimensions(getCurrentDimensions());
  };

  useEffect(() => {
    // 初期値を設定
    setCurrentDimensions(getCurrentDimensions());

    // resizeイベントリスナー
    const handleResize = () => {
      recordEvent('resize');
    };

    // orientationchangeイベントリスナー
    const handleOrientationChange = () => {
      recordEvent('orientationchange');
    };

    // focus/blurイベントリスナー（キーボードの表示/非表示を検知）
    const handleFocus = () => {
      recordEvent('focus');
    };

    const handleBlur = () => {
      recordEvent('blur');
    };

    // visibilitychangeイベントリスナー
    const handleVisibilityChange = () => {
      recordEvent('visibilitychange');
    };

    // visualViewportの変更を監視
    const handleVisualViewportChange = () => {
      recordEvent('resize');
    };

    // イベントリスナーを追加
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // visualViewportが利用可能な場合
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportChange);
    }

    // クリーンアップ
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleVisualViewportChange);
      }
    };
  }, [isRecording]);

  // 記録をクリアする関数
  const handleClearEvents = () => {
    setEvents([]);
  };

  // 記録を開始/停止する関数
  const handleToggleRecording = () => {
    setIsRecording(!isRecording);
  };

  // イベントタイプに応じた色を取得
  const getEventTypeColor = (type: ResizeEvent['type']) => {
    switch (type) {
      case 'resize':
        return 'bg-blue-500';
      case 'orientationchange':
        return 'bg-green-500';
      case 'focus':
        return 'bg-yellow-500';
      case 'blur':
        return 'bg-orange-500';
      case 'visibilitychange':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  // 時刻をフォーマットする関数
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('ja-JP', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3,
    });
  };

  return (
    <div className="container mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Resize Event Test Page</CardTitle>
          <p className="text-sm text-gray-600">
            Android仮想キーボードの表示/非表示時のresizeイベントをテストします
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 現在のディメンション表示 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="text-xs text-gray-600">Window Height</div>
              <div className="font-mono text-sm">{currentDimensions.windowHeight}px</div>
            </div>
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="text-xs text-gray-600">Window Width</div>
              <div className="font-mono text-sm">{currentDimensions.windowWidth}px</div>
            </div>
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="text-xs text-gray-600">Visual Viewport Height</div>
              <div className="font-mono text-sm">{currentDimensions.visualViewportHeight}px</div>
            </div>
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="text-xs text-gray-600">Visual Viewport Width</div>
              <div className="font-mono text-sm">{currentDimensions.visualViewportWidth}px</div>
            </div>
          </div>

          {/* コントロールボタン */}
          <div className="flex gap-2">
            <Button
              onClick={handleToggleRecording}
              variant={isRecording ? 'destructive' : 'default'}
            >
              {isRecording ? '記録停止' : '記録開始'}
            </Button>
            <Button onClick={handleClearEvents} variant="outline">
              イベントクリア
            </Button>
          </div>

          {/* デバイス情報 */}
          <div className="text-xs text-gray-600 space-y-1">
            <div>User Agent: {navigator.userAgent}</div>
            <div>Platform: {navigator.platform}</div>
            <div>Device Pixel Ratio: {window.devicePixelRatio}</div>
          </div>
        </CardContent>
      </Card>

      {/* イベントログ */}
      <Card>
        <CardHeader>
          <CardTitle>Event Log ({events.length} events)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {events.length === 0 ? (
              <p className="text-gray-500 text-center py-4">イベントが記録されていません</p>
            ) : (
              events.map((event, index) => (
                <div key={index} className="border rounded p-3 space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={getEventTypeColor(event.type)}>{event.type}</Badge>
                    <span className="text-xs text-gray-600">
                      {formatTimestamp(event.timestamp)}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                    <div>
                      <span className="text-gray-600">Window:</span>
                      <div className="font-mono">
                        {event.windowWidth} × {event.windowHeight}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Inner:</span>
                      <div className="font-mono">
                        {event.innerWidth} × {event.innerHeight}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Outer:</span>
                      <div className="font-mono">
                        {event.outerWidth} × {event.outerHeight}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Screen:</span>
                      <div className="font-mono">
                        {event.screenWidth} × {event.screenHeight}
                      </div>
                    </div>
                    {event.visualViewportHeight && event.visualViewportWidth && (
                      <div className="col-span-2">
                        <span className="text-gray-600">Visual Viewport:</span>
                        <div className="font-mono">
                          {event.visualViewportWidth} × {event.visualViewportHeight}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* テスト用の入力フィールド */}
      <Card>
        <CardHeader>
          <CardTitle>Test Input Fields</CardTitle>
          <p className="text-sm text-gray-600">
            これらのフィールドにフォーカスしてキーボードを表示させてください
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label htmlFor="text-input" className="block text-sm font-medium mb-2">
              Text Input
            </label>
            <input
              id="text-input"
              type="text"
              placeholder="テキストを入力してください"
              className="w-full p-2 border rounded"
            />
          </div>
          <div>
            <label htmlFor="number-input" className="block text-sm font-medium mb-2">
              Number Input
            </label>
            <input
              id="number-input"
              type="number"
              placeholder="数字を入力してください"
              className="w-full p-2 border rounded"
            />
          </div>
          <div>
            <label htmlFor="textarea-input" className="block text-sm font-medium mb-2">
              Textarea
            </label>
            <textarea
              id="textarea-input"
              placeholder="複数行のテキストを入力してください"
              rows={4}
              className="w-full p-2 border rounded"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
