'use client';

import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetect } from '@/hooks/use-device-detect';
import { useRouter } from '@/hooks/use-next-navigation';
import { Sex, useAuthStore } from '@/store/auth';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

export default function SetUserPage() {
  const { user, setUser } = useAuthStore();
  const router = useRouter();
  const { isIOS } = useDeviceDetect();

  // 本地state
  const [sex, setSex] = useState<Sex>(user?.sex ?? Sex.UNKNOWN);
  const [birthday, setBirthday] = useState<string>(user?.birthday ?? '');
  const [userID, setUserID] = useState<string>(user?.id ?? '');
  const [organizerID, setOrganizerID] = useState(user?.organizerID ?? '');
  const [useOrganizerID, setUseOrganizerID] = useState(user?.useOrganizerID ?? '');
  const [lat, setLat] = useState<number>(user?.lat ?? 0);
  const [lng, setLng] = useState<number>(user?.lng ?? 0);

  // user变化时同步state
  useEffect(() => {
    if (user) {
      setSex(user.sex);
      setBirthday(user.birthday);
      setUserID(user.id);
      setOrganizerID(user.organizerID);
      setUseOrganizerID(user.useOrganizerID ?? '');
      setLat(user.lat);
      setLng(user.lng);
    }
  }, [user]);

  const handleUpdate = () => {
    setUser({
      ...(user || {
        id: '222',
        name: '222',
        email: '<EMAIL>',
        birthday: '2025-01-01',
        sex: Sex.MALE,
        userAgent: 'test',
        organizerID: '802',
        organizerCode: '802',
        useOrganizerID: '802',
        lat: 35.681236,
        lng: 139.767125,
      }),
      sex,
      birthday,
      id: userID || '',
      organizerID: organizerID || '',
      useOrganizerID: useOrganizerID || '',
      lat,
      lng,
    });
    toast.success('ユーザー情報を更新しました');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <>
      <TopBar title="ユーザー情報設定" onBack={handleBack} />

      {/* メインコンテンツ */}
      <div className="p-4 space-y-6 pb-20">
        {/* 性別選択 */}
        <div>
          <div className="text-base font-medium text-gray-900 mb-2">性別</div>
          <RadioGroup
            value={sex ?? Sex.UNKNOWN}
            onValueChange={(v) => setSex(v as Sex)}
            className="flex flex-col space-y-3"
          >
            <div className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg">
              <RadioGroupItem value={Sex.MALE} id="male" />
              <Label htmlFor="male" className="text-base">
                男性
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg">
              <RadioGroupItem value={Sex.FEMALE} id="female" />
              <Label htmlFor="female" className="text-base">
                女性
              </Label>
            </div>
            <div className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg">
              <RadioGroupItem value="" id="not-set" />
              <Label htmlFor="not-set" className="text-base">
                未設定
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* 生年月日 */}
        <div>
          <div className="text-base font-medium text-gray-900 mb-2">生年月日</div>
          <DatePicker
            value={birthday ? new Date(birthday) : undefined}
            onChange={(date) => setBirthday(date?.toISOString() ?? '')}
          />
          <p className="text-sm text-gray-500">空欄にすると未設定になります</p>
        </div>

        {/* ユーザーID */}
        <div>
          <div className="text-base font-medium text-gray-900 mb-2">ユーザーID</div>
          <Input
            placeholder="例: 101"
            className="h-12 text-base"
            value={userID}
            onChange={(e) => setUserID(e.target.value)}
          />
        </div>

        {/* OrganizerID */}
        <div>
          <div className="text-base font-medium text-gray-900 mb-2">OrganizerID</div>
          <Input
            placeholder="例: 101,102"
            className="h-12 text-base"
            value={organizerID}
            onChange={(e) => setOrganizerID(e.target.value)}
          />
          <p className="text-sm text-gray-500">複数のIDはカンマ区切りで入力してください</p>
        </div>

        {/* UseOrganizerID */}
        <div>
          <div className="text-base font-medium text-gray-900 mb-2">UseOrganizerID</div>
          <Input
            placeholder="例: 101"
            className="h-12 text-base"
            value={useOrganizerID}
            onChange={(e) => setUseOrganizerID(e.target.value)}
          />
          <p className="text-sm text-gray-500">現在使用中のOrganizerIDを入力してください</p>
        </div>

        {/* location */}
        <div>
          <div className="text-base font-medium text-gray-900 mb-2">位置情報</div>
          <div className="text-base font-medium text-gray-900 mb-2">緯度</div>
          <Input
            placeholder="例: 35.681236"
            className="h-12 text-base"
            value={lat}
            onChange={(e) => setLat(Number(e.target.value))}
          />
          <div className="text-base font-medium text-gray-900 mb-2">経度</div>
          <Input
            placeholder="例: 139.767125"
            className="h-12 text-base"
            value={lng}
            onChange={(e) => setLng(Number(e.target.value))}
          />
        </div>

        {/* 現在のユーザー情報表示 */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h3 className="text-sm font-medium text-gray-900 mb-3">現在のユーザー情報</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <div>ID: {user?.id}</div>
            <div>名前: {user?.name}</div>
            <div>メール: {user?.email}</div>
            <div>
              性別: {user?.sex === Sex.MALE ? '男性' : user?.sex === Sex.FEMALE ? '女性' : '未設定'}
            </div>
            <div>生年月日: {user?.birthday || '未設定'}</div>
            <div>OrganizerID: {user?.organizerID}</div>
            <div>OrganizerCode: {user?.organizerCode}</div>
            <div>UseOrganizerID: {user?.useOrganizerID}</div>
            <div>緯度: {lat}</div>
            <div>経度: {lng}</div>
          </div>
        </div>

        {/* chagne page url to google.com */}
        <a href="https://google.com" rel="noopener noreferrer" className="">
          <Button type="button" className="w-full h-12 text-base font-medium my-4">
            Googleページに移動
          </Button>
        </a>

        <a href="https://dev-app.kenkomileage-renewal.net/menu" rel="noopener noreferrer">
          <Button type="button" className="w-full h-12 text-base font-medium">
            dev-appページに移動
          </Button>
        </a>

        {/* 送信ボタン */}
        <Button type="button" className="w-full h-12 text-base font-medium" onClick={handleUpdate}>
          更新する
        </Button>
      </div>
    </>
  );
}
