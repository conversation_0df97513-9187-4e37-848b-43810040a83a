'use client';

import TopBar from '@/components/layout/top-bar';
import VersionTag from '@/components/layout/version-tag';
import { Button } from '@/components/shared/button';
import { Input } from '@/components/ui/input';
import { useMiniAppHealthCheckupLink } from '@/hooks/use-health-checkup-link';
import { useRouter } from '@/hooks/use-next-navigation';
import { useDevModeStore } from '@/store/dev-mode';
import { getOrigin } from '@/utils/get-host';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useState } from 'react';

export default function TestPage() {
  const [path, setPath] = useState('/walking-course/detail/165');
  const router = useRouter();

  const { devMode, setDevMode } = useDevModeStore();
  const { openLink: openHealthCheckupLink } = useMiniAppHealthCheckupLink();
  const showUUID = () => {
    console.log('showUUID');
    sendMessageToNative({
      type: 'uuid',
      callback: (data) => {
        if (data?.code) {
          alert(`${data.code}`);
        }
      },
    });
  };
  const showLocation = () => {
    console.log('showLocation');
    sendMessageToNative({
      type: 'get-user-location',
      callback: (data) => {
        if (data) {
          alert(`${JSON.stringify(data, null, 2)}`);
        }
      },
    });
  };
  return (
    <>
      <TopBar onBack={() => router.back()} title="Test Page" />
      <div className="flex flex-col gap-4 mx-6 pt-6">
        <VersionTag />
        <div>Origin: {getOrigin()}</div>
        <div>NEXT_PUBLIC_DEV_CODE: {process.env.NEXT_PUBLIC_DEV_CODE}</div>
        <div>NEXT_PUBLIC_APP_ENV: {process.env.NEXT_PUBLIC_APP_ENV}</div>
        <div>NODE_ENV: {process.env.NODE_ENV}</div>
        <div>{devMode ? 'in dev mode' : 'in normal mode'}</div>
        <Button onClick={() => setDevMode(!devMode)}>toggle dev mode</Button>
        <Input type="text" value={path} onChange={(e) => setPath(e.target.value)} />
        <Button onClick={() => router.push(path)}>
          Go <span className="text-xs ">{path}</span>
        </Button>
        <Button onClick={() => router.push('/shared-preview')}>shared preview</Button>
        <Button onClick={() => router.push('/test-page/set-user')}>set user</Button>
        <Button onClick={() => router.push('/ranking')}>set ranking</Button>
        <Button onClick={showUUID}>show uuid</Button>
        <Button onClick={showLocation}>show location</Button>
        <Button onClick={() => openHealthCheckupLink()}>open health checkup link</Button>
      </div>
    </>
  );
}
