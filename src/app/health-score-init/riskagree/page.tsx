'use client';

import {
  CalcJudgementRequest,
  RiskOnOffPutRequest,
  riskAPI,
} from '@/api/modules/health-score-init';
import TopBar from '@/components/layout/top-bar';
import { RISK_PERMISSION } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { useDeviceDetect } from '@/hooks/use-device-detect';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useRiskData } from '@/hooks/use-risk-data';
import { useDevModeStore } from '@/store/dev-mode';
import { useGlobalStore } from '@/store/global';
import type { Asset, PageAsset } from '@/types/health-core-init';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useCallback, useEffect, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';

function RiskAgree() {
  let hasFrails = false;
  let hasBloods = false;
  let hasImmunitys = false;
  let hasSalt = false;
  let selectedItems: PageAsset[];
  let unselectedItems: PageAsset[];
  let existItems: PageAsset[];
  const { isAndroid } = useDeviceDetect();
  const { log } = useDevModeStore();
  const searchParams = useSearchParams();
  const { setIsRiskShowed, setIsGlobalInited, fromPage } = useGlobalStore();
  const router = useRouter();
  const { setLoading } = useLoading();
  const { refreshRiskData } = useRiskData();

  let from = '';

  if (searchParams != null) {
    const encodedData = searchParams.get('data') || '';
    const params = JSON.parse(atob(encodedData));
    const items = params.items;
    unselectedItems = params.offItems;
    existItems = params.existItems;
    from = params.from;
    if (items != null) {
      for (let i = 0; i < items.length; i++) {
        if (items[i].id === 'frail') {
          hasFrails = true;
        } else if (items[i].id === 'blood') {
          hasBloods = true;
        } else if (items[i].id === 'immunity') {
          hasImmunitys = true;
        } else if (items[i].id === 'salt') {
          hasSalt = true;
        }
      }
    }
    selectedItems = items;
  }

  const saveRisk = (
    list: Array<Asset>,
    unsetList: Array<Asset>,
    existList: Array<Asset> | undefined,
    from: string,
    agree: boolean,
  ) => {
    setIsRiskShowed(false);
    const request = new CalcJudgementRequest();
    let allList = list;
    if (unsetList !== null && unsetList !== undefined) {
      allList = [...list, ...unsetList];
    }

    if (existList !== null && existList !== undefined) {
      allList = [...allList, ...existList];
    }
    const assets = agree ? list.map((item) => Number(item.asset_type)) : [];

    const sendList = allList.map((item) => ({
      asset_type: item.asset_type,
      op_state: item.op_state == null ? '0' : item.op_state,
      op_permission: item.op_permission,
    }));
    request.asset_list = sendList;
    request.on_assets = assets;
    setLoading(true, { text: 'データを通信中...' });
    riskAPI
      .calcJudgementDate(request)
      .then((response) => {
        setLoading(false);
        refreshRiskData((riskState) => {
          log(`RiskAgree: saveRisk: refreshRiskData: ${JSON.stringify(riskState)}`);
          if (riskState?.isRiskInited) {
            setIsGlobalInited(true);
          }
          if (from === 'sel') {
            //アプリ起動時リスク推定設定時
            if (agree) {
              log('RiskAgree: agree to health score');
              router.createHistory([ROUTES.HOME]);
              router.push(ROUTES.HEALTH_SCORE);
            } else {
              router.createHistory([ROUTES.HOME]);
              router.push(ROUTES.HOME);
            }
          } else if (from === 'change') {
            //ヘルスチェックAIを変更画面から遷移場合、ヘルスチェックAI設定画面へ遷移
            router.createHistory([ROUTES.HOME]);
            router.push(ROUTES.RISK.SETTING);
          } else {
            //ヘルスチェックAI設定画面で全部ONでここに遷移された時
            if (agree) {
              //ヘルスチェックAI画面へ遷移
              log('RiskAgree: disagree to health score');
              router.createHistory([ROUTES.HOME]);
              router.push(ROUTES.HEALTH_SCORE);
            } else {
              router.createHistory([ROUTES.HOME]);
              router.push(ROUTES.RISK.SETTING);
            }
          }
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // //AI利用スイッチ取得API
  // const confirmHealthScoreList = useCallback(async () => {
  //   setLoading(true, { text: 'データを通信中...' });
  //   riskAPI
  //     .getOnOffList()
  //     .then((response) => {
  //       console.log(response);
  //       if (response != null && response.on_of_list != null) {
  //         const usingArray = response.on_of_list.filter((element) => element.op_state === '1');
  //         if (usingArray.length > 0) {
  //           //全部OFFではない ヘルスチェックAI画面へ遷移
  //           router.push(ROUTES.HEALTH_SCORE);
  //         } else {
  //           //全部OFF メニュー画面へ遷移
  //           router.push(ROUTES.RISK.SETTING);
  //         }
  //         setLoading(false);
  //       }
  //     })
  //     .catch((error) => {
  //       console.log(error);
  //       setLoading(false);
  //       return Promise.reject();
  //     });
  // }, [router, setLoading]);

  const [showFrails, setShowFrails] = useState(false);
  const [showBloods, setShowBloods] = useState(false);
  const [showImmunitys, setShowImmunitys] = useState(false);
  const [showSalts, setShowSalts] = useState(false);

  let frails = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.FRAIL_IOS_CONTENT.split('<br/>');
  if (isAndroid) {
    frails = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.FRAIL_ANDROID_CONTENT.split('<br/>');
  }

  let bloods = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.BLOODP_IOS_CONTENT.split('<br/>');
  if (isAndroid) {
    bloods = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.BLOODP_ANDROID_CONTENT.split('<br/>');
  }

  let immunitys = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.IMMUNITY_IOS_CONTENT.split('<br/>');
  if (isAndroid) {
    immunitys = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.IMMUNITY_ANDROID_CONTENT.split('<br/>');
  }

  let salt = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.SALT_IOS_CONTENT.split('<br/>');
  if (isAndroid) {
    salt = APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.SALT_ANDROID_CONTENT.split('<br/>');
  }

  const agree = () => {
    //同意APIを呼び出す
    handleBack();
    for (const item of selectedItems) {
      item.op_state = '1';
    }
    saveRisk(selectedItems, unselectedItems, undefined, from, true);
  };

  const disagree = () => {
    let needAllOff = false;
    //不同意APIを呼び出す
    for (const item of selectedItems) {
      //同意しない時、必須項目がある場合
      if (item.op_permission === RISK_PERMISSION.force) {
        needAllOff = true;
      }
      item.op_state = '0';
    }
    if (needAllOff) {
      //すでに利用してる項目もOFFにする
      for (const item of unselectedItems) {
        item.op_state = '0';
      }

      for (const item of existItems) {
        item.op_state = '0';
      }
      saveRisk(selectedItems, unselectedItems, existItems, from, false);
    } else {
      saveRisk(selectedItems, unselectedItems, undefined, from, false);
    }
  };

  const close = () => {
    if (from === 'sel') {
      if (fromPage === 'menu') {
        router.backTo(ROUTES.MENU.MENU);
      } else {
        router.backTo(ROUTES.HOME);
      }
    } else {
      router.backTo(ROUTES.RISK.SETTING);
    }
  };

  const handleBack = () => {
    const selectStr = selectedItems.map((item) => item.asset_type).join(',');
    sendMessageToNative({
      type: 'start-ai',
      data: { info: selectStr },
      callback: (data) => {},
    });
  };
  return (
    <div className="h-full bg-white ">
      <TopBar
        title={APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.TITLE}
        enableBack={true}
        enableClose={true}
        onClose={close}
      />
      <div className="flex flex-col items-start">
        <div>
          <div className="text-[16px] ">
            <div className="font-bold mb-[10px]">
              {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT2}
            </div>
            {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT21}
            <br />
            <div className="text-gray-500 text-[14px] mt-[10px] mb-[10px]">
              {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT22}
            </div>
            <div className="mb-[10px]">{APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT23}</div>
            {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT24}
            <br />
            <br />
            <div className="font-bold mb-2">{APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.DATA_TITLE}</div>
            {hasFrails && (
              <div className="flex justify-between mb-4">
                <span>
                  <span className="h-full border-l-4 border-primary-light" />
                  <span className="ml-2"> {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.FRAIL_TITLE}</span>
                </span>
                <button
                  type="button"
                  className="text-primary font-bold  inline-block no-underline cursor-pointer border-none"
                  onClick={() => setShowFrails(!showFrails)}
                >
                  {showFrails
                    ? APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_CLOSE
                    : APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_OPEN}
                </button>
              </div>
            )}
            <div className={`rounded-2xl text-[16px] bg-primary-5 ${showFrails ? 'p-5' : ''}`}>
              {showFrails &&
                frails.map((line, index) => (
                  <div className="text-[15px]" key={index}>
                    {line}
                  </div>
                ))}
            </div>
            {showFrails && <br />}
            {hasBloods && (
              <div className="flex justify-between  mb-4">
                <span>
                  <span className="h-full border-l-4 border-primary-light" />
                  <span className="ml-2">{APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.BLOODP_TITLE}</span>
                </span>
                <button
                  type="button"
                  className="text-primary font-bold inline-block no-underline cursor-pointer border-none"
                  onClick={() => setShowBloods(!showBloods)}
                >
                  {showBloods
                    ? APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_CLOSE
                    : APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_OPEN}
                </button>
              </div>
            )}
            <div className={`rounded-2xl bg-primary-5 ${showBloods ? 'p-5' : ''}`}>
              {showBloods &&
                bloods.map((line, index) => (
                  <div className="text-[15px] " key={index}>
                    {line}
                  </div>
                ))}
            </div>
            {showBloods && <br />}
            {hasImmunitys && (
              <div className="flex justify-between  mb-4">
                <span>
                  <span className="h-full border-l-4 border-primary-light" />
                  <span className="ml-2">
                    {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.IMMUNITY_TITLE}
                  </span>
                </span>
                <button
                  type="button"
                  className="text-primary font-bold inline-block no-underline cursor-pointer border-none"
                  onClick={() => setShowImmunitys(!showImmunitys)}
                >
                  {showImmunitys
                    ? APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_CLOSE
                    : APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_OPEN}
                </button>
              </div>
            )}
            <div className={`rounded-2xl text-[15px] bg-primary-5 ${showImmunitys ? 'p-5' : ''}`}>
              {showImmunitys &&
                immunitys.map((line, index) => (
                  <div className="text-[15px]" key={index}>
                    {line}
                  </div>
                ))}
            </div>
            {showImmunitys && <br />}
            {hasSalt && (
              <div className="flex justify-between  mb-4">
                <span>
                  <span className="h-full border-l-4 border-primary-light" />
                  <span className="ml-2"> {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.SALT_TITLE}</span>
                </span>
                <button
                  type="button"
                  className="text-primary font-bold inline-block no-underline cursor-pointer border-none"
                  onClick={() => setShowSalts(!showSalts)}
                >
                  {showSalts
                    ? APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_CLOSE
                    : APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.RISK_OPEN}
                </button>
              </div>
            )}
            <div className={`rounded-2xl text-[15px] bg-primary-5 ${showSalts ? 'p-5' : ''}`}>
              {showSalts &&
                salt.map((line, index) => (
                  <div className="text-[15px]" key={index}>
                    {line}
                  </div>
                ))}
            </div>
            <div className="h-[2px] bg-gray-300 transition-all duration-300 hover:w-full mt-5 mb-5" />
            <div>{APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT3}</div>
            <div className="text-gray-500 text-[14px] mt-2 mb-2">
              {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT31}
            </div>
            <div className="text-gray-500 text-[14px]">
              {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.USERTEXT32}
            </div>
            <br />

            <br />
          </div>
          <button
            className="bg-primary text-white  font-bold h-12  rounded-3xl w-full"
            type="button"
            onClick={agree}
          >
            {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.NEXT}
          </button>

          <button
            type="button"
            className="bg-white  rounded-3xl w-full border h-12 border-primary text-primary mt-5 font-bold mb-5"
            onClick={disagree}
          >
            {APP_TEXT.HEALTH_SCORE_INIT_AGREE_PAGE.CANCEL}
          </button>
        </div>
      </div>
    </div>
  );
}

export default RiskAgree;
