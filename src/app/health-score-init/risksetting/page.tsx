'use client';
import { RiskOnOffPutRequest, riskAPI } from '@/api/modules/health-score-init';
import TopBar from '@/components/layout/top-bar';

import { Button } from '@/components/shared/button';
import { DialogClose } from '@/components/shared/dialog';
import { Switch } from '@/components/shared/switch';
import { TextButton } from '@/components/shared/text-button';
import { RISK_PERMISSION, RiskTypeNum, SUPPORT_RISK } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import type { PageAsset } from '@/types/health-core-init';
import { useCallback, useEffect, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';
import { COMMON_TEXT } from '../../../const/text/common';

function RiskSetting() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const [switchOn, setSwitchOn] = useState(true);

  const { isShow, setDialog } = useMessageDialog();

  //Toggle 全部ON表示するFlg
  const [showSetAllOn, setShowSetAllOn] = useState(false);

  //全部項目
  const [allItems, setAllItems] = useState<PageAsset[]>([]);
  //必須利用
  const [noptions, setNoptions] = useState<PageAsset[]>([]);
  //任意利用
  const [options, setOptions] = useState<PageAsset[]>([]);
  //元任意利用で、選択済み
  const [oAddItems, setOaddItems] = useState<PageAsset[]>([]);

  //元任意利用で、これから追加項目
  const [addItems, setAddItems] = useState<PageAsset[]>([]);

  const [delteItems, setDeleteItems] = useState<PageAsset[]>([]);

  //AI利用スイッチ取得API
  const fetchHealthScoreList = useCallback(() => {
    setLoading(true, { text: 'データを通信中...' });
    riskAPI
      .getOnOffList()
      .then((response) => {
        console.log(response);

        if (response != null && response.on_of_list != null) {
          const extendedArray = response.on_of_list
            .filter(
              (element) =>
                element.asset_type != null &&
                SUPPORT_RISK.includes(element.asset_type as unknown as RiskTypeNum),
            )
            .map((item) => ({
              ...item,
              id: '',
              label: '',
            }));
          for (const item of extendedArray) {
            if (item.asset_type === RiskTypeNum.frail) {
              //1:フレイル予防
              item.id = 'frail';
              item.label = 'FRAIL';
            } else if (item.asset_type === RiskTypeNum.bloodPressure) {
              //2:血圧上昇習慣
              item.id = 'blood';
              item.label = 'BLOODP';
            } else if (item.asset_type === RiskTypeNum.immunity) {
              //4:免疫力
              item.id = 'immunity';
              item.label = 'IMMUNITY';
            } else if (item.asset_type === RiskTypeNum.bloodGlucoseAndNeutralFat) {
              //5:血糖値·中性脂肪改善習慣
              item.id = 'salt';
              item.label = 'SALT';
            }
          }
          setAllItems(extendedArray as PageAsset[]);
          //全部現状非ONのAI
          const offItems = extendedArray.filter((element) => element.op_state !== '1');

          if (offItems.length === extendedArray.length) {
            setShowSetAllOn(true);
          } else {
            setShowSetAllOn(false);
          }

          //現状Toggle変更できないAI
          const no_option = extendedArray.filter(
            (element) => element.op_permission === RISK_PERMISSION.force,
          );

          //現状Toggle変更るAI
          const options = extendedArray.filter(
            (element) => element.op_permission === RISK_PERMISSION.random,
          );

          //現状Toggle変更るAI中ONのAI
          const added = extendedArray.filter(
            (element) =>
              element.op_permission === RISK_PERMISSION.random && element.op_state === '1',
          );
          //これから強制追加AI
          const permissionAdded = extendedArray.filter(
            (element) =>
              element.op_permission === RISK_PERMISSION.force && element.op_state !== '1',
          );
          setOptions(options as PageAsset[]);
          setNoptions(no_option as PageAsset[]);
          setOaddItems(added as PageAsset[]);
          setAddItems([...(added as PageAsset[]), ...(permissionAdded as PageAsset[])]);
          setLoading(false);
          for (const item of options) {
            if (item.op_permission === RISK_PERMISSION.force) {
              if (selectedItems.filter((element) => element.id === item.id).length === 0) {
                selectedItems.push(item as PageAsset);
              }
            } else if (item.op_state === '1') {
              if (selectedItems.filter((element) => element.id === item.id).length === 0) {
                selectedItems.push(item as PageAsset);
              }
            }
          }
        }
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  }, [setLoading]);

  // Fetch data when search parameters change
  useEffect(() => {
    fetchHealthScoreList();
  }, [fetchHealthScoreList]);

  const [selectedItems, setSelectedItems] = useState<PageAsset[]>([]);

  function goNextPage(added: PageAsset[], existItem: PageAsset[]) {
    setShowSetAllOn(true);
  }

  // Keyから文字取得する
  const getText = (key: string, texts: Record<string, string>) => {
    return texts[key] || '';
  };

  // arrAかarrBの内容を除く
  const aMinusB = (arrA: PageAsset[], arrB: PageAsset[]) => {
    const setB = new Set(arrB);
    return arrA.filter((item) => !setB.has(item));
  };

  // 全部OFF Toggle動作 確認ポップアップを表示
  const switchAllOff = () => {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-[8px]]">
          <div className="text-[16px] mb-1">
            {APP_TEXT.HEALTH_SCORE_SET_PAGE.OFF_ALL1}
            <br />
            {APP_TEXT.HEALTH_SCORE_SET_PAGE.OFF_ALL2}
          </div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="destructive"
            className="w-full mb-2"
            onClick={() => {
              setDialog(false);
              setRiskAllOff(allItems);
            }}
          >
            {APP_TEXT.HEALTH_SCORE_SET_PAGE.OFF_BTN}
          </Button>
          <DialogClose asChild>
            <TextButton className="mt-2 w-full" variant="muted">
              {COMMON_TEXT.BUTTON.CANCEL}
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  };

  // 全部ON 動作
  const setRiskAllOn = () => {
    const params = {
      from: 'setting',
    };
    const paramsStr = btoa(JSON.stringify(params));
    router.push(`${ROUTES.RISK.SELECT}?data=${paramsStr}`);
  };

  // 全部OFF  「オフにする」動作
  const setRiskAllOff = useCallback(
    (items: Array<PageAsset>) => {
      const assetList = items.map((item) => ({
        asset_type: item.asset_type,
        op_state: '0',
      }));
      const request = new RiskOnOffPutRequest();
      request.assets = assetList;
      setLoading(true, { text: 'データを通信中...' });
      riskAPI
        .putOnOffList(request)
        .then((response) => {
          setLoading(false);
          fetchHealthScoreList();
        })
        .catch((error) => {
          // else
          console.log(error);
          setLoading(false);
          return Promise.reject();
        });
    },
    [setLoading, fetchHealthScoreList],
  );

  const close = () => {
    router.backTo(ROUTES.HEALTH_SCORE);
  };

  const goToRamPage = () => {
    router.push(ROUTES.RISK.CHANGE);
  };

  return (
    <div className="bg-white h-full">
      <TopBar
        title={APP_TEXT.HEALTH_SCORE_SET_PAGE.TITLE}
        enableBack={true}
        onBack={close}
        enableClose={false}
      />
      <div className="flex flex-col items-start">
        <div className=" w-full text-left text-[16px]">
          {APP_TEXT.HEALTH_SCORE_SET_PAGE.TOGGLE_TITLE}
        </div>
        {showSetAllOn && (
          <div className="flex items-center gap-2">
            <span className="text-[14px] text-gray-600">
              {APP_TEXT.HEALTH_SCORE_SET_PAGE.TOGGLE_CONTENT_ON}
            </span>
            <Switch
              checked={false}
              onCheckedChange={setRiskAllOn}
              className="ml-2 h-8 w-12 [&>span]:h-6 [&>span]:w-6"
            />
          </div>
        )}
        {!showSetAllOn && (
          <div className="flex items-center gap-2">
            <span className="text-[14px] text-gray-600">
              {APP_TEXT.HEALTH_SCORE_SET_PAGE.TOGGLE_CONTENT_OFF}
            </span>
            <Switch
              checked={switchOn}
              onCheckedChange={switchAllOff}
              className="ml-2 h-8 w-12 [&>span]:h-6 [&>span]:w-6"
            />
          </div>
        )}

        {noptions.length > 0 && (
          <div className="mt-5 bg-white  w-full font-bold text-left text-[18px] mb-4">
            {APP_TEXT.HEALTH_SCORE_SET_PAGE.NOT_CHECKBLE_TITLE}
          </div>
        )}
        {noptions.map((option, index) => (
          <div
            key={option.id}
            className={`w-full  flex items-center ${index !== noptions.length - 1 ? 'border-b-2' : ''} pb-3 pt-3`}
          >
            <div className="flex-1 ">
              <label className="text-[16px]  " htmlFor={option.id}>
                {getText(`${option.label}_TITLE`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
              </label>

              <label className="text-[14px] text-gray-500" htmlFor={option.id}>
                <br />
                {getText(`${option.label}_CONTENT`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
              </label>
            </div>
          </div>
        ))}
        {options.length > 0 && (
          <div className="flex justify-between  w-full ">
            <div className="mt-5 bg-white  font-bold text-left text-[18px] mb-4">
              {APP_TEXT.HEALTH_SCORE_SET_PAGE.CHECKBLE_TITLE}
            </div>
            {!showSetAllOn && (
              <button
                type="button"
                className="text-primary font-bold  inline-block no-underline cursor-pointer border-none"
                onClick={goToRamPage}
              >
                {APP_TEXT.HEALTH_SCORE_SET_PAGE.MODIFY_BUTTON}
              </button>
            )}
          </div>
        )}
        {options.length > 0 && (
          <div className="checkbox-group">
            {options.map((option, index) => (
              <div
                key={option.asset_type}
                className={`w-full  flex items-center pb-3 pt-3 ${index !== options.length - 1 ? 'border-b-2' : ''} `}
              >
                <div className="flex-1">
                  {option.op_state === '1' && (
                    <label
                      className="text-[12px] bg-primary-5 rounded-[6px] pt-1 pb-1 pl-2 pr-2 text-primary"
                      htmlFor={option.id}
                    >
                      現在利用中
                    </label>
                  )}
                  <div className="text-[16px]  mt-1 ">
                    {getText(`${option.label}_TITLE`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
                  </div>
                  <div className="text-[14px] text-gray-500">
                    {getText(`${option.label}_CONTENT`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default RiskSetting;
