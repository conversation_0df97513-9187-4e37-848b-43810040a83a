'use client';

import TopBar from '@/components/layout/top-bar';
import { ROUTES } from '@/const/routes';
import { useRouter } from '@/hooks/use-next-navigation';
import { APP_TEXT } from '../../../const/text/app';
import usageImage from '../../../images/risk-usage.png';
function RiskUsage() {
  const router = useRouter();

  const goNext = async () => {
    router.push(ROUTES.RISK.SELECT);
  };

  const goBack = async () => {
    router.replace(ROUTES.HOME);
  };

  return (
    <div className=" bg-white">
      <TopBar
        title={APP_TEXT.HEALTH_SCORE_INIT_USAGE_PAGE.TITLE}
        enableBack={false}
        enableClose={true}
        onClose={goBack}
      />
      <div className="flex flex-col items-start">
        <div>
          <div className="ml-5,mr-5">
            <img alt="desc" className="w-full h-auto " src={usageImage.src} />
          </div>

          <div className="mt-5 text-[18px] ">
            {APP_TEXT.HEALTH_SCORE_INIT_USAGE_PAGE.USERTEXT}
            <br />
            <br />
            {APP_TEXT.HEALTH_SCORE_INIT_USAGE_PAGE.USERTEXT2}
            <br />
            <br />
            {APP_TEXT.HEALTH_SCORE_INIT_USAGE_PAGE.USERTEXT4}
            <br />
          </div>
          <div className="text-[16px] text-gray-500 mt-4 mb-24 ">
            {APP_TEXT.HEALTH_SCORE_INIT_USAGE_PAGE.USEMISSIONTEXT}
            <br />
          </div>
          <div className="fixed mb-5 left-0 right-0 bottom-3  z-[2] items-center w-full font-bold text-center box-[border-box]">
            <button
              className="bg-primary text-white  font-bold h-12 ml-5 mr-5 rounded-3xl w-[88vw]"
              type="button"
              onClick={goNext}
            >
              {APP_TEXT.HEALTH_SCORE_INIT_USAGE_PAGE.NEXT}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RiskUsage;
