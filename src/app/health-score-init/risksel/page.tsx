'use client';
import { CalcJudgementRequest, riskAPI } from '@/api/modules/health-score-init';
import TopBar from '@/components/layout/top-bar';
import { Checkbox } from '@/components/shared/checkbox';
import { RISK_PERMISSION, RiskTypeNum, SUPPORT_RISK } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { CONSTANTS } from '@/lib/constants';
import { useGlobalStore } from '@/store/global';
import type { PageAsset } from '@/types/health-core-init';
import { useCallback, useEffect, useRef, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';
import { COMMON_TEXT } from '../../../const/text/common';

interface Props {
  title: string;
  content: string;
}

function RiskSel() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const [options, setOptions] = useState<PageAsset[]>([]);
  const searchParams = useSearchParams();
  const [from, setFrom] = useState('');
  const { fromPage } = useGlobalStore();
  const checkboxRef = useRef(null);
  //AI利用スイッチ取得API
  const fetchHealthScoreList = useCallback(() => {
    let from = '';
    if (searchParams != null) {
      const encodedData = searchParams.get('data') || '';
      if (encodedData !== undefined && encodedData !== '') {
        const params = JSON.parse(atob(encodedData));
        from = params.from;
        setFrom(from);
      }
    }
    setLoading(true, { text: 'データを通信中...' });
    riskAPI
      .getOnOffList()
      .then((response) => {
        console.log(response);

        if (response != null && response.on_of_list != null) {
          let filteredArray = response.on_of_list.filter(
            (element) =>
              element.op_state == null &&
              element.asset_type != null &&
              SUPPORT_RISK.includes(element.asset_type as unknown as RiskTypeNum),
          );
          if (from === 'setting') {
            filteredArray = response.on_of_list.filter(
              (element) =>
                element.asset_type != null &&
                SUPPORT_RISK.includes(element.asset_type as unknown as RiskTypeNum),
            );
          } else {
            const exsit = response.on_of_list.filter(
              (element) =>
                element.op_state != null &&
                element.asset_type != null &&
                SUPPORT_RISK.includes(element.asset_type as unknown as RiskTypeNum),
            );

            setExistItems(exsit as PageAsset[]);
          }

          const extendedArray = filteredArray.map((item) => ({
            ...item,
            id: '',
            label: '',
          }));
          for (const item of extendedArray) {
            if (item.asset_type === RiskTypeNum.frail) {
              //1:フレイル予防
              item.id = 'frail';
              item.label = 'FRAIL';
            } else if (item.asset_type === RiskTypeNum.bloodPressure) {
              //2:血圧上昇習慣
              item.id = 'blood';
              item.label = 'BLOODP';
            } else if (item.asset_type === RiskTypeNum.immunity) {
              //4:免疫力
              item.id = 'immunity';
              item.label = 'IMMUNITY';
            } else if (item.asset_type === RiskTypeNum.bloodGlucoseAndNeutralFat) {
              //5:血糖値·中性脂肪改善習慣
              item.id = 'salt';
              item.label = 'SALT';
            }
          }
          const sortedArray = [...extendedArray].sort((a, b) => {
            if (
              a.op_permission === RISK_PERMISSION.force &&
              b.op_permission !== RISK_PERMISSION.force
            )
              return -1;
            if (
              a.op_permission !== RISK_PERMISSION.force &&
              b.op_permission === RISK_PERMISSION.force
            )
              return 1;
            return 0;
          });
          setOptions(sortedArray as PageAsset[]);
        }
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  }, [setLoading, searchParams]);

  // Fetch data when search parameters change
  useEffect(() => {
    fetchHealthScoreList();
  }, [fetchHealthScoreList]);

  const [selectedItems, setSelectedItems] = useState<PageAsset[]>([]);
  const [existItems, setExistItems] = useState<PageAsset[]>([]);
  for (const item of options) {
    if (item.op_permission === RISK_PERMISSION.force) {
      const filteredArray = selectedItems.filter(
        (element) => element.asset_type === item.asset_type,
      );
      if (filteredArray.length === 0) {
        selectedItems.push(item);
      }
    }
  }

  const goNext = () => {
    const unselectItems = Array();
    for (const item of options) {
      const filteredArray = selectedItems.filter(
        (element) => element.asset_type === item.asset_type,
      );
      if (filteredArray.length === 0) {
        unselectItems.push(item);
      }
    }
    //未選択項目AIなし
    if (selectedItems.length === 0) {
      //不同意APIを呼び出す
      saveRisk(unselectItems, existItems);
    } else {
      //一つでも選択ある場合場合
      goNextPage(selectedItems, unselectItems, existItems);
    }
  };

  const saveRisk = useCallback(
    (offItems: Array<PageAsset>, existItems: Array<PageAsset>) => {
      const assetList = offItems.map((item) => ({
        asset_type: item.asset_type,
        op_state: '0',
        op_permission: item.op_permission,
      }));
      const existList = existItems.map((item) => ({
        asset_type: item.asset_type,
        op_state: item.op_state,
        op_permission: item.op_permission,
      }));

      //既存のリスク推定を送信しないようにします。
      // const sendList = [...assetList, ...existList];
      const sendList = assetList;
      const request = new CalcJudgementRequest();

      request.asset_list = sendList;
      request.on_assets = [];
      setLoading(true, { text: 'データを通信中...' });
      riskAPI
        .calcJudgementDate(request)
        .then((response) => {
          setLoading(false);
          router.backTo(ROUTES.HOME);
        })
        .catch((error) => {
          // else
          console.log(error);
          setLoading(false);
          return Promise.reject();
        });
    },
    [router, setLoading],
  );

  function goNextPage(onItems: PageAsset[], offItems: PageAsset[], existItems: PageAsset[]) {
    const tempOffItem = offItems.map((item) => ({
      id: item.id,
      asset_type: item.asset_type,
      op_state: '0',
      op_permission: item.op_permission,
    }));
    const tempExistItems = existItems.map((item) => ({
      id: item.id,
      asset_type: item.asset_type,
      op_state: item.op_state,
      op_permission: item.op_permission,
    }));
    // const sendExistItems = [...tempOffItem, ...tempExistItems];
    const params = {
      items: onItems.map((item) => ({
        id: item.id,
        asset_type: item.asset_type,
        op_permission: item.op_permission,
      })),
      offItems: tempOffItem,
      existItems: tempExistItems,
      from: from === '' ? 'sel' : from,
    };
    const paramsStr = btoa(JSON.stringify(params));
    router.push(`${ROUTES.RISK.AGREE}?data=${paramsStr}`);
  }

  const getText = (key: string, texts: Record<string, string>) => {
    return texts[key] || '';
  };

  const handleChange = (item: PageAsset, changeable: string | undefined) => {
    if (changeable === '0') {
      return;
    }
    setSelectedItems((prev) => {
      const exists = prev.some((selected) => selected.id === item.id);
      return exists
        ? prev.filter((selected) => selected.id !== item.id) // 移除
        : [...prev, item]; // 添加
    });
  };

  const close = () => {
    if (from === 'setting') {
      router.backTo(ROUTES.RISK.SETTING);
    } else {
      if (fromPage === 'menu') {
        router.backTo(ROUTES.MENU.MENU);
      } else {
        router.backTo(ROUTES.HOME);
      }
    }
  };

  const back = () => {
    router.back();
  };

  return (
    <div className="bg-white h-full">
      <TopBar
        title={APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE.TITLE}
        enableBack={true}
        onBack={back}
        enableClose={true}
        onClose={close}
      />
      <div>
        <div className="flex flex-col items-start">
          <div className="mb-[8px] text-[16px]">
            {APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE.USERTEXT2}
            <br />
          </div>
          <div className="text-[14px] text-gray-500 mb-[8px]">
            <div>{APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE.USERTEXT4}</div>
          </div>
          <div className="text-[14px] text-gray-500 mb-5">
            <div>{APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE.USERTEXT5}</div>
          </div>

          <div className="checkbox-group">
            {options.map((option: PageAsset) => (
              <div
                key={option.asset_type}
                className={`w-full mb-[18px] p-[15px] flex items-center rounded-2xl    ${option.op_permission === RISK_PERMISSION.force ? 'bg-primary-5' : selectedItems.map((item) => item.id).includes(option.id) ? 'border-primary border-2' : 'border-gray-300 border-2'}`}
              >
                <div key={option.id}>
                  <Checkbox
                    disabled={option.op_permission === RISK_PERMISSION.force}
                    checked={
                      selectedItems.map((item) => item.id).includes(option.id) ||
                      option.op_permission === RISK_PERMISSION.force
                    }
                    onCheckedChange={() => handleChange(option, option.op_permission)}
                    className="border border-border ring-none shadow-none"
                  />
                </div>
                <div className="flex-1 ml-[10px]">
                  {option.op_permission === RISK_PERMISSION.force && (
                    <label
                      className="text-[10px] text-white bg-primary p-1 rounded-md"
                      htmlFor={option.id}
                    >
                      利用必須
                      <br />
                    </label>
                  )}
                  <label className="text-5 font-bold" htmlFor={option.id}>
                    {getText(`${option.label}_TITLE`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
                  </label>
                  <label className="text-[14px] text-gray-700" htmlFor={option.id}>
                    <br />
                    {getText(`${option.label}_CONTENT`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
                  </label>
                </div>
              </div>
            ))}
          </div>
          <div className="z-[2] items-center w-full font-bold text-center box-[border-box] ">
            <button
              className="bg-primary text-white  font-bold h-12  rounded-3xl w-full"
              type="button"
              onClick={goNext}
            >
              {COMMON_TEXT.BUTTON.NEXT}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RiskSel;
