'use client';
import { RiskOnOffPutRequest, riskAPI } from '@/api/modules/health-score-init';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { Checkbox } from '@/components/shared/checkbox';
import { DialogClose } from '@/components/shared/dialog';
import { TextButton } from '@/components/shared/text-button';
import { RISK_PERMISSION, RiskTypeNum, SUPPORT_RISK } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import type { PageAsset } from '@/types/health-core-init';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';
import { COMMON_TEXT } from '../../../const/text/common';

function RiskChange() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const [changeItemFlg, setChangeItemFlg] = useState(false);

  //全部項目
  const [allItems, setAllItems] = useState<PageAsset[]>([]);

  //任意利用
  const [options, setOptions] = useState<PageAsset[]>([]);
  //元任意利用で、選択済み
  const [oAddItems, setOaddItems] = useState<PageAsset[]>([]);

  //元任意利用で、これから追加項目
  const [addItems, setAddItems] = useState<PageAsset[]>([]);

  const [delteItems, setDeleteItems] = useState<PageAsset[]>([]);

  const { isShow, setDialog } = useMessageDialog();

  //AI利用スイッチ取得API
  const fetchHealthScoreList = useCallback(() => {
    setLoading(true, { text: 'データを通信中...' });
    riskAPI
      .getOnOffList()
      .then((response) => {
        console.log(response);

        if (response != null && response.on_of_list != null) {
          const extendedArray = response.on_of_list
            .filter(
              (element) =>
                element.asset_type != null &&
                SUPPORT_RISK.includes(element.asset_type as unknown as RiskTypeNum),
            )
            .map((item) => ({
              ...item,
              id: '',
              label: '',
            }));
          for (const item of extendedArray) {
            if (item.asset_type === RiskTypeNum.frail) {
              //1:フレイル予防
              item.id = 'frail';
              item.label = 'FRAIL';
            } else if (item.asset_type === RiskTypeNum.bloodPressure) {
              //2:血圧上昇習慣
              item.id = 'blood';
              item.label = 'BLOODP';
            } else if (item.asset_type === RiskTypeNum.immunity) {
              //4:免疫力
              item.id = 'immunity';
              item.label = 'IMMUNITY';
            } else if (item.asset_type === RiskTypeNum.bloodGlucoseAndNeutralFat) {
              //5:血糖値·中性脂肪改善習慣
              item.id = 'salt';
              item.label = 'SALT';
            }
          }
          setAllItems(extendedArray as PageAsset[]);

          //現状Toggle変更るAI
          const options = extendedArray.filter(
            (element) => element.op_permission === RISK_PERMISSION.random,
          );

          //現状Toggle変更るAI中ONのAI
          const added = extendedArray.filter(
            (element) =>
              element.op_permission === RISK_PERMISSION.random && element.op_state === '1',
          );

          setOptions(options as PageAsset[]);
          setOaddItems(added as PageAsset[]);
          setAddItems(added as PageAsset[]);
          setLoading(false);
          for (const item of options) {
            if (item.op_state === '1') {
              if (selectedItems.filter((element) => element.id === item.id).length === 0) {
                selectedItems.push(item as PageAsset);
              }
            }
          }
        }
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  }, [setLoading]);

  // Fetch data when search parameters change
  useEffect(() => {
    fetchHealthScoreList();
  }, [fetchHealthScoreList]);

  useEffect(() => {
    const added = aMinusB(addItems, oAddItems);
    const deleted = aMinusB(oAddItems, addItems);
    if (added.length > 0 || deleted.length > 0) {
      setChangeItemFlg(true);
    } else {
      setChangeItemFlg(false);
    }
  }, [addItems, oAddItems]); // 状态变化后执行

  const [selectedItems, setSelectedItems] = useState<PageAsset[]>([]);

  const saveRisk = useCallback(
    (
      deleteItem: Array<PageAsset>,
      addItems: Array<PageAsset>,
      allItems: Array<PageAsset>,
      opStatus: string,
    ) => {
      const existItems = allItems.filter(
        (item) => !deleteItem.includes(item) && !addItems.includes(item),
      );
      const assetList = deleteItem.map((item) => ({
        asset_type: item.asset_type,
        op_state: opStatus,
      }));
      const request = new RiskOnOffPutRequest();

      request.assets = assetList;
      setLoading(true, { text: 'データを通信中...' });
      riskAPI
        .putOnOffList(request)
        .then((response) => {
          const changeDeleteItem = deleteItem.map((item) => ({
            asset_type: item.asset_type,
            id: item.id,
            label: item.label,
            op_permission: item.op_permission,
            op_state: '0',
          }));

          const targetItems = [...existItems, ...changeDeleteItem];
          setLoading(false);
          goNextPage(addItems, targetItems as PageAsset[]);
        })
        .catch((error) => {
          // else
          console.log(error);
          setLoading(false);
          return Promise.reject();
        });
    },
    [setLoading],
  );

  const goNext = () => {
    const added = aMinusB(addItems, oAddItems);
    const deleted = aMinusB(oAddItems, addItems);
    setDeleteItems(deleted);
    //追加AIがある、同意画面へ遷移する
    if (deleted.length > 0) {
      //不同意APIを呼び出す
      showPartDialog(deleted);
    } else {
      const existItems = allItems.filter((item) => !added.includes(item));
      //追加AIがある、同意画面へ遷移する
      goNextPage(added, existItems);
    }
  };

  const showPartDialog = (deletedItem: PageAsset[]) => {
    setDialog(true, {
      content: (
        <div className="mb-6">
          {APP_TEXT.HEALTH_SCORE_CHANGE_PAGE.OFF_PART1}
          <br />
          {APP_TEXT.HEALTH_SCORE_CHANGE_PAGE.OFF_PART2}
          <br />
          <br />
          <div className="rounded-2xl bg-red-50 p-4">
            <div className="mb-2">オフにするヘルスチェックAI</div>
            {deletedItem.map((option: PageAsset) => (
              <div key="dd" className="text-red-700 font-bold">
                ・{getText(`${option.label}`, APP_TEXT.HEALTH_SCORE_CHANGE_PAGE)}
              </div>
            ))}
          </div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="destructive"
            className="w-full mb-2"
            onClick={() => {
              setDialog(false);
              saveDeleteItem();
            }}
          >
            {APP_TEXT.HEALTH_SCORE_CHANGE_PAGE.OFF_BTN}
          </Button>
          <DialogClose asChild>
            <TextButton className="mt-2 w-full" variant="muted">
              {COMMON_TEXT.BUTTON.CANCEL}
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  };

  //AI項目個別削除時、「オフにする」動作
  const saveDeleteItem = () => {
    const added = aMinusB(addItems, oAddItems);

    saveRisk(delteItems, added, allItems, '0');
  };

  function goNextPage(added: PageAsset[], existItem: PageAsset[]) {
    //追加AIがある場合、同意画面へ遷移する
    if (added.length > 0) {
      const params = {
        items: added.map((item) => ({ id: item.id, asset_type: item.asset_type })),
        offItems: [],
        from: 'change',
      };
      const paramsStr = btoa(JSON.stringify(params));
      router.push(`${ROUTES.RISK.AGREE}?data=${paramsStr}`);
    } else {
      //ヘルスチェックAI設定画面へ戻る
      router.back();
    }
  }

  // Keyから文字取得する
  const getText = (key: string, texts: Record<string, string>) => {
    return texts[key] || '';
  };

  // arrAかarrBの内容を除く
  const aMinusB = (arrA: PageAsset[], arrB: PageAsset[]) => {
    const setB = new Set(arrB);
    return arrA.filter((item) => !setB.has(item));
  };

  // riskチェックボックス変更された時動作
  const handleChange = (option: PageAsset) => {
    //Add to AddItems
    const items = options.filter((element) => element.id === option.id);
    if (items.length > 0) {
      const item = items[0];
      setAddItems((prev) => {
        const exists = prev.some((selected) => selected.id === item.id);
        return exists
          ? prev.filter((selected) => selected.id !== item.id) // 削除
          : [...prev, item]; // 追加
      });
    }
    setSelectedItems((prev) => {
      const exists = prev.some((selected) => selected.id === option.id);
      return exists
        ? prev.filter((selected) => selected.id !== option.id) // 削除
        : [...prev, option]; // 追加
    });

    const added = aMinusB(addItems, oAddItems);
    const deleted = aMinusB(oAddItems, addItems);
  };

  const goBack = () => {
    router.back();
  };

  return (
    <div className="bg-white h-full">
      <TopBar
        title={APP_TEXT.HEALTH_SCORE_CHANGE_PAGE.TITLE}
        enableBack={true}
        onBack={goBack}
        enableClose={true}
        onClose={goBack}
      />
      <div className="flex flex-col items-start">
        {options.length > 0 && (
          <div className="w-full bg-white  font-bold text-left text-[18px] mb-4">
            {APP_TEXT.HEALTH_SCORE_CHANGE_PAGE.CHECKBLE_TITLE}
          </div>
        )}
        {options.length > 0 && (
          <div className="checkbox-group">
            {options.map((option, index) => (
              <div
                key={option.asset_type}
                className={`w-full  mb-[18px] p-[15px] flex items-center rounded-2xl  border-2  ${selectedItems.map((item) => item.id).includes(option.id) ? 'border-primary' : 'border-gray-300'} `}
              >
                <div key={option.id}>
                  <Checkbox
                    checked={
                      selectedItems.map((item) => item.id).includes(option.id) ||
                      option.op_permission === RISK_PERMISSION.force
                    }
                    onCheckedChange={() => handleChange(option)}
                    className="border border-border ring-none shadow-none"
                  />
                </div>
                <div className="flex-1 ml-3">
                  <label className="text-[18px]  mt-2 mb-2 font-bold" htmlFor={option.id}>
                    {getText(`${option.label}_TITLE`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
                  </label>
                  <label className="text-[14px] text-gray-500" htmlFor={option.id}>
                    <br />
                    {getText(`${option.label}_CONTENT`, APP_TEXT.HEALTH_SCORE_INIT_SEL_PAGE)}
                  </label>
                </div>
              </div>
            ))}
          </div>
        )}

        {options.length > 0 && (
          <div className="z-[2] items-center w-full font-bold text-center box-[border-box] mt-4">
            <Button
              className="bg-primary disabled:bg-primary-light text-white  font-bold h-12   rounded-[25px] w-[88vw]"
              type="button"
              onClick={goNext}
              disabled={!changeItemFlg}
            >
              {APP_TEXT.HEALTH_SCORE_CHANGE_PAGE.NEXT}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export default RiskChange;
