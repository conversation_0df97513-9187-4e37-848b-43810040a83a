import { useSafeArea } from '@/hooks/use-safe-area';
import Section from './section';

const code = `
const safeArea = useSafeArea();
<div className="text-sm text-text-secondary">
  <pre>{JSON.stringify(safeArea, null, 2)}</pre>
</div>
`;

export default function SafeAreaSection() {
  const safeArea = useSafeArea();
  return (
    <Section title="SafeArea" code={code}>
      <div className="text-sm text-text-secondary">
        <pre>{JSON.stringify(safeArea, null, 2)}</pre>
      </div>
    </Section>
  );
}
