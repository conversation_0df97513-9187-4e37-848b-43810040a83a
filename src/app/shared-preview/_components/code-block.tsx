'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Bar } from '@/components/shared/scroll-area';
import { cn } from '@/lib/utils';
import { Check, ClipboardList } from 'lucide-react';
import { Highlight, themes } from 'prism-react-renderer';
import { useState } from 'react';

interface CodeBlockProps {
  code: string;
  className?: string;
}

interface HighlightRenderProps {
  className: string;
  style: React.CSSProperties;
  tokens: Array<Array<{ types: string[]; content: string }>>;
  getLineProps: (props: { line: Array<{ types: string[]; content: string }> }) => {
    className: string;
  };
  getTokenProps: (props: { token: { types: string[]; content: string } }) => {
    className: string;
    children: string;
  };
}

export function CodeBlock({ code, className }: CodeBlockProps) {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(code);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  return (
    <div className={cn('relative bg-black', className)}>
      <button
        type="button"
        onClick={handleCopy}
        className="absolute z-10 right-2 top-2 p-2 rounded-lg bg-white hover:bg-muted transition-colors"
        aria-label={isCopied ? 'copied' : 'copy'}
      >
        {isCopied ? (
          <Check className="h-4 w-4 text-green-500" />
        ) : (
          <ClipboardList className="h-4 w-4 text-muted-foreground" />
        )}
      </button>
      <Highlight theme={themes.nightOwl} code={code} language="tsx">
        {({ className: _, style, tokens, getLineProps, getTokenProps }: HighlightRenderProps) => (
          <ScrollArea className="h-[300px]" type="auto">
            <pre className="pr-4 py-2 text-sm relative" style={style}>
              {tokens.map((line, i) => (
                <div key={i} {...getLineProps({ line })} className="table-row">
                  <span className="table-cell text-right px-2 select-none text-gray-500 text-opacity-50">
                    {i + 1}
                  </span>
                  <span className="table-cell">
                    {line.map((token, key) => (
                      <span key={key} {...getTokenProps({ token })} />
                    ))}
                  </span>
                </div>
              ))}
            </pre>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        )}
      </Highlight>
    </div>
  );
}
