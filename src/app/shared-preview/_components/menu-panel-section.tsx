import MenuPanel from '@/components/shared/menu-panel';
import Section from './section';

const code = `
<MenuPanel
    title="その他の記録"
    menuItems={[
    {
        label: '健診・検診日登録',
        href: '/',
    },
    {
        label: '健診・検診結果',
        href: '/',
    },
    {
        label: '服薬記録',
        href: '/',
    },
    ]}
/>
`;

// 导出一个默认函数，名为MenuPanelSection，接收一个参数title，类型为字符串
export default function MenuPanelSection({ title }: { title: string }) {
  return (
    <>
      <Section title={title} code={code}>
        <MenuPanel
          title="その他の記録"
          menuItems={[
            {
              label: '健診・検診日登録',
              href: '/',
            },
            {
              label: '健診・検診結果',
              href: '/',
            },
            {
              label: '服薬記録',
              href: '/',
            },
          ]}
        />
      </Section>
    </>
  );
}
