import { Button } from '@/components/shared/button';
import Section from './section';

const code = `
<Button className="w-[240px] flex-none">ボタン</Button>
<Button className="w-[240px] flex-none" variant="outline">
  ボタン(outline)
</Button>
<Button className="w-[240px] flex-none" variant="muted">
  ボタン
</Button>
<Button className="w-[240px] flex-none" variant="secondary">
  ボタン
</Button>
<Button className="w-[240px] flex-none" variant="destructive">
  ボタン
</Button>
</div>
<div className="flex gap-4 mt-8 flex-wrap bg-card p-4 rounded-lg">
<Button className="w-[240px] flex-none" size="sm">
  ボタン
</Button>
<Button className="w-[240px] flex-none" size="sm" variant="outline">
  ボタン
</Button>
<Button className="w-[240px] flex-none" size="sm" variant="muted">
  ボタン
</Button>
<Button className="w-[240px] flex-none" size="sm" variant="secondary">
  ボタン
</Button>
<Button className="w-[240px] flex-none" size="sm" variant="destructive">
  ボタン
</Button>
`;

export default function ButtonSection({ title }: { title: string }) {
  return (
    <>
      <Section title={title} code={code}>
        <div className="flex gap-4 flex-wrap bg-card p-4 rounded-lg">
          <Button className="w-[240px] flex-none">ボタン</Button>
          <Button className="w-[240px] flex-none" variant="outline">
            ボタン(outline)
          </Button>
          <Button className="w-[240px] flex-none" variant="muted">
            ボタン
          </Button>
          <Button className="w-[240px] flex-none" variant="secondary">
            ボタン
          </Button>
          <Button className="w-[240px] flex-none" variant="destructive">
            ボタン
          </Button>
        </div>
        <div className="flex gap-4 mt-8 flex-wrap bg-card p-4 rounded-lg">
          <Button className="w-[240px] flex-none" size="sm">
            ボタン
          </Button>
          <Button className="w-[240px] flex-none" size="sm" variant="outline">
            ボタン
          </Button>
          <Button className="w-[240px] flex-none" size="sm" variant="muted">
            ボタン
          </Button>
          <Button className="w-[240px] flex-none" size="sm" variant="secondary">
            ボタン
          </Button>
          <Button className="w-[240px] flex-none" size="sm" variant="destructive">
            ボタン
          </Button>
        </div>
      </Section>
    </>
  );
}
