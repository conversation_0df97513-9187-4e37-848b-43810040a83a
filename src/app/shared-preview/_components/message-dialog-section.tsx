import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import Section from './section';

const code = `
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { useMessageDialog } from '@/hooks/use-message-dialog';

const { isShow, setDialog } = useMessageDialog();

<Button
  className="mr-4"
  disabled={isShow}
  onClick={() => {
    setDialog(true, {
      title: 'タイトル',
      outSideClickClose: true,
      content: 'メッセージ',
    });
  }}
>
  {isShow ? '表示中' : 'Show Message Dialog'}
</Button>
<Button
  className="mr-4"
  disabled={isShow}
  onClick={() => {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-2 text-center">
          <p>フレンドを削除しますか？</p>
          <p>この操作は元に戻すことはできません。</p>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="destructive"
            className="w-full"
            onClick={() => {
              setDialog(false);
            }}
          >
            削除
          </Button>
          <DialogClose asChild>
            <TextButton className="mt-2 w-full" variant="muted">
              キャンセル
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  }}
>
  Custom Footer Dialog
</Button>
`;

export default function MessageDialogSection({ title }: { title: string }) {
  const { isShow, setDialog } = useMessageDialog();

  return (
    <>
      <Section title={title} code={code}>
        <Button
          className="mr-4"
          disabled={isShow}
          onClick={() => {
            setDialog(true, {
              title: 'タイトル',
              outSideClickClose: true,
              content: 'メッセージ',
            });
          }}
        >
          {isShow ? '表示中' : 'Show Message Dialog'}
        </Button>
        <Button
          className="mr-4"
          disabled={isShow}
          onClick={() => {
            setDialog(true, {
              content: (
                <div className="flex flex-col gap-2 text-center">
                  <p>フレンドを削除しますか？</p>
                  <p>この操作は元に戻すことはできません。</p>
                </div>
              ),
              outSideClickClose: false,
              footer: (
                <div className="flex-col ">
                  <Button
                    variant="destructive"
                    className="w-full"
                    onClick={() => {
                      setDialog(false);
                    }}
                  >
                    削除
                  </Button>
                  <DialogClose asChild>
                    <TextButton className="mt-2 w-full" variant="muted">
                      キャンセル
                    </TextButton>
                  </DialogClose>
                </div>
              ),
            });
          }}
        >
          Custom Footer Dialog
        </Button>
      </Section>
    </>
  );
}
