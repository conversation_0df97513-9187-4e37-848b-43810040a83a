import { Button } from '@/components/shared/button';
import { useLoading } from '@/hooks/use-loading';
import Section from './section';

const code = `
const { isLoading, setIsLoading, setLoading } = useLoading();
<Button
  className="mr-4"
  disabled={isLoading}
  onClick={() => {
    setLoading(true, { text: 'データを取得中...' });
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }}
>
  Show Text Loading
</Button>
<Button
  className="mr-4"
  disabled={isLoading}
  onClick={() => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }}
>
  Show simple Loading
</Button>
`;

export default function LoadingSection({ title }: { title: string }) {
  const { isLoading, setIsLoading, setLoading } = useLoading();

  return (
    <>
      <Section title={title} code={code}>
        <Button
          className="mr-4"
          disabled={isLoading}
          onClick={() => {
            setLoading(true, { text: 'データを取得中...' });
            setTimeout(() => {
              setIsLoading(false);
            }, 1000);
          }}
        >
          Show Text Loading
        </Button>
        <Button
          disabled={isLoading}
          onClick={() => {
            setIsLoading(true);
            setTimeout(() => {
              setIsLoading(false);
            }, 1000);
          }}
        >
          Show simple Loading
        </Button>
      </Section>
    </>
  );
}
