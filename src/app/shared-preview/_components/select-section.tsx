import { Select } from '@/components/shared/select';
import { useState } from 'react';
import Section from './section';

const SELECT_OPTIONS = [
  { value: 'all', name: 'すべて' },
  { value: 'score', name: 'ヘルスチェックAI' },
  { value: 'risk', name: 'リスクスコア' },
];

const code = `
const SELECT_OPTIONS = [
  { value: 'all', name: 'すべて' },
  { value: 'score', name: 'ヘルスチェックAI' },
  { value: 'risk', name: 'リスクスコア' },
];
const [selectedOption, setSelectedOption] = useState('all');
const handleOptionChange = (value: string) => {
  setSelectedOption(value);
};
<Select
  className="w-[240px] flex-none"
  defaultValue={selectedOption}
  options={SELECT_OPTIONS}
  title="表示するヘルスチェックAI"
  onChange={(value) => handleOptionChange(value)}
/>
`;

export default function SelectSection({ title }: { title: string }) {
  const [selectedOption, setSelectedOption] = useState('all');
  const handleOptionChange = (value: string) => {
    setSelectedOption(value);
  };
  return (
    <>
      <Section title={title} code={code}>
        <div className="text-sm text-gray-500 mb-2">Selected option: {selectedOption}</div>
        <div className="flex gap-4 flex-wrap bg-card p-4 rounded-lg">
          <Select
            className="w-[240px] flex-none"
            options={SELECT_OPTIONS}
            title="表示するヘルスチェックAI"
            placeholder="選択してください"
            onChange={(value) => handleOptionChange(value)}
          />
        </div>
      </Section>
    </>
  );
}
