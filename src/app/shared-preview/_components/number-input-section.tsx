import { NumberInput } from '@/components/shared/number-input';
import { useState } from 'react';
import Section from './section';

const code = `
  import { NumberInput } from '@/components/shared/number-input';
  const [height, setHeight] = useState('170');
  const [weight, setWeight] = useState('60');
  const [price, setPrice] = useState('1000');
  <NumberInput unit="cm" name="height" maxIntLen={3} value={height} onChange={setHeight} />
  <NumberInput unit="kg" name="weight" maxIntLen={3} value={weight} onChange={setWeight} />
  <NumberInput
    unit="円" 
    name="price"
    maxDecLen={0}
    maxIntLen={6}
    value={price}
    onChange={setPrice}
  />
`;

export default function NumberInputSection({ title }: { title: string }) {
  const [height, setHeight] = useState('170');
  const [weight, setWeight] = useState('60');
  const [price, setPrice] = useState('1000');
  return (
    <>
      <Section title={title} code={code}>
        <div className="flex flex-wrap gap-24">
          <NumberInput unit="cm" name="height" maxIntLen={3} value={height} onChange={setHeight} />
          <NumberInput unit="kg" name="weight" maxIntLen={3} value={weight} onChange={setWeight} />
          <NumberInput
            unit="円"
            name="price"
            maxDecLen={0}
            maxIntLen={6}
            value={price}
            onChange={setPrice}
          />
        </div>
      </Section>
    </>
  );
}
