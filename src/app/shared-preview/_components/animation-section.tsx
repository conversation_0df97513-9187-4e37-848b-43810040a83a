import { HeartAnimation } from '@/components/shared/animation';
import { But<PERSON> } from '@/components/shared/button';
import useCenterToast from '@/hooks/use-center-toast';
import { Heart } from 'lucide-react';
import Section from './section';

const code = `
import { HeartAnimation } from '@/components/shared/animation';

...
<HeartAnimation>
  <Button variant="icon">
    <Heart fill="red" stroke="red" className="w-10 h-10" />
  </Button>
</HeartAnimation>
`;

export default function AnimationSection({ title }: { title: string }) {
  const { toast } = useCenterToast();
  return (
    <>
      <Section title={title} code={code}>
        <HeartAnimation>
          <Button variant="icon">
            <Heart fill="red" stroke="red" className="w-10 h-10" />
          </Button>
        </HeartAnimation>
      </Section>
    </>
  );
}
