import { Button } from '@/components/shared/button';
import { cn } from '@/lib/utils';
import {
  registerM<PERSON>ageH<PERSON><PERSON>,
  remove<PERSON><PERSON>age<PERSON><PERSON><PERSON>,
  sendMessageToNative,
} from '@/utils/native-bridge';
import { useEffect } from 'react';
import { useState } from 'react';
import toast from 'react-hot-toast';
import Section from './section';

export default function NativeBridgeSection() {
  const [count, setCount] = useState(0);
  const [messageState, setMessageState] = useState(0);
  useEffect(() => {
    registerMessageHandler('receive-message-from-native', (data) => {
      console.log('receive-message-from-native', data);
      const num = count + (data?.num || 0);
      console.log('num', num);
      setCount(num);
    });
    return () => {
      removeMessageHandler('receive-message-from-native');
    };
  }, [count]);
  return (
    <Section title="NativeBridge">
      <div className="text-sm text-gray-500 mb-2">count: {count}</div>
      <Button
        onClick={() => {
          window.receiveMessageFromNative(
            JSON.stringify({
              type: 'receive-message-from-native',
              data: {
                num: 2,
              },
            }),
          );
        }}
      >
        Mock native send message
      </Button>
      <div className="text-sm text-gray-500 mt-4">
        {messageState > 0 ? 'Message sent and waiting for callback' : 'Message not sent'}
      </div>
      {messageState > 1 && (
        <div className="text-sm text-gray-500 mb-2">Message callback success</div>
      )}
      <Button
        onClick={() => {
          setMessageState(1);
          sendMessageToNative({
            type: 'test-native-bridge-callback',
            callback: (data) => {
              setMessageState(2);
            },
          });
        }}
      >
        Test native callback
      </Button>
    </Section>
  );
}
