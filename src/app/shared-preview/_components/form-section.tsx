import { Button } from '@/components/shared/button';
import { DatePicker, DateTimePicker } from '@/components/shared/datetime-picker';
import { FormError } from '@/components/shared/form-error';
import { NumberInput } from '@/components/shared/number-input';
import { Form, FormControl, FormField } from '@/components/ui/form';
import { formatDate } from '@/utils/date-format';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import Section from './section';

// Zod 表单验证 schema
const formSchema = z
  .object({
    recordDate: z.string().min(1, { message: '日付を入力してください。' }),

    weight: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const numVal = Number(val) || 0;
          return numVal >= 20 && numVal <= 300;
        },
        { message: '体重は20kgから300kgの範囲で設定してください。' },
      )
      .optional(),

    fatRate: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const numVal = Number(val) || 0;
          return numVal >= 5 && numVal <= 60;
        },
        { message: '体脂肪率は5%から60%の範囲で設定してください。' },
      )
      .optional(),

    sleepTime: z.string().refine(
      (val) => {
        if (!val) return true;
        return new Date(val) <= new Date();
      },
      { message: '未来の日付は設定できません。' },
    ),
  })
  .superRefine((data, ctx) => {
    const getDateOnly = (date: Date): Date => {
      return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    };

    const recordDate = new Date(data.recordDate);
    const sleepTime = new Date(data.sleepTime);

    const recordDateOnly = getDateOnly(recordDate);
    const sleepTimeOnly = getDateOnly(sleepTime);
    const previousDay = new Date(recordDateOnly);
    previousDay.setDate(previousDay.getDate() - 1);

    // 就寝時間は前日と当日のみ
    if (
      sleepTimeOnly.getTime() !== previousDay.getTime() &&
      sleepTimeOnly.getTime() !== recordDateOnly.getTime()
    ) {
      ctx.addIssue({
        path: ['sleepTime'],
        message: '就寝時間は前日と当日のみ設定できます。',
        code: z.ZodIssueCode.custom,
      });
    }
  });

type formSchema = z.infer<typeof formSchema>;

const code = `
const formSchema = z
  .object({
    recordDate: z.string().min(1, { message: '日付を入力してください。' }),

    weight: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const numVal = Number(val) || 0;
          return numVal >= 20 && numVal <= 300;
        },
        { message: '体重は20kgから300kgの範囲で設定してください。' },
      )
      .optional(),

    fatRate: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const numVal = Number(val) || 0;
          return numVal >= 5 && numVal <= 60;
        },
        { message: '体脂肪率は5%から60%の範囲で設定してください。' },
      )
      .optional(),

    sleepTime: z.string().refine(
      (val) => {
        if (!val) return true;
        return new Date(val) <= new Date();
      },
      { message: '未来の日付は設定できません。' },
    ),
  })
  .superRefine((data, ctx) => {
    const getDateOnly = (date: Date): Date => {
      return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    };

    const recordDate = new Date(data.recordDate);
    const sleepTime = new Date(data.sleepTime);

    const recordDateOnly = getDateOnly(recordDate);
    const sleepTimeOnly = getDateOnly(sleepTime);
    const previousDay = new Date(recordDateOnly);
    previousDay.setDate(previousDay.getDate() - 1);

    // 就寝時間は前日と当日のみ
    if (
      sleepTimeOnly.getTime() !== previousDay.getTime() &&
      sleepTimeOnly.getTime() !== recordDateOnly.getTime()
    ) {
      ctx.addIssue({
        path: ['sleepTime'],
        message: '就寝時間は前日と当日のみ設定できます。',
        code: z.ZodIssueCode.custom,
      });
    }
  });


return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="bg-card px-6 pb-4 pt-3">
          <div className="flex mt-4 w-[375px]">
            {/* 体重 */}
            <FormField
              name="weight"
              render={({ field }) => (
                <div className="flex-1">
                  <FormControl>
                    <NumberInput
                      className="w-full"
                      unit="kg"
                      maxDecLen={1}
                      maxIntLen={3}
                      {...field}
                    />
                  </FormControl>
                  <FormError className="mt-2" />
                </div>
              )}
            />

            {/* 体脂肪率 */}
            <FormField
              name="fatRate"
              render={({ field }) => (
                <div className="flex-1 ml-4">
                  <FormControl>
                    <NumberInput
                      className="w-full"
                      unit="%"
                      maxDecLen={1}
                      maxIntLen={2}
                      {...field}
                    />
                  </FormControl>
                  <FormError className="mt-2" />
                </div>
              )}
            />
          </div>

          {/* 记录日期（DatePicker） */}
          <div className="mt-4 w-[355px]">
            <FormField
              name="recordDate"
              render={({ field }) => (
                <div>
                  <FormControl>
                    <DatePicker
                      className="flex-1 w-full"
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormError className="mt-2" />
                </div>
              )}
            />
          </div>

          {/* 睡觉时间（DateTimePicker） */}
          <FormField
            name="sleepTime"
            render={({ field }) => (
              <div>
                <FormControl>
                  <DateTimePicker
                    className="w-full"
                    value={field.value}
                    onChange={field.onChange}
                  />
                </FormControl>
                <FormError className="mt-2" />
              </div>
            )}
          />
          <Button type="submit" className="w-full">
            保存
          </Button>
        </form>
      </Form>
  );
`;

// 主组件
export default function formerSection({ title }: { title: string }) {
  const [weight, setWeight] = useState('60');
  const [fatRate, setFatRate] = useState('1000');

  // 默认值
  const defaultValues: Partial<formSchema> = {
    recordDate: formatDate(new Date(), 'yyyy-MM-dd'),
    weight: weight,
    fatRate: fatRate,
    sleepTime: undefined,
  };

  // 表单 hook
  const form = useForm<formSchema>({
    resolver: zodResolver(formSchema),
    defaultValues,
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
  });

  // 表单提交处理
  const onSubmit = (data: formSchema) => {
    console.log('提交的数据:', data);
    alert(`提交的数据:\n${JSON.stringify(data, null, 2)}`);
  };

  return (
    <Section title={title} code={code}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="bg-card px-6 pb-4 pt-3">
          <div className="flex mt-4 w-[375px]">
            {/* 体重 */}
            <FormField
              name="weight"
              render={({ field }) => (
                <div className="flex-1">
                  <FormControl>
                    <NumberInput
                      className="w-full"
                      unit="kg"
                      maxDecLen={1}
                      maxIntLen={3}
                      {...field}
                    />
                  </FormControl>
                  <FormError className="mt-2" />
                </div>
              )}
            />

            {/* 体脂肪率 */}
            <FormField
              name="fatRate"
              render={({ field }) => (
                <div className="flex-1 ml-4">
                  <FormControl>
                    <NumberInput
                      className="w-full"
                      unit="%"
                      maxDecLen={1}
                      maxIntLen={2}
                      {...field}
                    />
                  </FormControl>
                  <FormError className="mt-2" />
                </div>
              )}
            />
          </div>

          {/* 记录日期（DatePicker） */}
          <div className="mt-4 w-[355px]">
            <FormField
              name="recordDate"
              render={({ field }) => (
                <div>
                  <FormControl>
                    <DatePicker
                      className="flex-1 w-full"
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormError className="mt-2" />
                </div>
              )}
            />
          </div>

          {/* 睡觉时间（DateTimePicker） */}
          <div className="mt-4 w-[375px]">
            <FormField
              name="sleepTime"
              render={({ field }) => (
                <div>
                  <FormControl>
                    <DateTimePicker
                      className="w-full"
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormError className="mt-2" />
                </div>
              )}
            />
          </div>

          <div className="mt-4 w-[355px]">
            <Button type="submit" className="w-full">
              保存
            </Button>
          </div>
        </form>
      </Form>
    </Section>
  );
}
