import { Button } from '@/components/shared/button';
import useCenterToast from '@/hooks/use-center-toast';
import Section from './section';

const code = `
import useCenterToast from '@/hooks/use-center-toast';
const {toast } = useCenterToast();

...
<Button
  className="mr-4"
  onClick={() => {
    toast({
      message: (
        <>
          <span>クーポンを</span>
          <br />
          <span>使用しました</span>
        </>
      ),
      duration: 3000,
    });
  }}
>
  Show Text toast
</Button>
`;

export default function ToastSection({ title }: { title: string }) {
  const { toast } = useCenterToast();
  return (
    <>
      <Section title={title} code={code}>
        <Button
          className="mr-4"
          onClick={() => {
            toast({
              message: (
                <>
                  <span>クーポンを</span>
                  <br />
                  <span>使用しました</span>
                </>
              ),
              duration: 3000,
            });
          }}
        >
          Show Text toast
        </Button>
      </Section>
    </>
  );
}
