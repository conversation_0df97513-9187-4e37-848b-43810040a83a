'use client';

import { CodeBlock } from '@/app/shared-preview/_components/code-block';
import { cn } from '@/lib/utils';
import { useState } from 'react';

type Tab = 'preview' | 'code';

export default function Section({
  title,
  code,
  children,
  open = true,
}: {
  title: string;
  open?: boolean;
  code?: string;
  children: React.ReactNode;
}) {
  const [isExpanded, setIsExpanded] = useState(open);
  const [activeTab, setActiveTab] = useState<Tab>('preview');

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="pb-4">
      <div className="flex items-center justify-between mt-8 pb-2 border-b-2 border-muted">
        <h2 className="text-2xl font-bold">{title}</h2>
        <div className="flex items-center gap-4">
          {code && (
            <div className="flex rounded-lg border border-border overflow-hidden">
              <button
                type="button"
                onClick={() => setActiveTab('preview')}
                className={cn(
                  'px-3 py-1 text-sm transition-colors',
                  activeTab === 'preview' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted',
                )}
              >
                Preview
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('code')}
                className={cn(
                  'px-3 py-1 text-sm transition-colors',
                  activeTab === 'code' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted',
                )}
              >
                Code
              </button>
            </div>
          )}
          <button
            type="button"
            className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            onClick={handleToggle}
            aria-expanded={isExpanded}
            aria-controls={`section-${title}`}
          >
            {isExpanded ? 'Hide' : 'Show'}
          </button>
        </div>
      </div>
      <div
        className={cn(
          'transition-all duration-200 overflow-hidden',
          isExpanded ? 'opacity-100 mt-4' : 'max-h-0 opacity-0',
        )}
      >
        {activeTab === 'preview' && children}
        {activeTab === 'code' && code && <CodeBlock code={code} />}
      </div>
    </div>
  );
}
