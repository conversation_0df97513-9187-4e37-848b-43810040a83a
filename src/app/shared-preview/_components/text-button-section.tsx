import { TextButton } from '@/components/shared/text-button';
import { Plus } from 'lucide-react';
import Section from './section';

const code = `
<TextButton className="w-[240px] flex-none">
    <Plus className="w-[20px] h-[20px]" />
    リンクボタン
    <Plus className="w-[20px] h-[20px]" />
</TextButton>
<TextButton className="w-[240px] flex-none" variant="muted">
    リンクボタン
</TextButton>
<TextButton className="w-[240px] flex-none" variant="destructive">
    リンクボタン
</TextButton>
</div>
<div className="flex gap-4 flex-wrap mt-4 bg-card p-4 rounded-lg">
<TextButton className="w-[240px] flex-none" size="sm">
    <Plus className="w-[19px] h-[19px]" />
    リンクボタン
    <Plus className="w-[19px] h-[19px]" />
</TextButton>
<TextButton className="w-[240px] flex-none" variant="muted" size="sm">
    リンクボタン
</TextButton>
<TextButton className="w-[240px] flex-none" variant="destructive" size="sm">
    リンクボタン
</TextButton>
`;

export default function TextButtonSection({ title }: { title: string }) {
  return (
    <Section title={title} code={code}>
      <div className="flex gap-4 flex-wrap bg-card p-4 rounded-lg">
        <TextButton className="w-[240px] flex-none">
          <Plus className="w-[20px] h-[20px]" />
          リンクボタン
          <Plus className="w-[20px] h-[20px]" />
        </TextButton>
        <TextButton className="w-[240px] flex-none" variant="muted">
          リンクボタン
        </TextButton>
        <TextButton className="w-[240px] flex-none" variant="destructive">
          リンクボタン
        </TextButton>
      </div>
      <div className="flex gap-4 flex-wrap mt-4 bg-card p-4 rounded-lg">
        <TextButton className="w-[240px] flex-none" size="sm">
          <Plus className="w-[19px] h-[19px]" />
          リンクボタン
          <Plus className="w-[19px] h-[19px]" />
        </TextButton>
        <TextButton className="w-[240px] flex-none" variant="muted" size="sm">
          リンクボタン
        </TextButton>
        <TextButton className="w-[240px] flex-none" variant="destructive" size="sm">
          リンクボタン
        </TextButton>
      </div>
    </Section>
  );
}
