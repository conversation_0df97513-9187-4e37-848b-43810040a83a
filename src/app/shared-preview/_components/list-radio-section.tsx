import ListRadio from '@/components/shared/list-radio-new';
import { useState } from 'react';
import Section from './section';

const PLAN_LIST = [
  {
    value: 'option-one',
    title: 'おすすめ歩数プラン',
    tip: '性別・年齢にあわせた、健康で長生きのための歩数を自動で目標に設定します。',
  },
  {
    value: 'option-two',
    title: '＋10プラン',
    help: '＋10プラン',
    tip: '普段のあなたの歩数に＋１０のウォーキングを自動計算して目標に設定します。',
  },
];

const code = `
const PLAN_LIST = [
  {
    value: 'option-one',
    title: 'おすすめ歩数プラン',
    tip: '性別・年齢にあわせた、健康で長生きのための歩数を自動で目標に設定します。',
  },
  {
    value: 'option-two',
    title: '＋10プラン',
    help: '＋10プラン',
    tip: '普段のあなたの歩数に＋１０のウォーキングを自動計算して目標に設定します。',
  },
];
const [selectedOption, setSelectedOption] = useState('option-one');
const handleOptionChange = (value: string) => {
  setSelectedOption(value);
};
return (
  <>
    <Section title={title} code={code}>
      <div className="text-sm text-gray-500 mb-2">Selected option: {selectedOption}</div>
      <div className="flex gap-4 flex-wrap">
        <ListRadio
          value={selectedOption}
          onChange={(value) => handleOptionChange(value)}
          options={PLAN_LIST}
          className="gap-4"
        />
      </div>
    </Section>
  </>
);
`;

export default function ListRadioSection({ title }: { title: string }) {
  const [selectedOption, setSelectedOption] = useState('option-one');
  const handleOptionChange = (value: string) => {
    setSelectedOption(value);
  };
  return (
    <>
      <Section title={title} code={code}>
        <div className="text-sm text-gray-500 mb-2">Selected option: {selectedOption}</div>
        <div className="flex gap-4 flex-wrap">
          <ListRadio
            value={selectedOption}
            onChange={(value) => handleOptionChange(value)}
            options={PLAN_LIST}
          />
        </div>
      </Section>
    </>
  );
}
