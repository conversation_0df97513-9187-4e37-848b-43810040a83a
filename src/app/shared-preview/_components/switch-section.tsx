import { Switch } from '@/components/shared/switch';
import { useState } from 'react';
import Section from './section';

const code = `
const [checked, setChecked] = useState(false);
<Switch checked={checked} onCheckedChange={setChecked} />
`;

export default function SwitchSection({ title }: { title: string }) {
  const [checked, setChecked] = useState(false);

  return (
    <>
      <Section title={title} code={code}>
        <div className="text-sm text-gray-500 mb-2">Checked: {checked.toString()}</div>
        <Switch checked={checked} onCheckedChange={setChecked} />
      </Section>
    </>
  );
}
