'use client';

import TopBar from '@/components/layout/top-bar';
import VersionTag from '@/components/layout/version-tag';
import AnimationSection from './_components/animation-section';
import ButtonSection from './_components/button-section';
import ColorsSection from './_components/colors-section';
import DateTimePickerSection from './_components/datetime-picker-section';
import DevToolSection from './_components/dev-tool';
import FormSection from './_components/form-section';
import ListRadioSection from './_components/list-radio-section';
import LoadingSection from './_components/loading-section';
import MenuPanelSection from './_components/menu-panel-section';
import MessageDialogSection from './_components/message-dialog-section';
import NativeBridgeSection from './_components/native-bridge-section';
import NumberInputSection from './_components/number-input-section';
import SafeAreaSection from './_components/safe-area-section';
import SectionTitleSection from './_components/section-title-section';
import SelectSection from './_components/select-section';
import SkeletonSection from './_components/skeleton-section';
import SwitchSection from './_components/switch-section';
import TextButtonSection from './_components/text-button-section';
import ToastSection from './_components/toast-section';

export default function SharedPreviewPage() {
  return (
    <>
      <TopBar title="共通コンポーネント" enableBack={true} />
      <div className="p-6">
        <VersionTag />
        <ColorsSection />
        <SwitchSection title="Switch" />
        <ButtonSection title="Button" />
        <TextButtonSection title="TextButton" />
        <SelectSection title="Select" />
        <DateTimePickerSection title="DatetimePicker" />
        <NumberInputSection title="NumberInput" />
        <ListRadioSection title="ListRadio" />
        <SectionTitleSection title="SectionTitle" />
        <MenuPanelSection title="MenuPanel" />
        <FormSection title="Form" />
        <LoadingSection title="Loading" />
        <MessageDialogSection title="MessageDialog" />
        <NativeBridgeSection />
        <SafeAreaSection />
        <DevToolSection />
        <ToastSection title="Toast" />
        <AnimationSection title="Animation" />
        <SkeletonSection />
      </div>
    </>
  );
}
