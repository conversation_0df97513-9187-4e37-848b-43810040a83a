'use client';

import { But<PERSON> } from '@/components/shared/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { useGlobalInit } from '@/hooks/use-global-init';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { Sex, useAuthStore } from '@/store/auth';
import { useGlobalStore } from '@/store/global';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { z } from 'zod';
import { NativeMessage } from '../../../types/native-bridge';

const formSchema = z.object({
  id: z.string().min(1, { message: 'ログインIDを入力してください' }),
  password: z.string().min(0, { message: 'パスワードを入力してください' }),
});

export default function LoginPage() {
  const { setUser, setToken } = useAuthStore();
  const { isLoading, setIsLoading } = useLoading();
  const { clearGlobalStore } = useGlobalStore();
  const { init } = useGlobalInit();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: '',
      password: '',
    },
  }); //

  useEffect(() => {
    clearGlobalStore();
    getGroupCode();
    getUUID();
  }, [clearGlobalStore]);

  function getGroupCode() {
    sendMessageToNative({
      type: 'group-code',
      callback: (data) => {
        // biome-ignore lint/complexity/useOptionalChain: <explanation>
        if (data && data.code) {
          nlog(`group-code 0:${data.code}`);
        }
      },
    });
  }

  function getUUID() {
    sendMessageToNative({
      type: 'uuid',
      callback: (data) => {
        // biome-ignore lint/complexity/useOptionalChain: <explanation>
        if (data && data.code) {
          nlog(`uuid 0:${data.code}`);
        }
      },
    });
  }

  function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    //TODO: ログイン処理
    getGroupCode();

    const id = values.id ? Number(values.id) : 0;
    const isPlural = id % 2 === 0;
    if (id > 0) {
      const user = {
        id: values.id,
        name: 'userid',
        email: '<EMAIL>',
        useOrganizerID: isPlural ? '102' : '101',
        organizerID: isPlural ? '101,102' : '101',
        birthday: '1980-01-01',
        sex: Sex.MALE,
        lat: 35.681236, // 東京駅
        lng: 139.767125, // 東京駅
      };
      setUser(user);

      setToken('token123');
      toast.success('ログインに成功しました！');
      init();
    } else {
      toast.error('ログインに失敗しました。もう一度お試しください');
    }
  }

  return (
    <div className="flex flex-col items-center justify-center px-4 py-12">
      <div className="w-full max-w-md">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#0066FF] to-[#0052CC]">
            ログイン
          </h1>
          <p className="text-muted-foreground mt-2">アカウントにログインして続行</p>
        </div>

        <div className="bg-background rounded-xl shadow-md border border-border overflow-hidden">
          <div className="p-6 sm:p-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                <FormField
                  control={form.control}
                  name="id"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="text-base font-medium text-foreground">
                        ログインID
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="ログインIDを入力してください"
                          className="h-12 rounded-md focus:ring-2 focus:ring-ring/20 focus:border-ring transition-all"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-destructive" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <div className="flex justify-between items-center">
                        <FormLabel className="text-base font-medium text-foreground">
                          パスワード
                        </FormLabel>
                      </div>
                      <FormControl>
                        <PasswordInput
                          placeholder="パスワードを入力してください"
                          className="h-12 rounded-md focus:ring-2 focus:ring-ring/20 focus:border-ring transition-all"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-destructive" />
                    </FormItem>
                  )}
                />

                <div className="pt-2 flex">
                  <Button className="flex-1" type="submit" disabled={isLoading}>
                    {isLoading ? '処理中...' : 'ログイン'}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>

        <div className="mt-8 p-5 border rounded-lg bg-card shadow-sm border-border">
          <ul className="space-y-3 text-sm text-muted-foreground pl-2">
            <li className="flex items-start">
              <span className="text-muted-foreground/70 mr-2">※</span>
              <span>
                ログインID、パスワードを忘れた場合は主催団体管理者またはシステム管理者にお問い合わせください。
              </span>
            </li>
            <li className="flex items-start">
              <span className="text-muted-foreground/70 mr-2">※</span>
              <span>
                パスワードを連続で5回間違えるとロックされ、ログイン操作ができなくなります。
              </span>
            </li>
            <li className="flex items-start">
              <span className="text-muted-foreground/70 mr-2">※</span>
              <span>
                ロックあるいは有効期限切れの場合は、主催団体管理者またはシステム管理者宛て仮パスワードの再設定を行い操作を行ってください。
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
