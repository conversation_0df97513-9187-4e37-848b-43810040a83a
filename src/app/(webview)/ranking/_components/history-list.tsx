'use client';

import type { RankingList } from '@/types/ranking';
import { formatNumberWithCommas } from '@/utils/string-format';
import { useEffect, useRef, useState } from 'react';
import { CATEGORY_TYPE, MYSELF_FLG } from '../_const';
interface HistoryListPageType {
  list: RankingList;
  activeTab: number;
}

export default function HistoryListPage(props: HistoryListPageType) {
  const { list, activeTab } = props;

  const setRankingBg = (order: number | undefined) => {
    switch (order) {
      case 1:
        return '/images/ranking/first.svg';
      case 2:
        return '/images/ranking/second.svg';
      case 3:
        return '/images/ranking/three.svg';
      default:
        return '-';
    }
  };

  return (
    <div
      className={`flex items-center gap-1 p-3 border-b border-border border-solid ${list?.selfFlg === MYSELF_FLG.SELF ? 'bg-[#E5E5E5]' : ''}`}
    >
      <div
        className="w-[72px] flex items-center justify-center text-sm font-normal  bg-no-repeat bg-center"
        style={{ backgroundImage: `url(${setRankingBg(list?.order)})` }}
      >
        {list?.order}
      </div>
      {list?.iconUrl && (
        <div className="relative w-[72px] h-[54px]">
          <div className="absolute top-0 left-1 !z-0 h-10 w-10 bg-primary-ranking rounded-full flex justify-center items-center">
            <img src={list?.iconUrl} alt="" width={30} height={30} className="rounded-full" />
            {/* TODO: wait iconUrl */}
            {/* <img src={'/images/ranking/animal.svg'} alt="" width={40} height={40} className="rounded-full" /> */}
          </div>
          <div
            className="bg-white text-xs font-bold rounded-sm h-[18px] absolute bottom-0 left-0 w-[48px] border-primary-soft border-2 border-solid text-primary flex items-center justify-center"
            style={{ transform: 'translateY(-2px)' }}
          >
            {list?.level}
          </div>
        </div>
      )}
      <div className="w-full">
        <div className="text-left text-sm font-normal">
          {list?.nickName}
          {list?.selfFlg === MYSELF_FLG.SELF && (
            <span className="bg-white text-primary text-xs font-normal inline-block w-8 h-5 text-center leading-5 rounded">
              自分
            </span>
          )}
        </div>
        <div className="text-right text-xl font-bold">
          {formatNumberWithCommas(list?.steps)}{' '}
          <span className="text-[11px] font-normal">
            {activeTab === CATEGORY_TYPE.MISSION ? '個' : '歩'}
          </span>
        </div>
      </div>
    </div>
  );
}
