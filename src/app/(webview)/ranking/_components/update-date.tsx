import { TextButton } from '@/components/shared/text-button';
import { useRankingStore } from '@/store/ranking';
import {
  addDays,
  addWeeks,
  endOfWeek,
  format,
  isEqual,
  isSameDay,
  isSameWeek,
  startOfWeek,
} from 'date-fns';
import { ja } from 'date-fns/locale';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { DAY_WEEK_TAB } from '../_const';
export interface DateType {
  startDate?: Date;
  endDate?: Date;
}

interface SelectDateDrawerProps {
  dateType?: string;
  updatedAt?: string | undefined;
  onSelect: (value: DateType) => void;
}

export function UpdateDatePage(props: SelectDateDrawerProps) {
  const { dateType, updatedAt, onSelect } = props;
  // const {
  //   dateType,
  //   setDateType,
  //   selectDate,
  //   setSelectDate,
  // } = useRankingStore();
  const today = new Date();
  // const defaultValue = selectDate.startDate;
  const monday = startOfWeek(today, { weekStartsOn: 1 });
  const sunday = endOfWeek(today, { weekStartsOn: 1 });
  const [startDate, setStartDate] = useState(dateType === DAY_WEEK_TAB.DAY ? today : monday);
  const [endDate, setEndDate] = useState(sunday);

  // Display day of the week unit
  const getWeekday = (v: Date) => {
    const weekday = format(v, 'eeee', { locale: ja })[0];
    return weekday;
  };
  // Connect date and week
  const getDateFormat = (v: Date, formatType: string) => {
    return v ? `${format(v, formatType)} (${getWeekday(v)})` : '-';
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (dateType === DAY_WEEK_TAB.DAY) {
      setStartDate(today);
    } else {
      setStartDate(monday);
      setEndDate(sunday);
    }
  }, [dateType]);

  const chagePrevDate = () => {
    let day = startDate;
    if (dateType === DAY_WEEK_TAB.DAY) {
      day = addDays(day, -1);
      setStartDate(day);
      onSelect({
        startDate: day,
      });
    } else {
      day = addWeeks(day, -1);
      const startDate = startOfWeek(day, { weekStartsOn: 1 });
      const endDate = endOfWeek(day, { weekStartsOn: 1 });
      setStartDate(startDate);
      setEndDate(endDate);
      onSelect({
        startDate,
        endDate,
      });
    }
  };

  const chageNextDate = () => {
    let day = startDate;
    if (dateType === DAY_WEEK_TAB.DAY) {
      day = addDays(day, 1);
      setStartDate(day);
      if (isSameDay(day, today)) {
        onSelect({});
      } else {
        onSelect({
          startDate: day,
        });
      }
    } else {
      day = addWeeks(day, 1);
      const startToday = startOfWeek(today, { weekStartsOn: 1 });
      const startDate = startOfWeek(day, { weekStartsOn: 1 });
      const endDate = endOfWeek(day, { weekStartsOn: 1 });
      setStartDate(startDate);
      setEndDate(endDate);

      if (isSameDay(startToday, startDate)) {
        onSelect({});
      } else {
        onSelect({
          startDate,
          endDate,
        });
      }
    }
  };

  return (
    <div className="flex justify-between items-center">
      <TextButton
        size="sm"
        variant="muted"
        onClick={chagePrevDate}
        className="text-black "
        disabled={
          dateType === DAY_WEEK_TAB.DAY
            ? startDate < addDays(today, -7)
            : startDate <= addWeeks(today, -4)
        }
      >
        <ChevronLeft className="!transform-none" />
      </TextButton>
      <div className="text-center">
        <div className="text-base font-normal">
          {dateType === DAY_WEEK_TAB.DAY
            ? getDateFormat(startDate, 'yyyy年MM月dd日')
            : `${getDateFormat(startDate, 'yyyy年MM月dd日')} - ${getDateFormat(endDate, 'MM月dd日')}`}
        </div>
        <div className="text-xs font-normal text-[#666666]">
          {updatedAt ? format(updatedAt, 'yyyy年MM月dd日 hh:mm') : '-'}更新
        </div>
      </div>
      <TextButton
        size="sm"
        variant="muted"
        onClick={chageNextDate}
        disabled={
          dateType === DAY_WEEK_TAB.DAY
            ? format(startDate, 'yyyy年MM月dd日') >= format(today, 'yyyy年MM月dd日')
            : format(endDate, 'yyyy年MM月dd日') >= format(today, 'yyyy年MM月dd日')
        }
        className=" text-black"
      >
        <ChevronRight className="!transform-none" />
      </TextButton>
    </div>
  );
}
