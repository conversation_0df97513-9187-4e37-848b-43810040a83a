import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
interface TabMenuProps {
  activeTab: number;
  onTabChange: (tab: number) => void;
}

export default function HealthTabMenu({ activeTab, onTabChange }: TabMenuProps) {
  const menus: { label: string; value: number }[] = [
    { label: '歩数', value: 1 },
    { label: 'ミッション', value: 2 },
    { label: '団体', value: 3 },
  ];

  return (
    <div className="flex flex-col w-full shadow-sm bg-card">
      {/* Scrollable tab bar using shadcn Tabs */}
      <div className="relative">
        {/* Tabs component with custom styling for horizontal scroll */}
        <Tabs
          value={String(activeTab)}
          onValueChange={(value) => onTabChange(Number(value))}
          className="w-full "
        >
          <div
            className="overflow-x-auto scrollbar-hide "
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            <TabsList className="h-[49px] p-0 bg-transparent flex justify-between rounded-none">
              {menus.map((menu) => (
                <TabsTrigger
                  key={menu.value}
                  value={String(menu.value)}
                  className={cn(
                    'h-12 flex-shrink-0 px-4 py-3 rounded-none data-[state=active]:shadow-none text-base font-bold border-t border-l border-border border-solid last:border-r-0',
                    'data-[state=active]:text-primary-foreground data-[state=active]:bg-primary data-[state=active]:border-b-2 data-[state=active]:border-primary',
                    'data-[state=inactive]:text-foreground data-[state=inactive]:bg-transparent',
                  )}
                >
                  {menu.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
