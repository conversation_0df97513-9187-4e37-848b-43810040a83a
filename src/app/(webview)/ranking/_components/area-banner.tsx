'use client';
import { useRouter } from '@/hooks/use-next-navigation';
import { usePointStore } from '@/store/point';
import type { OrganizerInfoBean } from '@/types/home-data';
import type { UserRankingInfoList } from '@/types/ranking';
import useEmblaCarousel from 'embla-carousel-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { IS_TRUE } from '../../point/_const';
import { formatNumberWithCommas } from '../../point/_utils';
import CarouselContentPage from './carousel-content';

interface CarouselProps {
  slides: UserRankingInfoList[];
  initialIndex?: number;
  onSlideChange?: (index: number) => void;
}

interface DebouncedFunction<T extends (...args: any[]) => void> {
  (...args: Parameters<T>): void;
  cancel: () => void;
}

const debounce = <T extends (...args: any[]) => void>(
  fn: T,
  delay: number,
): DebouncedFunction<T> => {
  let timeoutId: NodeJS.Timeout;

  const debouncedFn = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };

  debouncedFn.cancel = () => {
    clearTimeout(timeoutId);
  };

  return debouncedFn;
};

export default function SwipingAreaBanner({
  slides,
  initialIndex = 0,
  onSlideChange,
}: CarouselProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'center',
    startIndex: initialIndex,
    skipSnaps: false,
  });
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const initializedRef = useRef(false);
  const router = useRouter();
  const { setHomeCardInfo } = usePointStore();
  // スライド変化対応(手ブレ補正付き)
  const handleSlideChange = useCallback(
    debounce((newIndex: number) => {
      setCurrentIndex(newIndex);
      onSlideChange?.(newIndex);
    }, 500),
    [onSlideChange],
  );

  // エンブラとイベントの監視を初期化しる
  useEffect(() => {
    if (!emblaApi) return;

    const updateIndex = () => {
      const newIndex = emblaApi.selectedScrollSnap();
      setCurrentIndex(newIndex);
      initializedRef.current = true;
    };

    updateIndex();

    const handleEmblaSelect = () => {
      handleSlideChange(emblaApi.selectedScrollSnap());
    };

    emblaApi.on('select', handleEmblaSelect);

    return () => {
      emblaApi.off('select', handleEmblaSelect);
      (handleSlideChange as DebouncedFunction<typeof handleSlideChange>).cancel();
    };
  }, [emblaApi, handleSlideChange]);

  // 外部のinitialIndexの変化に対処
  useEffect(() => {
    if (!emblaApi || !initializedRef.current) return;
    if (emblaApi.selectedScrollSnap() !== initialIndex) {
      emblaApi.scrollTo(initialIndex);
    }
  }, [initialIndex, emblaApi]);

  const scrollTo = (index: number) => {
    emblaApi?.scrollTo(index);
  };

  return (
    <div className="max-w-3xl mx-auto ">
      <div className="overflow-visible mb-0">
        <div className="overflow-hidden " ref={emblaRef}>
          <div className="flex gap-2">
            <div className="flex-[0_0_5%] min-w-0" aria-hidden="true" />
            {slides.map((slide, index) => (
              <div key={index} className="flex-[0_0_79%] min-w-0">
                <div
                  className="bg-white rounded-2xl shadow-md p-3 my-3 h-[112px] flex items-center"
                  style={{ boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.15)' }}
                >
                  <CarouselContentPage {...slide} />
                </div>
              </div>
            ))}
            <div className="flex-[0_0_5%] min-w-0" aria-hidden="true" />
          </div>
        </div>
      </div>
      {/*インジケータ */}
      {slides.length > 1 ? (
        <div className="flex justify-center space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              type="button"
              onClick={() => scrollTo(index)}
              className={`w-1.5 h-1.5 rounded-full transition-all ${
                index === currentIndex
                  ? 'bg-primary-light opacity-100'
                  : 'bg-primary-light opacity-30'
              }`}
              aria-current={index === currentIndex}
            />
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}
