'use client';
import { Loading } from '@/components/layout/loading';
import { TextButton } from '@/components/shared/text-button';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useRankingStore } from '@/store/ranking';
import type { Pagination, RankingList } from '@/types/ranking';
import type React from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { MYSELF_FLG } from '../_const';

export interface BaseListItem {
  id: string | number; // unique identifier
  selfFlg?: 0 | 1; // 自分を表示
}

export interface InfiniteScrollListProps<T extends BaseListItem> {
  items: RankingList[];
  pagination: Pagination;
  loadingStatus: {
    isLoadingPrev: boolean;
    isLoadingNext: boolean;
  };
  hasMore: {
    hasPrevPage: boolean;
    hasNextPage: boolean;
  };
  isInitialLoading: boolean;
  onLoadPrev: () => void;
  onLoadNext: () => void;
  renderItem: (item: RankingList, index: number) => React.ReactNode;
  emptyText?: string;
  isSelfScroll?: MYSELF_FLG;
}

const HistoryScrollList = <T extends BaseListItem>({
  items,
  pagination,
  loadingStatus,
  hasMore,
  isInitialLoading,
  onLoadPrev,
  onLoadNext,
  renderItem,
  emptyText = 'データがありません。',
  isSelfScroll,
}: InfiniteScrollListProps<T>): React.ReactElement => {
  const { limit, page, total } = pagination;
  const { isLoadingPrev, isLoadingNext } = loadingStatus;
  const { hasPrevPage, hasNextPage } = hasMore;
  // Ref management
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const topLoaderRef = useRef<HTMLDivElement | null>(null);
  const bottomLoaderRef = useRef<HTMLDivElement | null>(null);
  const topObserverRef = useRef<IntersectionObserver | null>(null);
  const bottomObserverRef = useRef<IntersectionObserver | null>(null);
  const itemHeightRef = useRef(0);

  const {
    // activeTab,
    // setActiveTab,
    // groupAllTab,
    // setGroupAllTab,
    // dateType,
    // setDateType,
    // selectDate,
    // setSelectDate,
    myselfFlg,
    setMyselfFlg,
  } = useRankingStore();

  // Initialize observers
  useEffect(() => {
    // Top observer (load previous page when scrolling up)
    topObserverRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        // Trigger when top loader is visible, has previous page, not loading, and load more is allowed
        // TODO: && total > items?.length
        if (entry.isIntersecting && hasPrevPage && !isLoadingPrev) {
          onLoadPrev();
        }
      },
      { threshold: 0.5 },
    );

    // Bottom observer (load next page when scrolling down)
    bottomObserverRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasNextPage && !isLoadingNext) {
          onLoadNext();
        }
      },
      { threshold: 0.1 },
    );

    // Bind observers to their respective loaders
    if (topLoaderRef.current) {
      topObserverRef.current.observe(topLoaderRef.current);
    }
    if (bottomLoaderRef.current) {
      bottomObserverRef.current.observe(bottomLoaderRef.current);
    }

    // Cleanup function
    return () => {
      topObserverRef.current?.disconnect();
      bottomObserverRef.current?.disconnect();
    };
  }, [hasPrevPage, isLoadingPrev, hasNextPage, isLoadingNext, onLoadPrev, onLoadNext]);

  // Cache list item height (used for scroll compensation)
  useEffect(() => {
    if (items?.length > 0 && !itemHeightRef.current) {
      const firstItem = itemRefs.current[0];
      if (firstItem) {
        itemHeightRef.current = firstItem.offsetHeight;
      }
    }
  }, [items.length]);

  // Scroll compensation after loading previous page (called after parent component updates data)
  const handlePrevPageLoaded = useCallback((addedCount: number) => {
    if (addedCount > 0 && itemHeightRef.current) {
      const scrollTop = window.scrollY;
      const newScrollTop = scrollTop + addedCount * itemHeightRef.current;
      // Use setTimeout to ensure scroll happens after DOM update
      setTimeout(() => {
        window.scrollTo({ top: newScrollTop, behavior: 'auto' });
      }, 0);
    }
  }, []);

  // Scroll to self position when isSelfScroll changes
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    console.log('isSelfScroll====', isSelfScroll, myselfFlg, items?.length, limit, total);
    if (myselfFlg === MYSELF_FLG.NORMAL) {
      const targetIndex = items?.findIndex((item) => item.selfFlg === 1);
      if (targetIndex === -1) return;

      const targetRef = itemRefs.current[targetIndex];
      if (targetRef) {
        targetRef.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
      setTimeout(() => {
        setMyselfFlg(MYSELF_FLG.DEFAULT);
      }, 1000);
    }
  }, [myselfFlg, items]);

  // Store list item refs
  const setItemRef = useCallback((index: number, ref: HTMLDivElement | null) => {
    if (ref) {
      itemRefs.current[index] = ref;
    }
  }, []);

  return (
    <div className="relative !mt-0">
      <div className="overflow-hidden">
        {
          // isInitialLoading ? (
          //   // Initial loading state
          //   // <div className="flex flex-col items-center justify-center py-20">ロード中...</div>
          //   <Loading />
          // ) :
          items?.length === 0 ? (
            // Empty state
            <div className="flex flex-col items-center justify-center py-20 text-muted-foreground">
              <p>{emptyText}</p>
            </div>
          ) : (
            <>
              {/* Top loading trigger point */}
              <div ref={topLoaderRef} className="h-2 flex justify-center items-center">
                {/* {isLoadingPrev && 'Load the previous page...'} */}
              </div>

              {/* List content */}
              <div className="bg-white rounded-2xl overflow-hidden">
                {items?.map((item, index) => (
                  <div key={`item-${index}`} ref={(el) => setItemRef(index, el)}>
                    {renderItem(item, index)}
                  </div>
                ))}
              </div>

              {/* Bottom loading trigger point */}
              <div ref={bottomLoaderRef} className="h-4 flex justify-center items-center">
                {/* {isLoadingNext ? (
                'Load the next page...'
              ) : !hasNextPage && total > 1 ? (
                <span className="text-sm text-muted-foreground">全てのデータを表示しました。 </span>
              ) : null} */}
              </div>
            </>
          )
        }
      </div>
    </div>
  );
};

export default HistoryScrollList;
