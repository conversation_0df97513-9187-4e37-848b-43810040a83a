// グラフページコンポーネント
'use client';

import type { CategoryList, UserRankingInfoList } from '@/types/ranking';
import { formatNumberWithCommas, isEmptyToData } from '@/utils/string-format';
import { useEffect, useRef, useState } from 'react';
import { CATEGORY_TYPE, ICON_URL_TYPE } from '../_const';

type CarouselContentPageProps = UserRankingInfoList & {
  showOrganizerName?: boolean;
};

export default function CarouselContentPage(props: CarouselContentPageProps) {
  const { organizerId, organizerName, categoryList, showOrganizerName = true } = props;

  useEffect(() => {}, []);

  const getIcon = (v: number | undefined) => {
    switch (v) {
      case ICON_URL_TYPE.UP:
        return '/images/ranking/up.svg';
      case ICON_URL_TYPE.DOWN:
        return '/images/ranking/down.svg';
      case ICON_URL_TYPE.EQUAL_LEVEL:
        return '/images/ranking/equalLevel.svg';
      case ICON_URL_TYPE.MEDAL:
        return '/images/ranking/medal.svg';
    }
  };

  return (
    <div className="text-black w-full">
      {organizerName && showOrganizerName && (
        <div className="text-sm font-normal border-b border-border border-solid pb-2 mb-2">
          {organizerName}
        </div>
      )}

      <div className="grid grid-cols-3 gap-2">
        {categoryList?.map((data: CategoryList, index: number) => {
          return (
            <div key={index}>
              <div className="text-xs font-normal flex items-center justify-center ">
                {data.categoryName}
                <img
                  src={getIcon(data.iconUrl)}
                  alt=""
                  width={15}
                  height={15}
                  className="ml-[2px]"
                />
              </div>
              <div className="text-[14px] font-bold text-center">
                {isEmptyToData(formatNumberWithCommas(data.order))}
                <span className="text-[11px] font-normal">位</span>
              </div>
              <div className="text-[10px] font-normal text-muted-foreground text-center">
                {isEmptyToData(formatNumberWithCommas(data.headCnt))}
                {data.category === CATEGORY_TYPE.ORGANIZER ? '団体中' : '人中'}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
