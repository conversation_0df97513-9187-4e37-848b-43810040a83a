import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
interface TabMenuProps {
  className?: string;
  tabsTriggerClassName?: string;
  activeTab: string;
  tabGroup: { label: string; value: string }[];
  onTabChange: (tab: string) => void;
}

export default function TabGroupPage({
  className,
  tabsTriggerClassName,
  activeTab,
  onTabChange,
  tabGroup,
}: TabMenuProps) {
  return (
    <Tabs value={activeTab} onValueChange={(value) => onTabChange(value)} className={className}>
      <TabsList className="grid grid-cols-2 w-full bg-card ">
        {tabGroup?.map((item) => (
          <TabsTrigger key={item.value} value={item.value} className={tabsTriggerClassName}>
            {item.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
