import type React from 'react';
import { useEffect, useState } from 'react';

const FixedArea = ({ children }: { children: React.ReactNode }) => {
  const [isFixed, setIsFixed] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      // Set a threshold, such as fixing content when scrolling exceeds 160px
      if (scrollPosition > 160) {
        setIsFixed(true);
      } else {
        setIsFixed(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return <div className={`w-full z-10 ${isFixed ? 'fixed top-[47px]' : 'static'}`}>{children}</div>;
};

export default FixedArea;
