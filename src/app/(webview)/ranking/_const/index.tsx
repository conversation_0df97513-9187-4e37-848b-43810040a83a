export enum MYSELF_FLG {
  DEFAULT = '', //普通モード
  NORMAL = 0, //普通モード
  SELF = 1, //自分を表示モード
}

export enum RANK_EXCL_FLG {
  INSIDE = 0, //ランキング対象
  OUTSIDE = 1, //ランキング対象外
}

export const LOTTERY_SHOW_FLG: { [key: number]: string } = {
  0: '-',
  1: '歩数',
  2: 'ミッション',
  3: '団体',
};

export enum CATEGORY_TYPE {
  STEP = 1, //歩数
  MISSION = 2, //ミッション
  ORGANIZER = 3, //団体
}

export enum ICON_URL_TYPE {
  EQUAL_LEVEL = 0, //フラット、1
  UP = 1, //上昇
  DOWN = 2, //下降
  MEDAL = 3, //メダル
}

export enum GROUP_ALL_TAB {
  GROUP = 'group', //グループ
  ALL = 'all', //全ユーザー
}

export enum DAY_WEEK_TAB {
  DAY = 'day', //グループ
  WEEK = 'week', //全ユーザー
}

export const ALL_GROUP_TAB = [
  { label: 'グループ', value: 'group' },
  { label: '全ユーザー', value: 'all' },
];

export const CATEGORY_TAB = [
  { label: '日', value: 'day' },
  { label: '週', value: 'week' },
];

export enum IS_TRUE {
  NO = 0, //なし、無
  YES = 1, //あり、有
}

// ポイントヒントタイトル
export const RANKING_HINT_TITLE = [
  { value: '1', name: '歩数ランキング' },
  { value: '2', name: 'ミッション達成数ランキング' },
  { value: '3', name: '団体ランキング' },
];
