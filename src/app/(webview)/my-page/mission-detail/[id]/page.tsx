'use client';

import { gamificationAPI } from '@/api/modules/my-page';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useAuthStore } from '@/store/auth';
import type { MissionDetailResponse, MyPageResponse } from '@/types/my-page';
import { useParams } from 'next/navigation';
import router from 'next/router';
import React, { useEffect, useState } from 'react';

export default function MyPage() {
  const { user, token } = useAuthStore.getState();
  const [missionDetailData, setMissionDetailData] = useState<MissionDetailResponse>();
  const params = useParams(); // 获取路由参数
  const missionId = Array.isArray(params.id) ? params.id[0] : params.id;

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await gamificationAPI.getMissionDetail({ missionId: missionId });
        setMissionDetailData(response);
        // setMissionDetailData(missionDetail_temp);
      } catch (err) {
        console.error('Error fetching exam data:', err);
      }
    };

    fetchData();
  }, [missionId]);

  const missionDetail_temp = {
    missionContent: '目標歩数を設定する',
    missionDetail: '第一行\\n第二行\\n第三行',
  };

  return (
    <div className="flex bg-card flex-col h-[calc(100vh-61px)]">
      <TopBar
        title="ミッション詳細"
        hideShadow={true}
        onBack={() => {
          router.back();
        }}
      />
      <div className="pt-6 px-6 pb-4">
        <div className="text-xl font-bold">{missionDetailData?.missionContent}</div>
        <div className="mt-2 whitespace-pre-line">
          {missionDetailData?.missionDetail?.replace(/\\n/g, '\n')}
        </div>

        <Button
          className="absolute bottom-[81px] left-6 w-[calc(100%-48px)]"
          onClick={() => router.push('/my-page')}
          disabled
        >
          {APP_TEXT.MY_PAGE.MISSION_DETAIL.AUTO_COMPLETION}
        </Button>
      </div>
    </div>
  );
}
