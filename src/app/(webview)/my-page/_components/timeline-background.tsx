interface TimelineBackgroundProps {
  itemsLength: number;
  currentLevel: string;
  items: Array<{ level: string }>;
}

export default function TimelineBackground({
  itemsLength,
  currentLevel,
  items,
}: TimelineBackgroundProps) {
  if (itemsLength === 0) return null;

  const currentIndex = items.findIndex((item) => item.level === currentLevel);

  return (
    <>
      {/* Gray line from first to last (background layer) */}
      <div
        className="absolute w-[2px] bg-muted-foreground z-0"
        style={{
          left: '22px', // Center of the icon area (36px/2)
          top: '45px', // Start from first item icon center
          height: `${itemsLength * 96 + 33}px`,
        }}
      />

      {/* Primary color line from first to current (overlay layer) */}
      {currentIndex >= 0 && (
        <div
          className="absolute w-[2px] bg-primary z-[1]"
          style={{
            left: '22px', // Same position as gray line
            top: '45px', // Start from first item icon center
            height: `${currentIndex * 96 + 18}px`, // Height to extend below current item
          }}
        />
      )}
    </>
  );
}
