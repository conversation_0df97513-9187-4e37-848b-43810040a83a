import { cn } from '@/lib/utils';

interface TimelineNodeProps {
  level: string;
  newIcon?: string;
  newIconGettingFlg?: string;
  isDone: boolean;
  isNext: boolean;
  isPending: boolean;
}

export default function TimelineNode({
  level,
  newIcon,
  newIconGettingFlg,
  isDone,
  isNext,
  isPending,
}: TimelineNodeProps) {
  return (
    <div className="relative">
      {/* isPending时添加背景遮罩层 */}
      {isPending && <div className="absolute inset-0 bg-muted z-[5]" />}

      <div
        className={cn(
          'relative z-10 flex flex-col items-center justify-center',
          isPending ? 'opacity-50' : '',
        )}
      >
        {/* ノード(アイコン) */}
        {newIconGettingFlg === '1' && (
          <div className="relative w-9 h-9">
            {/* 圆形头像层 */}
            <div
              className={cn(
                'w-9 h-9 rounded-full flex items-center justify-center overflow-hidden',
                isDone
                  ? 'bg-primary-30'
                  : isNext
                    ? 'ring-2 ring-primary bg-primary-30'
                    : isPending
                      ? 'bg-gray-10'
                      : '',
              )}
            >
              <img src={newIcon} alt="icon" className="w-full h-full" />
            </div>

            {/* NEXT 悬浮图标 */}
            {isNext && (
              <img
                src="/images/my-page/NEXT.png"
                alt="overlay"
                className="absolute z-20 -top-[12px] -right-[10px] w-10 h-5"
              />
            )}
          </div>
        )}

        {/* レベルバッジ */}
        <div
          className={cn(
            'relative -mt-1 h-[18px] w-12 rounded-[4px] text-xs font-bold border flex items-center justify-center',
            isDone
              ? 'bg-card text-primary border-2 border-primary-90'
              : isNext
                ? 'bg-primary text-card border-2 border-primary'
                : isPending
                  ? 'bg-card text-muted-foreground border-2 border-muted'
                  : '',
          )}
        >
          Lv.{level}
          {/* アイコンがない場合 → レベルバッジ右上 */}
          {newIconGettingFlg !== '1' && isNext && (
            <img
              src="/images/my-page/NEXT.png"
              alt="overlay"
              className="absolute z-20 -top-[16px] -right-[10px] w-10 h-5"
            />
          )}
        </div>
      </div>
    </div>
  );
}
