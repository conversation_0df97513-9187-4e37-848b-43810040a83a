import { Button } from '@/components/ui/button';

interface CurrentMissionButtonProps {
  isVisible: boolean;
  bottom: number;
  onClick: () => void;
}

export default function CurrentMissionButton({
  isVisible,
  bottom,
  onClick,
}: CurrentMissionButtonProps) {
  if (isVisible) return null;

  return (
    <Button
      type="button"
      className="fixed left-[66.5px] right-[66.5px] rounded-full z-50"
      style={{ bottom }}
      onClick={onClick}
    >
      現在のミッションを表示する
    </Button>
  );
}
