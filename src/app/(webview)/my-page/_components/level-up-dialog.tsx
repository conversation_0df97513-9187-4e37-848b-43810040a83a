// components/level-up/LevelUpDialog.tsx
import { gamificationAPI } from '@/api/modules/my-page';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { getDialogImagePath, getLevelIconInfo, getNewIconGetLvlCnts } from '@/const/gamification';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import type { MissionListResponse } from '@/types/my-page';
import router from 'next/router';
import { useEffect, useState } from 'react';
import { LevelUpImage } from './lv-up-image';

export type LevelUpDialogProps = {
  title?: string;
  content?: string;
  footer?: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  homeUseFlg?: number;
  preventOutsideClick?: boolean;
};

export function LevelUpDialog({
  title,
  content,
  footer,
  isOpen,
  onClose,
  homeUseFlg = 0,
  preventOutsideClick = false,
}: LevelUpDialogProps) {
  const [levelUpData, setLevelUpData] = useState<MissionListResponse>();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // TODO:homeUseFlg is request param
        const response = await gamificationAPI.missionList({ homeUseFlg: homeUseFlg });
        setLevelUpData(response);
        // setLevelUpData(levelUpData_temp);
      } catch (err) {
        console.error('Error fetching exam data:', err);
      }
    };

    fetchData();
  }, [isOpen]);

  const levelUpData_temp = {
    levelBasicInfo: {
      currentLevel: '2',
      currentLevelIcon: '2',
    },
    levelUpInfo: {
      levelUpFlg: '1',
      iconName: 'ホッキョクグマ',
      speed: '水中時速10km/h',
      newIconGetFlg: '0',
      newIconGetLvlCnts: '4',
    },
    levelUpMissionCardInfo: {
      missionId: '94',
      missionTitle: '目標歩数を設定する',
      missionAchievedCount: '5',
      missionAchievedLimit: '5',
      todayAchievedFlg: '1',
      missionChangeFlg: '0',
    },
  };

  // 必要なデータを取得
  const { currentLevel } = levelUpData?.levelBasicInfo ?? {};
  const { speed } = levelUpData?.levelUpInfo ?? {};
  const { missionTitle } = levelUpData?.levelUpMissionCardInfo ?? {};

  //　アイコンデータを処理する
  const info = getLevelIconInfo(currentLevel ?? '1');
  const nextIconCnt = getNewIconGetLvlCnts(currentLevel ?? '1');

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        onInteractOutside={(e) => preventOutsideClick && e.preventDefault()}
        className="sm:max-w-[425px] w-[calc(100vw-48px)] rounded-3xl p-0 overflow-hidden [&>button]:hidden"
      >
        {/* 使用 LevelUpImage 组件 */}
        <LevelUpImage
          currentLevelImage={getDialogImagePath(info.imageUrl)}
          currentLevel={currentLevel}
          newIconGetFlg={info.getNewIcon}
          iconName={info.name}
          speed={speed}
        />

        {/* 内容アリア */}
        <div className="px-8 pb-8">
          <div className="rounded-2xl bg-secondary overflow-hidden h-22 mb-2">
            <div className="px-4 py-1">
              <p className="text-base font-medium mt-3 mb-1">達成したミッション</p>
              <p className="text-base text-primary font-bold mt-2 mb-3">{missionTitle}</p>
            </div>
          </div>

          <div className="mb-4">
            {currentLevel === '1'
              ? APP_TEXT.MY_PAGE.DIALOG.FIRST_TIME
              : currentLevel === '100'
                ? APP_TEXT.MY_PAGE.DIALOG.LEVEL_UP_MAX
                : info.getNewIcon
                  ? APP_TEXT.MY_PAGE.DIALOG.GET_ICON(String(nextIconCnt) ?? '')
                  : APP_TEXT.MY_PAGE.DIALOG.LEVEL_UP(
                      String(Number(currentLevel) + nextIconCnt) ?? '0',
                    )}
          </div>

          <div className="flex flex-col gap-4">
            <Button onClick={() => router.push('/my-page')}>{APP_TEXT.MY_PAGE.OPEN}</Button>
            <TextButton onClick={onClose} className="text-gray-600">
              {COMMON_TEXT.BUTTON.CLOSE}
            </TextButton>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
