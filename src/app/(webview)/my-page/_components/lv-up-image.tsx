// components/level-up/LevelUpImage.tsx
import React from 'react';

export type LevelUpImageProps = {
  currentLevelImage?: string;
  currentLevel?: string;
  newIconGetFlg?: string;
  iconName?: string;
  speed?: string;
  className?: string;
};

export function LevelUpImage({
  currentLevelImage,
  currentLevel,
  newIconGetFlg,
  iconName,
  speed,
  className = '',
}: LevelUpImageProps) {
  // 根据 newIconGetFlg 决定显示哪个图片
  const imageSrc = newIconGetFlg ? currentLevelImage : '/images/my-page/dialog-lvup.gif';
  const altText = `lv${currentLevel}`;
  const animal = 'animal';

  return (
    <div
      className={`w-full aspect-square flex items-center justify-center bg-muted-foreground relative ${className}`}
    >
      <img
        className="w-full h-full object-cover"
        src={imageSrc}
        alt={altText}
        onError={(e) => {
          // 图片加载失败时显示默认图片
          e.currentTarget.src = '/images/my-page/dialog-lvup.gif';
        }}
      />

      {/* 悬浮文本框 */}
      {animal && (
        <div className="absolute bottom-6 left-6 right-6 h-[65px] rounded-2xl bg-secondary overflow-hidden shadow-2xl flex items-center justify-center">
          <div className="text-center space-y-0">
            <p>
              <span className="text-primary font-bold text-xl mr-2 mt-1">Lv.{currentLevel}</span>
              <span className="font-bold text-base">になりました</span>
            </p>
            <p>
              <span className="text-sm font-normal mr-1">{iconName}</span>
              <span className="text-xs text-gray-60">({speed})</span>
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
