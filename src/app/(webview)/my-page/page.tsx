'use client';

import { gamificationAPI } from '@/api/modules/my-page';
import TopBar from '@/components/layout/top-bar';
import { type QaData, QaPage } from '@/components/shared/qa-page';
import { Button } from '@/components/ui/button';
import { getLevelIconInfo, getLevelIconPath } from '@/const/gamification';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useAuthStore } from '@/store/auth';
import type { MyPageResponse } from '@/types/my-page';
import React, { useEffect, useState } from 'react';
import LevelMissionTimeline from './_components/level-mission-timeline';
import { LevelUpDialog } from './_components/level-up-dialog';
import { LevelContent, MissionContent } from './_components/qa-content-components';
import UserProfileCard from './_components/user-profile-card';

// ダミーデータ（本番ではAPIやpropsから渡す）
const safeArea = { top: 0 };

const qaData: QaData[] = [
  {
    id: 'level-is',
    title: 'レベルとは',
    content: <LevelContent />,
  },
  {
    id: 'levelup-mission',
    title: 'レベルアップミッション',
    content: <MissionContent />,
  },
];

export default function MyPage() {
  const { user, token } = useAuthStore.getState();
  const { setSlidePage } = useSlidePage();
  const [myPageData, setMyPageData] = useState<MyPageResponse>();
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await gamificationAPI.getMyPage();
        setMyPageData(response);
        // setMyPageData(myPageData_temp);
      } catch (err) {
        console.error('Error fetching exam data:', err);
      }
    };

    fetchData();
  }, []);

  const myPageData_temp = {
    nickName: 'はなこ',
    currentLevel: '3',
    currentLevelIcon: '1',
    iconName: '猿',
    appStartDate: '2025-08-04',
    currAchvStatus: '2',
    missionChangeFlg: '1',
    newIconGetLvlCnts: '2',
    levelUpHistoryList: [
      {
        level: '1',
        newIcon: '4',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: '1日に8,000歩以上歩く',
        achievementCap: '5',
        achievementStatus: '1',
        achievementCounts: '2',
      },
      {
        level: '2',
        newIcon: '10',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: '1日に8,000歩以上歩く8,000歩',
        achievementCap: '10',
        achievementStatus: '1',
        achievementCounts: '3',
      },
      {
        level: '3',
        newIcon: '15',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: '1日に8,000歩以上歩く以上歩く8,000歩以上歩く8,000歩以上歩く8,000歩以上歩く',
        achievementCap: '10',
        achievementStatus: '0',
        achievementCounts: '0',
      },
      {
        level: '4',
        newIcon: '15',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: 'XXXXXX',
        achievementCap: '10',
        achievementStatus: '0',
        achievementCounts: '0',
      },
      {
        level: '5',
        newIcon: '15',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: 'XXXXXX',
        achievementCap: '10',
        achievementStatus: '0',
        achievementCounts: '0',
      },
      {
        level: '6',
        newIcon: '15',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: 'XXXXXX',
        achievementCap: '10',
        achievementStatus: '0',
        achievementCounts: '0',
      },
      {
        level: '7',
        newIcon: '15',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: 'XXXXXX',
        achievementCap: '10',
        achievementStatus: '0',
        achievementCounts: '0',
      },
      {
        level: '8',
        newIcon: '15',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: 'XXXXXX',
        achievementCap: '10',
        achievementStatus: '0',
        achievementCounts: '0',
      },
      {
        level: '9',
        newIcon: '15',
        newIconGettingFlg: '1',
        missionId: '15',
        missionContent: 'XXXXXX',
        achievementCap: '10',
        achievementStatus: '0',
        achievementCounts: '0',
      },
    ],
  };

  //　アイコンデータを処理する
  const info = getLevelIconInfo(myPageData?.currentLevel ?? 1);

  const handleProfileChange = () => {
    // プロフィール変更処理
    console.log('プロフィール変更');
  };

  return (
    <div className="flex flex-col h-[calc(100vh-61px)]">
      <TopBar
        title="マイページ"
        hideShadow={true}
        rightIcon={<img src="/images/ranking/hint.svg" alt="hint" width={20} height={20} />}
        rightIconClick={() => {
          setSlidePage(true, {
            title: 'レベルについて',
            isOverAll: true,
            content: <QaPage data={qaData} />,
            enableClose: false,
            enableBack: true,
            slideFrom: 'right',
          });
        }}
      />

      {/* User Profile Card */}
      <UserProfileCard
        nickName={myPageData?.nickName}
        userId={user?.id}
        currentLevel={myPageData?.currentLevel}
        currentLevelIcon={getLevelIconPath(info.imageUrl)}
        iconName={info.name}
        safeAreaTop={safeArea.top}
        onProfileChange={handleProfileChange}
      />

      {/* Timeline area - remain space */}
      <div className="flex-1 overflow-hidden">
        {myPageData && (
          <div className="h-full overflow-y-auto">
            <LevelMissionTimeline data={myPageData} />
          </div>
        )}
      </div>

      {/* Dialog area */}
      {/* 悬浮按钮 */}
      <Button
        className="fixed bottom-20 right-20 rounded-full w-14 h-14 shadow-lg"
        onClick={() => setIsDialogOpen(true)}
      >
        DIALOG
      </Button>

      <LevelUpDialog isOpen={isDialogOpen} onClose={() => setIsDialogOpen(false)} homeUseFlg={0} />
    </div>
  );
}
