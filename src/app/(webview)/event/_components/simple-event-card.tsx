import { FavoriteIcon } from '@/components/shared/favorite';
import { Card } from '@/components/ui/card';
import type { SimpleEventListItem } from '@/types/event-types';
import { formatDate } from '@/utils/date-format';

export default function SimpleEventCard({
  event,
  className,
  onClick,
}: { event: SimpleEventListItem; className?: string; onClick?: () => void }) {
  return (
    <Card
      className={`p-3 shadow-card-base hover:shadow-card-hover transition-shadow cursor-pointer ${className}`}
      onClick={onClick}
    >
      <div className="flex gap-3">
        {/* 固定サイズの画像 */}
        <div className="w-24 h-24 flex-shrink-0">
          {event.eventImageFilePath ? (
            <img
              className="w-24 h-24 object-cover rounded"
              src={event.eventImageFilePath}
              alt={event.eventName}
              onError={(e) => {
                // 画像の読み込みに失敗した場合の処理
                e.currentTarget.src = '/images/event/default-event.svg';
              }}
            />
          ) : (
            <img
              className="w-24 h-24 object-cover rounded"
              src="/images/event/default-event.svg"
              alt={event.eventName}
            />
          )}
        </div>

        {/* 活動信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start mb-1 ">
            <p className="text-sm font-bold  line-clamp-2 flex-1 mr-1 ">{event.eventName} </p>
            <FavoriteIcon isFavorited={event.isFavorite} />
          </div>

          <div className=" text-muted-foreground space-y-0.5">
            <p className="line-clamp-2 text-xs">
              {event.eventStartDate === event.eventEndDate ? (
                formatDate(event.eventStartDate, 'yyyy年MM月dd日(d)')
              ) : (
                <>
                  {formatDate(event.eventStartDate, 'yyyy年MM月dd日(d)')}~{' '}
                  {event.eventEndDate
                    ? formatDate(event.eventEndDate, 'yyyy年MM月dd日(d)')
                    : '終了日未定'}
                </>
              )}
            </p>
            <p className="line-clamp-1 text-xs">{event.venueName}</p>
          </div>
        </div>
      </div>
    </Card>
  );
}
