'use client';

import { Button } from '@/components/shared/button';
import { ChipSelect } from '@/components/shared/chip-select';
import { Select } from '@/components/shared/select';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { codeTypeList, useCodeOptions } from '@/hooks/use-common-code';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import { useEventStore } from '@/store/event-store';

import { DISTANCE_OPTIONS } from '@/const/options/common-options';
import { HAS_FEE_OPTIONS } from '@/const/options/event-options';
import type { EventOptions, SearchEventListRequest } from '@/types/event-types';
import { useEffect, useState } from 'react';
interface EventFilterFormProps {
  initialFilters?: SearchEventListRequest['filters'];
  onApplyFilters: (filters: SearchEventListRequest['filters']) => void;
  onClearFilters: () => void;
  onClose: () => void;
}

export default function EventFilterForm({
  initialFilters,
  onApplyFilters,
  onClearFilters,
  onClose,
}: EventFilterFormProps) {
  const { eventOptions } = useEventStore();
  const router = useRouter();
  const pathname = usePathname();
  const [hasPoint, setHasPoint] = useState(initialFilters?.hasPoint || '');
  const [hasFee, setHasFee] = useState(initialFilters?.hasFee || '');
  const [eventTypes, setEventTypes] = useState<string[]>(initialFilters?.type || []);
  const [distance, setDistance] = useState<number>(initialFilters?.distance || 0);

  // 添加调试日志来跟踪状态变化
  useEffect(() => {
    console.log('Filter states:', { hasPoint, hasFee, eventTypes, distance });
  }, [hasPoint, hasFee, eventTypes, distance]);
  const eventDistanceOptions = useCodeOptions(codeTypeList.EVENT_SEARCH_DISTANCE);
  const eventFeeOptions = useCodeOptions(codeTypeList.EVENT_SEARCH_FEE);
  const eventPointOptions = useCodeOptions(codeTypeList.EVENT_SEARCH_POINT);
  const eventTypeOptions = eventOptions?.eventTypeOptions;

  // 添加调试日志来检查选项数据
  useEffect(() => {
    console.log('Options loaded:', {
      eventDistanceOptions,
      eventFeeOptions,
      eventPointOptions,
      eventTypeOptions,
      eventOptions,
    });
  }, [eventDistanceOptions, eventFeeOptions, eventPointOptions, eventTypeOptions, eventOptions]);
  const handleApplyFilters = () => {
    const filters: SearchEventListRequest['filters'] = {};

    // 确保所有状态都被正确检查
    if (hasPoint && hasPoint.trim() !== '') filters.hasPoint = hasPoint;
    if (hasFee && hasFee.trim() !== '') filters.hasFee = hasFee;
    if (eventTypes && eventTypes.length > 0) filters.type = eventTypes;
    // 修复：distance为0时也应该被包含在filters中，因为0表示"指定なし"是一个有效的选择
    if (distance !== undefined && distance !== null) filters.distance = distance;

    console.log('Current states before applying filters:', {
      hasPoint,
      hasFee,
      eventTypes,
      distance,
    });
    console.log('Applied filters:', filters);

    // 添加额外的验证，确保filters不为空对象
    const hasAnyFilter = Object.keys(filters).length > 0;
    console.log('Has any filter:', hasAnyFilter);

    // 如果当前不在搜索结果页面，先跳转到搜索结果页面
    if (pathname !== '/event/search-result') {
      onApplyFilters(filters);
      onClose();
      router.push('/event/search-result');
      return;
    }

    onApplyFilters(filters);
    onClose();
  };

  const handleClearFilters = () => {
    console.log('Clearing filters');
    setHasPoint('');
    setHasFee('');
    setEventTypes([]);
    setDistance(0);
    onClearFilters();
  };

  return (
    <div className="bg-card">
      <div className="p-6 flex flex-col gap-6 pb-40">
        {/* ポイントの有無 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">ポイントの有無</h3>
          <RadioGroup
            value={hasPoint}
            onValueChange={(value) => {
              console.log('HasPoint changed:', value);
              setHasPoint(value);
            }}
            className="space-y-5"
          >
            {eventPointOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`point-${option.value}`} />
                <Label htmlFor={`point-${option.value}`} className="cursor-pointer font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        {/* 費用 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">費用</h3>
          <RadioGroup
            value={hasFee}
            onValueChange={(value) => {
              console.log('HasFee changed:', value);
              setHasFee(value);
            }}
            className="space-y-5"
          >
            {HAS_FEE_OPTIONS.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`fee-${option.value}`} />
                <Label htmlFor={`fee-${option.value}`} className="cursor-pointer font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        {/* 種別 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">種別</h3>
          <ChipSelect
            options={eventTypeOptions || []}
            selectedValues={eventTypes}
            onChange={(values) => {
              console.log('EventTypes changed:', values);
              setEventTypes(values);
            }}
            placeholder="活動類型を選択してください"
          />
        </div>
        {/* 距離 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">現在地からの距離</h3>
          <Select
            defaultValue={distance.toString()}
            options={DISTANCE_OPTIONS.map((option) => ({
              value: option.value,
              name: option.label,
            }))}
            title="現在地からの距離"
            onChange={(value) => {
              console.log('Distance changed:', value); // 添加调试日志
              setDistance(Number(value));
            }}
            className="flex items-center justify-between h-12 px-4 border border-input rounded-md  text-sm"
          />
        </div>

        <div className="fixed bottom-0 left-0 right-0 flex flex-col gap-4 p-4 bg-card border-t">
          <Button onClick={handleApplyFilters}>条件を適用する</Button>
          <Button variant="outline" onClick={handleClearFilters}>
            クリア
          </Button>
        </div>
      </div>
    </div>
  );
}
