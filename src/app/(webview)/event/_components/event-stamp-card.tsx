'use client';
import { ScrollArea } from '@/components/shared/scroll-area';
import { cn } from '@/lib/utils';
import type { EventDetail, PointPatternDetails, Stamp } from '@/types/event-types';

interface EventStampCardProps {
  showTitle?: boolean;
  stampList?: Stamp[];
  eventInfo?: EventDetail;
}

export function EventStampCard({ showTitle = true, stampList, eventInfo }: EventStampCardProps) {
  // ポイント付与パターンが0の場合、または詳細情報がない場合は表示しない
  if (eventInfo?.pointPattern === '0' || !eventInfo?.pointPatternDetails) {
    return null;
  }

  console.log('stampList', stampList);
  console.log('eventInfo', eventInfo);

  let isCompleted = false;

  // スタンプデータを生成する関数
  const generateStampData = () => {
    const pointPattern = eventInfo?.pointPattern;
    const pointPatternDetails = eventInfo?.pointPatternDetails;
    const attendedStamps = stampList || [];

    // 均一モード：stampList.length + 1 個のスタンプを表示、前stampList.length個は押印済み、最後の1個は空のスタンプ
    if (pointPattern === '1' && pointPatternDetails && 'points' in pointPatternDetails) {
      const stamps = [];

      // 押印済みのスタンプを追加
      for (let i = 0; i < attendedStamps.length; i++) {
        stamps.push({
          index: i + 1,
          isAttended: true,
          isGoal: false,
          points: pointPatternDetails.points,
          attendDate: attendedStamps[i].attendDate || '',
          history: attendedStamps[i],
        });
      }

      // 空のスタンプを1つ追加（最大数に達していない場合）
      if (attendedStamps.length < 10) {
        // 最大10個と仮定
        stamps.push({
          index: attendedStamps.length + 1,
          isAttended: false,
          isGoal: false,
          points: pointPatternDetails.points,
          attendDate: '',
          history: null,
        });
      }

      return stamps;
    }

    // 段階モード：totalTimes個の空のスタンプを固定で描画、stampListのデータ分だけスタンプを押印
    if (
      pointPattern === '2' &&
      pointPatternDetails &&
      'totalTimes' in pointPatternDetails &&
      'details' in pointPatternDetails
    ) {
      isCompleted = pointPatternDetails.totalTimes === attendedStamps.length;
      // 段階モード：totalTimes個の空のスタンプを固定で描画、stampListのデータ分だけスタンプを押印
      const stamps = [];
      const totalTimes = pointPatternDetails.totalTimes;

      for (let i = 0; i < totalTimes; i++) {
        const attendedStamp = attendedStamps[i];
        const detailInfo = pointPatternDetails.details.find((d) => d.sequence === i + 1);

        stamps.push({
          index: i + 1,
          isAttended:
            !!attendedStamp &&
            (attendedStamp.isParticipated === '1' || attendedStamp.attendDate !== ''),
          isGoal: i === totalTimes - 1, // 最後のスタンプがゴールスタンプ
          points: detailInfo?.points || attendedStamp?.points || 0,
          attendDate: attendedStamp?.attendDate || '',
          history: attendedStamp || null,
        });
      }

      return stamps;
    }

    // 一括モード：totalTimes個の空のスタンプを固定で描画、stampListのデータ分だけスタンプを押印
    if (
      pointPattern === '3' &&
      pointPatternDetails &&
      'totalTimes' in pointPatternDetails &&
      'finalBonusPoints' in pointPatternDetails
    ) {
      isCompleted = pointPatternDetails.totalTimes === attendedStamps.length;
      const stamps = [];
      const totalTimes = pointPatternDetails.totalTimes;
      const finalBonusPoints = pointPatternDetails.finalBonusPoints;

      for (let i = 0; i < totalTimes; i++) {
        const attendedStamp = attendedStamps[i];
        const isLastStamp = i === totalTimes - 1;
        const isAttended =
          !!attendedStamp &&
          (attendedStamp.isParticipated === '1' || attendedStamp.attendDate !== '');

        // 全てのスタンプが押印完了したかチェック（最後のスタンプがゴールスタンプ）
        const allStampsCompleted = attendedStamps.length >= totalTimes;
        const isGoal = isLastStamp && allStampsCompleted;

        stamps.push({
          index: i + 1,
          isAttended,
          isGoal,
          points: isGoal ? finalBonusPoints : attendedStamp?.points || 0,
          attendDate: attendedStamp?.attendDate || '',
          history: attendedStamp || null,
        });
      }

      return stamps;
    }

    return [];
  };

  const stampData = generateStampData();

  if (stampData.length === 0) {
    return null;
  }

  return (
    <div className="mx-auto max-w-[320px] rounded-[24px] overflow-hidden shadow-[0px_0px_8px_0px_rgba(0,0,0,0.15)]">
      {/* ヘッダー */}
      {isCompleted ? (
        <div className="py-2 px-6 bg-primary border-b border-dashed border-gray-30">
          <div className="flex items-center">
            <div className="bg-white text-primary px-3 py-0.5 rounded-2xl text-xs font-bold">
              STAMP COMPLETE!
            </div>
          </div>
        </div>
      ) : showTitle ? (
        <div className="py-2 px-6 bg-primary-5 border-b border-dashed border-gray-30">
          <div className="flex items-center">
            <div className={cn('text-sm', 'text-gray-60')}>{eventInfo?.eventName}</div>
          </div>
        </div>
      ) : null}

      {/* スタンプグリッド */}
      <ScrollArea className="h-[188px] bg-primary-5 p-4">
        <div className="grid grid-cols-5 gap-2">
          {stampData.map((stamp, index) => (
            <StampPoint
              key={index}
              index={stamp.index}
              isAttended={stamp.isAttended}
              isGoal={stamp.isGoal}
              points={stamp.points}
              attendDate={stamp.attendDate}
              pointPattern={eventInfo?.pointPattern}
              pointPatternDetails={eventInfo?.pointPatternDetails}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface StampPointProps {
  index: number;
  isAttended: boolean;
  isGoal: boolean;
  points: number;
  attendDate: string;
  pointPattern: string;
  pointPatternDetails: PointPatternDetails | undefined;
}

function StampPoint({
  index,
  isAttended,
  isGoal,
  points,
  attendDate,
  pointPattern,
  pointPatternDetails,
}: StampPointProps) {
  // スタンプ獲得済みの場合
  if (isAttended) {
    return (
      <div className="w-12">
        <div className="flex flex-col relative items-center justify-center bg-primary rounded-full w-12 h-12">
          {pointPattern === '1' ? (
            <>
              <span className="text-white text-xs mt-[-2px]">{points}p</span>
              <span className="text-white text-xs font-bold">GET!</span>
            </>
          ) : pointPattern === '3' ? (
            <span className="text-white text-xs font-bold transform -rotate-[20deg] scale-90">
              {isGoal ? 'CLEAR!' : 'STAMP'}
            </span>
          ) : (
            <>
              <span className="text-white text-xs mt-[-2px]">{points}p</span>
              <span className="text-white text-xs font-bold">GET!</span>
            </>
          )}
          <div className="border border-primary-50 rounded-full w-[44px] h-[44px] absolute top-[2px] left-[2px]" />
        </div>
        <div className="text-xs mt-0.5 text-center">
          {attendDate ? attendDate.split('-').slice(1).join('/') : ''}
        </div>
      </div>
    );
  }

  // スタンプ未獲得の場合
  return (
    <div className="w-12">
      <div className="flex flex-col items-center border border-dashed border-gray-30 justify-center bg-white rounded-full w-12 h-12">
        {isGoal ? (
          <span className="text-gray-30 font-bold text-xs">GOAL</span>
        ) : (
          <>
            <span className="text-gray-30 font-bold">{index}</span>
            {pointPattern !== '3' && (
              <span className="text-gray-60 text-xs mt-[-4px]">{points}p</span>
            )}
          </>
        )}
      </div>
      <div className="text-xs mt-0.5 text-center h-[18px]" />
    </div>
  );
}
