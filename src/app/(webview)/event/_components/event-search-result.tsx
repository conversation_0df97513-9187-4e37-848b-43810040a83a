import NoData from '@/components/shared/no-data';
import { Select } from '@/components/shared/select';
import { SORT_OPTIONS } from '@/const/options/event-options';
import { useRouter } from '@/hooks/use-next-navigation';
import { useEventStore } from '@/store/event-store';
import type { ComplexEventListItem, SearchEventListRequest } from '@/types/event-types';
import ComplexEventCard from './complex-event-card';
export default function EventSearchResult({
  searchEventsData,
  searchParams,
}: {
  searchEventsData: ComplexEventListItem[] | [];
  searchParams: SearchEventListRequest;
}) {
  const { setSearchParams } = useEventStore();
  const router = useRouter();
  return (
    <div>
      <div>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="font-bold">すべて</div>
            <div className="text-xs text-muted-foreground">{searchEventsData?.length || 0}件</div>
          </div>
          <Select
            defaultValue={searchParams.sortType || ''}
            options={SORT_OPTIONS.map((option) => ({
              value: option.value,
              name: option.label,
            }))}
            title="並び替え"
            onChange={(sortType: string) => {
              setSearchParams({
                ...searchParams,
                sortType,
              });
            }}
            className="flex items-center justify-between h-9  px-4 border border-input rounded-md w-[136px] text-sm"
          />
        </div>
        {!searchEventsData || searchEventsData?.length === 0 ? <EventSearchNoData /> : null}
        <div className="flex flex-col gap-2 py-2">
          {searchEventsData?.map((event) => (
            <ComplexEventCard
              key={event.eventId}
              event={event}
              onClick={() => router.push(`/event/${event.eventId}`)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

function EventSearchNoData() {
  return (
    <div>
      <NoData height={500} title="イベントがありません" imgUrl="/images/event/no-event.svg" />
    </div>
  );
}
