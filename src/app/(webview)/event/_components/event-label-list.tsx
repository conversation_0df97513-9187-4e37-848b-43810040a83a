import IconEvent from '@/components/icons/icon-event';

import { Badge } from '@/components/ui/badge';

export function EventLabelList({
  showEventLabel = true,
  eventStartDate,
  eventEndDate,
  hasFee,
  isReservationRequired,
}: {
  showEventLabel?: boolean;
  eventStartDate: string;
  eventEndDate?: string | null;
  hasFee: string;
  isReservationRequired?: string;
}) {
  return (
    <div className="flex gap-2 flex-wrap">
      {showEventLabel && <EventLabel />}

      <Badge variant="secondary" className="font-normal px-1">
        {getEventStatus(eventStartDate, eventEndDate)}
      </Badge>
      {hasFee && (
        <Badge variant="secondary" className="font-normal px-1">
          {hasFee === '1' ? '有料' : '無料'}
        </Badge>
      )}
      {isReservationRequired && (
        <Badge variant="secondary" className="font-normal px-1">
          {isReservationRequired === '1' ? '予約必須' : '予約不要'}
        </Badge>
      )}
    </div>
  );
}

const getEventStatus = (startDate: string, endDate?: string | null) => {
  const now = new Date();
  const start = new Date(startDate);
  // endDateが未設定、null、undefinedの場合は9999/12/31を使用
  const end = endDate ? new Date(endDate) : new Date('9999-12-31');
  let status = '';
  if (now < start) {
    status = '開催予定';
  } else if (now >= start && now <= end) {
    status = '開催中';
  } else {
    status = '開催終了';
  }
  return status;
};

function EventLabel() {
  return (
    <div className="font-normal flex items-center px-1 border-[1px] bg-[#F3FBF9] border-[#15B47F] text-xs rounded">
      <IconEvent className="w-4 h-4 text-[#15B47F]" />
      <span className="text-[#197A4B]">イベント</span>
    </div>
  );
}
