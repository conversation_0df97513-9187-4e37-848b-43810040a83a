'use client';
import { PointIcon, StampIcon, StepIcon, TimeIcon, WalkingIcon } from '@/components/icons/svg-icon';
import { COLORS } from '@/const/colors';
import { cn } from '@/lib/utils';
import type { PointPatternDetails, PointPatternDetailsStep } from '@/types/event-types';
interface PointCardProps {
  pointPattern: string;
  pointPatternDetails: PointPatternDetails | undefined;
  organizerName?: string;
}

export function DetailPointCard({
  pointPattern,
  pointPatternDetails,
  organizerName,
}: PointCardProps) {
  // ポイント付与パータンが0の場合，不显示
  if (pointPattern === '0' || !pointPatternDetails) {
    return null;
  }

  // ポイント付与パータンが1の場合 (均一)
  if (pointPattern === '1') {
    // タイプガード：PointPatternDetailsUniform タイプであることを確認
    if ('points' in pointPatternDetails) {
      return (
        <div className={'rounded-2xl text-sm bg-primary-5 p-6 flex flex-col gap-2'}>
          {organizerName && <p className="mb-0.5">{organizerName}</p>}
          <div className="flex items-center gap-1">
            <PointIcon size={18} fill={COLORS.primary[100]} /> <span>1回参加ごとに</span>
            <span className="flex-1" />
            <span className="font-bold text-primary text-lg">{pointPatternDetails?.points}</span>P
          </div>
        </div>
      );
    }
    return null;
  }

  // ポイント付与パータンが2の場合 (段階的)
  if (pointPattern === '2') {
    // タイプガード：PointPatternDetailsStep タイプであることを確認
    if ('totalTimes' in pointPatternDetails && 'details' in pointPatternDetails) {
      const { maxPoint, minPoint } = getMaxAndMinPoint(pointPatternDetails);
      return (
        <div className={'rounded-2xl text-sm bg-primary-5 p-6 flex flex-col gap-2'}>
          {organizerName && <p className="mb-0.5">{organizerName}</p>}
          <div className="flex items-center gap-1">
            <PointIcon size={18} fill={COLORS.primary[100]} />{' '}
            <span>{pointPatternDetails?.totalTimes}回参加ごとに</span>
            <span className="flex-1" />
            <span className="font-bold text-primary">
              {minPoint} ~ {maxPoint}
            </span>
            P
          </div>
        </div>
      );
    }
    return null;
  }

  // ポイント付与パータンが3の場合 (一括)
  if (pointPattern === '3') {
    // タイプガード：PointPatternDetailsBatch タイプであることを確認
    if ('totalTimes' in pointPatternDetails && 'finalBonusPoints' in pointPatternDetails) {
      return (
        <div className={'rounded-2xl text-sm bg-primary-5 p-6 flex flex-col gap-2'}>
          {organizerName && <p className="mb-0.5">{organizerName}</p>}
          <div className="flex items-center gap-1">
            <PointIcon size={18} fill={COLORS.primary[100]} />{' '}
            <span>{pointPatternDetails?.totalTimes}回参加すると</span>
            <span className="flex-1" />
            <span className="font-bold text-primary">{pointPatternDetails?.finalBonusPoints}</span>P
          </div>
        </div>
      );
    }

    return null;
  }
}

export function ListPointCard({
  pointPattern,
  pointPatternDetails,
  organizerName,
}: PointCardProps) {
  // ポイント付与パータンが0の場合，不显示
  if (pointPattern === '0' || !pointPatternDetails) {
    return null;
  }

  // ポイント付与パータンが1の場合 (均一)
  if (pointPattern === '1') {
    // タイプガード：PointPatternDetailsUniform タイプであることを確認
    if ('points' in pointPatternDetails) {
      return (
        <div className={'rounded-full text-sm bg-primary-5 px-3 py-1 '}>
          {organizerName && <p className="mb-0.5">{organizerName}</p>}
          <div className="flex items-center gap-1">
            <PointIcon size={18} fill={COLORS.primary[100]} /> <span>1回参加ごとに</span>
            <span className="font-bold text-primary">{pointPatternDetails?.points}</span>P
          </div>
        </div>
      );
    }
    return null;
  }

  // ポイント付与パータンが2の場合 (段階的)
  if (pointPattern === '2') {
    // タイプガード：PointPatternDetailsStep タイプであることを確認
    if ('totalTimes' in pointPatternDetails && 'details' in pointPatternDetails) {
      const { maxPoint, minPoint } = getMaxAndMinPoint(pointPatternDetails);
      return (
        <div className={'rounded-full text-sm bg-primary-5 px-3 py-1 '}>
          {organizerName && <p className="mb-0.5">{organizerName}</p>}
          <div className="flex items-center gap-1">
            <PointIcon size={18} fill={COLORS.primary[100]} />{' '}
            <span>{pointPatternDetails?.totalTimes}回参加ごとに</span>
            <span className="font-bold text-primary">
              {minPoint} ~ {maxPoint}
            </span>
            P
          </div>
        </div>
      );
    }
    return null;
  }

  // ポイント付与パータンが3の場合 (一括)
  if (pointPattern === '3') {
    // タイプガード：PointPatternDetailsBatch タイプであることを確認
    if ('totalTimes' in pointPatternDetails && 'finalBonusPoints' in pointPatternDetails) {
      return (
        <div className={'rounded-full text-sm bg-primary-5 px-3 py-1 '}>
          {organizerName && <p className="mb-0.5">{organizerName}</p>}
          <div className="flex items-center gap-1">
            <PointIcon size={18} fill={COLORS.primary[100]} />{' '}
            <span>{pointPatternDetails?.totalTimes}回参加すると</span>
            <span className="font-bold text-primary">{pointPatternDetails?.finalBonusPoints}</span>P
          </div>
        </div>
      );
    }

    return null;
  }
}

function getMaxAndMinPoint(pointPatternDetails: PointPatternDetailsStep) {
  const maxPoint = Math.max(...pointPatternDetails.details.map((detail) => detail.points));
  const minPoint = Math.min(...pointPatternDetails.details.map((detail) => detail.points));
  return { maxPoint, minPoint };
}
