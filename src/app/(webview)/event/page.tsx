'use client';
import { eventAPI } from '@/api/modules/event-api';
import IconEvent from '@/components/icons/icon-event';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import KeywordSearch from '@/components/shared/keyword-search';
import { SearchInput } from '@/components/shared/search-input';
import { Select } from '@/components/shared/select';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { SORT_OPTIONS } from '@/const/options/event-options';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useEventStore } from '@/store/event-store';
import type {
  ComplexEventListItem,
  SearchEventListRequest,
  SimpleEventListItem,
} from '@/types/event-types';
import { SlidersHorizontal, Tent } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import ComplexEventCard from './_components/complex-event-card';
import EventFilter from './_components/event-filter';
import EventSearchResult from './_components/event-search-result';
import SimpleEventCard from './_components/simple-event-card';

export default function EventPage() {
  const router = useRouter();
  const pathname = usePathname();
  const { location } = useGeolocation();
  const { setLoading } = useLoading();
  const { setSlidePage } = useSlidePage();
  const { searchParams, setSearchParams, updateSearchFilters } = useEventStore();

  const [recommendedEventsData, setRecommendedEventsData] = useState<SimpleEventListItem[]>([]);
  const [popularEventsData, setPopularEventsData] = useState<SimpleEventListItem[]>([]);
  const [searchEventsData, setSearchEventsData] = useState<ComplexEventListItem[]>([]);

  // エラーハンドリングの統一処理
  const handleApiError = useCallback((error: unknown, defaultMessage: string) => {
    console.log(error);
    const message = error instanceof Error ? error.message : defaultMessage;
    toast.error(message);
  }, []);

  // イベント検索リクエストの統一処理
  const searchEvents = useCallback(
    async (params = searchParams, showLoading = false) => {
      if (showLoading) setLoading(true);
      if (!location) return;
      const allParams = { ...params, filters: {} };
      const latitude = location?.lat.toFixed(6) || undefined;
      const longitude = location?.lng.toFixed(6) || undefined;
      allParams.latitude = latitude;
      allParams.longitude = longitude;

      try {
        const data = await eventAPI.searchEvents(allParams);
        if (data?.eventList) {
          setSearchEventsData(data.eventList);
        }
      } catch (error: unknown) {
        if (error instanceof Error) {
          handleApiError(error, 'イベント検索に失敗しました');
        }
      } finally {
        if (showLoading) setLoading(false);
      }
    },
    [searchParams, setLoading, handleApiError],
  );

  // 検索結果ページへのナビゲーション
  const navigateToSearchResult = useCallback(() => {
    if (pathname !== '/event/search-result') {
      router.push('/event/search-result');
      return true;
    }
    return false;
  }, [pathname, router]);

  // フィルターの更新と検索実行
  const updateFiltersAndSearch = useCallback(
    async (filters: SearchEventListRequest['filters'], shouldNavigate = true) => {
      updateSearchFilters(filters);

      if (shouldNavigate && navigateToSearchResult()) {
        return;
      }

      const newSearchParams = { ...searchParams, filters };
      await searchEvents(newSearchParams);
    },
    [searchParams, updateSearchFilters, navigateToSearchResult, searchEvents],
  );

  const handleBackClick = () => {
    router.back();
  };

  // ページ読み込み時にトップにスクロール、モバイル端末でのジャンプ時に中間位置が表示される問題を解決
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // 初期データの読み込み
  useEffect(() => {
    const fetchRecommendedEventsData = async () => {
      const data = await eventAPI.getRecommendedEvents();
      if (data?.eventList) {
        setRecommendedEventsData(data.eventList);
      }
    };

    const fetchPopularEventsData = async () => {
      const data = await eventAPI.getPopularEvents();
      if (data?.eventList) {
        setPopularEventsData(data.eventList);
      }
    };

    const fetchAllData = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchRecommendedEventsData(),
          fetchPopularEventsData(),
          searchEvents(searchParams),
        ]);
      } catch (error) {
        handleApiError(error, 'データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, [searchEvents, searchParams, setLoading, handleApiError]);

  // フィルター条件の適用処理
  const handleApplyFilters = useCallback(
    async (filters: SearchEventListRequest['filters']) => {
      await updateFiltersAndSearch(filters);
    },
    [updateFiltersAndSearch],
  );

  // フィルター条件のクリア処理
  const handleClearFilters = useCallback(async () => {
    await updateFiltersAndSearch(undefined, false);
  }, [updateFiltersAndSearch]);

  // キーワード検索の処理
  const handleKeywordSearch = useCallback(
    (keyword: string) => {
      const filters = {
        ...searchParams.filters,
        keyword,
      };
      updateSearchFilters(filters);
      navigateToSearchResult();
    },
    [searchParams.filters, updateSearchFilters, navigateToSearchResult],
  );

  // キーワードのクリア処理
  const handleClearKeyword = useCallback(async () => {
    const filters = {
      ...searchParams.filters,
      keyword: undefined,
    };
    await updateFiltersAndSearch(filters, false);
  }, [searchParams.filters, updateFiltersAndSearch]);

  // ソート選択の処理
  const handleSortChange = useCallback(
    async (sortType: string) => {
      const newSearchParams = {
        ...searchParams,
        sortType,
      };
      setSearchParams(newSearchParams);
      await searchEvents(newSearchParams, true);
    },
    [searchParams, setSearchParams, searchEvents],
  );

  const openFilterSlidePage = useCallback(() => {
    setSlidePage(true, {
      title: '絞り込み',
      content: (
        <EventFilter
          initialFilters={searchParams.filters}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          onClose={() => setSlidePage(false)}
        />
      ),
      isOverAll: true,
      enableClose: true,
      enableBack: false,
      slideFrom: 'bottom',
    });
  }, [searchParams.filters, handleApplyFilters, handleClearFilters, setSlidePage]);

  const openKeywordSlidePage = useCallback(() => {
    setSlidePage(true, {
      title: 'イベント',
      content: (
        <KeywordSearch
          initialKeyword={searchParams.filters?.keyword || ''}
          onKeywordSelect={handleKeywordSearch}
          onClose={() => setSlidePage(false)}
          keywordSearchFunction={eventAPI.keywordSearch}
          functionType="event"
          functionTypeName="イベント"
          placeholder="イベント名、住所、キーワードを入力"
          suggestionsIcon={<IconEvent className="h-6 w-6 text-muted-foreground" />}
          detailUrl="/event/[eventId]"
        />
      ),
      isOverAll: true,
      enableClose: false,
      enableBack: true,
      slideFrom: 'bottom',
    });
  }, [searchParams.filters?.keyword, handleKeywordSearch, setSlidePage]);

  // イベントカードのクリック処理
  const handleEventClick = useCallback(
    (eventId: string) => {
      router.push(`/event/${eventId}`);
    },
    [router],
  );

  return (
    <div className="bg-card">
      {/* イベント */}
      <TopBar title="イベント" enableBack={true} onBack={handleBackClick} />

      <div className="p-4 flex flex-row gap-4 border-b">
        <div onClick={openKeywordSlidePage} className="flex-1">
          <SearchInput
            placeholder="イベント名、住所、キーワードを入力"
            value={searchParams.filters?.keyword || ''}
            readOnly
            className="cursor-pointer"
            showClearButton={!!searchParams.filters?.keyword}
            onClear={handleClearKeyword}
          />
        </div>
        <Button
          onClick={openFilterSlidePage}
          variant="icon"
          className="flex flex-col items-center gap-0"
          type="button"
        >
          <SlidersHorizontal className="w-6 h-6" />
          <span className="text-[10px] font-normal h-4">絞り込み</span>
        </Button>
      </div>

      <div className="p-6 flex flex-col gap-4">
        {/* 推奨イベント一覧 */}
        {recommendedEventsData.length > 0 && (
          <div>
            <div className="font-bold">あなたにおすすめ</div>
            <ScrollArea>
              <div className="flex no-wrap gap-2 p-2">
                {recommendedEventsData.map((event) => (
                  <SimpleEventCard
                    className="w-[280px]"
                    key={event.eventId}
                    event={event}
                    onClick={() => handleEventClick(event.eventId)}
                  />
                ))}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>
        )}

        {/* 人気イベント一覧 */}
        {popularEventsData.length > 0 && (
          <div>
            <div className="font-bold">人気</div>
            <ScrollArea>
              <div className="flex no-wrap gap-2 p-2">
                {popularEventsData.map((event) => (
                  <SimpleEventCard
                    className="w-[280px]"
                    key={event.eventId}
                    event={event}
                    onClick={() => handleEventClick(event.eventId)}
                  />
                ))}
              </div>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>
          </div>
        )}

        {/* すべて */}
        <EventSearchResult searchEventsData={searchEventsData} searchParams={searchParams} />
      </div>
    </div>
  );
}
