export const isEventActive = (startDate: string, endDate?: string | null) => {
  const now = new Date();
  const start = new Date(startDate);
  // endDateが未設定、null、undefinedの場合は9999/12/31を使用
  const end = endDate ? new Date(endDate) : new Date('9999-12-31');
  return now >= start && now <= end;
};

export const getEventStatus = (startDate: string, endDate?: string | null) => {
  const now = new Date();
  const start = new Date(startDate);
  // endDateが未設定、null、undefinedの場合は9999/12/31を使用
  const end = endDate ? new Date(endDate) : new Date('9999-12-31');
  let status = '';
  if (now < start) {
    status = '開催予定';
  } else if (now >= start && now <= end) {
    status = '開催中';
  } else {
    status = '開催終了';
  }
  return status;
};
