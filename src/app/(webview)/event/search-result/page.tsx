'use client';
import { eventAPI } from '@/api/modules/event-api';
import IconEvent from '@/components/icons/icon-event';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import KeywordSearch from '@/components/shared/keyword-search';
import { SearchInput } from '@/components/shared/search-input';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useEventStore } from '@/store/event-store';
import type { ComplexEventListItem, SearchEventListRequest } from '@/types/event-types';
import { SlidersHorizontal, Tent } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import EventFilter from '../_components/event-filter';
import EventSearchResult from '../_components/event-search-result';

export default function EventSearchResultPage() {
  const router = useRouter();
  const { location } = useGeolocation();
  const { setLoading } = useLoading();
  const { setSlidePage } = useSlidePage();
  const { searchParams, setSearchParams, updateSearchFilters } = useEventStore();

  const [searchEventsData, setSearchEventsData] = useState<ComplexEventListItem[]>([]);

  // 统一的错误处理
  const handleApiError = useCallback((error: unknown, defaultMessage: string) => {
    console.error(error);
    const message = error instanceof Error ? error.message : defaultMessage;
    toast.error(message);
  }, []);

  // 统一的搜索事件请求
  const searchEvents = useCallback(
    async (params = searchParams, showLoading = false) => {
      if (showLoading) setLoading(true);
      try {
        if (!location) return;
        const latitude = location?.lat.toFixed(6) || undefined;
        const longitude = location?.lng.toFixed(6) || undefined;
        params.latitude = latitude;
        params.longitude = longitude;

        const data = await eventAPI.searchEvents(params);
        if (data?.eventList) {
          setSearchEventsData(data.eventList);
        }
      } catch (error) {
        handleApiError(error, 'イベント検索に失敗しました');
      } finally {
        if (showLoading) setLoading(false);
      }
    },
    [searchParams, setLoading, handleApiError],
  );

  const handleBackClick = () => {
    router.back();
  };

  // 初始化数据加载
  useEffect(() => {
    searchEvents(searchParams, true);
  }, [searchEvents, searchParams]);

  // 处理筛选条件应用
  const handleApplyFilters = useCallback(
    async (filters: SearchEventListRequest['filters']) => {
      updateSearchFilters(filters);
    },
    [updateSearchFilters],
  );

  // 处理清除筛选条件
  const handleClearFilters = useCallback(async () => {
    updateSearchFilters(undefined);
  }, [updateSearchFilters]);

  // 处理关键词搜索
  const handleKeywordSearch = useCallback(
    (keyword: string) => {
      const filters = {
        ...searchParams.filters,
        keyword,
      };
      updateSearchFilters(filters);
    },
    [searchParams.filters, updateSearchFilters],
  );

  // 处理清空关键词
  const handleClearKeyword = useCallback(async () => {
    const filters = {
      ...searchParams.filters,
      keyword: undefined,
    };
    updateSearchFilters(filters);
  }, [searchParams.filters, updateSearchFilters]);

  const openFilterSlidePage = useCallback(() => {
    setSlidePage(true, {
      title: '絞り込み',
      content: (
        <EventFilter
          initialFilters={searchParams.filters}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          onClose={() => setSlidePage(false)}
        />
      ),
      isOverAll: true,
      enableClose: true,
      enableBack: false,
      slideFrom: 'bottom',
    });
  }, [searchParams.filters, handleApplyFilters, handleClearFilters, setSlidePage]);

  const openKeywordSlidePage = useCallback(() => {
    setSlidePage(true, {
      title: 'イベント',
      content: (
        <KeywordSearch
          initialKeyword={searchParams.filters?.keyword || ''}
          onKeywordSelect={handleKeywordSearch}
          onClose={() => setSlidePage(false)}
          keywordSearchFunction={eventAPI.keywordSearch}
          functionType="event"
          functionTypeName="イベント"
          suggestionsIcon={<IconEvent className="h-6 w-6 text-muted-foreground" />}
          detailUrl="/event/[eventId]"
        />
      ),
      isOverAll: true,
      enableClose: false,
      enableBack: true,
      slideFrom: 'bottom',
    });
  }, [searchParams.filters?.keyword, handleKeywordSearch, setSlidePage]);

  return (
    <div className="bg-card">
      {/* イベント */}
      <TopBar title="イベント" enableBack={true} onBack={handleBackClick} />

      <div className="p-4 flex flex-row gap-4 border-b">
        <div onClick={openKeywordSlidePage} className="flex-1">
          <SearchInput
            placeholder="イベント名、住所、キーワードを入力"
            value={searchParams.filters?.keyword || ''}
            readOnly
            className="cursor-pointer"
            showClearButton={!!searchParams.filters?.keyword}
            onClear={handleClearKeyword}
          />
        </div>
        <Button
          onClick={openFilterSlidePage}
          variant="icon"
          className="flex flex-col items-center gap-0"
          type="button"
        >
          <SlidersHorizontal className="w-6 h-6" />
          <span className="text-[10px] font-normal h-4">絞り込み</span>
        </Button>
      </div>

      <div className="p-6 flex flex-col gap-4">
        {/* 検索結果 */}
        <EventSearchResult searchEventsData={searchEventsData} searchParams={searchParams} />
      </div>
    </div>
  );
}
