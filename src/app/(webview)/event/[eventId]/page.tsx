'use client';
import { eventAPI } from '@/api/modules/event-api';
import { Button } from '@/components/shared/button';
import MapComponent from '@/components/shared/map/map';
import { useGeolocation } from '@/hooks/use-geolocation';

import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { useEventStore } from '@/store/event-store';
import type { EventDetailResponse } from '@/types/event-types';
import { type Connection, type Marker, MarkerType } from '@/types/map';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeftIcon } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import EventDetailDrawer from './_components/event-detail-drawer';
export default function EventDetailPage() {
  const searchParams = useSearchParams();
  const isFromHome = searchParams.get('from') === 'home';
  const { eventId } = useParams();
  const router = useRouter();
  const safeArea = useSafeArea();
  const backButtonTop = safeArea.top + 16;
  const { location } = useGeolocation();
  const latitude = location?.lat.toFixed(6) || '35.681236';
  const longitude = location?.lng.toFixed(6) || '139.767125';
  const { eventRefreshTimeStamp } = useEventStore();

  const [showEventDetail, setShowEventDetail] = useState(true);
  const [showEventBanner, setShowEventBanner] = useState(true);

  useEffect(() => {
    if (showEventDetail) {
      document.body.style.overflow = 'hidden';
      document.body.style.height = '100vh';
      // clear vaul pointer events
      setTimeout(() => {
        document.body.style.pointerEvents = '';
      }, 200);
    } else {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
      document.body.style.height = 'auto';
    };
  }, [showEventDetail]);

  const { data: eventDetailData } = useQuery({
    queryKey: ['eventDetail', eventId, eventRefreshTimeStamp],
    queryFn: () => eventAPI.getEventDetail(eventId as string, latitude, longitude),
    enabled: !!eventId,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false,
  });

  const markers = useMemo(() => {
    const spot: Marker = {
      id: eventId as string,
      name: eventDetailData?.eventInfo?.eventName || '',
      type: MarkerType.EVENT,
      address: eventDetailData?.eventInfo?.address || '',
      latitude: eventDetailData?.eventInfo?.latitude || '',
      longitude: eventDetailData?.eventInfo?.longitude || '',
      imagePath: eventDetailData?.eventInfo?.eventImageFilePath,
    };
    const list: Marker[] = [spot];

    return list;
  }, [eventDetailData]);

  const lines = useMemo(() => {
    return [];
  }, [eventDetailData]);

  const handleClickMarker = (marker: Marker) => {
    setShowEventDetail(true);
    console.log(marker);
  };

  return (
    <div className="relative h-[100vh]">
      <Button
        onClick={() => {
          if (isFromHome) {
            router.backTo('/home');
          } else {
            router.backTo('/event');
          }
        }}
        variant="secondary"
        size="sm"
        style={{ top: backButtonTop, boxShadow: '4px 8px 16px 0px rgba(0, 0, 0, 0.25)' }}
        className="absolute left-4 z-[1] px-6 gap-0"
      >
        <ChevronLeftIcon className="h-[22px] w-[22px]" />
        前の画面に戻る
      </Button>
      <div
        className={cn(
          'transition-all duration-300 h-full w-full',
          showEventDetail ? 'translate-y-[-25%]' : 'translate-y-0',
        )}
      >
        <MapComponent
          markers={markers}
          lines={lines}
          onMarkerClick={(marker: Marker) => {
            handleClickMarker(marker);
          }}
        />
      </div>
      {/* Event Detail */}
      <EventDetailDrawer
        eventDetailData={eventDetailData}
        show={showEventDetail}
        setShowEventDetail={setShowEventDetail}
      />
    </div>
  );
}
