import { eventAPI } from '@/api/modules/event-api';
import { EventLabelList } from '@/app/(webview)/event/_components/event-label-list';
import { EventStampCard } from '@/app/(webview)/event/_components/event-stamp-card';
import { DetailPointCard } from '@/app/(webview)/event/_components/point-card';
import { getEventStatus, isEventActive } from '@/app/(webview)/event/_utils/event-helper';
import { Button } from '@/components/shared/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from '@/components/shared/drawer';
import { FavoriteToggle } from '@/components/shared/favorite';
import QaIcon from '@/components/shared/qa-icon';
import { type QaData, QaPage } from '@/components/shared/qa-page';
import Link from '@/components/shared/router-link';
import { ScrollArea } from '@/components/shared/scroll-area';

import { codeTypeList, useCodeOptions } from '@/hooks/use-common-code';

import { useSafeArea } from '@/hooks/use-safe-area';
import { useSlidePage } from '@/hooks/use-slide-page';
import { getOptionLabel } from '@/lib/utils';

import { useEventStore } from '@/store/event-store';
import type { EventDetailResponse } from '@/types/event-types';
import { formatDate } from '@/utils/date-format';
import { distanceFormat } from '@/utils/distance-format';
import { sendMessageToNative } from '@/utils/native-bridge';
import { phoneNumberFormat } from '@/utils/phone-number-format';
import { handleQrScan, parseQrCode } from '@/utils/qrcode';
import { ScanQrCode, X } from 'lucide-react';

import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

interface EventDetailDrawerProps {
  eventDetailData?: EventDetailResponse;
  show: boolean;
  setShowEventDetail: (show: boolean) => void;
}

const snapPoints = [0.5, 0.7, 1];

function EventPointContent() {
  return (
    <div>
      <p className="text-gray-700 leading-relaxed text-base">
        ポイント付与のあるイベントでは、会場または施設内にチェックイン用の二次元バーコードを設置しています。二次元バーコードを読み取るとスタンプが押され、ポイントが獲得できます。
      </p>
      <div className="w-[327px] h-[186px] overflow-hidden mt-2">
        <img
          src="/images/walking-course/sample-detail.png"
          alt="例"
          className="w-full h-full object-cover"
        />
      </div>
      <p className="text-gray-700 leading-relaxed text-base font-bold mt-6">ポイント付与の条件</p>
      <p className="text-gray-700 leading-relaxed text-base mt-2">
        ポイント付与条件はイベントごとに異なります。各イベントの詳細画面をご確認ください。
      </p>
    </div>
  );
}

function EventStampPushContent() {
  return (
    <div>
      <p className="text-gray-700 leading-relaxed text-base">
        イベント詳細画面に表示される「スタンプを獲得する」ボタンを押下すると、二次元バーコードを読み取る画面が表示されます。会場または施設内に設置されている二次元バーコードを枠内に入れてください。
      </p>{' '}
      <p className="text-muted-foreground leading-relaxed text-sm mt-2">
        ※二次元バーコードがうまく読み取れない場合は、イベントスタッフにお尋ねください。
      </p>
      <div className="w-[327px] h-[186px] overflow-hidden mt-2">
        <img
          src="/images/walking-course/sample-detail.png"
          alt="例"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
}

function EventReserveContent() {
  return (
    <div>
      <p className="text-gray-700 leading-relaxed text-base">
        予約方法・参加費用についてはイベント詳細画面のイベント概要欄に記載されています。
      </p>{' '}
      <p className="text-muted-foreground leading-relaxed text-sm mt-2">
        ※イベントに関するお問い合わせはイベント詳細画面に記載しているお問い合わせ先へお問い合わせください。
      </p>
      <div className="w-[327px] h-[186px] overflow-hidden mt-2">
        <img
          src="/images/walking-course/sample-detail.png"
          alt="例"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
}

const qaData: QaData[] = [
  {
    id: 'event-point',
    title: 'ポイントについて',
    content: <EventPointContent />,
  },
  {
    id: 'event-stamp-push',
    title: 'スタンプの押し方',
    content: <EventStampPushContent />,
  },
  {
    id: 'event-reserve',
    title: '予約方法・参加費用について',
    content: <EventReserveContent />,
  },
];

export default function EventDetailDrawer({
  eventDetailData,
  show,
  setShowEventDetail,
}: EventDetailDrawerProps) {
  const [snap, setSnap] = useState<number | string | null>(snapPoints[0]);
  const { setSlidePage } = useSlidePage();
  const safeArea = useSafeArea();
  const top = useMemo(() => safeArea.top + 72 + 53 + 16, [safeArea]);
  const eventInfo = eventDetailData?.eventInfo;

  return (
    <Drawer
      open={show}
      onOpenChange={setShowEventDetail}
      snapPoints={snapPoints}
      activeSnapPoint={snap}
      setActiveSnapPoint={setSnap}
    >
      <DrawerContent hideOverlay={true}>
        <DrawerTitle className="sr-only">{eventInfo?.eventName || '読み込み中...'}</DrawerTitle>
        <DrawerDescription className="sr-only">
          Set your daily activity goal.asdas
        </DrawerDescription>
        <div className="flex items-center h-[86px] shrink-0 px-6 relative">
          <div
            className={`absolute left-0 right-0 overflow-hidden rounded-t-3xl transition-all duration-300 ease-in-out ${
              snap === 0.5
                ? 'h-[80px] top-[-80px]'
                : snap === 0.7
                  ? 'h-[250px] top-[-250px]'
                  : snap === 0 || snap === 1
                    ? 'h-0 top-0'
                    : 'h-[250px] top-[-250px]'
            }`}
          >
            <img
              src={eventInfo?.eventImageFilePath || '/images/event/default-event.svg'}
              onError={(e) => {
                // 画像の読み込みに失敗した場合の処理
                e.currentTarget.src = '/images/event/default-event.svg';
              }}
              alt=""
              className="w-full h-full object-cover object-center"
            />
          </div>
          <h1 className="text-[22px] font-bold flex-1">
            {eventInfo?.eventName || '読み込み中...'}
          </h1>
          <Button
            aria-label="friend tips"
            size="xs"
            variant="icon"
            onClick={() => {
              setSlidePage(true, {
                title: 'イベントについて',
                isOverAll: true,
                content: <QaPage data={qaData} />,
                enableClose: false,
                enableBack: true,
                slideFrom: 'right',
              });
            }}
          >
            <QaIcon />
          </Button>
          <Button
            size="xs"
            className=" ml-4 relative"
            variant="icon"
            onClick={() => {
              setShowEventDetail(false);
              setSnap(snapPoints[0]);
            }}
          >
            <X size={24} />
          </Button>
        </div>
        <ScrollArea
          disable={snap !== 1}
          className="w-full"
          style={{ height: `calc(100vh - ${top}px)` }}
          type="hover"
        >
          {eventInfo && <EventDetailView data={eventDetailData} />}
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
}

function EventDetailView({ data }: { data: EventDetailResponse }) {
  const { eventOptions, setEventRefreshTimeStamp } = useEventStore();
  const eventFeeOptions = useCodeOptions(codeTypeList.EVENT_SEARCH_FEE);
  const eventPointOptions = useCodeOptions(codeTypeList.EVENT_SEARCH_POINT);
  const eventTypeOptions = eventOptions?.eventTypeOptions;
  const eventInfo = data.eventInfo;
  const stampList = data.stampList;
  const router = useRouter();

  const handleAttendEvent = () => {
    const eventId = eventInfo.eventId;
    handleQrScan({ eventId }, router);
    setEventRefreshTimeStamp(new Date().getTime().toString());
  };

  return (
    <div className="p-6 flex flex-col gap-3">
      <div className="flex ">
        <div className="flex-1">
          <EventLabelList
            eventStartDate={eventInfo.eventStartDate}
            eventEndDate={eventInfo.eventEndDate}
            hasFee={eventInfo.hasFee}
            isReservationRequired={eventInfo.isReservationRequired}
          />
        </div>
        <FavoriteToggle
          targetId={eventInfo.eventId}
          favoriteType="1"
          initialIsFavorited={eventInfo.isFavorite}
          onToggle={() => {
            // setEventRefreshTimeStamp(new Date().getTime().toString());
          }}
        />
      </div>
      <h1 className="text-muted-foreground">
        <p>
          <span>{distanceFormat(eventInfo.distance)}</span> <span>・</span>
          <span>{eventInfo.venueName}</span>
        </p>
        <p>
          {eventInfo.eventStartDate === eventInfo.eventEndDate ? (
            formatDate(eventInfo.eventStartDate, 'yyyy年MM月dd日(d)')
          ) : (
            <>
              {formatDate(eventInfo.eventStartDate, 'yyyy年MM月dd日(d)')}~{' '}
              {eventInfo.eventEndDate
                ? formatDate(eventInfo.eventEndDate, 'yyyy年MM月dd日(d)')
                : '終了日未定'}
            </>
          )}
        </p>
        <p>{eventInfo.eventDatetime}</p>
        <p>
          {getOptionLabel(eventTypeOptions, eventInfo.type1)}
          {eventInfo.type2 && (
            <>
              <span>・</span>
              {getOptionLabel(eventTypeOptions, eventInfo.type2)}
            </>
          )}
        </p>
      </h1>

      <DetailPointCard
        pointPattern={eventInfo.pointPattern}
        pointPatternDetails={eventInfo.pointPatternDetails}
        organizerName={eventInfo.organizerName}
      />

      <div className="mt-3">
        <h2 className="font-bold text-xl mb-2">イベント概要</h2>
        <p>{eventInfo.eventDescription}</p>
      </div>

      <div className=" mt-3 bg-primary-5 rounded-2xl p-6 flex flex-col gap-3">
        <div className="flex flex-col gap-1">
          <h2 className="font-bold">開催期間</h2>
          <p>
            {eventInfo.eventStartDate === eventInfo.eventEndDate ? (
              formatDate(eventInfo.eventStartDate, 'yyyy年MM月dd日(d)')
            ) : (
              <>
                {formatDate(eventInfo.eventStartDate, 'yyyy年MM月dd日(d)')}~{' '}
                {eventInfo.eventEndDate
                  ? formatDate(eventInfo.eventEndDate, 'MM月DD日(d)')
                  : '終了日未定'}
              </>
            )}
          </p>
        </div>
        <div className="flex flex-col gap-1">
          <h2 className="font-bold">開催場所</h2>
          <p>{eventInfo.venueName}</p>
        </div>
        {eventInfo.capacity && (
          <div className="flex flex-col gap-1">
            <h2 className="font-bold">定員</h2>
            <p>{eventInfo.capacity}</p>
          </div>
        )}
        {eventInfo.hasFee && (
          <div className="flex flex-col gap-1">
            <h2 className="font-bold">参加費</h2>
            <p>{eventInfo.hasFee ? '無料' : '有料'}</p>
          </div>
        )}

        <div className="flex flex-col gap-1">
          <h2 className="font-bold">種別</h2>
          <div>
            <p>{getOptionLabel(eventTypeOptions, eventInfo.type1)}</p>
            <p>{getOptionLabel(eventTypeOptions, eventInfo.type2)}</p>
          </div>
        </div>
        {eventInfo.eventDetailsUrl && (
          <div className="flex flex-col gap-1">
            <h2 className="font-bold">関連リンク</h2>
            <p>
              <Link
                className="text-primary"
                onClick={() => {
                  sendMessageToNative({
                    type: 'start-other-link',
                    data: {
                      title: '関連リンク',
                      link: eventInfo.eventDetailsUrl,
                      callbackLink: '',
                      returnLink: '',
                      enableClose: '1',
                    },
                    callback: (data) => {},
                  });
                }}
                href={eventInfo.eventDetailsUrl}
              >
                {eventInfo.eventDetailsUrl}
              </Link>
            </p>
          </div>
        )}
        <div className="flex flex-col gap-1">
          <h2 className="font-bold">お問い合わせ先</h2>

          <p>{phoneNumberFormat(eventInfo.publicPhoneNumber || '')}</p>
          <p>
            <Link target="_blank" className="text-primary" href={`mailto:${eventInfo.publicEmail}`}>
              {eventInfo.publicEmail}
            </Link>
          </p>
        </div>
      </div>

      <div className="mt-3 flex flex-col gap-6 ">
        <h2 className="font-bold text-xl">スタンプ</h2>
        <EventStampCard showTitle={false} stampList={stampList} eventInfo={eventInfo} />
        <p>
          「イベントに参加する」ボタンを押して会場にある二次元バーコードを読み取ってください。1回の参加ごとにスタンプが貯まります。
        </p>
      </div>

      {isEventActive(eventInfo.eventStartDate, eventInfo.eventEndDate) ? (
        <Button className="w-full my-3" onClick={handleAttendEvent}>
          <ScanQrCode className="w-5 h-5" />
          イベントに参加する
        </Button>
      ) : null}
    </div>
  );
}
