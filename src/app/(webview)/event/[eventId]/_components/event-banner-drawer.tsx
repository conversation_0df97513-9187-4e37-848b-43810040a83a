import { Button } from '@/components/shared/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from '@/components/shared/drawer';
import QaIcon from '@/components/shared/qa-icon';
import { type QaData, QaPage } from '@/components/shared/qa-page';
import { ScrollArea } from '@/components/shared/scroll-area';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useSlidePage } from '@/hooks/use-slide-page';
import type { EventDetail } from '@/types/event-types';
import { ChevronRightIcon, X } from 'lucide-react';
import { useState } from 'react';
const snapPoints = [0.7, 1];
export default function EventBannerDrawer({
  image = '',
  show,
  setShowEventBanner,
}: {
  image?: string;
  show: boolean;
  setShowEventBanner: (show: boolean) => void;
}) {
  const [snap, setSnap] = useState<number | string | null>(snapPoints[0]);
  const { top } = useSafeArea();
  return (
    <Drawer
      open={show}
      onOpenChange={setShowEventBanner}
      snapPoints={snapPoints}
      activeSnapPoint={snap}
      setActiveSnapPoint={setSnap}
    >
      <DrawerContent hideOverlay={true}>
        <ScrollArea
          disable={snap !== 1}
          className="w-full"
          style={{ height: `calc(100vh - ${top}px)` }}
          type="hover"
        >
          <img
            src={image}
            onError={(e) => {
              // 画像の読み込みに失敗した場合の処理
              e.currentTarget.src = '/images/event/event-banner.png';
            }}
            alt=""
            height={250}
            className="w-full h-full object-cover"
          />
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
}
