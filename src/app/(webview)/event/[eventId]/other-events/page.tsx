'use client';
import { eventAPI } from '@/api/modules/event-api';
import { Button } from '@/components/shared/button';
import MapComponent from '@/components/shared/map/map';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { type Marker, MarkerType } from '@/types/map';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeftIcon } from 'lucide-react';
import { useParams, useSearchParams } from 'next/navigation';
import { useMemo, useState } from 'react';
import OtherEventsDrawer from '../_components/other-events-drawer';

export default function OtherEventsPage() {
  const { eventId } = useParams();

  const { location } = useGeolocation();
  const latitude = location?.lat.toFixed(6) || '35.681236';
  const longitude = location?.lng.toFixed(6) || '139.767125';

  const router = useRouter();
  const safeArea = useSafeArea();
  const backButtonTop = safeArea.top + 16;

  const { data: eventsData } = useQuery({
    queryKey: ['events', eventId],
    queryFn: () =>
      eventAPI.searchEvents({
        sortType: 'distance',
        latitude: latitude as string,
        longitude: longitude as string,
        filters: {
          distance: 2000,
        },
      }),
    enabled: !!eventId,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false,
  });

  const [showOtherEvents, setShowOtherEvents] = useState(true);

  const markers = useMemo(() => {
    const list: Marker[] | undefined =
      eventsData?.eventList
        ?.filter((spot) => spot.eventId !== eventId)
        ?.map((spot) => ({
          id: spot.eventId,
          name: spot.eventName,
          type: MarkerType.EVENT,
          address: spot.address,
          latitude: spot.latitude,
          longitude: spot.longitude,
          imagePath: spot.eventImageFilePath,
          isCurrent: spot.eventId === eventId,
        })) || [];

    console.log(list);

    return list;
  }, [eventsData]);

  const lines = useMemo(() => {
    return [];
  }, [eventsData]);

  const handleClickMarker = (marker: Marker) => {
    console.log(marker);
  };

  return (
    <div className="relative h-[100vh]">
      <Button
        onClick={() => router.back()}
        variant="secondary"
        size="sm"
        style={{ top: backButtonTop, boxShadow: '4px 8px 16px 0px rgba(0, 0, 0, 0.25)' }}
        className="absolute left-4 z-[1] px-6 gap-0"
      >
        <ChevronLeftIcon className="h-[22px] w-[22px]" />
        前の画面に戻る
      </Button>
      <div
        className={cn(
          'transition-all duration-300 h-full w-full',
          showOtherEvents ? 'translate-y-[-25%]' : 'translate-y-0',
        )}
      >
        <MapComponent
          markers={markers}
          lines={lines}
          onMarkerClick={(marker: Marker) => {
            handleClickMarker(marker);
          }}
        />
      </div>
      {/* Other Events */}
      <OtherEventsDrawer
        eventList={eventsData?.eventList?.filter((event) => event.eventId !== eventId)}
        show={showOtherEvents}
        setShowOtherEvents={setShowOtherEvents}
      />
    </div>
  );
}
