'use client';

import { Check } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useState } from 'react';

// 定义主题配置
interface Theme {
  id: string;
  name: string;
  class: string;
  theme: {
    background: string;
    foreground: string;
    sidebarBackground: string;
    sidebarForeground: string;
    primary: string;
    border: string;
    card: string;
  };
}

const themes: Theme[] = [
  {
    id: 'light',
    name: 'Light-Blue',
    class: 'theme-blue-light',
    theme: {
      background: '229 57% 100%',
      foreground: '229 63% 4%',
      sidebarBackground: '229 57% 100%',
      sidebarForeground: '229 63% 4%',
      primary: '229 100% 62%',
      border: '220 13% 91%',
      card: '0 0% 99%',
    },
  },
  {
    id: 'dark',
    name: 'Dark-Gold',
    class: 'theme-gold-dark',
    theme: {
      background: '233 18% 19%',
      foreground: '173 24% 93%',
      sidebarBackground: '233 18% 16%',
      sidebarForeground: '173 24% 93%',
      primary: '44 100% 71%',
      border: '233 8% 24%',
      card: '233 18% 17%',
    },
  },
  {
    id: 'theme-stripe',
    name: 'Stripe',
    class: 'theme-stripe',
    theme: {
      background: '210 40% 96.08%',
      foreground: '334 55% 1%',
      sidebarBackground: '334 62% 100%',
      sidebarForeground: '240 5.3% 26.1%',
      primary: '242.93 100% 67.84%',
      border: '334 5% 95%',
      card: '334 62% 100%',
    },
  },

  {
    id: 'theme-mira',
    name: 'Mira',
    class: 'theme-mira',
    theme: {
      background: '220 33% 98%',
      foreground: '220 20% 20%',
      sidebarBackground: '224 71% 4%',
      sidebarForeground: '213 31% 91%',
      primary: '221 83% 53%',
      border: '220 13% 91%',
      card: '0 0% 100%',
    },
  },

  {
    id: 'theme-blue-dark',
    name: 'Dark-Blue',
    class: 'theme-blue-dark',
    theme: {
      background: '229 41% 4%',
      foreground: '229 23% 99%',
      sidebarBackground: '229 41% 5%',
      sidebarForeground: '0 0% 80%',
      primary: '229 100% 62%',
      border: '215 27.9% 16.9%',
      card: '229 41% 5%',
    },
  },
  {
    id: 'theme-netease-music',
    name: 'Netease',
    class: 'theme-netease-music',
    theme: {
      background: '240 15.56% 8.82%',
      foreground: '30 22.22% 96.47%',
      sidebarBackground: '240 11.86% 11.57%',
      sidebarForeground: '240 1.18% 66.67%',
      primary: '350.05 96.98% 60.98%',
      border: '240 15.56% 8.82%',
      card: '233 18% 17%',
    },
  },
  // 备用主题
  // {
  //   id: 'theme-backup',
  //   name: '备用',
  //   class: 'theme-backup',
  //   theme: {
  //     background: '',
  //     foreground: '',
  //     sidebarBackground: '',
  //     sidebarForeground: '',
  //     primary: '',
  //     border: '',
  //     card: '',
  //   },
  // },
];

// 将HSL字符串转换为CSS颜色
const hslToColor = (hsl: string) => {
  return `hsl(${hsl})`;
};

// 主题卡片组件
const ThemeCard = ({
  item,
  isSelected,
  onClick,
}: {
  item: Theme;
  isSelected: boolean;
  onClick: () => void;
}) => {
  return (
    <div
      className="relative cursor-pointer p-2 rounded hover:bg-muted/50 border-border border"
      onClick={onClick}
      onKeyDown={onClick}
    >
      <div className="flex flex-col items-center">
        <div className="w-full h-16 mb-2 rounded overflow-hidden border border-border flex">
          {/* 侧边栏部分 */}
          <div
            className="w-1/3 h-full relative"
            style={{
              backgroundColor: hslToColor(item.theme.sidebarBackground),
              borderRight: `1px solid ${hslToColor(item.theme.border)}`,
            }}
          >
            <div
              className="w-3/4 h-2 mt-2 mx-auto rounded-sm"
              style={{ backgroundColor: hslToColor(item.theme.primary) }}
            />
            <div
              className="w-3/4 h-1 mt-2 mx-auto rounded-sm"
              style={{
                backgroundColor: hslToColor(item.theme.sidebarForeground),
              }}
            />
            <div
              className="w-3/4 h-1 mt-1 mx-auto rounded-sm"
              style={{
                backgroundColor: hslToColor(item.theme.sidebarForeground),
              }}
            />
          </div>
          {/* 主内容区域 */}
          <div
            className="w-2/3 h-full flex items-center justify-center"
            style={{ backgroundColor: hslToColor(item.theme.background) }}
          >
            <div
              className="w-3/4 h-6 rounded-sm"
              style={{ backgroundColor: hslToColor(item.theme.card) }}
            />
          </div>
        </div>
        <span className="text-xs">{item.name}</span>
      </div>
      {isSelected && (
        <div className="absolute top-1 right-1 bg-primary text-primary-foreground rounded-full p-1">
          <Check className="h-3 w-3" />
        </div>
      )}
    </div>
  );
};

export default function ThemeSettingPage() {
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <div className="text-xs text-muted-foreground p-4">Choose your primary theme color</div>

      <div className="grid grid-cols-3 gap-1 p-2">
        {themes.slice(0, 6).map((item) => (
          <ThemeCard
            key={item.id}
            item={item}
            isSelected={theme === item.id}
            onClick={() => {
              setTheme(item.id);
              setIsOpen(false);
            }}
          />
        ))}
      </div>

      <div className="grid grid-cols-3 gap-1 p-2">
        {themes.slice(6, 9).map((item) => (
          <ThemeCard
            key={item.id}
            item={item}
            isSelected={theme === item.id}
            onClick={() => {
              setTheme(item.id);
              setIsOpen(false);
            }}
          />
        ))}
      </div>
    </div>
  );
}
