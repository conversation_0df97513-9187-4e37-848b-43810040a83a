'use client';
import TopBar from '@/components/layout/top-bar';
import RouterLink from '@/components/shared/router-link';
import SectionTitle from '@/components/shared/section-title';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import { ChevronDown } from 'lucide-react';
import MenuPanel from '../components/meu-panel';

export default function MenuSettingsPage() {
  const router = useRouter();
  const groups = [
    {
      name: '名古屋市',
    },
    {
      name: '札幌市',
    },
    {
      name: '〇〇市',
    },
  ];
  const getMenuSettingItems1 = (name: string) => {
    const MENU_SETTING_ITEMS1 = [
      {
        label: APP_TEXT.MENU.RELATED_LINK_INFO.CONTENT1(name),
        href: '',
        imgIcon: '/images/menu/icon-menu-terms.png',
        type: '1',
        onClick: () => {},
      },
    ];
    return MENU_SETTING_ITEMS1;
  };

  const getMenuSettingItems2 = (name: string) => {
    const MENU_SETTING_ITEMS2 = [
      {
        label: APP_TEXT.MENU.RELATED_LINK_INFO.CONTENT2(name),
        href: '',
        imgIcon: '/images/menu/icon-menu-terms.png',
        type: '1',
        onClick: () => {},
      },
      {
        label: APP_TEXT.MENU.RELATED_LINK_INFO.CONTENT3,
        href: '',
        imgIcon: '/images/menu/icon-menu-terms.png',
        type: '1',
        onClick: () => {},
      },
      {
        label: APP_TEXT.MENU.RELATED_LINK_INFO.CONTENT4,
        href: '',
        imgIcon: '/images/menu/icon-menu-terms.png',
        type: '1',
        onClick: () => {},
      },
    ];
    return MENU_SETTING_ITEMS2;
  };
  const handleBackClick = () => {
    router.push('/menu/settings');
  };

  return (
    <>
      <TopBar onBack={handleBackClick} title={APP_TEXT.MENU.RELATED_LINK} />
      <div className="flex flex-col items-start space-y-4 pl-6 pt-8 pb-2 mb-4">
        {groups.map((item, index) => (
          <RouterLink
            key={index}
            href={`#${item.name}`}
            onClick={(e) => {
              e.preventDefault();
              document
                .getElementById(item.name)
                ?.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }}
            className="flex items-center text-[15px] text-[#4457D1] font-bold"
          >
            {item.name}
            <ChevronDown size={18} color="#4457D1" />
          </RouterLink>
        ))}
      </div>
      <div>
        {groups.map((item, index) => (
          <div key={index} id={item.name} className="scroll-mt-16">
            <SectionTitle className="text-[20px] pt-0 h-[30px]">{item.name}</SectionTitle>
            <MenuPanel
              title={APP_TEXT.MENU.RELATED_LINK_INFO.TITLE_ONE(item.name)}
              menuItems={getMenuSettingItems1(item.name)}
              className="rounded-lg mt-2"
            />
            <MenuPanel
              title={APP_TEXT.MENU.RELATED_LINK_INFO.TITLE_TWO(item.name)}
              menuItems={getMenuSettingItems2(item.name)}
              className="rounded-lg mt-2 mb-8"
            />
            {index !== groups.length - 1 && (
              <div className="mx-6 border-b border-text-muted mb-8" />
            )}
          </div>
        ))}
      </div>
    </>
  );
}
