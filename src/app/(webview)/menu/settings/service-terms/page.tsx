'use client';
import { menuAPI } from '@/api/modules/menu';
import TopBar from '@/components/layout/top-bar';
import {
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/shared/drawer';
import { ScrollArea } from '@/components/shared/scroll-area';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { MenuItem } from '@/types/menu';
import { X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import MenuPanel from '../components/meu-panel';

export default function MenuSettingsPage() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<MenuItem>();
  const [menuSettingItems, setMenuSettingItems] = useState<MenuItem[]>([]);
  const { setLoading } = useLoading();
  const safeArea = useSafeArea();
  const top = useMemo(() => safeArea.top + 72 + 53 + 16, [safeArea]);

  useEffect(() => {
    const handleClick = async (item: MenuItem) => {
      setCurrentItem(item);
      setOpen(true);
    };

    const getMenuTerms = async () => {
      setLoading(true);
      try {
        const res = await menuAPI.getTerms();
        const converted: MenuItem[] = res.termList.map((item) => ({
          label: item.termName,
          href: item.termUrl,
          imgIcon: '/images/menu/icon-menu-terms.png',
          type: item.termType,
          onClick: handleClick,
        }));
        setMenuSettingItems(converted);
      } catch (error) {
        console.error('getMenuTerms error:', error);
      } finally {
        setLoading(false);
      }
    };
    getMenuTerms();
  }, []);

  const handleBackClick = () => {
    router.push('/menu/settings');
  };

  return (
    <>
      <TopBar onBack={handleBackClick} title={APP_TEXT.MENU.SERVICE_TERMS} />
      <div className="px-6 py-4">{APP_TEXT.MENU.SERVICE_TERMS_TIP}</div>
      <MenuPanel menuItems={menuSettingItems} className="rounded-lg" />
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent className="height-auto-important">
          <DrawerHeader className="relative">
            <DrawerTitle>{currentItem?.label}</DrawerTitle>
            <DrawerClose asChild>
              <button type="button" className="absolute right-5 top-3">
                <X size={24} />
              </button>
            </DrawerClose>
          </DrawerHeader>
          <ScrollArea className="w-full" style={{ height: `calc(100vh - ${top}px)` }} type="hover">
            {currentItem?.type === '2' ? (
              <iframe
                src={currentItem?.href}
                title={currentItem?.label}
                style={{ width: '100%', height: `calc(100vh - ${top}px)` }}
              />
            ) : (
              <div className="p-6">
                {/* biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>*/}
                <div dangerouslySetInnerHTML={{ __html: currentItem?.href || '' }} />
              </div>
            )}
            <div style={{ height: safeArea.bottom }} />
          </ScrollArea>
        </DrawerContent>
      </Drawer>
    </>
  );
}
