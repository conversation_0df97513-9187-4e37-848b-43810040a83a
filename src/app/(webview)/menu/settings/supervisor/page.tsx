'use client';
import TopBar from '@/components/layout/top-bar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';

export default function MenuSettingsPage() {
  const router = useRouter();
  const handleBackClick = () => {
    router.push('/menu/settings');
  };

  return (
    <div className="bg-[#FFFFFF]">
      <TopBar onBack={handleBackClick} title={APP_TEXT.MENU.SUPERVISOR} />
      <div className="p-6 pt-4 space-y-4">
        {APP_TEXT.MENU.SUPERVISOR_INFO.map((item, index) => (
          <Card key={index} className="bg-[#F6F8FF]">
            <CardHeader className="pb-0">
              <CardTitle>
                <div className="font-bold text-lg">{item.NAME}</div>
              </CardTitle>
              <CardDescription className="!mt-2 pb-4 border-b border-text-muted">
                <div>
                  <div className="mb-2 font-bold text-base space-y-2">
                    {item.CONTENT1.split('\n').map((line, index) => (
                      <div key={index}>
                        <div key={index}>{line}</div>
                      </div>
                    ))}
                  </div>
                  <div className="text-xs space-y-2">
                    {item.CONTENT2.split('\n').map((line, index) => (
                      <div key={index}>
                        <div key={index}>{line}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <ul className="ml-4 font-bold text-sm list-disc space-y-2 leading-normal">
                {item.SUBCONTENT1.split('\n').map((line, index) => (
                  <li key={index}>{line}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
