'use client';
import RouterLink from '@/components/shared/router-link';
import SectionTitle from '@/components/shared/section-title';
import { cn } from '@/lib/utils';
import type { MenuItem } from '@/types/menu';

interface SettingsMenuPanelProps {
  title?: string;
  className?: string;
  menuItems: MenuItem[];
}

export default function MenuPanel({ title, menuItems, className }: SettingsMenuPanelProps) {
  return (
    <>
      {title && <SectionTitle className="text-base pt-0 h-6 mt-6">{title}</SectionTitle>}
      <div className={cn('bg-card mx-6 rounded-2xl px-6', className)}>
        {menuItems.map((item, index) => (
          <div
            key={index}
            onClick={() => {
              item.onClick(item);
            }}
            className="flex items-center border-b border-text-muted last:border-b-0"
          >
            <div className="flex-1 items-center py-4 mr-3">
              <div className="text-base text-left">{item.label}</div>
              {item.subLabel}
            </div>
            <img src={item.imgIcon} alt="icon" className="w-6 h-6 text-gray-800" />
          </div>
        ))}
      </div>
    </>
  );
}
