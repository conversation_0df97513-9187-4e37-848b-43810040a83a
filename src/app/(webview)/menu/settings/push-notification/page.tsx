'use client';
import { menuAPI } from '@/api/modules/menu';
import TopBar from '@/components/layout/top-bar';
import { Switch } from '@/components/shared/switch';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import { ChevronRight } from 'lucide-react';
import { useEffect, useState } from 'react';

export default function MenuSettingsPage() {
  const router = useRouter();
  const [checked, setChecked] = useState(false);
  const [healthRecordCheckeds, setHealthRecordCheckeds] = useState(Array(5).fill(false));
  const [otherCheckeds, setOtherCheckeds] = useState(Array(8).fill(false));
  const [hasPermission, setPermission] = useState(true);

  useEffect(() => {
    fetchPushSendFlg();
  }, []);
  function checkNativePermission() {
    if (!checked) {
      //原生に権限があるかどうかを判断する
      sendMessageToNative({
        type: 'get-native-permission',
        data: { vitalType: 'push' },
        callback: (data) => {
          nlog(JSON.stringify(data));
          setPermission(data?.hasPermission === 1);
          // if (data?.hasPermission) {
          //   nlog(JSON.stringify(data?.hasPermission));
          //   setPermission(data?.hasPermission === 1);
          // }
        },
      });
    } else {
      setPermission(true);
    }
  }
  const fetchPushSendFlg = () => {
    menuAPI.getPushFlg().then((res) => {
      if (res.pushSendFlg) {
        setChecked(res.pushSendFlg === 1);
        checkNativePermission();
      } else {
        setChecked(false);
      }
    });
  };
  const sendPushSendFlg = () => {
    menuAPI.pushSendFlg({ pushSendFlg: checked ? '0' : '1' }).then((res) => {
      if (res) {
        setChecked(!checked);
        //スイッチがオンの時だけ表示されます。
        checkNativePermission();
      }
    });
  };

  const handleBackClick = () => {
    router.push('/menu/settings');
  };

  const healthRecordItems = [
    { label: APP_TEXT.MENU.STEP_COUNT },
    { label: APP_TEXT.MENU.BODY_WEIGHT },
    { label: APP_TEXT.MENU.SLEEP_TIME },
    { label: APP_TEXT.MENU.BLOOD_PRESSURE },
    { label: APP_TEXT.MENU.BLOOD_SUGAR_LEVEL },
  ];

  const otherItems = [
    { label: APP_TEXT.MENU.HEALTH_SCORE, subTitle: APP_TEXT.MENU.HEALTH_SCORE_SUBTITLE },
    { label: APP_TEXT.MENU.WALKING_COURSE, subTitle: APP_TEXT.MENU.WALKING_COURSE_SUBTITLE },
    { label: APP_TEXT.MENU.EVENT, subTitle: APP_TEXT.MENU.EVENT_SUBTITLE },
    { label: APP_TEXT.MENU.COUPON, subTitle: APP_TEXT.MENU.COUPON_SUBTITLE },
    { label: APP_TEXT.MENU.MISSION, subTitle: APP_TEXT.MENU.MISSION_SUBTITLE },
    { label: APP_TEXT.MENU.PHOTO_SUBMISSION, subTitle: APP_TEXT.MENU.PHOTO_SUBMISSION_SUBTITLE },
    { label: APP_TEXT.MENU.FRIEND, subTitle: APP_TEXT.MENU.FRIEND_SUBTITLE },
    { label: APP_TEXT.MENU.LOTTERY, subTitle: APP_TEXT.MENU.LOTTERY_SUBTITLE },
  ];

  const handleClick = () => {
    sendMessageToNative({
      type: 'open-native-setting',
      data: { vitalType: 'push' },
      callback: (data) => {},
    });
  };

  return (
    <>
      <TopBar onBack={handleBackClick} title={APP_TEXT.MENU.PUSH_NOTIFICATION_SETTINGS} />
      {!hasPermission ? (
        <div className="pt-6 mx-6">
          <div className="flex items-center bg-[#FDF2F2] rounded-2xl px-4 py-3 gap-3 border border-[#CE0000]">
            <img src="/images/menu/icon-warning.png" alt="" className="w-6 h-6 flex-shrink-0" />
            <div className="flex-1 text-sm text-left">
              {APP_TEXT.MENU.PUSH_NOTIFICATION_WARNING}
            </div>
            <div className="w-6 h-6 flex-shrink-0">
              <ChevronRight onClick={handleClick} />
            </div>
          </div>
        </div>
      ) : (
        <></>
      )}
      <div className="pt-6 mx-6">
        <div className="flex bg-card rounded-2xl px-6 py-4">
          <div className="text-base text-left content-center flex-1">
            {APP_TEXT.MENU.PUSH_NOTIFICATION}
          </div>
          <div className="w-30 flex-none">
            <Switch checked={checked} onCheckedChange={sendPushSendFlg} />
          </div>
        </div>
      </div>
      {/* <div className="p-6 font-bold">{APP_TEXT.MENU.HEALTH_RECORD}</div>
      <div className="bg-card rounded-2xl mx-6 px-6">
        {healthRecordItems.map((item, index) => (
          <div key={index} className="flex border-b items-center border-text-muted last:border-b-0">
            <div className="text-base text-left content-center py-4 flex-1">{item.label}</div>
            <div className="w-30 flex-none">
              <Switch
                checked={healthRecordCheckeds[index]}
                onCheckedChange={(value) => {
                  setHealthRecordCheckeds((prev) => {
                    const updated = [...prev];
                    updated[index] = value;
                    return updated;
                  });
                }}
              />
            </div>
          </div>
        ))}
      </div>
      <div className="p-6 font-bold">{APP_TEXT.MENU.OTHER}</div>
      <div className="bg-card rounded-2xl mx-6 px-6">
        {otherItems.map((item, index) => (
          <div key={index} className="flex border-b items-center border-text-muted last:border-b-0">
            <div className="text-base text-left content-center py-4 flex-1">
              {item.label}
              <p className="text-sm text-[#666666]">
                <span>{item.subTitle}</span>
              </p>
            </div>
            <div className="w-30 flex-none">
              <Switch
                checked={otherCheckeds[index]}
                onCheckedChange={(value) => {
                  setOtherCheckeds((prev) => {
                    const updated = [...prev];
                    updated[index] = value;
                    return updated;
                  });
                }}
              />
            </div>
          </div>
        ))}
      </div> */}
      {!checked && (
        <p className="m-6 text-sm text-[#697077]">
          <span>{APP_TEXT.MENU.PUSH_NOTIFICATION_TIP}</span>
        </p>
      )}
    </>
  );
}
