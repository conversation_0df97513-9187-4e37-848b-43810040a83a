'use client';
import TopBar from '@/components/layout/top-bar';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';

export default function MenuSettingsPage() {
  const router = useRouter();
  const unirxContent = APP_TEXT.MENU.LICENSE_UNIRX_CONTENT.split('\n');
  const linqContent = APP_TEXT.MENU.LICENSE_LINQ_CONTENT.split('\n');
  const handleBackClick = () => {
    router.push('/menu/settings');
  };
  return (
    <>
      <TopBar onBack={handleBackClick} title={APP_TEXT.MENU.LICENSE} />
      <div className="bg-[#ffffff] px-6 py-4">
        <div className="font-bold">{APP_TEXT.MENU.LICENSE_UNIRX_TITLE} </div>
        <br />
        {unirxContent.map((line, index) => (
          <div key={index}>
            <div key={index}>{line}</div>
            <br />
          </div>
        ))}
        <div className="font-bold">{APP_TEXT.MENU.LICENSE_LINQ_TITLE} </div>
        <br />
        {linqContent.map((line, index) => (
          <div key={`LINQ${index}`}>
            <div key={`LINQ${index}`}>{line}</div>
            <br />
          </div>
        ))}
      </div>
    </>
  );
}
