import { Scroll<PERSON><PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import type { CouponListItem } from '@/types/coupon-types';
import RecommendationsCouponCard from './recommendations-coupon-card';

export default function CouponScrollArea({
  couponList,
  className,
  onCouponClick,
}: {
  couponList: CouponListItem[];
  className?: string;
  onCouponClick: (id: string) => void;
}) {
  return (
    <div>
      <ScrollArea>
        <div className="flex no-wrap gap-2 p-2">
          {couponList.map((item, index) => (
            <RecommendationsCouponCard
              key={index}
              coupon={item}
              className={className}
              onClick={() => onCouponClick(item.couponId)}
            />
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}
