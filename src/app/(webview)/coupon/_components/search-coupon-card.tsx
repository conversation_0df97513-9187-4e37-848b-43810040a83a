import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { APP_TEXT } from '@/const/text/app';
import type { SearchCouponListItem, SearchShopListItem } from '@/types/coupon-types';
import { DateFormatEnum, formatDate } from '@/utils/date-format';

export default function SearchCouponCard({
  coupon,
  className,
  onClick,
}: { coupon: SearchCouponListItem; className?: string; onClick: (id: string) => void }) {
  return (
    <Card
      className={`w-[276px] h-[122px] py-1 border rounded-xl shadow-card-base hover:shadow-card-hover transition-shadow cursor-pointer ${className}`}
      onClick={() => onClick(coupon.couponId)}
    >
      <div className="flex gap-1">
        {/* 固定サイズの画像 */}
        <div className="w-14 flex-shrink-0 flex flex-col gap-1 items-center pt-6 pb-2 pl-1">
          <div className="w-[40px] h-[40px] flex items-center justify-center rounded overflow-hidden">
            <img
              className="w-[40px] h-[40px] object-cover rounded"
              src={coupon.imagePath ?? '/images/coupon/default-coupon.svg'}
              alt={coupon.couponName}
              onError={(e) => {
                // 画像の読み込みに失敗した場合の処理
                e.currentTarget.src = '/images/coupon/default-coupon.svg';
              }}
            />
          </div>
        </div>
        <div className="flex-1 min-w-0 flex flex-col gap-1">
          <p className="font-bold mb-0 line-clamp-2 text-sm h-10">{coupon.couponName}</p>
          <div className="flex gap-1">
            <Badge variant="secondary" className="font-normal px-1">
              {APP_TEXT.COUPON.TIMES_PER_PERSON(coupon.usageLimitPerUser.toString())}
            </Badge>
          </div>
          <div className="flex gap-1 text-xs">
            {APP_TEXT.COUPON.EXPIRATION_DATE +
              formatDate(coupon.endDate, DateFormatEnum.DATE_YMD_JP)}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-0 text-xs ml-7 mt-1.5">
        <b className="font-bold text-primary"> {coupon.userCount || 0}</b>
        <span className="text-muted-foreground">{APP_TEXT.COUPON.COUPON_USED_PEOPLE}</span>
      </div>
    </Card>
  );
}
