'use client';

import { Button } from '@/components/shared/button';
import { ChipSelect } from '@/components/shared/chip-select';
import { Select } from '@/components/shared/select';
import { APP_TEXT } from '@/const/text/app';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import type { SearchCouponRequest } from '@/types/coupon-types';
import { useEffect, useState } from 'react';
import { useShopHelper } from '../shops/_utils/shop-helper';
interface CouponFilterFormProps {
  initialFilters?: SearchCouponRequest['filters'];
  onApplyFilters: (filters: SearchCouponRequest['filters']) => void;
  onClearFilters: () => void;
  onClose: () => void;
}

export default function CouponFilterForm({
  initialFilters,
  onApplyFilters,
  onClearFilters,
  onClose,
}: CouponFilterFormProps) {
  const router = useRouter();
  const pathname = usePathname();

  const { shopTypeOptions, distanceOptions, distanceToKey, keyToDistance } = useShopHelper();

  const [categories, setCategories] = useState<string[]>(initialFilters?.categories || []);
  const couponDistanceOptions = distanceOptions;

  const [distances, setDistances] = useState<string>(distanceToKey(initialFilters?.distance));

  const handleApplyFilters = () => {
    const filters: SearchCouponRequest['filters'] = { categories: [] };

    if (distances) filters.distance = keyToDistance(distances);
    if (categories.length >= 0) filters.categories = categories;

    if (pathname !== '/coupon/search-result') {
      onApplyFilters(filters);
      onClose();
      router.push('/coupon/search-result');
      return;
    }

    onApplyFilters(filters);
    onClose();
  };

  const handleClearFilters = () => {
    setDistances('');
    setCategories([]);
    onClearFilters();
    onClose();
  };

  return (
    <div className="bg-card">
      <div className="p-6 flex flex-col gap-6 pb-40">
        {/* 種別 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">{APP_TEXT.COUPON.CATEGORY}</h3>
          <ChipSelect
            options={shopTypeOptions}
            selectedValues={initialFilters?.categories || []}
            onChange={(categories) => {
              setCategories(categories);
            }}
          />
        </div>
        {/* 距離 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">{APP_TEXT.COUPON.DISTANCE_FROM_HERE}</h3>
          <Select
            defaultValue={distances || '0'}
            options={couponDistanceOptions.map((option) => ({
              value: option.value,
              name: option.label,
            }))}
            title={APP_TEXT.COUPON.DISTANCE_FROM_HERE}
            onChange={(value) => setDistances(value)}
            className="flex items-center justify-between h-12 px-4 border border-input rounded-md text-sm"
          />
        </div>

        <div className="fixed bottom-0 left-0 right-0 flex flex-col gap-4 p-4 bg-card border-t">
          <Button onClick={handleApplyFilters}>{APP_TEXT.COUPON.CONDITIONS_APPLY}</Button>
          <Button variant="outline" onClick={handleClearFilters}>
            {APP_TEXT.COUPON.CLEAR}
          </Button>
        </div>
      </div>
    </div>
  );
}
