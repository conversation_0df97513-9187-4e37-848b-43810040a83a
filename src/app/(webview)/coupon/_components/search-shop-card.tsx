import { Card } from '@/components/ui/card';
import { <PERSON>rollA<PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { APP_TEXT } from '@/const/text/app';
import type { SearchShopListItem } from '@/types/coupon-types';
import { distanceFormat } from '@/utils/distance-format';
import { MapPin } from 'lucide-react';
import { useShopHelper } from '../shops/_utils/shop-helper';
import SearchCouponCard from './search-coupon-card';

export default function SearchShopCard({
  shop,
  className,
  couponIconBar,
  onShopClick,
  onCouponClick,
}: {
  shop: SearchShopListItem;
  className?: string;
  couponIconBar?: string;
  onShopClick: () => void;
  onCouponClick: (id: string) => void;
}) {
  const { keyToShopType } = useShopHelper();
  return (
    <Card className={`py-2 border-b  rounded-none transition-shadow cursor-pointer ${className}`}>
      <div className="flex gap-2" onClick={onShopClick}>
        {/* 固定サイズの画像 */}
        <div className="w-16 flex-shrink-0 flex flex-col gap-1 items-center">
          <div className="w-[48px] h-[48px] flex items-center justify-center rounded overflow-hidden">
            <img
              className="w-[48px] h-[48px] object-cover rounded"
              src={shop.shopIconPath ?? '/images/coupon/default-shop.svg'}
              alt={shop.shopName}
              onError={(e) => {
                // 画像の読み込みに失敗した場合の処理
                e.currentTarget.src = '/images/coupon/default-shop.svg';
              }}
            />
          </div>
          <div className="flex items-center justify-center gap-0 text-muted-foreground text-xs">
            <MapPin className="w-3 h-3" />
            {distanceFormat(shop.distanceFromHere)}
          </div>
        </div>
        <div className="flex-1 min-w-0 flex flex-col gap-1">
          <p className="font-bold mb-0 line-clamp-3 text-base">{shop.shopName}</p>
          {couponIconBar && (
            <img
              className="w-[72px] h-[20px] mt-1 object-cover rounded"
              src={couponIconBar ?? '/images/coupon/default-coupon.svg'}
              alt=""
            />
          )}
          <p className="mb-0 line-clamp-0 text-sm">{keyToShopType(shop.shopType)}</p>
        </div>
      </div>

      <div className="flex-1 min-w-0 flex flex-col gap-0 mt-3 ml-2">
        <p className="mb-0 line-clamp-0 text-sm">
          {APP_TEXT.COUPON.CURRENT_COUPON}({shop.couponList.length})
        </p>
        <ScrollArea>
          <div className="flex no-wrap gap-2 p-2">
            {shop.couponList.map((item, index) => (
              <SearchCouponCard
                key={index}
                coupon={item}
                onClick={() => onCouponClick(item.couponId)}
              />
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
    </Card>
  );
}
