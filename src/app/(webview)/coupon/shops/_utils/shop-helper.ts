import { codeTypeList, useCommonCode } from '@/hooks/use-common-code';

export function useShopHelper() {
  const commonCode = useCommonCode();
  const distanceOptions = commonCode.getOptions(codeTypeList.WALKING_COURSE_SEARCH_DISTANCE);
  const distanceToKey = (distanceFromHere: number | undefined | null) => {
    const codeList =
      commonCode.getType(codeTypeList.WALKING_COURSE_SEARCH_DISTANCE)?.codeList || [];
    const code = codeList.find((code) => code.metaData?.val === distanceFromHere);
    return code?.codeKey || '1';
  };
  const keyToDistance = (key: string) => {
    const numberValue = Number(
      commonCode.getRealValue(codeTypeList.WALKING_COURSE_SEARCH_DISTANCE, key),
    );
    return Number.isNaN(numberValue) || numberValue <= 0 ? undefined : numberValue;
  };

  const shopTypeOptions = commonCode.getOptions(codeTypeList.SHOP_TYPES);
  const shopTypeToKey = (shopType: string) => {
    const codeList = commonCode.getType(codeTypeList.SHOP_TYPES)?.codeList || [];
    const code = codeList.find((code) => code.codeName === shopType);
    return code?.codeKey || '1';
  };
  const keyToShopType = (key: string) => {
    const codeList = commonCode.getType(codeTypeList.SHOP_TYPES)?.codeList || [];
    const code = codeList.find((code) => code.codeKey === key);
    return code?.codeName || '';
  };

  return {
    distanceOptions,
    distanceToKey,
    keyToDistance,
    shopTypeOptions,
    shopTypeToKey,
    keyToShopType,
  };
}
