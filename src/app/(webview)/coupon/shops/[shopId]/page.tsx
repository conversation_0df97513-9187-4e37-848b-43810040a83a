'use client';

import { couponAPI } from '@/api/modules/coupon';
import MapComponent from '@/components/shared/map/map';
import { useMap } from '@/hooks/use-map';
import { useRouter } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import type { SearchCouponRequest } from '@/types/coupon-types';
import { type Connection, type Marker, MarkerType } from '@/types/map';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import ShopDetailDrawer from './_components/shop-detail-drawer';
import { StampSpotProvider, useStampSpotState } from './_context/use-stamp-spot-state';

function ShopDetailContent({ id }: { id: string }) {
  const { mapRef } = useMap();
  const router = useRouter();
  const {
    data: shopData,
    isLoading,
    isSuccess,
    error,
  } = useQuery({
    queryKey: ['shopDetail', id],
    queryFn: () => couponAPI.shopDetail(id),
    enabled: !!id,
  });

  useEffect(() => {
    if (isSuccess && shopData.shopInfo === undefined) {
      router.back();
    }
  }, [isSuccess]);

  const { showShopDetail, setShowShopDetail } = useStampSpotState();

  const [markers, setMarkers] = useState<Marker[]>([]);

  const handleClickMarker = (marker: Marker) => {
    console.log(marker);
    if (marker.id.toString() === id) {
      setShowShopDetail(true);
      return;
    }
    router.push(`/coupon/shops/${marker.id}`);
  };

  useEffect(() => {
    const fetchMarkers = async () => {
      if (!shopData) return;
      const params: SearchCouponRequest = {
        sortType: 'distance',
        latitude: shopData.shopInfo.latitude,
        longitude: shopData.shopInfo.longitude,
        filters: {
          distance: 5000,
          categories: [],
        },
      };

      try {
        const resp = await couponAPI.searchCoupon(params);
        const list: Marker[] =
          resp?.shopList?.map((shop) => ({
            id: shop.shopId,
            name: shop.shopName,
            type: MarkerType.COUPON,
            address: shop.address,
            latitude: shop.latitude,
            longitude: shop.longitude,
            imagePath: shop.shopIconPath,
            isCurrent: shop.shopId === id,
          })) || [];

        setMarkers(list);
      } catch (err) {
        console.log('Failed to fetch markers', err);
        setMarkers([]);
      }
    };
    fetchMarkers();
  }, [shopData]);

  const lines = useMemo(() => {
    const list: Connection[] | undefined = [];
    return list || [];
  }, [shopData]);

  return (
    <div className="relative h-[100vh]">
      <div
        className={cn(
          'transition-all duration-300 h-full w-full',
          showShopDetail ? 'translate-y-[-25%]' : 'translate-y-0',
        )}
      >
        <MapComponent
          markers={markers}
          lines={lines}
          onMarkerClick={(marker: Marker) => {
            handleClickMarker(marker);
          }}
        />
      </div>
      <ShopDetailDrawer
        show={showShopDetail}
        data={shopData?.shopInfo}
        onLeftIconClick={() => router.back()}
      />
    </div>
  );
}

export default function ShopDetailPage({ params }: { params: { shopId: string } }) {
  return (
    <StampSpotProvider>
      <ShopDetailContent id={params.shopId} />
    </StampSpotProvider>
  );
}
