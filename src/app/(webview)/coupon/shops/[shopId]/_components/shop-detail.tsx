'use client';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import type { ShopDetailResponse, shopInfo } from '@/types/coupon-types';
import { distanceFormat } from '@/utils/distance-format';
import { CircleHelp } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import ShopCouponCard from './shop-coupon-card';

import { X } from 'lucide-react';
import { useShopHelper } from '../../_utils/shop-helper';

export default function ShopDetail({
  shopDetail,
  handleSuperViewTop,
}: {
  shopDetail: shopInfo;
  handleSuperViewTop?: () => number;
}) {
  const router = useRouter();
  const titleRef = useRef<HTMLDivElement>(null);
  const [showStickyTitle, setShowStickyTitle] = useState(false);

  const { keyToShopType } = useShopHelper();

  useEffect(() => {
    const handleScroll = () => {
      if (titleRef.current) {
        const rect = titleRef.current.getBoundingClientRect();

        let superViewTop = 0;
        if (handleSuperViewTop) {
          superViewTop = handleSuperViewTop();
        }
        // 如果标题底部已经滚出顶部视口，就显示固定标题
        setShowStickyTitle(rect.bottom <= superViewTop);
      }
    };
    window.addEventListener('scroll', handleScroll, true);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleSuperViewTop]);

  // 店舗URL押下
  const handleClickRelatedLink = () => {
    if (shopDetail) {
      window.location.href = shopDetail?.relatedLink;
    }
  };

  // 閉じるボタン押下
  const handleCloseClick = () => {
    router.back();
  };

  const couponIconBar = '/images/coupon/coupon-icon-bar.png';

  return (
    <div className="pb-8">
      {showStickyTitle && (
        <div className="flex fixed w-full">
          <div className="w-full ml-1 pl-6 pr-6 pt-4 pb-2 bg-white ">
            <div className="font-bold line-clamp-1  mr-6 text-[21px]">{shopDetail?.shopName}</div>
          </div>
          <button
            type="button"
            className="flex flex-start mr-1 pr-4 pt-[16px] bg-white"
            onClick={handleCloseClick}
          >
            <X size={24} />
          </button>
        </div>
      )}

      <div className="flex gap-2">
        {/* 固定サイズの画像 */}
        <div className="w-[56px] h-[56px] flex items-center justify-center rounded overflow-hidden ml-[24px] mt-[16px]">
          <img
            className="w-[56px] h-[56px] object-cover rounded"
            src={shopDetail?.shopIconPath ?? '/images/coupon/default-shop.svg'}
            alt={shopDetail?.shopName}
            onError={(e) => {
              // 画像の読み込みに失敗した場合の処理
              e.currentTarget.src = '/images/coupon/default-shop.svg';
            }}
          />
        </div>
        <div ref={titleRef} className="flex-1 min-w-0 flex flex-col gap-1 mt-[16px] mr-[48px]">
          <p className="font-bold mb-0 line-clamp-2 text-[21px]">{shopDetail?.shopName}</p>
          <img
            className="w-[72px] h-[20px] mt-1 object-cover rounded"
            src={couponIconBar ?? '/images/coupon/default-coupon.svg'}
            alt=""
            onError={(e) => {
              // 画像の読み込みに失敗した場合の処理
              e.currentTarget.src = '/images/coupon/default-coupon.svg';
            }}
          />
          <p className="mb-0 line-clamp-0 text-sm text-muted-foreground">
            {shopDetail && distanceFormat(shopDetail.distanceFromHere)}・
            {keyToShopType(shopDetail.shopType)}
          </p>
        </div>

        <button type="button" className="flex flex-start pr-5 pt-[16px]" onClick={handleCloseClick}>
          <X size={24} />
        </button>
      </div>

      <div className="pl-6 pr-6 pt-6 pb-1">
        <div className="flex items-center font-bold text-[18px]">
          <span>
            {APP_TEXT.COUPON.CURRENT_COUPON}({shopDetail?.couponList?.length})
          </span>
          <CircleHelp
            size={22}
            className="text-gray-500 ml-1"
            onClick={() => {
              // TODO クーポンヘルプ画面へ遷移する
              // router.push(`/coupon/help`);
            }}
          />
        </div>

        {shopDetail && (
          <div>
            {shopDetail?.couponList?.map((item, index) => (
              <ShopCouponCard
                coupon={item}
                key={index}
                onClick={() => {
                  router.push(`/coupon/coupons/${item.couponId}`);
                }}
              />
            ))}
          </div>
        )}
      </div>

      <div className="pl-6 pr-6 pt-3 pb-2 font-bold text-[18px]">{APP_TEXT.COUPON.SHOP_INFO}</div>

      <div className="max-w-md rounded-lg shadow-md pl-6 pr-6 pt-4 pb-4 bg-[#F6F8FF] ml-[24px] mr-[24px] space-y-2 text-sm">
        <div className="font-bold">{APP_TEXT.COUPON.SHOP_ADDRESS}</div>
        <div className="text-gray-600">
          {`${shopDetail?.address}${shopDetail?.buildingName || ''}${shopDetail?.floor || ''}`}
        </div>
        <div className="font-bold">{APP_TEXT.COUPON.SHOP_PHONENUMBER}</div>
        <div className="text-gray-600">{shopDetail?.phoneNumber}</div>
        <div className="font-bold">{APP_TEXT.COUPON.SHOP_RELATEDLINK}</div>
        <button type="button" className="text-primary" onClick={handleClickRelatedLink}>
          {shopDetail?.relatedLink}
        </button>
      </div>
    </div>
  );
}
