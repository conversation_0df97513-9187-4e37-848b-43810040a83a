import { Button } from '@/components/shared/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from '@/components/shared/drawer';
import NoData from '@/components/shared/no-data';
import { type QaData, QaPage } from '@/components/shared/qa-page';
import { ScrollArea } from '@/components/shared/scroll-area';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { SearchCouponResponse, SearchShopListItem } from '@/types/coupon-types';
import { ChevronLeftIcon, X } from 'lucide-react';
import { useMemo, useState } from 'react';
import SearchShopCard from '../../_components/search-shop-card';
import { useStampSpotState } from '../[shopId]/_context/use-stamp-spot-state';

const snapPoints = [0.6, 1];

const qaData: QaData[] = [
  {
    id: 'shop-detail',
    title: 'shop詳細',
    content: 'shop詳細content',
  },
];

export default function ShopsNearbyDrawer({
  data,
  show,
  onLeftIconClick,
  onCouponClick,
}: {
  data?: SearchCouponResponse;
  show: boolean;
  onLeftIconClick: () => void;
  onCouponClick: (id: string) => void;
}) {
  const { setShowShopDetail } = useStampSpotState();
  const [snap, setSnap] = useState<number | string | null>(snapPoints[0]);
  const safeArea = useSafeArea();
  const top = useMemo(() => safeArea.top + 72 + 53 + 16, [safeArea]);

  return (
    <Drawer
      open={show}
      onOpenChange={setShowShopDetail}
      snapPoints={snapPoints}
      activeSnapPoint={snap}
      setActiveSnapPoint={setSnap}
    >
      <DrawerContent hideOverlay={true}>
        <DrawerTitle className="sr-only">
          {data ? APP_TEXT.COUPON.OTHER_AREA_COUPON : COMMON_TEXT.MESSAGE.LOADING}
        </DrawerTitle>
        <DrawerDescription className="sr-only">shop list</DrawerDescription>
        <div className="flex items-center h-[53px] shrink-0 px-6">
          <Button
            aria-label="course detail close"
            size="xs"
            className=" ml-0 relative"
            variant="icon"
            onClick={onLeftIconClick}
          >
            <ChevronLeftIcon className="h-[24px] w-[24px]" />
          </Button>
          <h1 className="text-[22px] font-bold flex-1 text-center">
            {data ? APP_TEXT.COUPON.OTHER_AREA_COUPON : COMMON_TEXT.MESSAGE.LOADING}
          </h1>

          <Button
            aria-label="course detail close"
            size="xs"
            className=" ml-4 relative"
            variant="icon"
            onClick={() => {
              setShowShopDetail(false);
              setSnap(snapPoints[0]);
            }}
          >
            <X size={24} />
          </Button>
        </div>
        <ScrollArea
          disable={snap !== 1}
          className="w-full"
          style={{ height: `calc(100vh - ${top}px)` }}
          type="hover"
        >
          <div className="flex flex-col gap-0 py-0">
            {data && data.shopList.length > 0 ? (
              data.shopList.map((shop: SearchShopListItem) => (
                <SearchShopCard
                  key={shop.shopId}
                  shop={shop}
                  couponIconBar={'/images/coupon/coupon-icon-bar.png'}
                  onShopClick={() => {}}
                  onCouponClick={onCouponClick}
                />
              ))
            ) : (
              <div className="text-center py-24">
                <NoData />
              </div>
            )}
          </div>
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
}
