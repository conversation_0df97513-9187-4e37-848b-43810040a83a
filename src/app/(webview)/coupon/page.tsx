'use client';
import { couponAPI } from '@/api/modules/coupon';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import NoData from '@/components/shared/no-data';
import { SearchInput } from '@/components/shared/search-input';
import { Select } from '@/components/shared/select';

import KeywordSearch from '@/components/shared/keyword-search';
import { APP_TEXT } from '@/const/text/app';
import { codeTypeList, useCommonCode } from '@/hooks/use-common-code';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useCouponStore } from '@/store/coupon-store';
import type { CouponListItem, SearchCouponRequest, SearchShopListItem } from '@/types/coupon-types';
import { SlidersHorizontal, Tent } from 'lucide-react';
import { useEffect, useState } from 'react';
import CouponFilter from './_components/coupon-filter';
import CouponScrollArea from './_components/coupon-scroll-area';
import SearchShopCard from './_components/search-shop-card';
import { SuggestionCouponIcon } from './_components/svg-icon';

export default function CouponPage() {
  const { setLoading } = useLoading();
  const router = useRouter();
  const pathname = usePathname();

  // Geolocation hook should be called at the top level
  const { location } = useGeolocation();

  const commonCode = useCommonCode();
  const sortOptions = commonCode.getOptions(codeTypeList.WALKING_COURSE_SEARCH_SORT);
  const sortToKey = (sortType: string) => {
    const codeList = commonCode.getType(codeTypeList.WALKING_COURSE_SEARCH_SORT)?.codeList || [];
    const code = codeList.find((code) => code.metaData?.val === sortType);
    return code?.codeKey || '1';
  };
  const keyToSort = (key: string) => {
    return (
      commonCode.getRealValue(codeTypeList.WALKING_COURSE_SEARCH_SORT, key)?.toString() || 'popular'
    );
  };

  const { setSlidePage } = useSlidePage();

  const { searchParams, setSearchParams, setGeolocation, updateSearchFilters } = useCouponStore();

  const [shopList, setShopList] = useState<SearchShopListItem[]>([]);

  const [recommendedCouponsData, setRecommendedCouponsData] = useState<CouponListItem[] | []>([]);
  const [popularCouponsData, setPopularCouponsData] = useState<CouponListItem[] | []>([]);

  const handleBackClick = () => {
    router.back();
  };
  const handleCloseClick = () => {
    router.back();
  };

  useEffect(() => {
    const fetchRecommendedCouponData = async () => {
      const data = await couponAPI.getRecommendations();
      if (data) {
        setRecommendedCouponsData(data.couponList || []);
      }
    };
    const fetchPopularCouponData = async () => {
      const data = await couponAPI.getPopularCoupon();
      if (data) {
        setPopularCouponsData(data.couponList || []);
      }
    };

    const searchShopsData = async () => {
      setLoading(true);
      try {
        if (!location) return;
        const data = await couponAPI.searchCoupon(searchParams);
        if (data) {
          setShopList(data.shopList || []);
        }
      } catch (error) {
        console.log('Failed to search coupon data:', error);
        // toast.error('Failed to search coupon data');
      } finally {
        setLoading(false);
      }
    };

    const fetchAllData = async () => {
      setLoading(true);
      try {
        if (!location) return;
        if (!searchParams.latitude) {
          setGeolocation(location);
          return;
        }
        await Promise.all([
          fetchRecommendedCouponData(),
          fetchPopularCouponData(),
          searchShopsData(),
        ]);
        setLoading(false);
      } catch (error) {
        console.log('Failed to fetch coupon data:', error);
        // toast.error('Failed to fetch coupon data');
        setLoading(false);
      }
    };

    fetchAllData();
  }, [searchParams]);

  // 处理筛选条件应用
  const handleApplyFilters = async (filters: SearchCouponRequest['filters']) => {
    updateSearchFilters(filters);
  };

  // 处理清除筛选条件
  const handleClearFilters = async () => {
    updateSearchFilters({ categories: [] });
  };

  // 处理关键词搜索
  const handleKeywordSearch = (keyword: string) => {
    if (pathname !== '/coupon/search-result') {
      const filters: SearchCouponRequest['filters'] = {
        ...searchParams.filters,
        keyword,
        categories: searchParams.filters?.categories || [],
      };
      updateSearchFilters(filters);
      router.push('/coupon/search-result');
      return;
    }
  };

  // 处理清空关键词
  const handleClearKeyword = () => {
    const filters: SearchCouponRequest['filters'] = {
      ...searchParams.filters,
      keyword: undefined,
      categories: searchParams.filters?.categories || [],
    };
    updateSearchFilters(filters);
  };

  // 处理排序变更
  const handleSortChange = (sortType: string) => {
    setSearchParams({
      ...searchParams,
      sortType: keyToSort(sortType),
    });
  };

  const openFilterSlidePage = () => {
    setSlidePage(true, {
      title: APP_TEXT.COUPON.FILTER,
      content: (
        <CouponFilter
          initialFilters={searchParams.filters}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          onClose={() => setSlidePage(false)}
        />
      ),
      isOverAll: true,
      enableClose: true,
      enableBack: false,
      slideFrom: 'bottom',
    });
  };

  // 打开关键词搜索滑动页面
  const openKeywordSlidePage = () => {
    setSlidePage(true, {
      title: APP_TEXT.COUPON.COUPON_TITLE,
      content: (
        <KeywordSearch
          initialKeyword={searchParams.filters?.keyword || ''}
          onKeywordSelect={handleKeywordSearch}
          onClose={() => setSlidePage(false)}
          functionType="coupon"
          functionTypeName={APP_TEXT.COUPON.COUPON_TITLE}
          suggestionsIcon={<SuggestionCouponIcon />}
          detailUrl="/coupon/coupons/[couponId]"
        />
      ),
      isOverAll: true,
      enableClose: false,
      enableBack: true,
      slideFrom: 'bottom',
    });
  };

  return (
    <>
      <TopBar
        title={APP_TEXT.COUPON.COUPON_TITLE}
        enableBack={true}
        enableClose={false}
        onBack={handleBackClick}
        onClose={handleCloseClick}
      />

      <div className="p-6 flex flex-row gap-4 bg-card">
        <div onClick={openKeywordSlidePage} className="flex-1">
          <SearchInput
            placeholder={APP_TEXT.COUPON.SEARCH_PLACEHOLDER}
            value={searchParams.filters?.keyword || ''}
            readOnly
            className="cursor-pointer"
            showClearButton={!!searchParams.filters?.keyword}
            onClear={handleClearKeyword}
          />
        </div>
        <Button
          onClick={openFilterSlidePage}
          variant="icon"
          className="flex flex-col items-center gap-0"
          type="button"
        >
          <SlidersHorizontal className="w-6 h-6" />
          <span className="text-[10px] font-normal h-4">{APP_TEXT.COUPON.FILTER}</span>
        </Button>
      </div>

      {recommendedCouponsData.length > 0 && (
        <div className="p-4 pt-0 flex flex-col gap-1 bg-card">
          {/* すべて */}
          <div className="font-medium flex items-center justify-between ml-1">
            <div>
              <div className="font-bold text-lg">{APP_TEXT.COUPON.RECOMMENDED}</div>
            </div>
          </div>
          <CouponScrollArea
            couponList={recommendedCouponsData}
            onCouponClick={(couponId: string) => {
              router.push(`/coupon/coupons/${couponId}`);
            }}
          />
        </div>
      )}

      {popularCouponsData.length > 0 && (
        <div className="p-4  pt-0 flex flex-col gap-1 bg-card">
          {/* すべて */}
          <div className="font-medium flex items-center justify-between ml-1">
            <div>
              <div className="font-bold text-lg">{APP_TEXT.COUPON.POPULAR}</div>
            </div>
          </div>
          <CouponScrollArea
            couponList={popularCouponsData}
            onCouponClick={(couponId: string) => {
              router.push(`/coupon/coupons/${couponId}`);
            }}
          />
        </div>
      )}

      <div className="p-4  pt-0 flex flex-col gap-1 bg-card">
        {/* すべて */}
        <div className="font-medium flex items-center justify-between ml-1">
          <div>
            <div className="font-bold text-lg">{APP_TEXT.COUPON.SHOP_WITH_COUPON}</div>
            <p className="text-sm text-muted-foreground">
              {shopList.length + APP_TEXT.COUPON.ITEMS}
            </p>
          </div>
          <Select
            defaultValue={sortToKey(searchParams.sortType || 'popular')}
            options={sortOptions.map((option) => ({
              value: option.value,
              name: option.label,
            }))}
            title={APP_TEXT.COUPON.SORT}
            onChange={handleSortChange}
            className="flex items-center justify-between h-9  px-4 border border-input rounded-md w-[136px] text-sm mr-2"
          />
        </div>

        <div className="flex flex-col gap-0 py-0">
          {shopList.length > 0 ? (
            shopList.map((shop: SearchShopListItem) => (
              <SearchShopCard
                key={shop.shopId}
                shop={shop}
                onShopClick={() => {
                  router.push(`/coupon/shops/${shop.shopId}`);
                }}
                onCouponClick={(couponId: string) => {
                  router.push(`/coupon/coupons/${couponId}`);
                }}
              />
            ))
          ) : (
            <div className="text-center py-24">
              <NoData />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
