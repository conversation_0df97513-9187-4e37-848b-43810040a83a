// グラフページコンポーネント
'use client';

import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';
import { formatDate } from '@/utils/date-format';
import { useCallback, useEffect, useRef } from 'react';
import ChartArea from './_components/chart-area';
import MenuTab from './_components/menu-tab';
import TimeRangeTab from './_components/time-range-tab';

export default function GraphPage() {
  const {
    activeTab,
    timeRange,
    startDate,
    endDate,
    data,
    setActiveTab,
    setTimeRange,
    setDateRange,
    fetchData,
  } = useGraphStore();

  const { setIsLoading } = useLoading();
  const router = useRouter();

  const isLoadingRef = useRef(false);
  const searchParams = useSearchParams();

  // ページの初回読み込み時のみルートパラメータを解析
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    const dateParam = searchParams.get('date') || formatDate(new Date(), 'yyyy-MM-dd');

    if (tabParam) {
      setActiveTab(tabParam as typeof activeTab);
    }

    if (dateParam) {
      // timeRangeをweekに設定
      setTimeRange(TimeRangeType.WEEK);

      // dateパラメータを解析し、週の開始日と終了日を計算
      const targetDate = new Date(dateParam);
      if (!Number.isNaN(targetDate.getTime())) {
        const currentDay = targetDate.getDay(); // 0は日曜日、1は月曜日、...

        // その日付が属する週の開始日（月曜日）と終了日（日曜日）を計算
        const startDate = new Date(targetDate);
        // 日曜日(0)の場合、月曜日は6日前；そうでなければ(currentDay-1)日前
        const daysToMonday = currentDay === 0 ? 6 : currentDay - 1;
        startDate.setDate(targetDate.getDate() - daysToMonday);

        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);

        setDateRange(startDate, endDate);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // コンポーネントマウント時のみ実行

  const handleTabChange = useCallback(
    (tab: typeof activeTab) => {
      setActiveTab(tab);

      // URLのtabパラメータのみ更新し、他のパラメータは保持
      const url = new URL(window.location.href);
      url.searchParams.set('tab', tab);
      // dateパラメータを削除し、タブ切り替え時の日付再解析を回避
      url.searchParams.delete('date');
      router.replace(url.pathname + url.search, { scroll: false });
    },
    [router, setActiveTab],
  );

  // useEffect(() => {
  //   setTimeRange(TimeRangeType.WEEK);
  // }, [setTimeRange]);

  useEffect(() => {
    if (isLoadingRef.current) return;
    isLoadingRef.current = true;
    // ローディング状態を開始（コメントアウト）
    setIsLoading(true);

    fetchData().finally(() => {
      isLoadingRef.current = false;
      // ローディング状態を終了（コメントアウト）

      setIsLoading(false);
    });
  }, [activeTab, timeRange, startDate, endDate, setIsLoading]);

  return (
    <div className="graph-page ">
      <MenuTab activeTab={activeTab} onTabChange={handleTabChange} />

      <TimeRangeTab
        timeRange={timeRange}
        startDate={startDate}
        endDate={endDate}
        onTimeRangeChange={setTimeRange}
        onDateRangeChange={setDateRange}
      />

      <ChartArea activeTab={activeTab} timeRange={timeRange} data={data[activeTab]} />
    </div>
  );
}
