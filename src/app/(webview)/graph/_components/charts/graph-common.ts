import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { useRef, useState } from 'react';

/**
 * チャートデータ項目の汎用型、固定のdateフィールドとその他の可変フィールドを含む
 */
export type ChartDataItem<T = Record<string, any>> = {
  date: string;
} & T;

/**
 * チャートデータから特定位置の日付を取得してX軸目盛り値として使用
 * @param data チャートデータ配列、dateフィールドとその他の可変フィールドを含む
 * @param timeRange 時間範囲タイプ
 * @returns 日付配列、X軸目盛り用
 */
export const getTicksFromChartData = <T extends Record<string, any>>(
  data: Array<ChartDataItem<T>>,
  timeRange: TimeRangeType,
) => {
  // 年ビューまたはデータが空の場合、空配列を返す
  if (timeRange === TimeRangeType.YEAR || data.length === 0) {
    return [];
  }

  const dataLength = data.length;

  // 週ビューの場合、すべての日付を返し、完全な表示を確保
  if (timeRange === TimeRangeType.WEEK) {
    // 週ビューでは、すべてのデータの日付を返し、数量制限なし
    // これにより、データが不完全でも存在するすべての日付を表示できる
    return data.map((item) => item.date);
  }

  // 月ビュー処理ロジック
  const ticks: string[] = [];

  // 常に最初の要素の日付を追加
  ticks.push(data[0].date);

  // データ長に基づいて他の位置の日付を追加
  // 8番目の要素（インデックス7）
  if (dataLength > 7) {
    ticks.push(data[7].date);
  }

  // 15番目の要素（インデックス14）
  if (dataLength > 14) {
    ticks.push(data[14].date);
  }

  // 22番目の要素（インデックス21）
  if (dataLength > 21) {
    ticks.push(data[21].date);
  }

  // 最後の要素（最初の要素でない場合）
  if (dataLength > 1 && data[dataLength - 1].date !== ticks[ticks.length - 1]) {
    ticks.push(data[dataLength - 1].date);
  }

  return ticks;
};
/**
 * X軸目盛り値をフォーマット
 * @param value 日付文字列
 * @param timeRange 時間範囲タイプ
 * @param chartData チャートデータ、dateフィールドとその他の可変フィールドを含む
 * @returns フォーマット後の目盛りテキスト
 */
export const formatTickValue = <T extends Record<string, any>>(
  value: string,
  timeRange: TimeRangeType,
  chartData: Array<ChartDataItem<T>>,
) => {
  // 入力パラメータを検証
  if (!value || typeof value !== 'string') {
    return '';
  }

  // 年間データは月を表示
  if (timeRange === TimeRangeType.YEAR) {
    try {
      const parts = value.split('-');
      if (parts.length >= 2) {
        const month = Number.parseInt(parts[1]);
        return Number.isNaN(month) ? '' : `${month}`;
      }
      return '';
    } catch (error) {
      console.warn(`Error parsing year date: ${value}`, error);
      return '';
    }
  }

  // 週/月データは日付を表示
  const date = new Date(value);

  // 日付が有効かチェック
  if (Number.isNaN(date.getTime())) {
    console.warn(`Invalid date string in formatTickValue: ${value}`);
    return '';
  }

  try {
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // 最後の日のデータかチェック
    const isLastDay = chartData.length > 0 && value === chartData[chartData.length - 1].date;

    // 最後の日の場合、曜日を表示
    if (isLastDay) {
      // date-fnsを使用して日本語の曜日を取得
      return `${month}/${day}${format(date, '(E)', { locale: ja })}`;
    }

    return `${month}/${day}`;
  } catch (error) {
    console.log(`Error formatting tick value: ${value}`, error);
    return '';
  }
};

export const formatDate = (date: string, timeRange: TimeRangeType) => {
  // 入力パラメータを検証
  if (!date || typeof date !== 'string') {
    return '-';
  }

  // 日付オブジェクトを作成し、その有効性を検証
  const dateObj = new Date(date);

  // 日付が有効かチェック
  if (Number.isNaN(dateObj.getTime())) {
    console.warn(`Invalid date string: ${date}`);
    return '-';
  }

  try {
    if (timeRange === TimeRangeType.YEAR) {
      return format(dateObj, 'yyyy年M月', { locale: ja });
    }
    return format(dateObj, 'M月d日(E)', { locale: ja });
  } catch (error) {
    console.log(`Error formatting date: ${date}`, error);
    return '-';
  }
};

export const formatValue = (value: number | null, fixed?: number) => {
  if (!value) {
    return '-';
  }
  // fixedが指定されていない場合は0を指定,元値をそのまま表示する
  if (!fixed) {
    return value.toLocaleString();
  }
  return value.toFixed(fixed).toLocaleString();
};

/**
 * チャートスワイプ処理のカスタムHook
 * タッチイベント処理関数を提供し、チャートの左右スワイプで期間切り替え機能を実現
 * @returns タッチイベント処理関数を含むオブジェクト
 */
export const useChartSwipe = () => {
  const touchStartRef = useRef<number | null>(null);
  const touchMoveRef = useRef<number | null>(null);
  const [isSwiping, setIsSwiping] = useState(false);

  // タッチ開始イベントを処理
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartRef.current = e.touches[0].clientX;
    setIsSwiping(false);
  };

  // タッチ移動イベントを処理
  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartRef.current === null) return;
    touchMoveRef.current = e.touches[0].clientX;
  };

  // タッチ終了イベントを処理
  const handleTouchEnd = () => {
    if (touchStartRef.current === null || touchMoveRef.current === null) return;

    const graphStore = useGraphStore.getState();
    const touchDiff = touchMoveRef.current - touchStartRef.current;
    const minSwipeDistance = 50; // 最小スワイプ距離、必要に応じて調整可能

    if (Math.abs(touchDiff) > minSwipeDistance) {
      setIsSwiping(true);
      if (touchDiff > 0) {
        // 右スワイプ、前期間を読み込み
        graphStore.goToPreviousPeriod();
      } else {
        // 左スワイプ、次期間を読み込み
        // 現在の期間でない場合のみ左スワイプで次期間に移動可能
        if (!graphStore.isCurrentPeriod()) {
          graphStore.goToNextPeriod();
        }
      }
    }

    // タッチ状態をリセット
    touchStartRef.current = null;
    touchMoveRef.current = null;
  };

  return {
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    isSwiping,
  };
};

/**
 * Y轴最大值を計算する共通関数
 * 8個の等距離刻度を表示するように調整
 * @param chartData チャートデータ配列
 * @param dataKey データのキー名（例：'steps', 'distance', 'time'）
 * @param defaultMax デフォルト最大値
 * @returns 計算されたY軸最大値
 */
export const calculateYAxisMax = <T extends Record<string, any>>(
  chartData: Array<ChartDataItem<T>>,
  dataKey: string,
  defaultMax: number,
): number | 'auto' => {
  if (!chartData || chartData.length === 0) return defaultMax;

  // データの最大値を取得
  const maxDataValue = Math.max(...chartData.map((item) => Number(item[dataKey]) || 0));

  // 睡眠データの場合、defaultMaxは時間単位なので分単位に変換
  const isMinuteData = dataKey === 'sleep';
  const adjustedDefaultMax = isMinuteData ? defaultMax * 60 : defaultMax;

  if (maxDataValue <= 0) return adjustedDefaultMax;

  // データの最大値がデフォルト最大値以下の場合、デフォルト値を使用して等距離刻度を確保
  // if (maxDataValue <= adjustedDefaultMax) {
  //   return adjustedDefaultMax;
  // }

  const calculatedMax = (maxDataValue * 6) / 5;

  // より自然な数値に丸める
  const magnitude = 10 ** Math.floor(Math.log10(calculatedMax));
  const normalized = calculatedMax / magnitude;

  let roundedNormalized: number;
  if (normalized <= 1.5) roundedNormalized = 1.5;
  else if (normalized <= 2) roundedNormalized = 2;
  else if (normalized <= 3) roundedNormalized = 3;
  else if (normalized <= 5) roundedNormalized = 5;
  else roundedNormalized = 10;

  return Math.ceil(roundedNormalized * magnitude);
};
