'use client';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';
import type {
  BloodPressureGraphData,
  DailyBloodPressure,
  YearlyAverageBloodPressure,
} from '@/types/graph';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  ReferenceArea,
  ReferenceLine,
  XAxis,
  YAxis,
} from 'recharts';
import {
  formatDate,
  formatTickValue,
  formatValue,
  getTicksFromChartData,
  useChartSwipe,
} from './graph-common';

interface BloodPressureChartProps {
  data: BloodPressureGraphData;
  timeRange: TimeRangeType;
}

// チャートデータ項目の型を定義
interface BloodPressureChartItem {
  date: string;
  morningHigh: number;
  morningLow: number;
  afternoonHigh: number;
  afternoonLow: number;
  measureAtAm?: string;
  measureAtPm?: string;
}

const CHART_COMMON_COLORS = {
  REFERENCE_LINE: '#aaa',
  REFERENCE_AREA: '#f0f0f0',
};

const chartConfig = {
  morningHigh: {
    label: '午前（高）',
    color: 'hsl(var(--primary))',
  },
  morningLow: {
    label: '午前（低）',
    color: 'hsl(var(--primary))',
  },
  afternoonHigh: {
    label: '午後（高）',
    color: '#000',
  },
  afternoonLow: {
    label: '午後（低）',
    color: '#333',
  },
} satisfies ChartConfig;

export default function BloodPressureChart({ data, timeRange }: BloodPressureChartProps) {
  const [referenceX, setReferenceX] = useState<string | null>(null);
  const [tooltipX, setTooltipX] = useState<number>(0);
  const [tooltipWidth, setTooltipWidth] = useState<number>(
    timeRange === TimeRangeType.YEAR ? 210 : 220,
  );
  const [chartBounds, setChartBounds] = useState<{
    left: number;
    right: number;
  }>({ left: 60, right: 410 });

  // Ref of the chart container
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // グラフストアの状態を監視
  const { startDate, endDate } = useGraphStore();

  // timeRangeが変更されたときにtooltipWidthを更新
  useEffect(() => {
    setTooltipWidth(timeRange === TimeRangeType.YEAR ? 210 : 220);
  }, [timeRange]);

  // チャート境界を更新する関数
  const updateChartBounds = useCallback(() => {
    if (chartContainerRef.current) {
      const containerRect = chartContainerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;

      // Y軸の幅と余白を考慮してチャート境界を計算
      const yAxisWidth = 50;
      const leftMargin = 10;
      const rightMargin = 20;
      const additionalLeftPadding = 0; // Y軸ラベル等の余白

      const chartLeft = yAxisWidth + leftMargin + additionalLeftPadding;
      const chartRight = containerWidth - rightMargin;

      setChartBounds({
        left: chartLeft,
        right: chartRight,
      });
    }
  }, []);

  // 変化を検出して更新
  useEffect(() => {
    updateChartBounds();

    const handleResize = () => {
      updateChartBounds();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [updateChartBounds]);

  // tooltipのX位置を動的に計算する関数
  const calculateTooltipX = useCallback(
    (currentChartX: number): number => {
      const halfTooltipWidth = tooltipWidth / 2;
      const { left: chartLeftBoundary, right: chartRightBoundary } = chartBounds;

      // tooltipの理想的な位置を計算（マウス位置を中心に表示）
      const idealTooltipX = currentChartX - halfTooltipWidth;

      // 左境界検出：tooltipが左境界を超えないようにする
      if (idealTooltipX < chartLeftBoundary) {
        return chartLeftBoundary;
      }

      // 右境界検出：tooltipが右境界を超えないようにする
      if (idealTooltipX + tooltipWidth > chartRightBoundary) {
        return chartRightBoundary - tooltipWidth;
      }

      // 通常の場合：tooltipをマウス位置を中心に表示
      return idealTooltipX;
    },
    [tooltipWidth, chartBounds],
  );

  // コンポーネントマウント時に日付範囲を初期化

  // useEffect(() => {
  //   const graphStore = useGraphStore.getState();

  //   // 現在のtimeRangeに基づいて対応する日付範囲を設定
  //   switch (timeRange) {
  //     case TimeRangeType.WEEK:
  //       graphStore.setCurrentWeekRange();
  //       break;
  //     case TimeRangeType.MONTH:
  //       graphStore.setCurrentMonthRange();
  //       break;
  //     case TimeRangeType.YEAR:
  //       graphStore.setCurrentYearRange();
  //       break;
  //   }
  // }, [timeRange]);

  // 共通のチャートスワイプ処理Hookを使用
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useChartSwipe();

  // timeRangeに基づいてチャートデータを処理
  const chartData = (() => {
    if (!data) return [];

    if (timeRange === TimeRangeType.YEAR && data.yearBloodPressure) {
      // 年間データ - yearBloodPressureを使用
      return data.yearBloodPressure.map(
        (item: YearlyAverageBloodPressure) =>
          ({
            date: `${item.date}`,
            morningHigh: item.highAmAverage || null,
            morningLow: item.lowAmAverage || null,
            afternoonHigh: item.highPmAverage || null,
            afternoonLow: item.lowPmAverage || null,
          }) as BloodPressureChartItem,
      );
    }

    // 週/月データ - bloodPressureを使用
    // 防御的チェックを追加、bloodPressureの存在を確認
    if (!data.bloodPressure) return [];

    return data.bloodPressure.map(
      (item: DailyBloodPressure) =>
        ({
          date: `${item.date}`,
          morningHigh: item.highAm || null,
          morningLow: item.lowAm || null,
          afternoonHigh: item.highPm || null,
          afternoonLow: item.lowPm || null,
          measureAtAm: item.measureAtAm || null,
          measureAtPm: item.measureAtPm || null,
        }) as BloodPressureChartItem,
    );
  })();

  // カスタム午前血圧棒グラフ形状
  const CustomMorningBar = (props: any) => {
    const { x, y, width, height, fill } = props;

    // 高さが0より大きい場合のみレンダリング
    if (height <= 0) return null;

    const barWidth = width * 0.7; // 柱状图宽度为原宽度的70%
    const xOffset = x + (width - barWidth); // 靠右显示，为下午数据留出空间

    return (
      <g>
        {/* メイン塗りつぶし部分 */}
        <rect
          x={xOffset}
          y={y}
          width={barWidth}
          height={height}
          fill="hsl(var(--primary-60))"
          rx={1}
          ry={1}
        />

        {/* 上部エッジ - ダーク */}
        <rect
          x={xOffset}
          y={y}
          width={barWidth}
          height={4}
          fill="hsl(var(--primary))"
          rx={1}
          ry={1}
        />

        {/* 下部エッジ - ダーク */}
        <rect
          x={xOffset}
          y={y + height - 4}
          width={barWidth}
          height={4}
          fill="hsl(var(--primary))"
          rx={1}
          ry={1}
        />
      </g>
    );
  };

  // カスタム午後血圧棒グラフ形状
  const CustomAfternoonBar = (props: any) => {
    const { x, y, width, height, fill } = props;

    // 高さが0より大きい場合のみレンダリング
    if (height <= 0) return null;

    const barWidth = width * 0.7; // 柱状图宽度为原宽度的70%
    const xOffset = x; // 靠左显示，紧贴上午数据

    return (
      <g>
        {/* メイン塗りつぶし部分 */}
        <rect x={xOffset} y={y} width={barWidth} height={height} fill="#666" rx={1} ry={1} />

        {/* 上部エッジ - ブラック */}
        <rect x={xOffset} y={y} width={barWidth} height={4} fill="#000" rx={1} ry={1} />

        {/* 下部エッジ - ブラック */}
        <rect
          x={xOffset}
          y={y + height - 4}
          width={barWidth}
          height={3}
          fill="#000"
          rx={1}
          ry={1}
        />
      </g>
    );
  };

  // カスタムツールチップ内容
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div
          className="bg-primary-10 shadow-md p-4 rounded-2xl relative"
          style={{ width: tooltipWidth }}
        >
          <div className="text-sm mb-1 flex items-center justify-between">
            <span> {formatDate(String(data.date), timeRange)}</span>

            <span className="text-xs ml-3">最高/最低 mmHg</span>
          </div>

          <div className="mt-2">
            <p className="text-lg">
              <span className="text-xs">
                午前 {timeRange === TimeRangeType.YEAR ? '平均' : data.measureAtAm || '-'}
              </span>
              <span className="text-2xl font-bold mx-1">
                {formatValue(data.morningHigh)}/{formatValue(data.morningLow)}
              </span>{' '}
              <span className="text-xs">mmHg</span>
            </p>
            <p className="text-lg mt-2">
              <span className="text-xs">
                午後 {timeRange === TimeRangeType.YEAR ? '平均' : data.measureAtPm || '-'}
              </span>
              <span className="text-2xl font-bold mx-1">
                {formatValue(data.afternoonHigh)}/{formatValue(data.afternoonLow)}
              </span>{' '}
              <span className="text-xs">mmHg</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="">
      <Card className="rounded-none">
        <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => useGraphStore.getState().goToPreviousPeriod()}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <h3 className="">{useGraphStore.getState().getFormattedDateRangeTitle()}</h3>

          {/* Next Button - 現在の期間では非表示 */}
          {!useGraphStore.getState().isCurrentPeriod() && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => useGraphStore.getState().goToNextPeriod()}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
          {/* 現在の期間ではプレースホルダー要素を表示、レイアウトの一貫性を保つ */}
          {useGraphStore.getState().isCurrentPeriod() && <div className="w-9 h-9" />}
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          <div className="relative">
            <ChartContainer
              key={`${timeRange}-${chartData.length}-${data ? JSON.stringify(Object.keys(data)) : 'no-data'}`}
              ref={chartContainerRef}
              config={chartConfig}
              className="h-[450px] w-full"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <BarChart
                accessibilityLayer
                data={chartData}
                margin={{ top: 20, right: 20, left: 0, bottom: 10 }}
                barSize={20}
                onMouseMove={(state) => {
                  if (state?.activeLabel && state?.chartX !== undefined) {
                    setReferenceX(state.activeLabel);

                    // tooltipのX位置を動的に計算
                    const newTooltipX = calculateTooltipX(state.chartX);
                    setTooltipX(newTooltipX);
                  }
                }}
              >
                {/* 血圧正常範囲外のグレー背景エリア */}
                <ReferenceArea
                  y1={0}
                  y2={85}
                  fill={CHART_COMMON_COLORS.REFERENCE_AREA}
                  fillOpacity={0.6}
                />
                <ReferenceArea
                  y1={135}
                  fill={CHART_COMMON_COLORS.REFERENCE_AREA}
                  fillOpacity={0.6}
                />
                <ReferenceLine y={85} stroke={CHART_COMMON_COLORS.REFERENCE_LINE} strokeWidth={1} />
                <ReferenceLine
                  y={135}
                  stroke={CHART_COMMON_COLORS.REFERENCE_LINE}
                  strokeWidth={1}
                />

                <CartesianGrid
                  strokeDasharray="3 3"
                  horizontal={true}
                  vertical={false}
                  stroke="hsl(var(--muted-foreground))"
                />
                <XAxis
                  key={timeRange}
                  dataKey="date"
                  tickMargin={15}
                  tickLine={false}
                  axisLine={true}
                  height={50}
                  ticks={getTicksFromChartData(chartData, timeRange)}
                  tickFormatter={(value) => formatTickValue(value, timeRange, chartData)}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tickCount={7}
                  domain={[0, 280]}
                  width={50}
                  tickFormatter={(value) => value.toLocaleString()}
                  label={{
                    value: '(mmHg)',
                    position: 'top',
                    offset: 12,
                    style: { textAnchor: 'middle' },
                    dx: 15,
                  }}
                />

                {referenceX && <ReferenceLine x={referenceX} stroke="#666" strokeWidth={1} />}
                <ChartTooltip
                  position={{ x: tooltipX, y: -10 }}
                  content={<CustomTooltip />}
                  cursor={false}
                  wrapperStyle={{ zIndex: 100 }}
                />

                {/* 境界参照線 */}
                <ReferenceLine y={80} stroke="#aaa" strokeWidth={1} strokeDasharray="3 3" />
                <ReferenceLine y={120} stroke="#aaa" strokeWidth={1} strokeDasharray="3 3" />

                {/* 午前血圧データ - カスタム形状を使用 */}
                <Bar
                  dataKey={(data) => [data.morningLow, data.morningHigh]}
                  name="午前"
                  shape={<CustomMorningBar />}
                  isAnimationActive={false}
                  barSize={15}
                />

                {/* 午後血圧データ - カスタム形状を使用 */}
                <Bar
                  dataKey={(data) => [data.afternoonLow, data.afternoonHigh]}
                  name="午後"
                  shape={<CustomAfternoonBar />}
                  isAnimationActive={false}
                  barSize={15}
                />

                <Legend
                  verticalAlign="bottom"
                  height={30}
                  wrapperStyle={{
                    fontSize: '14px',
                    fontWeight: '500',
                    display: 'flex',
                    justifyContent: 'center',

                    width: '98%',
                  }}
                  content={({ payload }) => {
                    if (payload?.length) {
                      return (
                        <div className="flex justify-center items-center gap-[60px] ">
                          {payload.map((entry, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <div
                                className=""
                                style={{
                                  width: '32px',
                                  height: '8px',
                                  backgroundColor: entry.color,
                                }}
                              />
                              <span className="text-sm font-medium">{entry.value}</span>
                            </div>
                          ))}
                        </div>
                      );
                    }
                    return null;
                  }}
                  payload={[
                    { value: '午前', type: 'rect', color: 'hsl(var(--primary-60))' },
                    { value: '午後', type: 'rect', color: '#666' },
                  ]}
                />
              </BarChart>
            </ChartContainer>
          </div>
        </CardContent>
        <div className="border border-gray-200  text-center mt-4">
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1 flex items-center justify-center" />
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1 py-2  ">午前</div>
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1 py-2 ">午後</div>
          </div>
          <hr className="border-zinc-100" />
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex-1 flex items-center justify-center ">
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の平均最高血圧
            </div>
            <div className="  text-xs flex-1 py-2   ">
              <span className="mx-1 text-xl font-bold ">{data?.periodHighAmAverage || '-'}</span>{' '}
              mmHg
            </div>
            <div className=" text-xs flex-1 py-2  ">
              <span className="mx-1 text-xl font-bold ">{data?.periodHighPmAverage || '-'}</span>{' '}
              mmHg
            </div>
          </div>
          <hr className="border-zinc-100" />
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex-1 flex items-center justify-center  ">
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の平均最低血圧
            </div>
            <div className="  text-xs flex-1 py-2  ">
              <span className="mx-1 text-xl font-bold ">
                {formatValue(data?.periodLowAmAverage)}
              </span>{' '}
              mmHg
            </div>
            <div className=" text-xs flex-1 py-2  ">
              <span className="mx-1 text-xl font-bold ">
                {formatValue(data?.periodLowPmAverage)}
              </span>{' '}
              mmHg
            </div>
          </div>
        </div>
      </Card>

      {/* 詳細データリスト */}
      <Card className="m-4">
        <CardContent className="p-0">
          <div className="rounded-lg overflow-hidden">
            {chartData.length > 0 && (
              <div className="divide-y">
                {chartData.map((item, index) => {
                  // item.dateが存在し有効であることを確認
                  if (!item.date) {
                    return null;
                  }

                  return (
                    <div key={index} className="flex justify-between items-center py-4 px-6">
                      <div className="text-left">{formatDate(item.date, timeRange)}</div>

                      {timeRange === TimeRangeType.YEAR ? (
                        <div className="text-right">
                          <div>
                            <span className="text-xs mr-2 text-muted-foreground">
                              <span className="mr-1">午前</span> <span>平均</span>
                            </span>
                            <span>
                              {formatValue(item.morningHigh)}/{formatValue(item.morningLow)}
                            </span>{' '}
                            mmHg
                          </div>
                          <div>
                            <span className="text-xs mr-2 text-muted-foreground">
                              <span className="mr-1">午後</span> <span>平均</span>
                            </span>
                            <span>
                              {formatValue(item.afternoonHigh)}/{formatValue(item.afternoonLow)}
                            </span>{' '}
                            mmHg
                          </div>
                        </div>
                      ) : (
                        <div className="text-right">
                          <div>
                            <span className="text-xs mr-2 text-muted-foreground">
                              {item?.measureAtAm || '午前'}
                            </span>
                            <span>
                              {formatValue(item.morningHigh)}/{formatValue(item.morningLow)}
                            </span>{' '}
                            mmHg
                          </div>
                          <div>
                            <span className="text-xs mr-2 text-muted-foreground">
                              {item?.measureAtPm || '午後'}
                            </span>
                            <span>
                              {formatValue(item.afternoonHigh)}/{formatValue(item.afternoonLow)}
                            </span>{' '}
                            mmHg
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
