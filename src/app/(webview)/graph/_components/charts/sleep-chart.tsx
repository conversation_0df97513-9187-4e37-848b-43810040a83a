'use client';
import type { DailySleep, MonthlyAverageSleep, SleepGraphData } from '@/types/graph';
import { useCallback, useEffect, useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';

import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Bar, BarChart, CartesianGrid, ReferenceLine, XAxis, YAxis } from 'recharts';
import {
  calculateYAxisMax,
  formatDate,
  formatTickValue,
  formatValue,
  getTicksFromChartData,
  useChartSwipe,
} from './graph-common';
interface SleepChartProps {
  data: SleepGraphData;
  timeRange: TimeRangeType;
}

const chartConfig = {
  sleep: {
    label: '睡眠時間',
    color: 'hsl(var(--primary-60))',
  },
} satisfies ChartConfig;

const formatSleepTimeAverage = (
  minutes: number | undefined,
): { hours: number; remainingMins: number } => {
  if (minutes === undefined || minutes === null) return { hours: 0, remainingMins: 0 };

  // 确保minutes是数字类型
  const mins = Number(minutes);
  if (Number.isNaN(mins)) return { hours: 0, remainingMins: 0 };

  const hours = Math.floor(mins / 60);
  const remainingMins = mins % 60;

  return { hours, remainingMins };
};

export default function SleepChart({ data, timeRange }: SleepChartProps) {
  const [referenceX, setReferenceX] = useState<string | null>(null);
  const [tooltipX, setTooltipX] = useState<number>(0);
  const [tooltipWidth, setTooltipWidth] = useState<number>(
    timeRange === TimeRangeType.YEAR ? 190 : 170,
  );
  const [chartBounds, setChartBounds] = useState<{
    left: number;
    right: number;
  }>({ left: 60, right: 410 });

  // Ref of the chart container
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // グラフストアの状態を監視
  const { startDate, endDate } = useGraphStore();

  // timeRangeが変更されたときにtooltipWidthを更新
  useEffect(() => {
    setTooltipWidth(timeRange === TimeRangeType.YEAR ? 190 : 170);
  }, [timeRange]);

  // チャート境界を更新する関数
  const updateChartBounds = useCallback(() => {
    if (chartContainerRef.current) {
      const containerRect = chartContainerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;

      // Y軸の幅と余白を考慮してチャート境界を計算
      const yAxisWidth = 50;
      const leftMargin = 10;
      const rightMargin = 20;
      const additionalLeftPadding = 0; // Y軸ラベル等の余白

      const chartLeft = yAxisWidth + leftMargin + additionalLeftPadding;
      const chartRight = containerWidth - rightMargin;

      setChartBounds({
        left: chartLeft,
        right: chartRight,
      });
    }
  }, []);

  // 変化を検出して更新
  useEffect(() => {
    updateChartBounds();

    const handleResize = () => {
      updateChartBounds();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [updateChartBounds]);

  // tooltipのX位置を動的に計算する関数
  const calculateTooltipX = useCallback(
    (currentChartX: number): number => {
      const halfTooltipWidth = tooltipWidth / 2;
      const { left: chartLeftBoundary, right: chartRightBoundary } = chartBounds;

      // tooltipの理想的な位置を計算（マウス位置を中心に表示）
      const idealTooltipX = currentChartX - halfTooltipWidth;

      // 左境界検出：tooltipが左境界を超えないようにする
      if (idealTooltipX < chartLeftBoundary) {
        return chartLeftBoundary;
      }

      // 右境界検出：tooltipが右境界を超えないようにする
      if (idealTooltipX + tooltipWidth > chartRightBoundary) {
        return chartRightBoundary - tooltipWidth;
      }

      // 通常の場合：tooltipをマウス位置を中心に表示
      return idealTooltipX;
    },
    [tooltipWidth, chartBounds],
  );

  // コンポーネントマウント時に日付範囲を初期化

  // useEffect(() => {
  //   const graphStore = useGraphStore.getState();

  //   // 現在のtimeRangeに基づいて対応する日付範囲を設定
  //   switch (timeRange) {
  //     case TimeRangeType.WEEK:
  //       graphStore.setCurrentWeekRange();
  //       break;
  //     case TimeRangeType.MONTH:
  //       graphStore.setCurrentMonthRange();
  //       break;
  //     case TimeRangeType.YEAR:
  //       graphStore.setCurrentYearRange();
  //       break;
  //   }
  // }, [timeRange]);

  // 共通のチャートスワイプ処理Hookを使用
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useChartSwipe();

  // timeRangeに基づいてチャートデータを処理
  const chartData = (() => {
    if (!data) return [];

    if (timeRange === TimeRangeType.YEAR && data.yearSleep) {
      // 年間データ - yearSleepを使用
      return data.yearSleep.map((item: MonthlyAverageSleep) => ({
        date: item.date,
        sleep: item.monthAverageSleepTime || null,
      }));
    }

    // 週/月データ - sleepListを使用
    // 防御的チェックを追加、sleepListの存在を確認
    if (!data.sleep) return [];

    return data.sleep.map((item: DailySleep) => ({
      date: item.date,
      sleep: item.sleepTime || null,
    }));
  })();

  const DEFAULT_MAX = 21;
  // Y轴の最大値を计算（共通関数を使用）
  const yAxisMax = calculateYAxisMax(chartData, 'sleep', DEFAULT_MAX);

  return (
    <div className="  ">
      <Card className="rounded-none ">
        <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => useGraphStore.getState().goToPreviousPeriod()}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <h3 className="">{useGraphStore.getState().getFormattedDateRangeTitle()}</h3>

          {/* Next Button - 現在の期間では非表示 */}
          {!useGraphStore.getState().isCurrentPeriod() && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => useGraphStore.getState().goToNextPeriod()}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
          {/* 現在の期間ではプレースホルダー要素を表示、レイアウトの一貫性を保つ */}
          {useGraphStore.getState().isCurrentPeriod() && <div className="w-9 h-9" />}
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          <ChartContainer
            key={`${timeRange}-${chartData.length}-${data ? JSON.stringify(Object.keys(data)) : 'no-data'}`}
            ref={chartContainerRef}
            config={chartConfig}
            className="h-[400px] w-full"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <BarChart
              accessibilityLayer
              data={chartData}
              margin={{ top: 30, right: 20, left: -10, bottom: 10 }}
              barSize={15}
              barGap={0.2}
              onMouseMove={(state) => {
                if (state?.activeLabel && state?.chartX !== undefined) {
                  setReferenceX(state.activeLabel);

                  // tooltipのX位置を動的に計算
                  const newTooltipX = calculateTooltipX(state.chartX);
                  setTooltipX(newTooltipX);
                }
              }}
            >
              <CartesianGrid
                strokeDasharray="2 4"
                horizontal={true}
                vertical={false}
                stroke="hsl(var(--muted-foreground))"
              />
              <XAxis
                key={timeRange}
                dataKey="date"
                tickMargin={15}
                tickLine={false}
                axisLine={true}
                height={50}
                ticks={getTicksFromChartData(chartData, timeRange)}
                tickFormatter={(value) => formatTickValue(value, timeRange, chartData)}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tickCount={7}
                domain={[0, yAxisMax]}
                width={50}
                type="number"
                allowDataOverflow={false}
                tickFormatter={(value) => {
                  // Y軸の分数を時間に変換
                  return `${Math.floor(value / 60)}`;
                }}
                label={{
                  value: '(時間)',
                  position: 'top',
                  offset: 12,
                  dx: 15,
                  style: { textAnchor: 'middle' },
                }}
              />
              {referenceX && <ReferenceLine x={referenceX} stroke="#666" strokeWidth={1} />}

              <ChartTooltip
                position={{ x: tooltipX, y: 20 }}
                cursor={false}
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length && label) {
                    // 分を時間と分に変換
                    const sleepMinutes = Number(payload[0]?.value || 0);
                    const hours = Math.floor(sleepMinutes / 60);
                    const mins = sleepMinutes % 60;

                    return (
                      <div
                        className="bg-primary-10 shadow-md p-4 rounded-2xl relative"
                        style={{ width: tooltipWidth }}
                      >
                        <div className="text-sm font-medium mb-1">
                          {formatDate(String(label), timeRange)}
                        </div>
                        <div className="text-2xl font-bold">
                          {timeRange === TimeRangeType.YEAR ? (
                            <span className="text-sm mr-1">平均</span>
                          ) : (
                            ''
                          )}
                          {hours} <span className="text-base font-normal">時間</span> {mins}{' '}
                          <span className="text-base font-normal">分</span>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />

              {/* 睡眠目標参照線 - SleepGraphDataにsleepTarget属性なし */}

              <Bar
                dataKey="sleep"
                fill="hsl(var(--primary-60))"
                radius={[4, 4, 0, 0]}
                name="睡眠時間"
                maxBarSize={50}
                barSize={timeRange === TimeRangeType.MONTH ? 8 : undefined}
              />
            </BarChart>
          </ChartContainer>
        </CardContent>

        <div className="border border-zinc-200 ">
          <div className="w-full flex justify-between items-stretch ">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex items-center justify-center px-4 ">
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の平均/日
            </div>

            <div className=" flex-1 text-right py-2 pr-4">
              {data?.periodAverageSleepTime ? (
                <div className="flex items-center justify-end">
                  <span className="text-xl font-bold mr-1">
                    {formatSleepTimeAverage(data?.periodAverageSleepTime).hours}
                  </span>
                  <span className="text-sm">時間</span>
                  <span className="text-xl font-bold mx-1">
                    {formatSleepTimeAverage(data?.periodAverageSleepTime).remainingMins}
                  </span>
                  <span className="text-sm">分</span>
                </div>
              ) : (
                <span className="text-sm">- 時間 - 分</span>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* データカード */}
      <Card className="m-4">
        <CardContent className="p-0">
          <div className="bg-white rounded-lg overflow-hidden">
            {chartData.length > 0 && (
              <div className="divide-y">
                {chartData.map((item: any, index: number) => {
                  // item.dateが存在し有効であることを確認
                  if (!item.date) {
                    return null;
                  }

                  // 分を時間と分に変換
                  const sleepMinutes = Number(item.sleep || 0);
                  const hours = Math.floor(sleepMinutes / 60);
                  const mins = sleepMinutes % 60;

                  return (
                    <div key={index} className="flex justify-between items-center py-4 px-6">
                      <div className="text-left ">{formatDate(item.date, timeRange)}</div>
                      <div className="text-right ">
                        {timeRange === TimeRangeType.YEAR && (
                          <span className="text-xs mr-2">平均</span>
                        )}
                        <span className="font-normal">
                          {hours ? hours : '-'} 時間 {mins ? mins : '-'} 分
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
