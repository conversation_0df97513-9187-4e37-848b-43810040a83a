'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';
import type {
  DailyStepDistanceTime,
  MonthlyAverageStepDistanceTime,
  StepDistanceTimeGraphData,
} from '@/types/graph';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ReferenceLine,
  XAxis,
  YAxis,
} from 'recharts';
import {
  calculateYAxisMax,
  formatDate,
  formatTickValue,
  formatValue,
  getTicksFromChartData,
  useChartSwipe,
} from './graph-common';
interface StepDistanceTimeChartProps {
  data: StepDistanceTimeGraphData;
  timeRange: TimeRangeType;
}

const chartConfig = {
  distance: {
    label: '歩行距離',
    color: 'hsl(var(--primary-60))',
  },
  time: {
    label: '歩行時間',
    color: 'hsl(var(--primary))',
  },
} satisfies ChartConfig;

export default function StepDistanceTimeChart({ data, timeRange }: StepDistanceTimeChartProps) {
  // グラフストアの状態を監視
  const { startDate, endDate } = useGraphStore();
  const [referenceX, setReferenceX] = useState<any>();
  const [tooltipX, setTooltipX] = useState<number>(0);
  const [tooltipWidth, setTooltipWidth] = useState<number>(240);
  const [chartBounds, setChartBounds] = useState<{
    left: number;
    right: number;
  }>({ left: 60, right: 374 });

  // Ref of the chart container
  const chartContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setTooltipWidth(timeRange === TimeRangeType.YEAR ? 240 : 200);
  }, [timeRange]);

  // timeRangeが変更されたときにtooltipWidthを更新
  const updateChartBounds = useCallback(() => {
    if (chartContainerRef.current) {
      const containerRect = chartContainerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;

      // Y軸の幅と余白を考慮してチャート境界を計算
      const leftYAxisWidth = 50;
      const rightYAxisWidth = 50;
      const leftMargin = 5;
      const rightMargin = 5;
      const additionalLeftPadding = 0; // Y軸ラベル等の余白

      const chartLeft = leftYAxisWidth + leftMargin + additionalLeftPadding;
      const chartRight = containerWidth - rightYAxisWidth - rightMargin;

      setChartBounds({
        left: chartLeft,
        right: chartRight,
      });
    }
  }, []);

  // 监听容器尺寸变化
  useEffect(() => {
    updateChartBounds();

    const handleResize = () => {
      updateChartBounds();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [updateChartBounds]);

  // tooltipのX位置を動的に計算する関数
  const calculateTooltipX = useCallback(
    (currentChartX: number): number => {
      const halfTooltipWidth = tooltipWidth / 2;
      const { left: chartLeftBoundary, right: chartRightBoundary } = chartBounds;

      // tooltipの理想的な位置を計算（マウス位置を中心に表示）
      const idealTooltipX = currentChartX - halfTooltipWidth;

      // 左境界検出：tooltipが左境界を超えないようにする
      if (idealTooltipX < chartLeftBoundary) {
        return chartLeftBoundary;
      }

      // 右境界検出：tooltipが右境界を超えないようにする
      if (idealTooltipX + tooltipWidth > chartRightBoundary) {
        return chartRightBoundary - tooltipWidth;
      }

      // 通常の場合：tooltipをマウス位置を中心に表示
      return idealTooltipX;
    },
    [tooltipWidth, chartBounds],
  );

  // コンポーネントマウント時に日付範囲を初期化

  // useEffect(() => {
  //   const graphStore = useGraphStore.getState();

  //   // 現在のtimeRangeに基づいて対応する日付範囲を設定
  //   switch (timeRange) {
  //     case TimeRangeType.WEEK:
  //       graphStore.setCurrentWeekRange();
  //       break;
  //     case TimeRangeType.MONTH:
  //       graphStore.setCurrentMonthRange();
  //       break;
  //     case TimeRangeType.YEAR:
  //       graphStore.setCurrentYearRange();
  //       break;
  //   }
  // }, [timeRange]);

  // 共通のチャートスワイプ処理Hookを使用
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useChartSwipe();

  // timeRangeに基づいてチャートデータを処理、Distanceは小数点1桁を保持
  const chartData = (() => {
    if (!data) return [];

    if (timeRange === TimeRangeType.YEAR && data.yearStepDistanceTime) {
      // 年間データ - yearStepDistanceTimeを使用
      return data.yearStepDistanceTime.map((item: MonthlyAverageStepDistanceTime) => ({
        date: item.date,
        distance: item.monthAverageDistance || null,
        time: item.monthAverageTime || null,
      }));
    }

    if (!data.stepDistanceTime) return [];

    // 週/月データ - stepDistanceTimeを使用
    return data.stepDistanceTime.map((item: DailyStepDistanceTime) => ({
      date: item.date,
      distance: item.distance || null,
      time: item.exerciseTime || null,
    }));
  })();

  const DEFAULT_DISTANCE_MAX = 6;
  const DEFAULT_TIME_MAX = 30;

  // Y轴の最大値を計算（共通関数を使用）
  const distanceYAxisMax = calculateYAxisMax(chartData, 'distance', DEFAULT_DISTANCE_MAX);
  const timeYAxisMax = calculateYAxisMax(chartData, 'time', DEFAULT_TIME_MAX);

  return (
    <div className="  ">
      <Card className="rounded-none ">
        <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => useGraphStore.getState().goToPreviousPeriod()}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <h3 className="">{useGraphStore.getState().getFormattedDateRangeTitle()}</h3>

          {/* Next Button - 現在の期間では非表示 */}
          {!useGraphStore.getState().isCurrentPeriod() && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => useGraphStore.getState().goToNextPeriod()}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
          {/* 現在の期間ではプレースホルダー要素を表示、レイアウトの一貫性を保つ */}
          {useGraphStore.getState().isCurrentPeriod() && <div className="w-9 h-9" />}
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          {/* Legendとより大きなmarginが占有するスペースを補償するためにコンテナ高さを増加 */}
          <ChartContainer
            key={`${timeRange}-${chartData.length}-${data ? JSON.stringify(Object.keys(data)) : 'no-data'}`}
            ref={chartContainerRef}
            config={chartConfig}
            className="h-[440px] w-full"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <ComposedChart
              accessibilityLayer
              data={chartData}
              margin={{ top: 30, right: -10, left: -10, bottom: 10 }}
              barSize={15}
              barGap={0.2}
              onMouseMove={(state) => {
                if (state?.activeLabel && state?.chartX !== undefined) {
                  console.log(state.activeLabel);
                  setReferenceX(state.activeLabel);

                  // tooltipのX位置を動的に計算
                  const newTooltipX = calculateTooltipX(state.chartX);

                  setTooltipX(newTooltipX);
                }
              }}
            >
              <CartesianGrid
                strokeDasharray="2 4"
                horizontal={true}
                vertical={false}
                stroke="hsl(var(--muted-foreground))"
              />
              {/* 重複するReferenceLineを削除 */}
              <XAxis
                key={timeRange}
                dataKey="date"
                tickLine={false}
                tickMargin={15}
                axisLine={true}
                height={50}
                ticks={getTicksFromChartData(chartData, timeRange)}
                tickFormatter={(value) => formatTickValue(value, timeRange, chartData)}
              />
              <YAxis
                yAxisId="left"
                axisLine={false}
                tickLine={false}
                tickCount={7}
                domain={[0, distanceYAxisMax]}
                width={50}
                tickFormatter={(value) => value.toLocaleString()}
                label={{
                  value: '(km)',
                  position: 'top',
                  offset: 12,
                  dx: 18,
                  style: { textAnchor: 'middle' },
                }}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                axisLine={false}
                tickLine={false}
                tickCount={7}
                domain={[0, timeYAxisMax]}
                width={50}
                tickFormatter={(value) => value.toLocaleString()}
                label={{
                  value: '(分)',
                  position: 'top',
                  offset: 12,
                  dx: -12,
                  style: { textAnchor: 'middle' },
                }}
              />
              {referenceX && (
                <ReferenceLine x={referenceX} yAxisId="left" stroke="#666" strokeWidth={1} />
              )}

              <Legend
                verticalAlign="bottom"
                height={30}
                wrapperStyle={{
                  fontSize: '14px',
                  fontWeight: '500',
                  display: 'flex',
                  justifyContent: 'center',
                  gap: '16px',
                  width: '98%',
                  maxWidth: '100%',
                  overflow: 'hidden',
                }}
                content={({ payload }) => {
                  if (payload?.length) {
                    return (
                      <div className="flex justify-center items-center gap-6 px-4 max-w-full ">
                        {payload.map((entry, index) => (
                          <div key={index} className="flex items-center gap-2 min-w-0">
                            {entry.value === '歩行距離' ? (
                              <div
                                className="flex-shrink-0 ml-4"
                                style={{
                                  width: '32px',
                                  height: '8px',
                                  backgroundColor: entry.color,
                                }}
                              />
                            ) : (
                              <div className="flex items-center flex-shrink-0">
                                <div
                                  style={{
                                    width: '32px',
                                    height: '2px',
                                    backgroundColor: entry.color,
                                  }}
                                />
                                <div
                                  className="rounded-full"
                                  style={{
                                    width: '8px',
                                    height: '8px',
                                    backgroundColor: entry.color,
                                    marginLeft: '-18px',
                                    marginRight: '12px',
                                  }}
                                />
                              </div>
                            )}
                            <span className="text-sm font-medium whitespace-nowrap">
                              {entry.value}
                            </span>
                          </div>
                        ))}
                      </div>
                    );
                  }
                  return null;
                }}
                payload={[
                  { value: '歩行距離', type: 'rect', color: 'hsl(var(--primary-60))' },
                  { value: '歩行時間', type: 'line', color: 'hsl(var(--primary))' },
                ]}
              />

              <ChartTooltip
                position={{ x: tooltipX, y: 20 }}
                cursor={{
                  opacity: 0,
                }}
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div
                        className="bg-primary-10 shadow-md p-4 rounded-2xl relative"
                        style={{ width: `${tooltipWidth}px` }}
                      >
                        <div className="text-sm font-medium mb-1">
                          {formatDate(String(label), timeRange)}
                        </div>
                        <div className="text-2xl d">
                          {timeRange === TimeRangeType.YEAR ? (
                            <span className="text-sm">平均</span>
                          ) : (
                            ''
                          )}
                          <span className="font-bold">
                            {' '}
                            {payload[0]?.value ? payload[0]?.value.toLocaleString() : '-'}{' '}
                          </span>

                          <span className="text-sm">km</span>
                          <span className="mx-2">/</span>
                          <span className="font-bold">
                            {payload[1]?.value ? payload[1]?.value.toLocaleString() : '-'}
                          </span>
                          <span className="text-sm ml-1">分</span>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />

              <ChartTooltip
                position={{ x: tooltipX, y: 20 }}
                cursor={{
                  opacity: 0,
                }}
                offset={0}
                allowEscapeViewBox={{ x: false, y: true }}
                content={({ active, payload, label, coordinate }) => {
                  if (active && payload && payload.length && coordinate) {
                    return (
                      <div
                        className="bg-primary-10 shadow-md p-4 rounded-2xl  "
                        style={{ width: `${tooltipWidth}px` }}
                      >
                        <div className="text-sm font-medium mb-2 text-left ml-2">
                          {formatDate(String(label), timeRange)}
                        </div>
                        <div className="flex flex-col items-center">
                          <div className="text-2xl font-bold">
                            {payload[0]?.value?.toLocaleString() || '-'}{' '}
                            <span className="text-sm">km</span>
                            {payload[1]?.value?.toLocaleString() || '-'} /
                            <span className="text-sm">分</span>
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              {data?.distanceTarget && (
                <ReferenceLine
                  yAxisId="left"
                  y={data.distanceTarget}
                  stroke="hsl(var(--success))"
                  strokeWidth={2}
                />
              )}

              <Bar
                dataKey="distance"
                yAxisId="left"
                fill="hsl(var(--primary-60))"
                radius={[4, 4, 0, 0]}
                name="歩行距離"
                maxBarSize={50}
                barSize={timeRange === TimeRangeType.MONTH ? 8 : undefined}
              />

              <Line
                dataKey="time"
                yAxisId="right"
                stroke="hsl(var(--primary))"
                strokeWidth={2}
                dot={{ r: 4, fill: 'hsl(var(--primary))' }}
                activeDot={{ r: 6 }}
                name="歩行時間"
              />
            </ComposedChart>
          </ChartContainer>
        </CardContent>

        <div className="border border-gray-200  text-center">
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1  flex items-center justify-center  " />
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1 py-2 px-4 ">
              歩行距離
            </div>
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1 py-2 px-4 ">
              歩行時間
            </div>
          </div>
          <hr className="border-zinc-100" />
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex-1 flex items-center justify-center   ">
              目標歩数からの想定
            </div>
            <div className="  text-xs flex-1 py-2 px-4  ">
              {' '}
              <span className="mx-1 text-xl font-bold ">
                {formatValue(data?.distanceTarget, 2)}
              </span>{' '}
              km
            </div>
            <div className=" text-xs flex-1 py-2 px-4  ">
              {' '}
              <span className="mx-1 text-xl font-bold ">
                {formatValue(data?.exerciseTimeTarget)}
              </span>{' '}
              分
            </div>
          </div>
          <hr className="border-zinc-100" />
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex-1 flex items-center justify-center  ">
              {' '}
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の平均/日
            </div>
            <div className="  text-xs flex-1 py-2 px-4  ">
              {' '}
              <span className="mx-1 text-xl font-bold ">
                {data?.periodAverageDistance ? formatValue(data.periodAverageDistance, 2) : '-'}
              </span>{' '}
              km
            </div>
            <div className=" text-xs flex-1 py-2 px-4  ">
              {' '}
              <span className="mx-1 text-xl font-bold ">
                {formatValue(data?.periodAverageTime)}
              </span>{' '}
              分
            </div>
          </div>
        </div>
      </Card>

      {/* データカード */}
      <Card className="m-4">
        <CardContent className="p-0">
          <div className="bg-white rounded-lg overflow-hidden">
            {chartData.length > 0 && (
              <div className="divide-y">
                {chartData.map((item, index) => {
                  // item.dateが存在し有効であることを確認
                  if (!item.date) {
                    return null;
                  }

                  return (
                    <div key={index} className="flex justify-between items-center py-4 px-6">
                      <div className="text-left ">{formatDate(item.date, timeRange)}</div>
                      <div className="text-right ">
                        {timeRange === TimeRangeType.YEAR && (
                          <span className="text-xs mr-2">平均</span>
                        )}
                        {item.distance ? formatValue(item.distance, 2) : '-'} km /{' '}
                        {formatValue(item.time)} 分
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
