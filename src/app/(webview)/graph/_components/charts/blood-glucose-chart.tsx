'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';
import type {
  BloodGlucoseGraphData,
  DailyBloodGlucose,
  MonthlyAverageBloodGlucose,
} from '@/types/graph';

import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ReferenceArea,
  ReferenceLine,
  XAxis,
  YAxis,
} from 'recharts';
import {
  calculateYAxisMax,
  formatDate,
  formatTickValue,
  formatValue,
  getTicksFromChartData,
  useChartSwipe,
} from './graph-common';

// ○
function CircleIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <circle cx="12" cy="12" r="10" />
    </svg>
  );
}

// 正方形
function SquareIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <rect width="18" height="18" x="3" y="3" rx="2" />
    </svg>
  );
}

// 五角星
function PentagramIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z" />
    </svg>
  );
}

// 菱形
function DiamondIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z" />
    </svg>
  );
}

// 三角形
function TriangleIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
    </svg>
  );
}

// 五边形
function PentagonIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M12 2 L22.35 8.82 L18.71 21.18 L5.29 21.18 L1.65 8.82 Z" />
    </svg>
  );
}

// 六角形
function HexagonIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
    </svg>
  );
}

// 星形
function StarIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 6}
      y={cy - 6}
      width={12}
      height={12}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M12 1L16.24 7.76L23 12L16.24 16.24L12 23L7.76 16.24L1 12L7.76 7.76L12 1Z" />
    </svg>
  );
}

interface BloodGlucoseChartProps {
  data: BloodGlucoseGraphData;
  timeRange: TimeRangeType;
}

const CHART_COLORS = [
  '#02828C',
  '#197A4B',
  '#4457D1',
  '#C43089',
  '#6947B9',
  '#C2560E',
  '#0770C2',
  '#4D4D4D',
];

const CHART_ACTIVE_DOT_COLORS = [
  '#29AEB8',
  '#15B47F',
  '#4F74F9',
  '#C43089',
  '#8F6AFF',
  '#FF945F',
  '#55BCEC',
  '#808080',
];

const CHART_COMMON_COLORS = {
  REFERENCE_LINE: '#aaa',
  REFERENCE_AREA: '#f0f0f0',
};

// チャート設定を定義（カスタム設定）
const customChartConfig = {
  beforeBfBg: {
    label: '朝食前',
    color: CHART_COLORS[0],
    dotColor: CHART_ACTIVE_DOT_COLORS[0],
    icon: CircleIcon,
  },
  afterBfBg: {
    label: '朝食後',
    color: CHART_COLORS[1],
    dotColor: CHART_ACTIVE_DOT_COLORS[1],
    icon: SquareIcon,
  },
  beforeLnBg: {
    label: '昼食前',
    color: CHART_COLORS[2],
    dotColor: CHART_ACTIVE_DOT_COLORS[2],
    icon: PentagramIcon,
  },
  afterLnBg: {
    label: '昼食後',
    color: CHART_COLORS[3],
    dotColor: CHART_ACTIVE_DOT_COLORS[3],
    icon: DiamondIcon,
  },
  beforeDnBg: {
    label: '夕食前',
    color: CHART_COLORS[4],
    dotColor: CHART_ACTIVE_DOT_COLORS[4],
    icon: TriangleIcon,
  },
  afterDnBg: {
    label: '夕食後',
    color: CHART_COLORS[5],
    dotColor: CHART_ACTIVE_DOT_COLORS[5],
    icon: PentagonIcon,
  },
  beforeSlBg: {
    label: '就寝前',
    color: CHART_COLORS[6],
    dotColor: CHART_ACTIVE_DOT_COLORS[6],
    icon: HexagonIcon,
  },
  onceDailyBg: {
    label: '1日1回',
    color: CHART_COLORS[7],
    dotColor: CHART_ACTIVE_DOT_COLORS[7],
    icon: StarIcon,
  },
};

// Shadcn ChartConfig（icon なし）
const chartConfig = {
  beforeBfBg: {
    label: '朝食前',
    color: CHART_COLORS[0],
  },
  afterBfBg: {
    label: '朝食後',
    color: CHART_COLORS[1],
  },
  beforeLnBg: {
    label: '昼食前',
    color: CHART_COLORS[2],
  },
  afterLnBg: {
    label: '昼食後',
    color: CHART_COLORS[3],
  },
  beforeDnBg: {
    label: '夕食前',
    color: CHART_COLORS[4],
  },
  afterDnBg: {
    label: '夕食後',
    color: CHART_COLORS[5],
  },
  beforeSlBg: {
    label: '就寝前',
    color: CHART_COLORS[6],
  },
  onceDailyBg: {
    label: '1日1回',
    color: CHART_COLORS[7],
  },
} satisfies ChartConfig;

// データキー名リストを定義
const dataKeys = [
  'beforeBfBg',
  'afterBfBg',
  'beforeLnBg',
  'afterLnBg',
  'beforeDnBg',
  'afterDnBg',
  'beforeSlBg',
  'onceDailyBg',
];

export default function BloodGlucoseChart({ data, timeRange }: BloodGlucoseChartProps) {
  // グラフストアの状態を監視
  const { startDate, endDate } = useGraphStore();

  // 各折れ線の表示状態を追跡するステートを追加
  const [visibleLines, setVisibleLines] = useState<Record<string, boolean>>({
    onceDailyBg: true,
    beforeBfBg: true,
    afterBfBg: true,
    beforeLnBg: true,
    afterLnBg: true,
    beforeDnBg: true,
    afterDnBg: true,
    beforeSlBg: true,
  });

  // Legendクリックイベントを処理
  const handleLegendClick = (dataKey: string) => {
    setVisibleLines((prev) => ({
      ...prev,
      [dataKey]: !prev[dataKey],
    }));
  };

  // コンポーネントマウント時に日付範囲を初期化

  // useEffect(() => {
  //   const graphStore = useGraphStore.getState();

  //   // 現在のtimeRangeに基づいて対応する日付範囲を設定
  //   switch (timeRange) {
  //     case TimeRangeType.WEEK:
  //       graphStore.setCurrentWeekRange();
  //       break;
  //     case TimeRangeType.MONTH:
  //       graphStore.setCurrentMonthRange();
  //       break;
  //     case TimeRangeType.YEAR:
  //       graphStore.setCurrentYearRange();
  //       break;
  //   }
  // }, [timeRange]);

  // 共通のチャートスワイプ処理Hookを使用
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useChartSwipe();

  // timeRangeに基づいてチャートデータを処理
  const chartData = (() => {
    if (!data) return [];

    if (timeRange === TimeRangeType.YEAR && data.yearBloodGlucose) {
      // 年度データ - yearBloodGlucoseを使用
      return data.yearBloodGlucose.map((item: MonthlyAverageBloodGlucose) => ({
        date: item.date,
        onceDailyBg: item.monthAverageOnceDailyBg || null,
        beforeBfBg: item.monthAverageBeforeBfBg || null,
        afterBfBg: item.monthAverageAfterBfBg || null,
        beforeLnBg: item.monthAverageBeforeLnBg || null,
        afterLnBg: item.monthAverageAfterLnBg || null,
        beforeDnBg: item.monthAverageBeforeDnBg || null,
        afterDnBg: item.monthAverageAfterDnBg || null,
        beforeSlBg: item.monthAverageBeforeSlBg || null,
      }));
    }

    if (!data.bloodGlucose) return [];

    // 週/月データ - bloodGlucoseを使用
    return data.bloodGlucose.map((item: DailyBloodGlucose) => ({
      date: item.date,
      onceDailyBg: item.onceDailyBg || null,
      beforeBfBg: item.beforeBfBg || null,
      afterBfBg: item.afterBfBg || null,
      beforeLnBg: item.beforeLnBg || null,
      afterLnBg: item.afterLnBg || null,
      beforeDnBg: item.beforeDnBg || null,
      afterDnBg: item.afterDnBg || null,
      beforeSlBg: item.beforeSlBg || null,
    }));
  })();

  // chartDataの全y座標値の最大値を計算する関数
  const getMaxYValue = (data: typeof chartData): number => {
    if (!data || data.length === 0) return 0;

    let maxValue = 0;

    for (const item of data) {
      // 各血糖値データのy座標値をチェック
      const values = [
        item.onceDailyBg,
        item.beforeBfBg,
        item.afterBfBg,
        item.beforeLnBg,
        item.afterLnBg,
        item.beforeDnBg,
        item.afterDnBg,
        item.beforeSlBg,
      ];

      for (const value of values) {
        if (value !== null && value !== undefined && value > maxValue) {
          maxValue = value;
        }
      }
    }

    return maxValue;
  };

  // YAxisのdomainを動的に決定
  const getYAxisDomain = (): [number, number] => {
    const maxValue = getMaxYValue(chartData);

    if (maxValue === 0) {
      return [0, 180];
    }

    // graph-commonのcalculateYAxisMaxと同様の計算方法を使用
    const calculatedMax = (maxValue * 7) / 5;

    // より自然な数値に丸める
    const magnitude = 10 ** Math.floor(Math.log10(calculatedMax));
    const normalized = calculatedMax / magnitude;

    let roundedNormalized: number;
    if (normalized <= 1.5) roundedNormalized = 1.5;
    else if (normalized <= 2) roundedNormalized = 2;
    else if (normalized <= 3) roundedNormalized = 3;
    else if (normalized <= 5) roundedNormalized = 5;
    else roundedNormalized = 10;

    const finalMax = Math.ceil(roundedNormalized * magnitude);

    return [0, finalMax];
  };

  // カスタムLegendを作成 - 2行表示、各行4個
  const renderCustomizedLegend = () => {
    // dataKeysを2つのグループに分割、各グループ4個
    const firstRowKeys = dataKeys.slice(0, 4);
    const secondRowKeys = dataKeys.slice(4);

    // 単一の凡例項目のレンダリング関数を作成
    const renderLegendItem = (dataKey: string) => {
      const config = customChartConfig[dataKey as keyof typeof customChartConfig];
      const isSelected = visibleLines[dataKey];
      const IconComponent = config.icon;

      return (
        <div
          key={dataKey}
          className="flex items-center cursor-pointer border rounded-xl px-1.5 py-0.5 "
          onClick={() => handleLegendClick(dataKey)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleLegendClick(dataKey);
            }
          }}
          aria-pressed={!isSelected}
          style={{
            backgroundColor: isSelected ? config.color : 'transparent',
            borderColor: config.color,
          }}
        >
          <div className="w-4 h-4 flex items-center justify-center">
            <IconComponent cx={8} cy={8} color={isSelected ? '#fff' : config.color} />
          </div>
          <span
            className="text-xs px-1"
            style={{
              color: isSelected ? '#fff' : '#000',
            }}
          >
            {config.label}
          </span>
        </div>
      );
    };

    return (
      <div className="flex flex-col gap-2 mt-2 w-screen ">
        {/* 第1行 - 4つの凡例項目 */}
        <div className="flex justify-center gap-2">{firstRowKeys.map(renderLegendItem)}</div>
        {/* 第2行 - 4つの凡例項目 */}
        <div className="flex justify-center gap-2">{secondRowKeys.map(renderLegendItem)}</div>
      </div>
    );
  };

  return (
    <div>
      <Card className="rounded-none ">
        <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => useGraphStore.getState().goToPreviousPeriod()}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <h3 className="">{useGraphStore.getState().getFormattedDateRangeTitle()}</h3>

          {/* Next Button - 現在の期間では非表示 */}
          {!useGraphStore.getState().isCurrentPeriod() && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => useGraphStore.getState().goToNextPeriod()}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
          {/* 現在の期間ではプレースホルダー要素を表示、レイアウトの一貫性を保持 */}
          {useGraphStore.getState().isCurrentPeriod() && <div className="w-9 h-9" />}
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          {/* Legendとより大きなmarginが占有するスペースを補償するためにコンテナの高さを増加 */}
          <ChartContainer
            key={`${timeRange}-${chartData.length}-${data ? JSON.stringify(Object.keys(data)) : 'no-data'}`}
            config={chartConfig}
            className="h-[440px] w-full"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <LineChart
              accessibilityLayer
              data={chartData}
              margin={{ top: 30, right: 20, left: 0, bottom: 10 }}
            >
              <CartesianGrid
                strokeDasharray="2 4"
                horizontal={true}
                vertical={false}
                stroke="hsl(var(--muted-foreground))"
              />
              <XAxis
                key={timeRange}
                dataKey="date"
                tickLine={false}
                tickMargin={15}
                axisLine={true}
                height={50}
                ticks={getTicksFromChartData(chartData, timeRange)}
                tickFormatter={(value) => formatTickValue(value, timeRange, chartData)}
              />

              <YAxis
                axisLine={false}
                tickLine={false}
                tickCount={7}
                domain={getYAxisDomain()}
                width={50}
                tickFormatter={(value) => value.toLocaleString()}
                label={{
                  value: '(mg/dl)',
                  position: 'top',
                  offset: 12,
                  dx: 15,
                  style: { textAnchor: 'middle' },
                }}
              />
              <Legend content={renderCustomizedLegend} verticalAlign="bottom" height={36} />
              {/* 境界参照線 */}

              <ReferenceLine y={200} stroke={CHART_COMMON_COLORS.REFERENCE_LINE} strokeWidth={1} />

              {/* 血糖正常範囲外のグレー背景エリア */}

              <ReferenceArea y1={200} fill={CHART_COMMON_COLORS.REFERENCE_AREA} fillOpacity={0.6} />
              <ChartTooltip
                cursor={{
                  strokeWidth: 1,
                  strokeDasharray: '',
                }}
                content={({ active, payload, label }) => {
                  if (!active || !payload || !payload.length) return null;

                  return (
                    <div className="bg-white shadow-md p-4 rounded-lg ">
                      <div className="text-sm font-medium mb-2">
                        {formatDate(String(label), timeRange)}
                        {timeRange === TimeRangeType.YEAR && 'の平均'}
                      </div>
                      <div className="space-y-1.5">
                        {dataKeys.map((key) => {
                          // 対応するkeyのpayload項目を検索
                          const item = payload.find((p) => p.dataKey === key);
                          // その折れ線が非表示またはデータがない場合は表示しない
                          if (!visibleLines[key] || !item || item.value === null) return null;

                          const config = customChartConfig[key as keyof typeof customChartConfig];
                          const IconComponent = config.icon;

                          return (
                            <div key={key} className="flex items-center justify-between">
                              <div className="flex items-center gap-1.5">
                                <div className="w-3 h-3 flex items-center justify-center">
                                  <IconComponent cx={6} cy={6} color={config.dotColor} />
                                </div>
                                <span className="text-sm">{config.label}:</span>
                              </div>
                              <span className="text-sm font-medium">
                                <span className="text-normal font-bold">
                                  {formatValue(item.value as number | null)}{' '}
                                </span>

                                <span className="text-xs">mg/dl</span>
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                }}
              />

              {/* 表示状態に基づいて条件付きで折れ線をレンダリング */}

              {visibleLines.beforeBfBg && (
                <Line
                  dataKey="beforeBfBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[0]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.beforeBfBg !== null) {
                      return (
                        <CircleIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[0]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.beforeBfBg !== null) {
                      return (
                        <CircleIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[0]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  name="朝食前"
                />
              )}
              {visibleLines.afterBfBg && (
                <Line
                  dataKey="afterBfBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[1]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.afterBfBg !== null) {
                      return (
                        <SquareIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[1]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.afterBfBg !== null) {
                      return (
                        <SquareIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[1]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  name="朝食後"
                />
              )}
              {visibleLines.beforeLnBg && (
                <Line
                  dataKey="beforeLnBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[2]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.beforeLnBg !== null) {
                      return (
                        <PentagonIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[2]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.beforeLnBg !== null) {
                      return (
                        <PentagonIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[2]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  name="昼食前"
                />
              )}
              {visibleLines.afterLnBg && (
                <Line
                  dataKey="afterLnBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[3]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.afterLnBg !== null) {
                      return (
                        <DiamondIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[3]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.afterLnBg !== null) {
                      return (
                        <DiamondIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[3]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  name="昼食後"
                />
              )}
              {visibleLines.beforeDnBg && (
                <Line
                  dataKey="beforeDnBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[4]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.beforeDnBg !== null) {
                      return (
                        <TriangleIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[4]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.beforeDnBg !== null) {
                      return (
                        <TriangleIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[4]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  name="夕食前"
                />
              )}
              {visibleLines.afterDnBg && (
                <Line
                  dataKey="afterDnBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[5]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.afterDnBg !== null) {
                      return (
                        <PentagramIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[5]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.afterDnBg !== null) {
                      return (
                        <PentagramIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[5]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  name="夕食後"
                />
              )}
              {visibleLines.beforeSlBg && (
                <Line
                  dataKey="beforeSlBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[6]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.beforeSlBg !== null) {
                      return (
                        <HexagonIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[6]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.beforeSlBg !== null) {
                      return (
                        <HexagonIcon
                          cx={props.cx}
                          cy={props.cy}
                          color={CHART_ACTIVE_DOT_COLORS[6]}
                        />
                      );
                    }
                    return <g />;
                  }}
                  name="就寝前"
                />
              )}

              {visibleLines.onceDailyBg && (
                <Line
                  dataKey="onceDailyBg"
                  stroke={CHART_ACTIVE_DOT_COLORS[7]}
                  strokeWidth={2}
                  dot={(props: any) => {
                    if (props.payload && props.payload.onceDailyBg !== null) {
                      return (
                        <StarIcon cx={props.cx} cy={props.cy} color={CHART_ACTIVE_DOT_COLORS[7]} />
                      );
                    }
                    return <g />;
                  }}
                  activeDot={(props: any) => {
                    if (props.payload && props.payload.onceDailyBg !== null) {
                      return (
                        <StarIcon cx={props.cx} cy={props.cy} color={CHART_ACTIVE_DOT_COLORS[7]} />
                      );
                    }
                    return <g />;
                  }}
                  name="1日1回"
                />
              )}
            </LineChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="flex-col items-start gap-4 text-sm " />
        <div className="border border-zinc-200 mt-4">
          <div className="w-full flex justify-between items-stretch ">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex items-center justify-center px-4 ">
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の平均/日
            </div>

            <div className="text-xl font-bold  flex-1 text-right py-2 pr-4">
              {data?.periodAverageBloodGlucose
                ? data.periodAverageBloodGlucose.toLocaleString()
                : '-'}
              <span className="text-sm font-normal ml-1">mg/dl</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Data Card */}
      <Card className="m-4">
        <CardContent className="p-0">
          <div className="rounded-lg overflow-hidden">
            {chartData.length > 0 && (
              <div className="divide-y">
                {chartData.map((item, index) => {
                  // hasOnceDailyBgに基づく条件判断は行わず、常にすべてのデータを表示
                  return (
                    <div key={index} className="py-4 px-6">
                      {timeRange === TimeRangeType.YEAR ? (
                        <div>
                          <div className="text-left  flex justify-between">
                            <span>{formatDate(String(item.date), timeRange)}</span>
                          </div>
                          <div className="text-xs text-muted-foreground mt-2">(平均/日)</div>
                          <div className="grid grid-cols-2 gap-x-12 gap-y-1 text-sm mt-2">
                            <div className="flex justify-between">
                              <span>朝食前:</span>
                              <span>{formatValue(item.beforeBfBg)} mg/dl</span>
                            </div>
                            <div className="flex justify-between">
                              <span>朝食後:</span>
                              <span>{formatValue(item.afterBfBg)} mg/dl</span>
                            </div>
                            <div className="flex justify-between">
                              <span>昼食前:</span>
                              <span>{formatValue(item.beforeLnBg)} mg/dl</span>
                            </div>
                            <div className="flex justify-between">
                              <span>昼食後:</span>
                              <span>{formatValue(item.afterLnBg)} mg/dl</span>
                            </div>
                            <div className="flex justify-between">
                              <span>夕食前:</span>
                              <span>{formatValue(item.beforeDnBg)} mg/dl</span>
                            </div>
                            <div className="flex justify-between">
                              <span>夕食後:</span>
                              <span>{formatValue(item.afterDnBg)} mg/dl</span>
                            </div>
                            <div className="flex justify-between">
                              <span>就寝前:</span>
                              <span>{formatValue(item.beforeSlBg)} mg/dl</span>
                            </div>
                            <div className="flex justify-between">
                              <span>1日1回:</span>
                              <span>{formatValue(item.onceDailyBg)} mg/dl</span>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div>
                          {!item.beforeBfBg &&
                          !item.afterBfBg &&
                          !item.beforeLnBg &&
                          !item.afterLnBg &&
                          !item.beforeDnBg &&
                          !item.afterDnBg &&
                          !item.beforeSlBg ? (
                            <div className="text-left  flex justify-between">
                              <span>{formatDate(String(item.date), timeRange)}</span>
                              <span>{formatValue(item.onceDailyBg)} mg/dl</span>
                            </div>
                          ) : (
                            <div>
                              <div className="text-left  flex justify-between">
                                <span>{formatDate(String(item.date), timeRange)}</span>
                              </div>
                              <div className="grid grid-cols-2 gap-x-12 gap-y-1 text-sm mt-2">
                                <div className="flex justify-between">
                                  <span>朝食前:</span>
                                  <span>{formatValue(item.beforeBfBg)} mg/dl</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>朝食後:</span>
                                  <span>{formatValue(item.afterBfBg)} mg/dl</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>昼食前:</span>
                                  <span>{formatValue(item.beforeLnBg)} mg/dl</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>昼食後:</span>
                                  <span>{formatValue(item.afterLnBg)} mg/dl</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>夕食前:</span>
                                  <span>{formatValue(item.beforeDnBg)} mg/dl</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>夕食後:</span>
                                  <span>{formatValue(item.afterDnBg)} mg/dl</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>就寝前:</span>
                                  <span>{formatValue(item.beforeSlBg)} mg/dl</span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
