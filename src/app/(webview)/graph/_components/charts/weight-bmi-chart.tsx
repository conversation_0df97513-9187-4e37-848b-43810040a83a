'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';
import type { DailyWeightBmi, MonthlyAverageWeightBmi, WeightBmiGraphData } from '@/types/graph';

import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  Area,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ReferenceLine,
  XAxis,
  YAxis,
} from 'recharts';
// import BMIDialog from '../bmi-dialog';
import {
  calculateYAxisMax,
  formatDate,
  formatTickValue,
  formatValue,
  getTicksFromChartData,
  useChartSwipe,
} from './graph-common';
interface WeightBmiChartProps {
  data: WeightBmiGraphData;
  timeRange: TimeRangeType;
}

const chartConfig = {
  weight: {
    label: '体重',
    color: 'hsl(var(--primary))',
  },
  // bmi: {
  //   label: 'BMI',
  //   color: 'hsl(var(--primary-60))',
  // },
} satisfies ChartConfig;

export default function WeightBmiChart({ data, timeRange }: WeightBmiChartProps) {
  // グラフストアの状態を監視
  const { startDate, endDate } = useGraphStore();
  const [referenceX, setReferenceX] = useState<any>();
  const [tooltipX, setTooltipX] = useState<number>(0);
  const [tooltipWidth, setTooltipWidth] = useState<number>(150);
  const [chartBounds, setChartBounds] = useState<{
    left: number;
    right: number;
  }>({ left: 55, right: 374 });

  // Ref of the chart container
  const chartContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setTooltipWidth(timeRange === TimeRangeType.YEAR ? 150 : 130);
  }, [timeRange]);

  // チャート境界を更新する関数
  const updateChartBounds = useCallback(() => {
    if (chartContainerRef.current) {
      const containerRect = chartContainerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;

      // Y軸の幅と余白を考慮してチャート境界を計算
      const leftYAxisWidth = 50;
      const rightYAxisWidth = 50;
      const leftMargin = 5;
      const rightMargin = 5;
      const additionalLeftPadding = 0; // Y軸ラベル等の余白

      const chartLeft = leftYAxisWidth + leftMargin + additionalLeftPadding;
      const chartRight = containerWidth - rightYAxisWidth - rightMargin;

      setChartBounds({
        left: chartLeft,
        right: chartRight,
      });
    }
  }, []);

  // 変化を検出して更新
  useEffect(() => {
    updateChartBounds();

    const handleResize = () => {
      updateChartBounds();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [updateChartBounds]);

  // tooltipのX位置を動的に計算する関数
  const calculateTooltipX = useCallback(
    (currentChartX: number): number => {
      const halfTooltipWidth = tooltipWidth / 2;
      const { left: chartLeftBoundary, right: chartRightBoundary } = chartBounds;

      // tooltipの理想的な位置を計算（マウス位置を中心に表示）
      const idealTooltipX = currentChartX - halfTooltipWidth;

      // 左境界検出：tooltipが左境界を超えないようにする
      if (idealTooltipX < chartLeftBoundary) {
        return chartLeftBoundary;
      }

      // 右境界検出：tooltipが右境界を超えないようにする
      if (idealTooltipX + tooltipWidth > chartRightBoundary) {
        return chartRightBoundary - tooltipWidth;
      }

      // 通常の場合：tooltipをマウス位置を中心に表示
      return idealTooltipX;
    },
    [tooltipWidth, chartBounds],
  );
  // コンポーネントマウント時に日付範囲を初期化

  // useEffect(() => {
  //   const graphStore = useGraphStore.getState();

  //   // 現在のtimeRangeに基づいて対応する日付範囲を設定
  //   switch (timeRange) {
  //     case TimeRangeType.WEEK:
  //       graphStore.setCurrentWeekRange();
  //       break;
  //     case TimeRangeType.MONTH:
  //       graphStore.setCurrentMonthRange();
  //       break;
  //     case TimeRangeType.YEAR:
  //       graphStore.setCurrentYearRange();
  //       break;
  //   }
  // }, [timeRange]);

  // 共通のチャートスワイプ処理Hookを使用
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useChartSwipe();

  // timeRangeに基づいてチャートデータを処理
  const chartData = (() => {
    if (!data) return [];

    if (timeRange === TimeRangeType.YEAR && data.yearWeightBmi) {
      // 年間データ - yearWeightBmiを使用
      return data.yearWeightBmi.map((item: MonthlyAverageWeightBmi) => ({
        date: item.date,
        weight: item.monthAverageWeight || null,
        // bmi: item.monthAverageBmi || null,
      }));
    }

    if (!data.weightBmi) return [];

    // 週/月データ - weightBmiを使用
    return data.weightBmi.map((item: DailyWeightBmi) => ({
      date: item.date,
      weight: item.weight || null,
      // bmi: item.bmi || null,
    }));

    // 共通のチャートスワイプ処理Hookを使用
  })();

  const DEFAULT_WEIGHT_MAX = 60;
  // const DEFAULT_BMI_MAX = 30;

  // Y轴の最大値を計算（共通関数を使用）
  const weightYAxisMax = calculateYAxisMax(chartData, 'weight', DEFAULT_WEIGHT_MAX);
  // const bmiYAxisMax = calculateYAxisMax(chartData, 'bmi', DEFAULT_BMI_MAX);

  // 如果最大值为0，则最大值设置为DEFAULT_BMI_MAX,否则设置为最大值+1
  // const getBmiMax = () => {
  //   const maxValue = Math.max(...chartData.map((item) => item.bmi || 0));
  //   return maxValue === 0 ? DEFAULT_BMI_MAX : maxValue + 1;
  // };

  // const getBmiMin = () => {
  //   const minValue = Math.min(...chartData.map((item) => item.bmi || 0));
  //   return minValue === 0 ? 0 : minValue - 1;
  // };

  const getWeightMax = () => {
    const maxValue = Math.max(...chartData.map((item) => item.weight || 0));
    return maxValue === 0 ? DEFAULT_WEIGHT_MAX : maxValue + 2;
  };

  const getWeightMin = () => {
    const minValue = Math.min(...chartData.map((item) => item.weight || 0));
    return minValue === 0 ? 0 : minValue - 2;
  };

  return (
    <div className="  ">
      <Card className="rounded-none ">
        <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => useGraphStore.getState().goToPreviousPeriod()}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <h3 className="">{useGraphStore.getState().getFormattedDateRangeTitle()}</h3>

          {/* Next Button - 現在の期間では非表示 */}
          {!useGraphStore.getState().isCurrentPeriod() && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => useGraphStore.getState().goToNextPeriod()}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
          {/* 現在の期間ではプレースホルダー要素を表示、レイアウトの一貫性を保つ */}
          {useGraphStore.getState().isCurrentPeriod() && <div className="w-9 h-9" />}
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          {/* Legendとより大きなmarginが占有するスペースを補償するためにコンテナ高さを増加 */}
          <ChartContainer
            key={`${timeRange}-${chartData.length}-${data ? JSON.stringify(Object.keys(data)) : 'no-data'}`}
            ref={chartContainerRef}
            config={chartConfig}
            className="h-[430px] w-full"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <ComposedChart
              accessibilityLayer
              data={chartData}
              margin={{ top: 30, right: 20, left: -10, bottom: 10 }}
              onMouseMove={(state) => {
                if (state?.activeLabel && state?.chartX !== undefined) {
                  console.log(state.activeLabel);
                  setReferenceX(state.activeLabel);

                  // tooltipのX位置を動的に計算
                  const newTooltipX = calculateTooltipX(state.chartX);

                  setTooltipX(newTooltipX);
                }
              }}
            >
              <defs>
                <linearGradient id="weightAreaGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="hsl(var(--primary))" stopOpacity={1} />
                  <stop offset="100%" stopColor="hsl(var(--primary))" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid
                strokeDasharray="2 4"
                horizontal={true}
                vertical={false}
                stroke="hsl(var(--muted-foreground))"
              />
              <XAxis
                key={timeRange}
                dataKey="date"
                tickLine={false}
                tickMargin={15}
                axisLine={true}
                height={50}
                ticks={getTicksFromChartData(chartData, timeRange)}
                tickFormatter={(value) => formatTickValue(value, timeRange, chartData)}
              />
              <YAxis
                yAxisId="left"
                axisLine={false}
                tickLine={false}
                domain={[getWeightMin(), weightYAxisMax]}
                width={50}
                tickCount={7}
                tickFormatter={(value) => value.toLocaleString()}
                label={{
                  value: '(kg)',
                  position: 'top',
                  offset: 12,
                  dx: 15,
                  style: { textAnchor: 'middle' },
                }}
              />
              {/* <YAxis
                yAxisId="right"
                orientation="right"
                axisLine={false}
                tickLine={false}
                tickCount={7}
                domain={[getBmiMin(), bmiYAxisMax]}
                width={50}
                tickFormatter={(value) => value.toLocaleString()}
              /> */}

              {referenceX && (
                <ReferenceLine x={referenceX} yAxisId="left" stroke="#666" strokeWidth={1} />
              )}
              <Legend
                verticalAlign="bottom"
                height={30}
                wrapperStyle={{
                  fontSize: '14px',
                  fontWeight: '500',
                  display: 'flex',
                  justifyContent: 'center',

                  width: '98%',
                }}
                content={({ payload }) => {
                  if (payload?.length) {
                    return (
                      <div className="flex justify-center items-center gap-[40px]">
                        {payload.map((entry, index) => (
                          <div key={index} className="flex items-center gap-2">
                            {entry.value === '体重' ? (
                              <div className="flex items-center ml-8">
                                <div
                                  style={{
                                    width: '32px',
                                    height: '2px',
                                    backgroundColor: entry.color,
                                  }}
                                />
                                <div
                                  className="rounded-full"
                                  style={{
                                    width: '8px',
                                    height: '8px',
                                    backgroundColor: entry.color,
                                    marginLeft: '-18px',
                                    marginRight: '12px',
                                  }}
                                />
                              </div>
                            ) : (
                              <div className="flex items-center">
                                <div
                                  style={{
                                    width: '32px',
                                    height: '2px',
                                    backgroundColor: '#000',
                                  }}
                                />
                                <div
                                  className="rounded-full"
                                  style={{
                                    width: '8px',
                                    height: '8px',
                                    backgroundColor: '#000',
                                    marginLeft: '-18px',
                                    marginRight: '12px',
                                  }}
                                />
                              </div>
                            )}
                            <span className="text-sm font-medium">{entry.value}</span>
                            {/* {entry.value === 'BMI' && <BMIDialog />} */}
                          </div>
                        ))}
                      </div>
                    );
                  }
                  return null;
                }}
                payload={[
                  { value: '体重', type: 'rect', color: 'hsl(var(--primary))' },
                  // { value: 'BMI', type: 'line', color: '#000' },
                ]}
              />
              <ChartTooltip
                position={{ x: tooltipX, y: 20 }}
                cursor={{
                  opacity: 0,
                }}
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length && label) {
                    return (
                      <div
                        className="bg-primary-10 shadow-md p-4 rounded-2xl relative"
                        style={{ width: `${tooltipWidth}px` }}
                      >
                        <div className="text-sm font-medium mb-1">
                          {formatDate(String(label), timeRange)}
                        </div>
                        <div className="text-2xl d">
                          {timeRange === TimeRangeType.YEAR ? (
                            <span className="text-sm">平均</span>
                          ) : (
                            ''
                          )}
                          <span className="font-bold">
                            {' '}
                            {payload[0]?.value ? payload[0]?.value.toLocaleString() : '-'}{' '}
                          </span>

                          <span className="text-sm">kg</span>
                        </div>
                        {/* <div>
                          <span className="mr-2">BMI:</span>
                          <span>
                            {payload[1]?.value ? payload[1]?.value.toLocaleString() : '-'}
                          </span>
                        </div> */}
                      </div>
                    );
                  }
                  return null;
                }}
              />

              <Area
                dataKey="weight"
                yAxisId="left"
                stroke="hsl(var(--primary))"
                fill="url(#weightAreaGradient)"
                strokeWidth={2}
                dot={{ r: 3, fill: 'hsl(var(--primary))' }}
                activeDot={{ r: 6 }}
                name="体重"
              />

              {/* <Line
                dataKey="bmi"
                yAxisId="right"
                stroke="#000"
                strokeWidth={2}
                dot={{ r: 3, fill: '#000' }}
                activeDot={{ r: 6 }}
                name="BMI"
              /> */}
            </ComposedChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="flex-col items-start gap-4 text-sm p-0" />
        <div className="border border-gray-200  text-center">
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1  flex items-center justify-center  " />
            <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1 py-2 px-4 ">体重</div>
            {/* <div className="text-muted-foreground bg-zinc-200 text-xs  flex-1 py-2 px-4 ">BMI</div> */}
          </div>
          <hr className="border-zinc-100" />
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex-1 flex items-center justify-center   ">
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の平均/日
            </div>
            <div className="  text-xs flex-1 py-2 px-4  ">
              <span className="mx-1 text-xl font-bold ">
                {formatValue(data?.periodAverageWeight, 1)}
              </span>{' '}
              kg
            </div>
            {/* <div className=" text-xs flex-1 py-2 px-4  ">
              <span className="mx-1 text-xl font-bold ">
                {formatValue(data?.periodAverageBmi, 1)}
              </span>
            </div> */}
          </div>
          <hr className="border-zinc-100" />
          <div className="w-full flex justify-between items-stretch flex-1">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex-1 flex items-center justify-center  ">
              前
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              との比較
            </div>
            <div className="text-xs flex-1 py-2 px-4">
              <span className="mx-1 text-xl font-bold">
                {data?.periodAverageWeightCompare === 999
                  ? '-'
                  : data?.periodAverageWeightCompare > 0
                    ? `+${formatValue(data.periodAverageWeightCompare, 1)}`
                    : formatValue(data?.periodAverageWeightCompare, 1) || '-'}
              </span>{' '}
              kg
            </div>
            {/* <div className="text-xs flex-1 py-2 px-4">
              <span className="mx-1 text-xl font-bold">
                {data?.periodAverageBmiCompare === 999
                  ? '-'
                  : data?.periodAverageBmiCompare > 0
                    ? `+${formatValue(data.periodAverageBmiCompare, 1)}`
                    : formatValue(data?.periodAverageBmiCompare, 1) || '-'}
              </span>{' '}
            </div> */}
          </div>
        </div>
      </Card>

      {/* データカード */}
      <Card className="m-4">
        <CardContent className="p-0">
          <div className="bg-white rounded-lg overflow-hidden">
            {chartData.length > 0 && (
              <div className="divide-y">
                {chartData.map((item, index) => {
                  // item.dateが存在し有効であることを確認
                  if (!item.date) {
                    return null;
                  }

                  return (
                    <div key={index} className="flex justify-between items-center py-4 px-6">
                      <div className="text-left ">{formatDate(item.date, timeRange)}</div>
                      <div className="text-right ">
                        {timeRange === TimeRangeType.YEAR && (
                          <span className="text-xs mr-2">平均</span>
                        )}
                        {formatValue(item.weight, 1)} kg {/* / {formatValue(item.bmi, 1)} */}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
