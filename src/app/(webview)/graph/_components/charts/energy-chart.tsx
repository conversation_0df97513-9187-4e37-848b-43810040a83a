'use client';
import type { DailyEnergy, EnergyGraphData, MonthlyAverageEnergy } from '@/types/graph';
import { useCallback, useEffect, useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { COLORS } from '@/const/colors';
import { useGraphStore } from '@/store/graph';
import { TimeRangeType } from '@/types/enums';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Bar, BarChart, CartesianGrid, ReferenceLine, XAxis, YAxis } from 'recharts';
import {
  calculateYAxisMax,
  formatDate,
  formatTickValue,
  formatValue,
  getTicksFromChartData,
  useChartSwipe,
} from './graph-common';

// 自定义游标组件 - 黑色直线
const CustomizedCursor = ({ points }: { points?: Array<{ x: number; y: number }> }) => {
  if (!points || points.length === 0) return null;

  const { x } = points[0];

  return (
    <line x1={x} y1={0} x2={x} y2="100%" stroke="#000000" strokeWidth={1} strokeDasharray="none" />
  );
};

interface EnergyChartProps {
  data: EnergyGraphData;
  timeRange: TimeRangeType;
}

const chartConfig = {
  energy: {
    label: '消費カロリー',
    color: 'hsl(var(--primary-60))',
  },
} satisfies ChartConfig;

export default function EnergyChart({ data, timeRange }: EnergyChartProps) {
  const [referenceX, setReferenceX] = useState<string | null>(null);
  const [tooltipX, setTooltipX] = useState<number>(0);
  const [tooltipWidth, setTooltipWidth] = useState<number>(
    timeRange === TimeRangeType.YEAR ? 160 : 140,
  );
  const [chartBounds, setChartBounds] = useState<{
    left: number;
    right: number;
  }>({ left: 60, right: 410 });

  // Ref of the chart container
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // グラフストアの状態を監視
  const { startDate, endDate } = useGraphStore();

  // timeRangeが変更されたときにtooltipWidthを更新
  useEffect(() => {
    setTooltipWidth(timeRange === TimeRangeType.YEAR ? 160 : 140);
  }, [timeRange]);

  // チャート境界を更新する関数
  const updateChartBounds = useCallback(() => {
    if (chartContainerRef.current) {
      const containerRect = chartContainerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;

      // Y軸の幅と余白を考慮してチャート境界を計算
      const yAxisWidth = 50;
      const leftMargin = 10;
      const rightMargin = 20;
      const additionalLeftPadding = 0; // Y軸ラベル等の余白

      const chartLeft = yAxisWidth + leftMargin + additionalLeftPadding;
      const chartRight = containerWidth - rightMargin;

      setChartBounds({
        left: chartLeft,
        right: chartRight,
      });
    }
  }, []);

  // 変化を検出して更新
  useEffect(() => {
    updateChartBounds();

    const handleResize = () => {
      updateChartBounds();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [updateChartBounds]);

  // tooltipのX位置を動的に計算する関数
  const calculateTooltipX = useCallback(
    (currentChartX: number): number => {
      const halfTooltipWidth = tooltipWidth / 2;
      const { left: chartLeftBoundary, right: chartRightBoundary } = chartBounds;

      // tooltipの理想的な位置を計算（マウス位置を中心に表示）
      const idealTooltipX = currentChartX - halfTooltipWidth;

      // 左境界検出：tooltipが左境界を超えないようにする
      if (idealTooltipX < chartLeftBoundary) {
        return chartLeftBoundary;
      }

      // 右境界検出：tooltipが右境界を超えないようにする
      if (idealTooltipX + tooltipWidth > chartRightBoundary) {
        return chartRightBoundary - tooltipWidth;
      }

      // 通常の場合：tooltipをマウス位置を中心に表示
      return idealTooltipX;
    },
    [tooltipWidth, chartBounds],
  );
  // コンポーネントマウント時に日付範囲を初期化

  // useEffect(() => {
  //   const graphStore = useGraphStore.getState();

  //   // 現在のtimeRangeに基づいて対応する日付範囲を設定
  //   switch (timeRange) {
  //     case TimeRangeType.WEEK:
  //       graphStore.setCurrentWeekRange();
  //       break;
  //     case TimeRangeType.MONTH:
  //       graphStore.setCurrentMonthRange();
  //       break;
  //     case TimeRangeType.YEAR:
  //       graphStore.setCurrentYearRange();
  //       break;
  //   }
  // }, [timeRange]);

  // 共通のチャートスワイプ処理Hookを使用
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useChartSwipe();

  // timeRangeに基づいてチャートデータを処理
  const chartData = (() => {
    if (!data) return [];

    if (timeRange === TimeRangeType.YEAR && data.yearEnergyList) {
      // 年間データ - yearEnergyListを使用
      return data.yearEnergyList.map((item: MonthlyAverageEnergy) => ({
        date: item.date,
        energy: item.monthAverageEnergy || null,
      }));
    }

    // 週/月データ - energyListを使用
    // 防御的チェックを追加、energyListの存在を確認
    if (!data.energyList) return [];

    return data.energyList.map((item: DailyEnergy) => ({
      date: item.date,
      energy: item.energy || null,
    }));
  })();

  const DEFAULT_MAX = 600;
  // Y轴の最大値を計算（共通関数を使用）
  const yAxisMax = calculateYAxisMax(chartData, 'energy', DEFAULT_MAX);

  return (
    <div className="  ">
      <Card className="rounded-none ">
        <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => useGraphStore.getState().goToPreviousPeriod()}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <h3 className="">{useGraphStore.getState().getFormattedDateRangeTitle()}</h3>

          {/* Next Button - 現在の期間では非表示 */}
          {!useGraphStore.getState().isCurrentPeriod() && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => useGraphStore.getState().goToNextPeriod()}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
          {/* 現在の期間ではプレースホルダー要素を表示、レイアウトの一貫性を保つ */}
          {useGraphStore.getState().isCurrentPeriod() && <div className="w-9 h-9" />}
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          <ChartContainer
            key={`${timeRange}-${chartData.length}-${data ? JSON.stringify(Object.keys(data)) : 'no-data'}`}
            ref={chartContainerRef}
            config={chartConfig}
            className="h-[400px] w-full"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <BarChart
              accessibilityLayer
              data={chartData}
              margin={{ top: 30, right: 20, left: -10, bottom: 10 }}
              barSize={15}
              barGap={0.2}
              onMouseMove={(state) => {
                if (state?.activeLabel && state?.chartX !== undefined) {
                  setReferenceX(state.activeLabel);

                  // tooltipのX位置を動的に計算
                  const newTooltipX = calculateTooltipX(state.chartX);
                  setTooltipX(newTooltipX);
                }
              }}
            >
              <CartesianGrid
                strokeDasharray="2 4"
                horizontal={true}
                vertical={false}
                stroke="hsl(var(--muted-foreground))"
              />
              <XAxis
                key={timeRange}
                dataKey="date"
                tickMargin={15}
                tickLine={false}
                axisLine={true}
                height={50}
                ticks={getTicksFromChartData(chartData, timeRange)}
                tickFormatter={(value) => formatTickValue(value, timeRange, chartData)}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                domain={[0, yAxisMax]}
                tickCount={7}
                width={50}
                tickFormatter={(value) => value.toLocaleString()}
                label={{
                  value: '(kcal)',
                  position: 'top',
                  offset: 12,
                  dx: 15,
                  style: { textAnchor: 'middle' },
                }}
              />
              {referenceX && <ReferenceLine x={referenceX} stroke="#666" strokeWidth={1} />}
              <ChartTooltip
                position={{ x: tooltipX, y: 20 }}
                cursor={false}
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length && label) {
                    return (
                      <div
                        className="bg-primary-10 shadow-md p-4 rounded-2xl relative"
                        style={{ width: tooltipWidth }}
                      >
                        <div className="text-sm font-medium mb-1">
                          {formatDate(String(label), timeRange)}
                        </div>
                        <div className="text-2xl font-bold">
                          {timeRange === TimeRangeType.YEAR ? (
                            <span className="text-sm mr-1">平均</span>
                          ) : (
                            ''
                          )}
                          {payload[0]?.value ? payload[0]?.value.toLocaleString() : '-'}{' '}
                          <span className="text-base font-normal">kcal</span>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />

              {data?.energyTarget && (
                <ReferenceLine y={data.energyTarget} stroke={COLORS.success} strokeWidth={2} />
              )}

              <Bar
                dataKey="energy"
                fill="hsl(var(--primary-60))"
                radius={[4, 4, 0, 0]}
                name="消費カロリー"
                maxBarSize={50}
                barSize={timeRange === TimeRangeType.MONTH ? 8 : undefined}
              />
            </BarChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="flex-col items-start gap-4 text-sm ">
          <div className="w-full text-center flex items-center justify-center">
            <span>
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の目標達成日数{' '}
            </span>

            <span className="text-primary font-bold text-lg mx-1">
              {data?.targetAchieve || '0'}
            </span>
            <span>日</span>
          </div>
        </CardFooter>
        <div className="border border-zinc-200 ">
          <div className="w-full flex justify-between items-stretch ">
            <div className="text-muted-foreground bg-zinc-200 text-xs flex items-center justify-center px-4 ">
              この
              {timeRange === TimeRangeType.WEEK
                ? '週'
                : timeRange === TimeRangeType.MONTH
                  ? '月'
                  : '年'}
              の平均/日
            </div>

            <div className="text-xl font-bold  flex-1 text-right py-2 pr-4">
              {data?.periodAverageEnergy ? data?.periodAverageEnergy.toLocaleString() : '-'}{' '}
              <span className="text-sm font-normal">kcal</span>
            </div>
          </div>
        </div>
      </Card>

      {/* データカード */}
      <Card className="m-4">
        <CardContent className="p-0">
          <div className="bg-white rounded-lg overflow-hidden">
            {chartData.length > 0 && (
              <div className="divide-y">
                {chartData.map((item, index) => {
                  // item.dateが存在し有効であることを確認
                  if (!item.date) {
                    return null;
                  }

                  return (
                    <div key={index} className="flex justify-between items-center py-4 px-6">
                      <div className="text-left ">{formatDate(item.date, timeRange)}</div>
                      <div className="text-right ">
                        {timeRange === TimeRangeType.YEAR && (
                          <span className="text-xs mr-2">平均</span>
                        )}
                        <span className="font-normal"> {formatValue(item.energy)}</span>
                        <span className="font-normal"> kcal</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
