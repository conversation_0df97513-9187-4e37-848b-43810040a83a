'use client';

import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TimeRangeType } from '@/types/enums';
import { endOfMonth, endOfWeek, endOfYear, startOfMonth, startOfWeek, startOfYear } from 'date-fns';
import { useEffect } from 'react';

interface TimeRangeTabProps {
  timeRange: TimeRangeType;
  startDate: Date;
  endDate: Date;
  onTimeRangeChange: (value: TimeRangeType) => void;
  onDateRangeChange: (start: Date, end: Date) => void;
}

export default function TimeRangeTab({
  timeRange,
  onTimeRangeChange,
  onDateRangeChange,
}: TimeRangeTabProps) {
  const updateDateRange = (rangeType: TimeRangeType) => {
    const now = new Date();
    let start: Date;
    let end: Date;

    switch (rangeType) {
      case TimeRangeType.WEEK:
        start = startOfWeek(now, { weekStartsOn: 1 });
        end = endOfWeek(now, { weekStartsOn: 1 });
        break;
      case TimeRangeType.MONTH:
        start = startOfMonth(now);
        end = endOfMonth(now);
        break;
      case TimeRangeType.YEAR:
        start = startOfYear(now);
        end = endOfYear(now);
        break;
      default:
        start = now;
        end = now;
    }

    onDateRangeChange(start, end);
  };

  const handleTimeRangeChange = (value: string) => {
    let newTimeRange: TimeRangeType;
    switch (value) {
      case TimeRangeType.WEEK:
        newTimeRange = TimeRangeType.WEEK;
        break;
      case TimeRangeType.MONTH:
        newTimeRange = TimeRangeType.MONTH;
        break;
      case TimeRangeType.YEAR:
        newTimeRange = TimeRangeType.YEAR;
        break;
      default:
        newTimeRange = TimeRangeType.WEEK;
    }

    onTimeRangeChange(newTimeRange);
  };

  // useEffect(() => {
  //   updateDateRange(timeRange);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  return (
    <div className="w-full flex justify-center ">
      <Tabs
        defaultValue={timeRange}
        value={timeRange}
        onValueChange={handleTimeRangeChange}
        className="w-full max-w-md p-4 "
      >
        <TabsList className="grid grid-cols-3 w-full bg-card">
          <TabsTrigger value={TimeRangeType.WEEK} className="data-[state=active]:font-bold">
            週
          </TabsTrigger>
          <TabsTrigger value={TimeRangeType.MONTH} className="data-[state=active]:font-bold">
            月
          </TabsTrigger>
          <TabsTrigger value={TimeRangeType.YEAR} className="data-[state=active]:font-bold">
            年
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
