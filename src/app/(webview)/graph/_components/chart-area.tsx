import type { TimeRangeType } from '@/types/enums';
import type {
  BloodGlucoseGraphData,
  BloodPressureGraphData,
  EnergyGraphData,
  FatPercentageGraphData,
  GraphTab,
  SleepGraphData,
  StepDistanceTimeGraphData,
  StepGraphData,
  WeightBmiGraphData,
} from '@/types/graph';
import BloodGlucoseChart from './charts/blood-glucose-chart';
import BloodPressureChart from './charts/blood-pressure-chart';
import EnergyChart from './charts/energy-chart';
import FatPercentageChart from './charts/fat-percentage-chart';
import SleepChart from './charts/sleep-chart';
import StepChart from './charts/step-chart';
import StepDistanceTimeChart from './charts/step-distantce-time-chart';
import WeightBmiChart from './charts/weight-bmi-chart';
interface ChartAreaProps {
  activeTab: GraphTab;
  timeRange: TimeRangeType;
  data: unknown;
}

export default function ChartArea({ activeTab, timeRange, data }: ChartAreaProps) {
  const renderChart = () => {
    switch (activeTab) {
      case 'step':
        return <StepChart data={data as StepGraphData} timeRange={timeRange} />;
      case 'stepDistanceTime':
        return (
          <StepDistanceTimeChart data={data as StepDistanceTimeGraphData} timeRange={timeRange} />
        );
      case 'sleep':
        return <SleepChart data={data as SleepGraphData} timeRange={timeRange} />;
      case 'fatPercentage':
        return <FatPercentageChart data={data as FatPercentageGraphData} timeRange={timeRange} />;
      case 'weightBmi':
        return <WeightBmiChart data={data as WeightBmiGraphData} timeRange={timeRange} />;
      case 'energy':
        return <EnergyChart data={data as EnergyGraphData} timeRange={timeRange} />;
      case 'bloodPressure':
        return <BloodPressureChart data={data as BloodPressureGraphData} timeRange={timeRange} />;
      case 'bloodGlucose':
        return <BloodGlucoseChart data={data as BloodGlucoseGraphData} timeRange={timeRange} />;
      default:
        return null;
    }
  };

  return <div>{renderChart()}</div>;
}
