'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { CircleHelp } from 'lucide-react';

export default function BMIDialog() {
  const { setDialog } = useMessageDialog();

  const handleOpenBMIDialog = () => {
    setDialog(true, {
      title: 'BMIとは？',
      content: (
        <div className="space-y-4">
          <p className="t">
            BMIとは肥満や低体重の判定に用いられるもので、以下の計算式で求めることができます。
          </p>
          <p className="text-lg font-medium  my-4 ">体重(kg)÷{'{身長(m)の2乗}'}</p>
          <div className="overflow-x-auto ">
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  <th className="bg-primary text-white py-2 px-4 " align="left">
                    BMI
                  </th>
                  <th className="bg-primary text-white py-2 px-4 " align="left">
                    肥満の判定
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border bg-primary/40 py-2 px-4 text-sm">18.5未満</td>
                  <td className="border bg-primary/40 py-2 px-4 text-sm">低体重（やせ）</td>
                </tr>
                <tr>
                  <td className="border bg-primary/20 py-2 px-4 text-sm">18.5～25未満</td>
                  <td className="border bg-primary/20 py-2 px-4 text-sm">普通体重</td>
                </tr>
                <tr>
                  <td className="border bg-primary/40 py-2 px-4 text-sm">25～30未満</td>
                  <td className="border bg-primary/40 py-2 px-4 text-sm">肥満（1度）</td>
                </tr>
                <tr>
                  <td className="border bg-primary/20 py-2 px-4 text-sm">30～35未満</td>
                  <td className="border bg-primary/20 py-2 px-4 text-sm">肥満（2度）</td>
                </tr>
                <tr>
                  <td className="border bg-primary/40 py-2 px-4 text-sm">35～40未満</td>
                  <td className="border bg-primary/40 py-2 px-4 text-sm">肥満（3度）</td>
                </tr>
                <tr>
                  <td className="border bg-primary/20 py-2 px-4 text-sm">40以上</td>
                  <td className="border bg-primary/20 py-2 px-4 text-sm">肥満（4度）</td>
                </tr>
              </tbody>
            </table>
          </div>
          <p className="text-sm">
            BMIが表示されていない場合は、<span className="text-primary">ページ名が入ります</span>
            から身長を登録するとBMIが算出されるようになります。
          </p>
        </div>
      ),
      outSideClickClose: true,
    });
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-6 w-6 rounded-full p-0 "
      onClick={handleOpenBMIDialog}
    >
      <CircleHelp className="h-5 w-5 " />
      <span className="sr-only">BMI情報</span>
    </Button>
  );
}
