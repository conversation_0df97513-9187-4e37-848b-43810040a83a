import TopBar from '@/components/layout/top-bar';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import type { GraphTab } from '@/types/graph';

import { useEffect, useRef, useState } from 'react';

interface TabMenuProps {
  activeTab: GraphTab;
  onTabChange: (tab: GraphTab) => void;
}

export default function HealthTabMenu({ activeTab, onTabChange }: TabMenuProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const previousActiveTabRef = useRef<GraphTab | null>(null);

  const menus: { label: string; value: GraphTab }[] = [
    { label: '歩数', value: 'step' },
    { label: '歩行距離・時間', value: 'stepDistanceTime' },
    { label: '消費カロリー', value: 'energy' },
    { label: '体重', value: 'weightBmi' },
    { label: '体脂肪率', value: 'fatPercentage' },
    { label: '睡眠時間', value: 'sleep' },
    { label: '血圧', value: 'bloodPressure' },
    { label: '血糖値', value: 'bloodGlucose' },
  ];

  const checkScrollPosition = () => {
    const container = scrollContainerRef.current;
    if (container) {
      setShowLeftArrow(container.scrollLeft > 0);
      setShowRightArrow(container.scrollLeft < container.scrollWidth - container.clientWidth - 5);
    }
  };

  const handleScroll = () => {
    // ユーザーがスクロール中であることをマーク
    setIsUserScrolling(true);
    checkScrollPosition();

    // 前のタイマーをクリア
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // スクロール停止後にフラグをリセットするタイマーを設定
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, 500);
  };

  useEffect(() => {
    checkScrollPosition();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, []);

  // 初期化時にフラグを設定
  useEffect(() => {
    if (!isInitialized) {
      setIsInitialized(true);
      // 初期化時のみ一度だけ自動スクロールを実行
      setTimeout(() => {
        const currentIndex = menus.findIndex((menu) => menu.value === activeTab);
        if (currentIndex !== -1) {
          const container = scrollContainerRef.current;
          const tabElements = container?.querySelectorAll('[role="tab"]');
          if (container && tabElements) {
            const tabElement = tabElements[currentIndex];
            if (tabElement) {
              tabElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
            }
          }
        }
      }, 100);
    }
  }, [isInitialized, activeTab, menus]);

  // activeTabが変更された時、選択中のタブが表示領域内にあることを確認
  useEffect(() => {
    // まだ初期化されていない場合はスキップ
    if (!isInitialized) return;

    // ユーザーがスクロール中の場合は自動スクロールを実行しない
    if (isUserScrolling) return;

    // activeTabが実際に変更されていない場合はスキップ
    if (previousActiveTabRef.current === activeTab) return;

    // 前のactiveTabを更新
    previousActiveTabRef.current = activeTab;

    // activeTabが変更された時に自動スクロールを実行
    const currentIndex = menus.findIndex((menu) => menu.value === activeTab);
    if (currentIndex === -1) return;

    setTimeout(() => {
      const container = scrollContainerRef.current;
      const tabElements = container?.querySelectorAll('[role="tab"]');
      if (container && tabElements && !isUserScrolling) {
        const tabElement = tabElements[currentIndex];
        if (tabElement) {
          tabElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
      }
    }, 200);
  }, [activeTab, menus, isUserScrolling, isInitialized]);

  return (
    <div className="flex flex-col w-full shadow-sm bg-card">
      <TopBar title="グラフ" />

      {/* shadcn Tabsを使用したスクロール可能なタブバー */}
      <div className="relative">
        {/* 水平スクロール用のカスタムスタイルを適用したTabsコンポーネント */}
        <Tabs
          value={activeTab}
          onValueChange={(value) => onTabChange(value as GraphTab)}
          className="w-full "
        >
          <div
            ref={scrollContainerRef}
            className="overflow-x-auto scrollbar-hide "
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            <TabsList className="h-auto p-0 bg-transparent flex w-max ">
              {menus.map((menu) => (
                <TabsTrigger
                  key={menu.value}
                  value={menu.value}
                  className={cn(
                    'flex-shrink-0 px-4 py-3 rounded-none data-[state=active]:shadow-none',
                    'data-[state=active]:text-primary-foreground data-[state=active]:font-bold  data-[state=active]:bg-primary data-[state=active]:border-b-2 data-[state=active]:border-primary',
                    'data-[state=inactive]:text-foreground data-[state=inactive]:bg-transparent',
                  )}
                >
                  {menu.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
