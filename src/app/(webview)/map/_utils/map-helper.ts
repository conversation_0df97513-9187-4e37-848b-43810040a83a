import { codeTypeList, useCommonCode } from '@/hooks/use-common-code';
import { MarkerType } from '@/types/map';
import type { CouponPin, EventPin, Pin, WalkingCoursePin } from '@/types/map-types';

export function useMapHelper() {
  // const commonCode = useCommonCode();
  // const mapTypeOptions = commonCode.getOptions(codeTypeList.MAP_TYPE);
  // const mapTypeToKey = (mapType: string) => {
  //   const codeList = commonCode.getType(codeTypeList.MAP_TYPE)?.codeList || [];
  //   const code = codeList.find((code) => code.codeName === mapType);
  //   return code?.codeKey || '1';
  // };
  // const keyToMapType = (key: string) => {
  //   const codeList = commonCode.getType(codeTypeList.MAP_TYPE)?.codeList || [];
  //   const code = codeList.find((code) => code.codeKey === key);
  //   return code?.codeName || '';
  // };

  const pinToTypedPin = (pin: Pin) => {
    if (pin.pinType === '1') {
      return pin as EventPin;
    } else if (pin.pinType === '2') {
      return pin as CouponPin;
    } else if (pin.pinType === '3') {
      return pin as WalkingCoursePin;
    } else {
      return pin as Pin;
    }
  };

  const typedPinToMarker = (pin: Pin) => {
    const typedPin = pinToTypedPin(pin);
    if (typedPin.pinType === '1') {
      return eventPinToMarker(typedPin as EventPin);
    } else if (typedPin.pinType === '2') {
      return couponPinToMarker(typedPin as CouponPin);
    } else if (typedPin.pinType === '3') {
      return walkingCoursePinToMarker(typedPin as WalkingCoursePin);
    } else {
      return undefined;
    }
  };

  const eventPinToMarker = (pin: EventPin) => {
    return {
      id: pin.eventId,
      name: pin.eventName,
      type: MarkerType.EVENT,
      address: '',
      latitude: pin.eventLatitude,
      longitude: pin.eventLongitude,
      imagePath: '',
      isFav: pin.favoriteFlg === '1',
    };
  };

  const couponPinToMarker = (pin: CouponPin) => {
    return {
      id: pin.shopId,
      name: pin.shopName,
      type: MarkerType.COUPON,
      address: '',
      latitude: pin.shopLatitude,
      longitude: pin.shopLongitude,
      imagePath: '',
      isFav: pin.favoriteFlg === '1',
    };
  };

  const walkingCoursePinToMarker = (pin: WalkingCoursePin) => {
    return {
      id: pin.courseId,
      name: pin.courseName,
      type: MarkerType.WALKING_COURSE,
      address: '',
      latitude: pin.courseRouteList[0].path[0].lat,
      longitude: pin.courseRouteList[0].path[0].lng,
      imagePath: '',
      isFav: pin.favoriteFlg === '1',
    };
  };

  return {
    // mapTypeOptions,
    // mapTypeToKey,
    // keyToMapType,
    pinToTypedPin,
    typedPinToMarker,
  };
}
