import { Button } from '@/components/shared/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from '@/components/shared/drawer';
import { ScrollArea } from '@/components/shared/scroll-area';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useSlidePage } from '@/hooks/use-slide-page';
import type { ShopDetailResponse, shopInfo } from '@/types/coupon-types';
import { ChevronLeftIcon, X } from 'lucide-react';
import { useMemo, useRef, useState } from 'react';
import { IconLeft } from 'react-day-picker';
import { useStampSpotState } from '../_context/use-stamp-spot-state';
import MapItem from './map-item';

const snapPoints = [0.6, 1];

export default function MapDrawer({ show }: { show: boolean; onLeftIconClick: () => void }) {
  const { setShowMapDrawer } = useStampSpotState();
  const [snap, setSnap] = useState<number | string | null>(snapPoints[0]);
  const safeArea = useSafeArea();
  const top = useMemo(() => safeArea.top + 72 + 53 + 16, [safeArea]);

  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // ScrollAreaのTop位置取得する
  const handleScrollAreaTop = () => {
    if (scrollAreaRef.current) {
      return scrollAreaRef.current.getBoundingClientRect().top;
    }
    return 0;
  };

  return (
    <Drawer
      open={show}
      onOpenChange={setShowMapDrawer}
      snapPoints={snapPoints}
      activeSnapPoint={snap}
      setActiveSnapPoint={setSnap}
    >
      <DrawerContent hideOverlay={true}>
        <DrawerTitle className="sr-only">{COMMON_TEXT.MESSAGE.LOADING}</DrawerTitle>
        <DrawerDescription className="sr-only">Set your daily activity goal.</DrawerDescription>
        <ScrollArea
          disable={snap !== 1}
          className="w-full"
          style={{ height: `calc(100vh - ${top}px)` }}
          type="hover"
          ref={scrollAreaRef}
        >
          {show && <MapItem />}
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
}
