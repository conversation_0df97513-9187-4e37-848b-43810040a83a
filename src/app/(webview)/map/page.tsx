//import dynamic from 'next/dynamic';
// const MapComponent = dynamic(() => import('./_components/map'), { ssr: false });

import { mapAPI } from '@/api/modules/map-api';
import MapComponent from '@/components/shared/map/map';
import { useGeolocation } from '@/hooks/use-geolocation';
import { cn } from '@/lib/utils';
import { useMapStore } from '@/store/map-store';
import { MarkerType, type Connection, type Marker } from '@/types/map';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import MapDrawer from './_components/map-drawer';
import { StampSpotProvider, useStampSpotState } from './_context/use-stamp-spot-state';
import { useMapHelper } from './_utils/map-helper';

export function MapPageContent() {
  const { location } = useGeolocation();

  const { searchParams, setGeolocation, setSearchParams, clearSearchParams } = useMapStore();

  const { showMapDrawer, setShowMapDrawer } = useStampSpotState();

  const { pinToTypedPin, typedPinToMarker } = useMapHelper();

  const { data: pinData } = useQuery({
    queryKey: ['mapPins'],
    queryFn: () => mapAPI.listPins(searchParams),
  });

  useEffect(() => {
    if (location) {
      setGeolocation(location);
    }
  }, [location]);

  const handleClickMarker = (marker: Marker) => {
    console.log(marker);
  };
  const markers = useMemo(() => {
    const list: Marker[] =
      pinData?.pinList?.map((pin) => ({
        ...typedPinToMarker(pinToTypedPin(pin)),
      })) || [];
    return list;
  }, [pinData]);

  const lines = useMemo(() => {
    const list: Connection[] | undefined = [];
    return list || [];
  }, [pinData]);

  return (
    <div className="relative h-[100vh]">
      <div
        className={cn(
          'transition-all duration-300 h-full w-full',
          showMapDrawer ? 'translate-y-[-25%]' : 'translate-y-0',
        )}
      >
        <MapComponent
          markers={markers}
          lines={lines}
          onMarkerClick={(marker: Marker) => {
            handleClickMarker(marker);
          }}
        />
      </div>
      <MapDrawer show={showMapDrawer} />
    </div>
  );
}

export default function MapPage() {
  return (
    <StampSpotProvider>
      <MapPageContent />
    </StampSpotProvider>
  );
}
