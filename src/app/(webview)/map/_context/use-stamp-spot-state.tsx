'use client';

import { type ReactNode, createContext, useContext, useState } from 'react';

// スタンプスポット状態の型定義
interface StampSpotState {
  showMapDrawer: boolean;
}

// 状態更新関数の型定義
interface StampSpotActions {
  setShowMapDrawer: (show: boolean) => void;
}

// Context の型定義
interface StampSpotContextType extends StampSpotState, StampSpotActions {}

// Context の作成
const StampSpotContext = createContext<StampSpotContextType | undefined>(undefined);

// Provider の Props 型定義
interface StampSpotProviderProps {
  children: ReactNode;
}

/**
 * スタンプスポット状態管理 Provider
 * fixedShop は Zustand store で永続化される
 */
export const StampSpotProvider = ({ children }: StampSpotProviderProps) => {
  // UI 状態は useState で管理
  const [showMapDrawer, setShowMapDrawer] = useState(true);

  const value: StampSpotContextType = {
    showMapDrawer,
    setShowMapDrawer,
  };

  return <StampSpotContext.Provider value={value}>{children}</StampSpotContext.Provider>;
};

/**
 * スタンプスポット状態管理 Hook
 * Provider の外で使用した場合エラーを投げる
 */
export function useStampSpotState(): StampSpotContextType {
  const context = useContext(StampSpotContext);

  if (context === undefined) {
    throw new Error('useStampSpotState は StampSpotProvider の中で使用する必要があります');
  }

  return context;
}
