import { RiskTypeNum } from '@/const/app';
import { COLORS } from '@/const/colors';
import { APP_TEXT } from '@/const/text/app';
import type { ChartDateRange, RiskType, ScoreChartConfig } from '@/types/risk-score';
import { Circle, Diamond, Square, Star, Triangle } from 'lucide-react';

function CircleIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 4}
      y={cy - 4}
      width={8}
      height={8}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <circle cx="12" cy="12" r="10" />
    </svg>
  );
}

function DiamondIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 4}
      y={cy - 4}
      width={8}
      height={8}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z" />
    </svg>
  );
}

function HexagonIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 4}
      y={cy - 4}
      width={8}
      height={8}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
    </svg>
  );
}

function PentagonIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 4}
      y={cy - 4}
      width={8}
      height={8}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
    </svg>
  );
}

function SquareIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 4}
      y={cy - 4}
      width={8}
      height={8}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <rect width="18" height="18" x="3" y="3" rx="2" />
    </svg>
  );
}

function StarIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 4}
      y={cy - 4}
      width={8}
      height={8}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z" />
    </svg>
  );
}

function TriangleIcon({ cx, cy, color }: { cx: number; cy: number; color: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 4}
      y={cy - 4}
      width={8}
      height={8}
      viewBox="0 0 24 24"
      strokeWidth="0"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill={color}
    >
      <path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
    </svg>
  );
}

export const SCORE_CHART_CONFIG: ScoreChartConfig[] = [
  {
    value: RiskTypeNum.frail,
    key: 'frail', // フレイル予防
    name: APP_TEXT.HEALTH_SCORE_PAGE.FRAIL,
    icon: Circle,
    svgIcon: CircleIcon,
    color: COLORS.chart.riskScoreHistory1,
  },
  {
    value: RiskTypeNum.bloodPressure,
    key: 'hypertension', // 血圧上昇習慣
    name: APP_TEXT.HEALTH_SCORE_PAGE.BLOODP,
    icon: Square,
    svgIcon: SquareIcon,
    color: COLORS.chart.riskScoreHistory2,
  },
  {
    value: RiskTypeNum.immunity,
    key: 'immunity', // 免疫力
    name: APP_TEXT.HEALTH_SCORE_PAGE.IMMUNITY,
    icon: Star,
    svgIcon: StarIcon,
    color: COLORS.chart.riskScoreHistory3,
  },
  {
    value: RiskTypeNum.bloodGlucoseAndNeutralFat,
    key: 'bloodSugar', // 血糖値改善習慣
    name: APP_TEXT.HEALTH_SCORE_PAGE.BLOOD_GLUCOSE,
    icon: Diamond,
    svgIcon: DiamondIcon,
    color: COLORS.chart.riskScoreHistory4,
  },
  {
    value: RiskTypeNum.bloodGlucoseAndNeutralFat,
    key: 'neuterFat', // 中性脂肪改善習慣
    name: APP_TEXT.HEALTH_SCORE_PAGE.NEUTER_FAT,
    icon: Triangle,
    svgIcon: TriangleIcon,
    color: COLORS.chart.riskScoreHistory5,
  },

  /* 仕様上で「ストレス推定」、「Well-being」のヘルスチェックAI種類不要なので、文言も含めてコメントアウトしました
  {
    value: '5',
    key: 'wellBeing',
    name: APP_TEXT.HEALTH_SCORE_PAGE.WELL_BEING,
    icon: Pentagon,
    svgIcon: PentagonIcon,
    color: COLORS.chart.riskScoreHistory6,
  },
  {
    value: '6',
    key: 'stress',
    name: APP_TEXT.HEALTH_SCORE_PAGE.STRESS,
    icon: Hexagon,
    svgIcon: HexagonIcon,
    color: COLORS.chart.riskScoreHistory7,
  },
  */
];

export const CHART_DATE_RANGE_LIST: ChartDateRange[] = [
  { value: '0', name: APP_TEXT.HEALTH_SCORE_PAGE.ONE_MONTH, gapType: 'week', gap: 1, maxTicks: 4 },
  {
    value: '1',
    name: APP_TEXT.HEALTH_SCORE_PAGE.THREE_MONTHS,
    gapType: 'week',
    gap: 2,
    maxTicks: 6,
  },
  {
    value: '2',
    name: APP_TEXT.HEALTH_SCORE_PAGE.SIX_MONTHS,
    gapType: 'month',
    gap: 1,
    maxTicks: 6,
  },
  { value: '3', name: APP_TEXT.HEALTH_SCORE_PAGE.ONE_YEAR, gapType: 'month', gap: 1, maxTicks: 12 },
];

export type WeekRiskScore = {
  assetType: string;
  type: RiskType;
  title: string;
  desc: string;
};

// 3301:フレイル予防 3302:血圧上昇習慣 3303:免疫力 3304:血糖值·中性脂肪改善習慣
export const WEEK_RISK_SCORE_CONFIG: WeekRiskScore[] = [
  {
    assetType: RiskTypeNum.frail,
    type: 'frail',
    title: APP_TEXT.HEALTH_SCORE_PAGE.FRAIL,
    desc: APP_TEXT.HEALTH_SCORE_PAGE.FRAIL_DESC,
  },
  {
    assetType: RiskTypeNum.bloodPressure,
    type: 'hypertension',
    title: APP_TEXT.HEALTH_SCORE_PAGE.BLOODP,
    desc: APP_TEXT.HEALTH_SCORE_PAGE.BLOODP_DESC,
  },
  {
    assetType: RiskTypeNum.immunity,
    type: 'immunity',
    title: APP_TEXT.HEALTH_SCORE_PAGE.IMMUNITY,
    desc: APP_TEXT.HEALTH_SCORE_PAGE.IMMUNITY_DESC,
  },
  {
    assetType: RiskTypeNum.bloodGlucoseAndNeutralFat,
    type: 'bloodSugar',
    title: APP_TEXT.HEALTH_SCORE_PAGE.BLOOD_GLUCOSE,
    desc: APP_TEXT.HEALTH_SCORE_PAGE.BLOOD_GLUCOSE_DESC,
  },
  {
    assetType: RiskTypeNum.bloodGlucoseAndNeutralFat,
    type: 'neuterFat',
    title: APP_TEXT.HEALTH_SCORE_PAGE.NEUTER_FAT,
    desc: APP_TEXT.HEALTH_SCORE_PAGE.NEUTER_FAT_DESC,
  },
  /* 仕様上で「ストレス推定」、「Well-being」のヘルスチェックAI種類不要なので、文言も含めてコメントアウトしました
  {
    assetType: '3',
    type: 'wellBeing',
    title: APP_TEXT.HEALTH_SCORE_PAGE.WELL_BEING,
    desc: APP_TEXT.HEALTH_SCORE_PAGE.WELL_BEING_DESC,
  },
  {
    assetType: '6',
    type: 'stress',
    title: APP_TEXT.HEALTH_SCORE_PAGE.STRESS,
    desc: APP_TEXT.HEALTH_SCORE_PAGE.STRESS_DESC,
  },
  */
];

export const MESSAGE_TEMPLATE = '今週の判定結果の改善ポイントは$1です。';

export const RANDOM_RISK_IMAGES_LIST: Record<string, string[]> = {
  '7day': ['/images/health-score/message-2.png', '/images/health-score/message-7.png'],
  '6day': ['/images/health-score/message-2.png', '/images/health-score/message-7.png'],
  '5day': ['/images/health-score/message-2.png', '/images/health-score/message-6.png'],
  '4day': ['/images/health-score/message-2.png', '/images/health-score/message-6.png'],
  '3day': ['/images/health-score/message-3.png', '/images/health-score/message-5.png'],
  '2day': ['/images/health-score/message-3.png', '/images/health-score/message-5.png'],
  '1day': ['/images/health-score/message-1.png', '/images/health-score/message-4.png'],
  '0day': ['/images/health-score/message-1.png', '/images/health-score/message-4.png'],
};

export const RANDOM_RISK_MESSAGE_LIST: Record<string, string[]> = {
  '7day': [
    `先週は7日間、改善ミッションを見事に達成しました！素晴らしいです！
$1
今週もその調子で頑張りましょう！`,
    `先週、7日連続で改善ミッションにチャレンジし達成しましたね！素敵です！
$1
今週も引き続き頑張ってください！`,
    `先週7日間の改善ミッション、達成おめでとうございます！素晴らしいです！
$1
今週もがんばりましょう！`,
    `先週7日間連続で改善ミッションを達成したあなたは流石です！
$1
今週もその勢いで挑戦していきましょう！`,
    `先週は7日間、改善ミッションを実行しましたね！あっぱれです！
$1
今週も頑張っていきましょう！`,
    `先週は、すばらしい7日間を過ごしましたね！今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！
$1`,
    `先週は7日間改善ミッション達成！あなたの努力が光っています！
$1
今週もぜひ同じ調子で頑張ってくださいね！`,
    `先週7日間、改善ミッションを達成したあなたは素晴らしいです！
$1
今週も引き続き頑張ってください！`,
    `先週は見事に7日間改善ミッションを達成しましたね！素晴らしい努力です！
$1
今週も頑張りましょう！`,
    `先週、7日連続で改善ミッション達成を続けられたあなたには驚かされます！
$1
今週もぜひ同じ調子で頑張ってくださいね！`,
    `先週の改善ミッション7日達成、あなたは素晴らしい成果を上げました！
$1
この勢いで今週も挑んでいきましょう！`,
    `先週7日間の改善ミッションを見事に達成しましたね！とても素晴らしいです！
$1
今週も頑張りましょう！`,
    `先週は7日間、改善ミッション達成を続けました。素晴らしい努力です！
$1
今週もその調子で行きましょう！`,
    `先週7日間改善ミッションを達成したあなたは流石です！
$1
今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！`,
    `先週は7日間、しっかり改善ミッションを達成されましたね！素晴らしいです！
$1
今週も引き続き頑張りましょう！`,
    `先週の7日間の改善ミッション達成、おめでとうございます！
$1
そのままの気持ちで今週も頑張ってください！`,
    `先週の7日間の改善ミッション達成、あなたの努力が光っています！
$1
今週も前向きに取り組んでいきましょう！`,
    `先週7日間、見事に改善ミッションに取り組みましたね！素晴らしいです！
$1
今週も頑張りましょう！`,
    `先週の7日間、改善ミッション達成を成し遂げたことは素晴らしいです！
$1
この勢いで今週も挑戦していきましょう！`,
    `先週7日間改善ミッションを継続されたことに拍手です！
$1
今週も改善ミッションを取り組んで健康的な日々を送りましょう！`,
  ],
  '6day': [
    `先週は6日、改善ミッションを達成しましたね！おめでとうございます！
$1
今週も頑張りましょう！`,
    `先週は6日、改善ミッション達成、おめでとうございます！素晴らしいです！
$1
今週も引き続き取り組んでいきましょう！`,
    `先週、6日の改善ミッション達成、お疲れ様でした！そして、おめでとうございます！
$1
今週も改善ミッションを取り組んで健康的な日々を送りましょう！`,
    `先週6日、改善ミッションを見事に達成しました！おめでとうございます！
$1
今週も改善ミッションを続けましょう！`,
    `すばらしい！先週は6日、改善ミッションを達成しましたね！
$1
今週もその調子で頑張りましょう！`,
    `先週は6日の改善ミッション達成、よく頑張りましたね！おめでとうございます！
$1
そのままの気持ちで今週も頑張ってください！`,
    `先週6日、改善ミッションを成し遂げたあなたに拍手！おめでとうございます！
$1
この勢いで今週も挑んでいきましょう！`,
    `先週は6日改善ミッション達成、素晴らしい成果でしたね！おめでとうございます！
$1
今週も改善ミッションに取り組んでいきましょう！`,
    `先週は6日改善ミッション達成、心からおめでとうございます！
$1
今週も健康第一で改善ミッションに挑んでいきましょう！`,
    `改善ミッション6日達成、おめでとうございます！
$1
今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！`,
    `先週、6日改善ミッション達成を頑張ったあなたにお祝いを！素晴らしいです！
$1
今週もその調子で行きましょう！`,
    `先週6日改善ミッション達成、流石ですね！おめでとうございます！
$1
この勢いで今週も挑戦していきましょう！`,
    `おめでとうございます！先週の6日改善ミッション達成、素晴らしい成果です！
$1
今週も引き続き頑張りましょう！`,
    `先週は6日改善ミッションを達成しましたね！お祝い申し上げます！
$1
今週もその調子で続けてくださいね！`,
    `先週は6日改善ミッション達成、とても良く頑張りましたね！おめでとうございます！
$1
そのままの気持ちで今週も頑張ってください！`,
    `先週6日改善ミッション達成、お見事です！おめでとうございます！
$1
今週もぜひ同じ調子で頑張ってくださいね！`,
    `先週は6日も改善ミッションに取り組めたあなた、本当に素敵です！おめでとうございます！
$1
今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！`,
    `先週は6日、改善ミッションを達成しましたね！素晴らしいです！
$1`,
    `先週は6日改善ミッション、達成おめでとうございます！
$1
今週も改善ミッションへのチャレンジを続けましょう！`,
    `素晴らしいです！先週は6日改善ミッションを達成しましたね！おめでとうございます！
$1
今週もぜひ同じ調子で頑張ってくださいね！`,
  ],
  '5day': [
    `先週は5日の改善ミッション達成、お見事でした！
$1
今週もその調子で続けていきましょう！`,
    `先週は5日の改善ミッション達成、おめでとうございます！
$1
今週も頑張りましょう！`,
    `素晴らしいです！先週は5日も改善ミッション達成できましたね。
$1
今週も引き続き改善ミッションにチャレンジしましょう！`,
    `先週は5日の改善ミッション達成、素晴らしい成果ですね！
$1
この勢いで今週も挑んでいきましょう！`,
    `お疲れ様です！先週は5日の改善ミッション達成、すごいです！
$1
今週も継続していきましょう！`,
    `先週は5日、改善ミッションを達成したんですね！あなたの努力が光っています！
$1
引き続き、今週もがんばりましょう！`,
    `先週は5日の改善ミッション達成、素晴らしかったです！
$1
今週も頑張りましょう！`,
    `先週は5日の改善ミッション達成、おめでとうございます！
$1
今週も元気に改善ミッションに取り組みましょう！`,
    `先週は5日の改善ミッション達成、努力が光っています！
$1
今週もその調子で続けてくださいね！`,
    `素晴らしいです！先週は5日、改善ミッション達成できましたね。
$1
今週もがんばりましょう！`,
    `先週は5日の改善ミッション達成、おめでとうございます！
$1
今週もその調子で続けてくださいね！`,
    `先週は改善ミッション5日達成、素晴らしいですね！
$1
今週も続けていきましょう。`,
    `先週は改善ミッション5日達成の成果は見事です！
$1
今週も同じように健康を意識していきましょう！`,
    `先週は5日、改善ミッションを達成しましたね！
$1
この調子で、今週も続けていきましょう！`,
    `先週は改善ミッション達成を5日、成功させましたね！
$1
この勢いで今週も挑戦していきましょう！`,
    `素晴らしいです！先週は5日も改善ミッション達成できましたね。
$1
そのままの気持ちで今週も頑張ってください！`,
    `先週は、改善ミッション5日達成、おめでとうございます！
$1
今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！`,
    `先週は改善ミッション5日達成、あなたの努力が光っています！
$1
今週も達成日数の目標を立ててがんばりましょう！`,
    `先週の改善ミッション達成5日、お見事です！
$1
今週も気持ちを高めて改善ミッションに取り組みましょう♪`,
    `先週は5日の改善ミッション達成、あなたの努力が素晴らしいです！
$1
今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！`,
  ],
  '4day': [
    `先週は4日、改善ミッションを達成しましたね！素晴らしいです！
$1
今週は5日以上の達成目標を立ててみましょう！`,
    `先週は4日改善ミッション達成、おめでとうございます！
$1
今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！`,
    `先週は4日改善ミッションを頑張ったあなた、なかなか良い成果です！
$1
今週は5日以上の達成にチャレンジしてみましょう！`,
    `先週は4日改善ミッション達成、お見事です！
$1
今週はさらに達成日を増やして、新しい目標を設定してみましょう！`,
    `あなたは先週、4日改善ミッションを成し遂げました！素晴らしいです！
$1
そのままの気持ちで今週も頑張ってください！`,
    `先週4日、改善ミッションをしっかり達成しましたね！自分を褒めてあげましょう！
$1
今週もその調子で続けてくださいね！`,
    `先週は4日の改善ミッション達成、お見事でした！
$1
引き続き、今週もがんばりましょう！`,
    `先週は4日の改善ミッション達成、おめでとうございます！
$1
今週もがんばりましょう！`,
    `あなたは先週4日、改善ミッションを達成しましたね！とても良い成果です！
$1
今週も頑張りましょう！`,
    `先週は4日改善ミッションを達成しました！自分を誇りに思いましょう！
$1
今週はさらに目標を高く設定してみましょう！`,
    `先週は4日改善ミッション達成、よく頑張りましたね！素晴らしい成果です！
$1
今週は5日以上の達成を目指しましょう！`,
    `先週は4日の改善ミッション達成、本当に素晴らしいです！
$1
今週はさらなる達成日数を目標にチャレンジしてみませんか？`,
    `先週は4日、改善ミッションを達成しましたね！いい感じです！
$1
今週も改善ミッションに取り組んで、さらに良い習慣を築きましょう！`,
    `先週は改善ミッション、4日達成おめでとうございます！
$1
今週は更に高い目標を目指してみましょう！`,
    `先週の4日改善ミッション達成は素晴らしい努力でした！
$1
今週は5日以上達成にチャレンジしてみませんか？`,
    `先週4日も改善ミッション達成したあなた、いい成果です！おめでとうございます！
$1
今週はさらにチャレンジしてみましょう！`,
    `先週、4日改善ミッション達成、とても良い結果でした！
$1
今週もその調子で続けてくださいね！`,
    `おめでとうございます！先週は4日改善ミッションを達成しましたね！
$1
今週は新たな目標に挑戦しましょう！`,
    `先週は4日、見事に改善ミッションを達成しました！自分を褒めてあげましょう！
$1
今週も頑張ろう！`,
    `先週の4日の改善ミッション達成は、なかなか素晴らしいです！おめでとうございます！
$1
さあ、今週は更なる挑戦です！`,
  ],
  '3day': [
    `先週は3日の改善ミッション達成でしたね！良い感じです！
$1
今週はもう一日増やしてみましょう！`,
    `先週の改善ミッションは3日達成です。あなたの努力が光ります！
$1
今週も改善ミッションを続けていきましょう！`,
    `先週は3日改善ミッションを達成しましたね！
$1
今週は4日を目指して頑張りましょう！`,
    `先週は3日改善ミッション達成、いいですね！お疲れ様です！
$1
今週はさらにチャレンジしてみましょう！`,
    `先週は3日改善ミッション達成、おめでとうございます！
$1
今週はもうちょっとだけ頑張って4日達成を目指してみましょう！`,
    `先週は改善ミッション3日達成の成果は素晴らしいです！あと一歩ですね！
$1
今週は4日以上の達成に挑戦してみましょう！`,
    `先週は3日改善ミッションを達成しましたね！いい感じです！
$1
今週は4日達成を目指して頑張ってみましょう！`,
    `改善ミッション、先週は3日達成しましたね！素敵です！
$1
今週はもう少しだけ頑張ってみましょう！`,
    `先週3日、しっかり改善ミッション達成できたあなた、いいですね！
$1
今週はさらに一歩進んで4日達成を指しましょう！`,
    `先週は3日改善ミッション達成に成功しましたね！素晴らしいです！
$1
今週は4日達成を目指して頑張りましょう！`,
    `先週は3日改善ミッションを達成しましたが、今週はもうちょっとだけがんばってみましょう。
$1
今週は4日達成を目標にしてみましょう。`,
    `先週は3日改善ミッション達成、素晴らしい成果ですね！
$1
今週は4日達成を目標に頑張っていきましょう！`,
    `先週は3日改善ミッション達成おめでとうございます！もう少しですね！
$1
今週は4日改善ミッション達成への挑戦してみましょう！`,
    `先週は3日の改善ミッション達成お疲れ様です！良い成果でしたね！
$1
今週はやる気で4日の改善ミッション達成を目指してみましょう。`,
    `先週、改善ミッションを3日達成したあなたにお祝いを！素晴らしいです！
$1
今週はさらにチャレンジしてみましょう！`,
    `先週は3日改善ミッション達成、良い成果でしたね！
$1
今週は4日達成を目標にしていきましょう。`,
    `先週は3日改善ミッション達成、しっかり頑張りましたね！なかなか良い感じです！
$1
今週は4日以上の達成を目指しましょう！`,
    `先週は3日改善ミッション達成しましたね、素晴らしいです！
$1
今週もさらに挑戦を続けていきましょう！`,
    `先週は3日改善ミッション達成しましたね！素敵です。
$1
今週は4日達成を目指して頑張るチャンスです！`,
    `先週3日も改善ミッション達成を頑張ったあなた！お見事です！
$1
今週は新たな目標に挑戦してみましょう。`,
  ],
  '2day': [
    `先週は改善ミッションを2日達成、良い感じですね！
$1
今週は3日達成を目指してさらに頑張りましょう。`,
    `先週は2日改善ミッションを達成しましたね、今週はもう少しチャレンジしてみませんか？
$1
今週は4日達成を目指してみましょう！`,
    `先週は改善ミッション2日達成でした、少しずつ着実に進んでますよ！
$1
今週はまずは3日達成を目指すチャレンジを期待しています。`,
    `先週は2日改善ミッション達成しましたね、今週はもうちょっとがんばってみましょう！
$1
今週は3日達成を目標に。`,
    `先週は2日の改善ミッション達成、もう少しだけがんばれば、3日はいけそうです！
$1
今週もがんばってみましょう！`,
    `先週は2日の改善ミッション達成、お疲れ様でした！
$1
今週は4日達成をねらってみてくださいね。`,
    `先週は2日の改善ミッション達成、うーん、もう少しですね！
$1
今週は3日以上達成をねらってみましょう！`,
    `先週は改善ミッション2日達成、順調ですね！
$1
今週は4日達成を目標に頑張りましょう！`,
    `先週は2日改善ミッション達成、少しずつ前進中！
$1
今週は3日達成を目指してチャレンジしてみましょう。`,
    `先週は改善ミッション2日達成でしたね、少しずつ進んでいます！
$1
今週は4日達成を目指してみましょう。`,
    `先週は2日の改善ミッション達成、ほかの日も頑張ってみたいですね！
$1
今週は3日達成を目指していきましょう。`,
    `先週の改善ミッション達成は2日でした、もう少し伸ばせそうですね！
$1
今週は4日達成を目指して頑張ってみましょう。`,
    `先週は2日の改善ミッション達成、嬉しい成果ですね！
$1
今週はさらに伸ばして3日達成を狙いましょう！`,
    `先週は2日改善ミッションを達成、もう一歩です！
$1
今週は4日達成を目指してみましょう。`,
    `先週は2日改善ミッションを達成しましたね、良い感触です！
$1
今週は3日達成を目指しましょう。`,
    `先週改善ミッションを2日達成、うまくいってますね！
$1
今週は4日達成を目標に向かってGo！`,
    `先週は2日改善ミッション達成しました、少しずつ頑張っていますね！
$1
今週は3日達成を目標に設定してみましょう。`,
    `先週改善ミッション2日間達成、もうちょっとですね！
$1
今週は4日達成を目標にチャレンジしてみましょう！`,
    `先週は2日間の改善ミッション達成でした、今週はもう少しチャレンジしましょう！
$1
今週は3日達成を目指しましょう！`,
  ],
  '1day': [
    `先週は改善ミッション1日達成でしたね！
$1
今週は達成目標を3日にしてみましょう！`,
    `先週は改善ミッション1日だけの達成でした。
$1
今週は3日達成を目指して頑張りましょう！`,
    `先週の改善ミッション、1日達成お疲れ様です！
$1
今週は3日達成をねらってみましょう！`,
    `先週は改善ミッション1日達成でした！
$1
今週は新たな挑戦、3日達成を目指しましょう！`,
    `先週は改善ミッションは1日達成でしたが、今週は3日達成目指していきましょう！
$1
今週は新たに3日を目指して頑張ってみませんか？`,
    `あと一歩！先週の改善ミッション達成は1日だったので、今週は3日達成を目指して頑張ってみましょう。
$1
今週は3日達成にチャレンジしてみてください！`,
    `先週の改善ミッション達成は1日でした、お疲れ様でした！
$1
今週は3日以上達成を目標にしてみましょう！`,
    `先週の改善ミッション達成は1日達成でしたが、今週はより高い目標である3日達成を目指してみましょう！
$1
今週は3日達成を目標にして、取り組みましょう。`,
    `先週の改善ミッション達成は1日でしたね！
$1
今週は3日達成を目指して、さらにステップを進めましょう！`,
    `先週は改善ミッションは1日達成でした！
$1
今週は3日達成を目指すのが目標です、頑張りましょう！`,
    `先週の改善ミッションは1日達成でした！
$1
今週は3日達成を意識してみましょう！`,
    `先週の改善ミッション達成は1日でしたね。
$1
今週は3日以上の達成を目標にして進めましょう！`,
    `先週の改善ミッション達成は1日でしたね、もう少しでした！
$1
今週は3日達成目指してさらにチャレンジしていきましょう！`,
    `先週は改善ミッション1日達成でした！
$1
今週は3日達成を目指す新たな一歩を踏み出しましょう！`,
    `先週の改善ミッション達成は1日でしたが、これからが本番です！
$1
今週は3日達成を狙っていきましょう！`,
  ],
  '0day': [
    `先週は改善ミッションを1日も達成できませんでしたね。誰にでもそういう週はあります。
$1
今週は3日達成を目指して頑張りましょう！`,
    `残念ながら先週は改善ミッションを1日も達成できませんでしたね。
$1
今週は3日達成を目指して取り組んでみましょう！`,
    `先週改善ミッションを達成できなかったのは少し残念です。
$1
今週は3日達成できるように工夫してみましょう！`,
    `先週は1日も改善ミッションを達成できずに残念でしたね。
$1
気を取り直して、今週は3日達成を目指していきましょう！`,
    `先週は改善ミッション達成0日でしたね。そういうこともありますよね。
$1
今週は3日達成を目標に頑張ってみませんか？`,
    `先週は改善ミッションを達成できなかったですね。でも大丈夫、今週は3日達成を目標にして取り組んでみましょう！
$1
今週は3日達成を目標にして取り組んでみましょう！`,
    `先週の改善ミッションは残念ながら1日も達成できませんでした。今週は気持ちを新たに、3日達成を目指して頑張りましょう！
$1
今週は3日達成の目標を立てて、挑戦してみましょう！`,
    `先週は改善ミッション達成0日でちょっと残念ですね。
$1
今週は3日達成の目標を立てて、挑戦してみましょう！`,
    `先週は1日もできませんでしたね、そういうこともあると思います。
$1
今週は3日の達成を目指しましょう！`,
    `残念ながら先週は改善ミッションに取り組めませんでした。
$1
今週は3日達成を目指して、意識的に取り組んでみましょう！`,
    `先週は全く改善ミッションを達成できなかったですね。
$1
新たな気持ちで、今週は3日達成に挑戦していきましょう！`,
    `先週は1日もできずに残念でした。
$1
今週は3日達成できるように、少しずつ進んでみましょう。`,
    `先週は改善ミッション未達成でしたね。
$1
今週は3日達成を目指して、より意識的に取り組みましょう！`,
    `先週は改善ミッションが1日もできず残念です。
$1
今週は3日達成を目指して、少しずつ取り組んでいきましょう！`,
    `先週の改善ミッションは達成ゼロでしたけれど、気を落とさず今週は3日達成を目指して頑張ってみましょう！
$1
今週は3日達成を目指し、進んでいきましょう！`,
    `先週は残念ながら改善ミッション達成0日でした。
$1
今週は3日達成を目指して、まずは小さなステップから始めてみましょう！`,
    `先週は1日も改善ミッションを達成できずに残念でしたが、今週は3日達成を目指して頑張りましょう！
$1
今週は3日達成を目指して、まずは小さなステップから始めてみましょう！`,
    `残念ながら先週は改善ミッションなしでした。しかし、今週は3日達成を目指して進んでみましょう！
$1
今週は3日達成を目指して心を新たにしましょう！`,
  ],
};

// improve_point_type：1:歩数、2:起床時間、3:睡眠時間、4:外出時間、5:就寝時間
export const IMPROVE_POINT_TYPE_MESSAGE_MAP: Record<string, string> = {
  '0': '未達成',
  '1': '歩数',
  '2': '起床時間',
  '3': '睡眠時間',
  '4': '外出時間',
  '5': '就寝時間',
  '6': '気温差対策',
  '7': '帰宅時刻',
  '8': '在宅時間',
  '9': '歩行能力の低下',
  '10': '歩幅',
  '11': '歩行速度',
  '12': 'BMI',
  '13': '外出回数',
  '14': '運動量',
  '15': '物事への興味関心度',
  '16': '体重',
  '17': '食習慣',
};
