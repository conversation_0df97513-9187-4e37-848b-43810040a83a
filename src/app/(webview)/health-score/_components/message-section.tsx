import { APP_TEXT } from '@/const/text/app';
import type { MissionCardType } from '@/types/mission';
import type { HealthScoreJudgmentResponse } from '@/types/risk-score';
import { useEffect, useState } from 'react';
import {
  IMPROVE_POINT_TYPE_MESSAGE_MAP,
  MESSAGE_TEMPLATE,
  RANDOM_RISK_IMAGES_LIST,
  RANDOM_RISK_MESSAGE_LIST,
} from '../_const';

export default function MessageSection({
  judgmentResultData,
  missionData,
}: {
  judgmentResultData: HealthScoreJudgmentResponse | undefined;
  missionData: MissionCardType[] | undefined;
}) {
  const defaultImage = '/images/health-score/message-2.png';
  const defaultMessage = APP_TEXT.HEALTH_SCORE_PAGE.MESSAGE_CALCULATING;
  const [randomMessage, setRandomMessage] = useState<string>();
  const [randomImage, setRandomImage] = useState<string>();

  useEffect(() => {
    const improvePointType =
      judgmentResultData?.judgment_result?.judgment_result_data.improve_point_type;
    const targetAchieveDay = missionData?.[0]?.targetDays || 0;
    const topRandMessage = judgmentResultData?.judgment_result?.top_rand_msg || 0;
    const topRandImage = judgmentResultData?.judgment_result?.top_rand_img || 0;
    if (improvePointType && targetAchieveDay > 0) {
      const targetAchieveDayKey = `${targetAchieveDay}day`;
      const typeName = IMPROVE_POINT_TYPE_MESSAGE_MAP[improvePointType];
      const randomMessageList = RANDOM_RISK_MESSAGE_LIST[targetAchieveDayKey] || [];
      const randomImageList = RANDOM_RISK_IMAGES_LIST[targetAchieveDayKey] || [];
      const resultMessage = MESSAGE_TEMPLATE.replace('$1', typeName);
      const randomRiskMessage = randomMessageList[topRandMessage] || '';
      const randomRiskImage = randomImageList[topRandImage] || '';
      setRandomMessage(randomRiskMessage.replace('$1', resultMessage));
      setRandomImage(randomRiskImage);
    }
  }, [judgmentResultData, missionData]);

  return (
    <div className="flex p-4 bg-card mx-6 rounded-2xl items-center">
      <div className="flex-shrink-0 flex-grow-0  w-[80px] flex items-center justify-center">
        <img src={randomImage || defaultImage} alt="random-image" className="w-full" />
      </div>
      <div className="ml-3 min-h-[96px]">{randomMessage || defaultMessage}</div>
    </div>
  );
}

function isNumberString(str: string): boolean {
  const num = Number(str);
  return !Number.isNaN(num) && num < 20;
}
