'use client';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/shared/scroll-area';
import { COLORS } from '@/const/colors';
import { APP_TEXT } from '@/const/text/app';
import { useWindowSize } from '@/hooks/use-window-size';
import { cn } from '@/lib/utils';
import type { ChartDateRange, RiskType, ScoreChartConfig, ScoreData } from '@/types/risk-score';
import React from 'react';
import type { TooltipProps } from 'recharts';
import { CartesianGrid, Cross, Customized, Line, LineChart, Tooltip, XAxis, YAxis } from 'recharts';
import { SCORE_CHART_CONFIG } from '../_const';

const xAxisData = [
  {
    date: '1/1',
    value1: 0,
  },
  {
    date: '1/2',
    value1: 100,
  },
];

function YAxisTick({
  x,
  y,
  payload,
}: { x: number; y: number; payload: { value: number; index: number } }) {
  return (
    <g transform={`translate(${x},${y})`}>
      <text x={-30} y={3} textAnchor="start" fill={COLORS.text.secondary} className="text-[11px]">
        <tspan>{payload.value === 0 ? '' : payload.value}</tspan>
      </text>
    </g>
  );
}

function XAxisTick({
  x,
  y,
  payload,
  data,
}: {
  x: number;
  y: number;
  payload: { value: string; index: number };
  data: ScoreData[];
}) {
  const item = data[payload.index];
  return (
    <g transform={`translate(${x},${y})`}>
      <text x={0} y={12} textAnchor="middle" fill={COLORS.text.secondary} className="text-[10px]">
        {item.isHeadOfYear ? item.year : ''}
      </text>
      <text x={0} y={27} textAnchor="middle" fill={COLORS.text.secondary} className="text-[11px]">
        {item.label}
      </text>
    </g>
  );
}

const CustomizedCross = ({ width, height }: { width: number; height: number }) => {
  return (
    <Cross
      y={206}
      x={-10}
      top={0}
      left={0}
      height={height}
      width={width}
      stroke={'hsl(0 0 40%)'}
      fill={'none'}
    />
  );
};

const ScoreChartConfigMap = SCORE_CHART_CONFIG.reduce(
  (acc, config) => {
    acc[config.key] = config;
    return acc;
  },
  {} as Record<RiskType, ScoreChartConfig>,
);

const CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    const date = `${payload[0].payload.year}/${label}`;
    return (
      <div className="bg-card rounded-lg border border-border p-2 text-xs">
        <div>{date}</div>
        {payload.map((item) => {
          const key = item.dataKey as RiskType;
          const scoreConfig = ScoreChartConfigMap[key];
          return (
            <div key={key} className="flex items-center gap-2 mt-1">
              <scoreConfig.icon size={12} fill={scoreConfig.color} strokeWidth={0} />
              {item.value}
              {APP_TEXT.HEALTH_SCORE_PAGE.POINT2}
            </div>
          );
        })}
      </div>
    );
  }

  return null;
};

function generateEmptyData() {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth() + 1;
  const day = today.getDate();
  const defaultData: ScoreData[] = [
    {
      date: `${year}/${month}/${day}`,
      label: `${month}/1`,
      isHeadOfYear: true,
      year: `${year}`,
      month: `${month}`,
      day: `${day}`,
      isEmpty: true,
      frail: 0,
    },
  ];
  return defaultData;
}

const defaultData = generateEmptyData();

export function ScoreHistoryChart({
  className,
  data,
  chartDateRange,
}: {
  className?: string;
  data: ScoreData[];
  chartDateRange: ChartDateRange;
}) {
  const { width } = useWindowSize();
  if (data.length === 0) {
    data = defaultData;
  }
  const item = data[0];
  const SCORE_CHART_CONFIG_VIEW = SCORE_CHART_CONFIG.filter((config) => {
    // if config.key in item, return true
    return item[config.key] !== undefined;
  });
  if (SCORE_CHART_CONFIG_VIEW.length === 0) {
    return null;
  }
  const height = 250;
  const bottom = 13;
  const viewWidth = width - 48 - 30;
  const gapWidth = viewWidth / chartDateRange.maxTicks;
  const sumWidth = gapWidth * data.length + 30;
  return (
    <div className={cn('w-full relative select-none mt-9', className)}>
      <div className="text-[8px] text-text-secondary absolute top-[-20px] left-0">
        ({APP_TEXT.HEALTH_SCORE_PAGE.SCORE})
      </div>
      <div className="w-[25px] h-[205px] absolute bg-card top-0 left-0 z-30 overflow-hidden">
        <LineChart
          width={width - 48}
          height={height}
          data={xAxisData}
          margin={{
            top: 15,
            right: 0,
            left: 0,
            bottom,
          }}
        >
          <XAxis style={{ opacity: 0 }} />
          <YAxis
            axisLine={false}
            tickLine={false}
            width={40}
            padding={{ top: 0 }}
            tick={({ x, y, payload }) => YAxisTick({ x, y, payload })}
          />
          <Line dataKey="value1" stroke="transparent" dot={false} />
        </LineChart>
      </div>
      <div className="w-full h-full absolute bg-card top-0 left-0 z-10">
        <LineChart
          width={width - 48}
          height={height}
          data={xAxisData}
          margin={{
            top: 15,
            right: 0,
            left: 0,
            bottom,
          }}
        >
          <XAxis style={{ opacity: 0 }} />
          <YAxis hide={true} />
          <Customized component={() => CustomizedCross({ width, height })} />
          <CartesianGrid
            vertical={false}
            stroke="hsl(0 0 40%)"
            strokeDasharray="3"
            horizontalCoordinatesGenerator={(props) => {
              const { height, offset } = props;
              const top = offset.top || 0;
              const bottom = offset.bottom || 0;
              const space = (height - top - bottom) / 4;
              return [top, top + space, top + space * 2, top + space * 3];
            }}
          />
        </LineChart>
      </div>
      <ScrollArea className="h-[272px] w-full z-20" type="auto">
        <div className={`w-[${width * 2}px]`}>
          <LineChart
            width={sumWidth}
            height={height}
            data={data}
            // @ts-expect-error
            connectNulls
            margin={{
              top: 15,
              right: 0,
              left: 25,
              bottom,
            }}
          >
            <XAxis
              dataKey="label"
              padding={{ left: 15, right: 15 }}
              axisLine={false}
              tickLine={false}
              tick={({ x, y, payload }) => XAxisTick({ x, y, payload, data })}
            />
            <YAxis hide={true} domain={[0, 100]} />
            <Tooltip content={<CustomTooltip />} />
            {SCORE_CHART_CONFIG_VIEW.map((scoreType) => (
              <Line
                key={scoreType.name}
                dataKey={scoreType.key}
                stroke={scoreType.color}
                isAnimationActive={false}
                dot={(props) => {
                  const item = data[props.index];
                  if (item.isEmpty) {
                    return <span key={props.key} className="w-4 h-4" />;
                  }
                  return (
                    <scoreType.svgIcon
                      color={scoreType.color}
                      key={props.key}
                      cx={props.cx}
                      cy={props.cy}
                    />
                  );
                }}
              />
            ))}
          </LineChart>
        </div>
        <ScrollBar orientation="horizontal" className="bg-secondary rounded-full" />
      </ScrollArea>
    </div>
  );
}

export default React.memo(ScoreHistoryChart, (prevProps, nextProps) => {
  const result =
    prevProps.data === nextProps.data && prevProps.chartDateRange === nextProps.chartDateRange;
  return result;
});
