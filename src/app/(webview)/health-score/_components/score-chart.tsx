'use client';

import { Label, <PERSON>AngleAxis, PolarRadiusAxis, RadialBar, RadialBarChart } from 'recharts';

import { type ChartConfig, ChartContainer } from '@/components/ui/chart';
import { COLORS } from '@/const/colors';
import { APP_TEXT } from '@/const/text/app';
import type { HealthScoreJudgmentResponse } from '@/types/risk-score';
import { useMemo } from 'react';

const chartConfig = {
  score: {
    label: 'score',
    color: COLORS.chart.riskScore1,
  },
} satisfies ChartConfig;

const tickConfig = [
  {
    x: 16,
    y: -29,
    text: '0',
  },
  {
    x: 20,
    y: -25,
    text: '', // 10
  },
  {
    x: 20,
    y: 0,
    text: '', // 20
  },
  {
    x: 40,
    y: 10,
    text: APP_TEXT.HEALTH_SCORE_PAGE.WORRY,
  },
  {
    x: 10,
    y: 10,
    text: '', // 40
  },
  {
    x: 10,
    y: 10,
    text: '', //50
  },
  {
    x: -30,
    y: 30,
    text: '60',
  },
  {
    x: -37,
    y: 6,
    text: APP_TEXT.HEALTH_SCORE_PAGE.CAUTION,
  },
  {
    x: -42,
    y: -15,
    text: '80',
  },
  {
    x: -25,
    y: -27,
    text: APP_TEXT.HEALTH_SCORE_PAGE.GOOD,
  },
  {
    x: -20,
    y: -29,
    text: '100',
  },
];

function renderTick(tick: { x: number; y: number; textAnchor: string; index: number }) {
  return (
    <text
      orientation="outer"
      stroke="none"
      x={tick.x}
      y={tick.y}
      className="recharts-text recharts-polar-angle-axis-tick-value"
      textAnchor={tick.textAnchor}
      fill="#666666"
    >
      <tspan className="text-[#666666]" dx={tickConfig[tick.index].x} dy={tickConfig[tick.index].y}>
        {tickConfig[tick.index].text}
      </tspan>
    </text>
  );
}

function getChartData(score: number) {
  if (score >= 80) {
    return {
      score: score,
      fill: COLORS.chart.riskScore1,
      text: APP_TEXT.HEALTH_SCORE_PAGE.GOOD,
    };
  }
  if (score >= 60) {
    return {
      score: score,
      fill: COLORS.chart.riskScore2,
      text: APP_TEXT.HEALTH_SCORE_PAGE.CAUTION,
    };
  }
  if (score === 0) {
    return {
      score: score,
      fill: COLORS.text.secondary,
      text: APP_TEXT.HEALTH_SCORE_PAGE.CALCULATING,
    };
  }
  return {
    score: score,
    fill: COLORS.chart.riskScore3,
    text: APP_TEXT.HEALTH_SCORE_PAGE.WORRY,
  };
}

export default function ScoreChart({
  data,
  isFailed,
}: { data: HealthScoreJudgmentResponse | undefined; isFailed: boolean }) {
  const score = Number(data?.judgment_result?.judgment_result_data.score || 0);
  const chartData = [getChartData(score)];
  return (
    <ChartContainer config={chartConfig} className="w-[300px] h-[270px]">
      <RadialBarChart
        data={chartData}
        startAngle={233}
        endAngle={-53}
        innerRadius={90}
        outerRadius={140}
      >
        <RadialBar stroke="#fff" strokeWidth="4" dataKey="score" background cornerRadius={10} />
        <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
          <Label
            content={({ viewBox }) => {
              if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                return (
                  <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                    {score > 0 && (
                      <>
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          dy={-10}
                          className="fill-foreground text-[56px] font-bold"
                        >
                          {score}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy || 0}
                          dy={34}
                          fill={chartData[0].fill}
                          className="text-[22px] font-bold"
                        >
                          {chartData[0].text}
                        </tspan>
                      </>
                    )}
                    {!isFailed && score === 0 && (
                      <tspan
                        x={viewBox.cx}
                        y={viewBox.cy || 0}
                        fill={chartData[0].fill}
                        className="text-[22px] font-bold"
                      >
                        {chartData[0].text}
                      </tspan>
                    )}
                    {isFailed && (
                      <>
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy || 0}
                          dy={-36}
                          fill={chartData[0].fill}
                          className="text-[22px] font-bold"
                        >
                          測定失敗
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy || 0}
                          dy={-6}
                          fill={chartData[0].fill}
                          className="text-[14px] w-[84px] text-wrap"
                        >
                          AIによるヘルス
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy || 0}
                          dy={15}
                          fill={chartData[0].fill}
                          className="text-[14px] w-[84px] text-wrap"
                        >
                          チェックAIの推定
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy || 0}
                          dy={36}
                          fill={chartData[0].fill}
                          className="text-[14px] w-[84px] text-wrap"
                        >
                          ができませんでし
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy || 0}
                          dy={57}
                          fill={chartData[0].fill}
                          className="text-[14px] w-[84px] text-wrap"
                        >
                          た。
                        </tspan>
                      </>
                    )}
                  </text>
                );
              }
            }}
          />
        </PolarRadiusAxis>
        <PolarAngleAxis
          type="number"
          domain={[0, 100]}
          angleAxisId={0}
          tick={renderTick}
          tickCount={11}
        />
      </RadialBarChart>
    </ChartContainer>
  );
}
