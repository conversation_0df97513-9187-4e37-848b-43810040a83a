import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { APP_TEXT } from '@/const/text/app';
import type { HealthScoreMission } from '@/types/risk-score';

export default function MissionInfo({ data }: { data: HealthScoreMission | undefined }) {
  if (!data || !data.mission_id) {
    return (
      <div className="rounded-lg bg-card py-6 mx-6 flex flex-col items-center justify-center">
        <p className="font-medium text-gray-700 dark:text-gray-300">
          {APP_TEXT.HEALTH_SCORE_PAGE.MISSION_NOT_FOUND}
        </p>
      </div>
    );
  }
  let title = data.mission_title;
  const maxLength = 16;
  if (data.mission_title.length > maxLength) {
    title = `${data.mission_title.slice(0, maxLength)}...`;
  }
  const achieveDay = data.achieve_day;
  const targetAchieveDay = data.target_achieve_day;
  const progress = (Number(achieveDay) / Number(targetAchieveDay)) * 100;
  return (
    <div className="flex pl-[16px] pt-[10px] pr-[12px] bg-card rounded-2xl mx-6 h-20">
      <div className="w-[48px] h-[48px] relative mt-[2px]">
        <Badge className="bg-card border-2 w-[56px] border-border-badge text-textprimary rounded-full p-0 text-[10px] h-[20px] flex items-center justify-center absolute bottom-[-10px] left-[-4px]">
          {APP_TEXT.HEALTH_SCORE_PAGE.POINT}
        </Badge>
        <div className="w-full h-12 flex items-center justify-center overflow-hidden">
          <img className="w-full" src="/images/mission-coin.png" alt="mission-info" />
        </div>
      </div>
      <div className="flex-1 ml-[12px]">
        <p className="text-base font-bold">{title}</p>
        <div className="flex items-center gap-2 mt-[2px] mb-[6px]">
          <div className="text-xs h-[18px] flex items-center px-1 rounded-sm text-primary  bg-secondary">
            {APP_TEXT.HEALTH_SCORE_PAGE.MISSION}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Progress value={progress} />
          <div className="text-xxs leading-[10px]">
            {achieveDay}/{targetAchieveDay}
          </div>
        </div>
      </div>
      {/* <ChevronRight className="mt-[22px]" /> */}
    </div>
  );
}
