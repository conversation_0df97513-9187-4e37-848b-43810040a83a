import DialogTipsContent from '@/components/shared/dialog-tips';
import { Dialog, DialogTrigger } from '@/components/ui/dialog';
import { RiskTypeNum } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import type { HealthScoreJudgmentResponse } from '@/types/risk-score';
import { formatDate } from '@/utils/date-format';
import { CircleHelp } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import type { WeekRiskScore } from '../_const';
import { WEEK_RISK_SCORE_CONFIG } from '../_const';
import ScoreChart from './score-chart';
export default function ScoreChartSection({
  judgmentResultData,
  allowedHealthCareType,
}: {
  judgmentResultData: HealthScoreJudgmentResponse | undefined;
  allowedHealthCareType: string[];
}) {
  const [currentWeekRiskScore, setCurrentWeekRiskScore] = useState<WeekRiskScore>();
  const isFailed = useMemo(
    () => judgmentResultIsFailed(judgmentResultData, allowedHealthCareType),
    [judgmentResultData, allowedHealthCareType],
  );
  const nextEstimationDate = useMemo(() => {
    if (judgmentResultData?.judgment_result?.next_judgment_date) {
      return formatDate(judgmentResultData.judgment_result.next_judgment_date, 'yyyy年M月d日');
    }
    return '';
  }, [judgmentResultData]);

  useEffect(() => {
    if (judgmentResultData?.judgment_result?.judgment_result_data.asset_type) {
      const assetType = judgmentResultData.judgment_result?.judgment_result_data.asset_type;
      const targetSpeName =
        judgmentResultData.judgment_result?.judgment_result_data.target_spe_name;
      const assetTypeConfig = WEEK_RISK_SCORE_CONFIG.find((item) => {
        if (
          item.assetType === assetType &&
          item.assetType !== RiskTypeNum.bloodGlucoseAndNeutralFat
        ) {
          return true;
        }

        if (
          item.assetType === assetType &&
          item.assetType === RiskTypeNum.bloodGlucoseAndNeutralFat
        ) {
          if (targetSpeName.toLowerCase().indexOf('hb') > -1 && item.type === 'bloodSugar') {
            return true;
          }
          if (targetSpeName.toLowerCase().indexOf('tg') > -1 && item.type === 'neuterFat') {
            return true;
          }
        }

        return false;
      });
      if (assetTypeConfig) {
        setCurrentWeekRiskScore(assetTypeConfig);
      }
    }
  }, [judgmentResultData]);

  return (
    <div className="flex flex-col bg-card mx-6 rounded-2xl items-center relative pb-5">
      <div className="h-[211px] overflow-hidden mt-6">
        <div className="transform -translate-y-[22px]">
          <ScoreChart data={judgmentResultData} isFailed={isFailed} />
        </div>
      </div>
      {currentWeekRiskScore && (
        <HealthScoreTips data={currentWeekRiskScore}>
          <div className="flex items-center justify-center text-[18px] h-[27px] mt-1">
            <span>{currentWeekRiskScore.title}</span>
            <CircleHelp size={16} className="text-gray-500 ml-1" />
          </div>
        </HealthScoreTips>
      )}
      {isFailed && (
        <div className=" w-full px-6 mt-4">
          <div className="font-bold text-sm">次回の推定予定日：</div>
          <div>{nextEstimationDate}(深夜の時間帯)</div>
          <div className="text-gray-60 text-xs">※翌日の朝より判定結果をご確認いただけます。</div>
        </div>
      )}
    </div>
  );
}

const tips1 = APP_TEXT.HEALTH_SCORE_PAGE.TIP1;
const tips2 = APP_TEXT.HEALTH_SCORE_PAGE.TIP2;
const bloodSugarAndNeuterFatTitle = APP_TEXT.HEALTH_SCORE_PAGE.BLOOD_GLUCOSE_AND_NEUTER_FAT;

function HealthScoreTips({ data, children }: { data: WeekRiskScore; children: React.ReactNode }) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogTipsContent
        title={
          data.assetType === RiskTypeNum.bloodGlucoseAndNeutralFat
            ? bloodSugarAndNeuterFatTitle
            : data.title
        }
      >
        <div className="mt-2">
          <p className="text-sm leading-6">{data.desc}</p>
          <p className="text-sm text-text-secondary mt-2">{tips1}</p>
          <p className="text-sm text-text-secondary">{tips2}</p>
        </div>
      </DialogTipsContent>
    </Dialog>
  );
}

function judgmentResultIsFailed(
  data: HealthScoreJudgmentResponse | undefined,
  allowedHealthCareType: string[] | undefined,
) {
  // console.log('allowedHealthCareType', allowedHealthCareType);
  // if (data?.judgment_result?.judgment_result_data.asset_failure_type && allowedHealthCareType) {
  //   const assertFailureType = data?.judgment_result?.judgment_result_data.asset_failure_type;
  //   if (assertFailureType.length === unique(allowedHealthCareType).length) {
  //     return true;
  //   }
  // }
  // return false;】
  if (data?.judgment_result?.judgment_result_data.judgment_result_type === '3') {
    return true;
  }
  return false;
}

// function unique(strings: string[]) {
//   return [...new Set(strings)];
// }
