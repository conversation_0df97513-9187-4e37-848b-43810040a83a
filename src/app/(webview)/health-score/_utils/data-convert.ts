import type {
  ChartDateRange,
  HealthScoreHistoryListResponseFlat,
  HealthScoreHistoryListResponseUnwrap,
  ScoreChartConfig,
  ScoreData,
} from '@/types/risk-score';

export function convertHealthScoreHistoryDataToChartData(
  data: HealthScoreHistoryListResponseUnwrap,
): ScoreData[] {
  const dataList = flatHealthScoreHistoryData(data);
  return dataList
    .sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    })
    .map((item) => {
      const arr = item.date.split('/');
      const year = arr[0];
      const month = arr[1].replace(/^0+/, '');
      const day = arr[2].replace(/^0+/, '');
      const scoreData: ScoreData = {
        date: item.date,
        year: year,
        month: month,
        day: day,
        label: `${month}/${day}`,
        isHeadOfYear: false,
      };

      if (item.frail) {
        scoreData.frail = Number(item.frail);
      }
      if (item.hypertension) {
        scoreData.hypertension = Number(item.hypertension);
      }
      if (item.immunity) {
        scoreData.immunity = Number(item.immunity);
      }
      if (item.health_checkup_hba1c) {
        scoreData.bloodSugar = Number(item.health_checkup_hba1c);
      }
      if (item.health_checkup_tg) {
        scoreData.neuterFat = Number(item.health_checkup_tg);
      }
      if (item.well_being) {
        scoreData.wellBeing = Number(item.well_being);
      }
      if (item.stress) {
        scoreData.stress = Number(item.stress);
      }
      return scoreData;
    });
}

export function filterDataByGapInfoAndScoreType(
  data: ScoreData[],
  gapInfo: ChartDateRange,
  scoreType: string,
) {
  if (data.length === 0) {
    return [];
  }
  let headOfYear = '';
  const firstData = data[0];
  let prevDate = new Date(firstData.date);
  let result: ScoreData[] = [firstData];
  const fileterResult = data.filter((item) => {
    const date = new Date(item.date);
    const gap = gapInfo.gap;
    const gapType = gapInfo.gapType;
    if (
      gapType === 'week' &&
      date.getTime() >= prevDate.getTime() + gap * 7 * 24 * 60 * 60 * 1000
    ) {
      prevDate = date;
      return true;
    }

    if (gapType === 'month' && compareYearMonth(date, prevDate)) {
      prevDate = date;
      return true;
    }
    return false;
  });
  result = result.concat(fileterResult);
  result = result.map((item) => {
    let cloneItem = { ...item };
    if (scoreType !== 'all') {
      cloneItem = {
        date: item.date,
        year: item.year,
        month: item.month,
        day: item.day,
        label: item.label,
        isHeadOfYear: item.isHeadOfYear,
      };
      cloneItem[scoreType as keyof ScoreData] = item[scoreType as keyof ScoreData] as never;
    }
    if (gapInfo.gapType === 'month') {
      cloneItem.label = cloneItem.month;
    }
    if (cloneItem.year !== headOfYear) {
      headOfYear = cloneItem.year;
      cloneItem.isHeadOfYear = true;
    }

    return cloneItem;
  });
  if (gapInfo.gapType === 'month') {
    return addMonthEmptyData(result);
  }
  return result;
}

export function filterDataByRiskType(data: ScoreData[], riskType: string) {
  return data.filter((item) => item[riskType as keyof ScoreData] !== undefined);
}

/**
 * 月間のデータに空のデータを追加します。
 * 例えば、データが7月と9月のみの場合、8月の空のデータを追加します。
 *
 * @param data - 月間のデータ
 * @returns 空のデータを追加したデータ
 */
function addMonthEmptyData(data: ScoreData[]) {
  if (data.length === 0) {
    return [];
  }

  const result: ScoreData[] = [];
  result.push(data[0]);

  for (let i = 1; i < data.length; i++) {
    const currentItem = data[i];
    const prevItem = data[i - 1];
    const currentDate = new Date(currentItem.date);
    const prevDate = new Date(prevItem.date);
    prevDate.setMonth(prevDate.getMonth() + 1);
    while (compareYearMonth(currentDate, prevDate)) {
      result.push({
        date: prevItem.date,
        month: `${prevDate.getMonth() + 1}`,
        day: '1',
        year: prevDate.getFullYear().toString(),
        label: `${prevDate.getMonth() + 1}`,
        isHeadOfYear: false,
        isEmpty: true,
      });
      prevDate.setMonth(prevDate.getMonth() + 1);
    }

    result.push(currentItem);
  }

  return result;
}

function compareYearMonth(date1: Date, date2: Date): boolean {
  const yearMonth1 = date1.getFullYear() * 12 + date1.getMonth();
  const yearMonth2 = date2.getFullYear() * 12 + date2.getMonth();

  return yearMonth1 > yearMonth2;
}

function flatHealthScoreHistoryData(
  data: HealthScoreHistoryListResponseUnwrap,
): HealthScoreHistoryListResponseFlat[] {
  // 获取所有日期
  const allDates = new Set<string>();
  const allKeys = Object.keys(data);

  // 收集所有日期
  for (const item of Object.values(data).flat()) {
    if (allDates.has(item.day)) {
      continue;
    }
    allDates.add(item.day);
  }

  // 将日期转换为 ScoreData 数组
  return Array.from(allDates).map((date) => {
    const result: HealthScoreHistoryListResponseFlat = {
      date,
    };
    for (const key of allKeys) {
      result[key as keyof HealthScoreHistoryListResponseUnwrap] = data[
        key as keyof HealthScoreHistoryListResponseUnwrap
      ]?.find((item) => item.day === date)?.score;
    }
    return result;
  });
}
