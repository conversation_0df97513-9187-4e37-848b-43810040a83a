'use client';

import { healthScoreAPI } from '@/api/modules/health-score';
import { riskAPI } from '@/api/modules/health-score-init';
import { homePageAPI } from '@/api/modules/home-page';
import TopBar from '@/components/layout/top-bar';
import MenuPanel from '@/components/shared/menu-panel';
import { Select } from '@/components/shared/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SUPPORT_RISK } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import { useGlobalStore } from '@/store/global';
import type { MissionCardType } from '@/types/mission';
import type {
  ChartDateRange,
  HealthScoreHistoryListResponseUnwrap,
  HealthScoreJudgmentResponse,
  HealthScoreMission,
  ScoreChartConfig,
  ScoreData,
} from '@/types/risk-score';

import { ROUTES } from '@/const/routes';
import { useRouter } from '@/hooks/use-next-navigation';
import { type Asset, AssetOpState } from '@/types/health-core-init';
import { useEffect, useMemo, useState } from 'react';
import MissionCard from '../mission/_components/mission-card';
import MessageSection from './_components/message-section';
import ScoreChartSection from './_components/score-chart-section';
import ScoreHistoryChart from './_components/score-history-chart';
import SectionTitle from './_components/section-title';
import { CHART_DATE_RANGE_LIST, SCORE_CHART_CONFIG } from './_const';
import {
  convertHealthScoreHistoryDataToChartData,
  filterDataByGapInfoAndScoreType,
} from './_utils/data-convert';

const SETTING_MENU_ITEMS = [
  {
    label: APP_TEXT.HEALTH_SCORE_SET_PAGE.TITLE,
    href: ROUTES.RISK.SETTING,
  },
];

// ヘルスチェックAIページ
export default function HealthScorePage() {
  const [onOffState, setOnOffState] = useState<Asset[]>([]);
  const [assetGraphData, setAssetGraphData] = useState<HealthScoreHistoryListResponseUnwrap>({});
  const [judgmentResultData, setJudgmentResultData] = useState<HealthScoreJudgmentResponse>({});
  const [scoreType, setScoreType] = useState<string>('all');
  const [chartDateRange, setChartDateRange] = useState<ChartDateRange>(CHART_DATE_RANGE_LIST[0]);
  const [scoreHistoryChartALLData, setScoreHistoryChartALLData] = useState<ScoreData[]>([]);
  const [scoreHistoryChartViewData, setScoreHistoryChartViewData] = useState<ScoreData[]>([]);
  const [missionList, setMissionList] = useState<MissionCardType[]>();
  const { getPreviousPath, push, isPathEqual, back } = useRouter();

  // ユーザ様許可されたヘルスチェックAIの種類
  const scoreChartConfigAllowed = useMemo(() => {
    return SCORE_CHART_CONFIG.filter((item) => {
      const index = onOffState.findIndex(
        (asset) => asset.op_state === AssetOpState.ON && asset.asset_type === item.value,
      );
      return index !== -1;
    });
  }, [onOffState]);

  const allowedHealthCareType: string[] = useMemo(() => {
    return scoreChartConfigAllowed.map((item) => item.value);
  }, [scoreChartConfigAllowed]);

  // 凡例で表示するヘルスチェックAIの種類
  const scoreChartConfigList = useMemo(() => {
    return scoreChartConfigAllowed.filter((item) => scoreType === 'all' || scoreType === item.key);
  }, [scoreType, scoreChartConfigAllowed]);

  const [missionData, setMissionData] = useState<HealthScoreMission>();
  const { isRiskEnabled } = useGlobalStore();
  const scoreTypeOptions = useMemo(() => {
    return [
      { value: 'all', name: APP_TEXT.HEALTH_SCORE_PAGE.ALL },
      ...scoreChartConfigAllowed.map((item) => ({ value: item.key, name: item.name })),
    ];
  }, [scoreChartConfigAllowed]);
  const fetchOnOffState = () => {
    riskAPI.getOnOffList().then((res) => {
      if (res.on_of_list) {
        setOnOffState(res.on_of_list);
      }
    });
  };
  const fetchAssetGraph = () => {
    healthScoreAPI
      .assetGraph({
        month: 6,
        asset_type: SUPPORT_RISK.map((item) => item.toString()),
      })
      .then((res) => {
        if (res.graph_list) {
          setAssetGraphData(res.graph_list);
        }
      });
  };
  const fetchJudgmentResult = () => {
    healthScoreAPI.judgmentResult({}).then((res) => {
      if (res.judgment_result) {
        setJudgmentResultData(res);
      }
    });
  };
  const fetchJudgmentResultMission = () => {
    healthScoreAPI.mission({}).then((res) => {
      const missionList = res.judgment_result_mission_list;
      if (missionList) {
        const missionData = missionList.find((item) => !!item.mission_id);
        if (missionData) {
          setMissionData(missionData);
        }
      }
    });
  };
  const fetchMissionInfo = () => {
    // setIsLoading(true);
    homePageAPI
      .getHomeMission({ sourceType: 2 })
      .then((response) => {
        if (response != null && response.missionList != null) {
          setMissionList(response.missionList);
        }
      })
      .catch((error) => {});
  };
  // Fetch data when search parameters change
  useEffect(() => {
    if (isRiskEnabled) {
      fetchOnOffState();
      fetchAssetGraph();
      fetchJudgmentResult();
      // fetchJudgmentResultMission();
      fetchMissionInfo();
    }
  }, [isRiskEnabled]);

  useEffect(() => {
    const data = convertHealthScoreHistoryDataToChartData(assetGraphData);
    setScoreHistoryChartALLData(data);
  }, [assetGraphData]);

  useEffect(() => {
    setScoreHistoryChartViewData(
      filterDataByGapInfoAndScoreType(scoreHistoryChartALLData, chartDateRange, scoreType),
    );
  }, [chartDateRange, scoreHistoryChartALLData, scoreType]);

  // 表示するヘルスチェックAI選択時実行される
  const handleScoreTypeChange = (value: string) => {
    setScoreType(value);
  };

  const handleBack = () => {
    const previousPath = getPreviousPath();
    if (previousPath && isPathEqual(previousPath, ROUTES.RISK.AGREE)) {
      push(ROUTES.HOME);
    } else {
      back();
    }
  };

  if (!isRiskEnabled) {
    return (
      <>
        <TopBar onBack={handleBack} title={APP_TEXT.HEALTH_SCORE_PAGE.TITLE} />
        <MenuPanel title={APP_TEXT.HEALTH_SCORE_PAGE.SETTING} menuItems={SETTING_MENU_ITEMS} />
      </>
    );
  }

  return (
    <>
      <TopBar onBack={handleBack} title={APP_TEXT.HEALTH_SCORE_PAGE.TITLE} />
      {!judgmentResultIsFailed(judgmentResultData) && (
        <>
          <SectionTitle>{APP_TEXT.HEALTH_SCORE_PAGE.MESSAGE}</SectionTitle>
          <MessageSection judgmentResultData={judgmentResultData} missionData={missionList} />
        </>
      )}
      <SectionTitle>{APP_TEXT.HEALTH_SCORE_PAGE.WEEK_SCORE}</SectionTitle>
      <ScoreChartSection
        judgmentResultData={judgmentResultData}
        allowedHealthCareType={allowedHealthCareType}
      />
      <SectionTitle>{APP_TEXT.HEALTH_SCORE_PAGE.MISSION}</SectionTitle>
      {missionList && missionList.length > 0 ? (
        <div className="px-6 mt-2">
          {missionList.map((item: MissionCardType, i: number) => (
            <MissionCard
              key={i}
              missionCardItem={item}
              routerFlag="score"
              category={item?.category}
            />
          ))}
        </div>
      ) : (
        <div className="rounded-2xl bg-card py-6 mx-6 flex flex-col items-center justify-center">
          <p className="font-medium text-gray-700 dark:text-gray-300">
            {APP_TEXT.HEALTH_SCORE_PAGE.MISSION_NOT_FOUND}
          </p>
        </div>
      )}
      {/* <MissionInfo data={missionData} /> */}
      <SectionTitle>{APP_TEXT.HEALTH_SCORE_PAGE.HISTORY}</SectionTitle>
      <div className="p-6 bg-card">
        {/* 表示するヘルスチェックAI、プルダウンメニュー */}
        <Select
          defaultValue="all"
          options={scoreTypeOptions}
          title={APP_TEXT.HEALTH_SCORE_PAGE.HISTORY_TITLE}
          onChange={(value) => handleScoreTypeChange(value)}
        />

        {/* 時期選択タブ */}
        <Tabs defaultValue="0" className="mt-4">
          <TabsList className="w-full bg-[hsl(var(--primary-5))]">
            {CHART_DATE_RANGE_LIST.map((range) => (
              <TabsTrigger
                onClick={() => {
                  setChartDateRange(range);
                }}
                value={range.value}
                key={range.value}
              >
                {range.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {/* グラフ表示 */}
        <ScoreHistoryChart data={scoreHistoryChartViewData} chartDateRange={chartDateRange} />

        {/* グラフの凡例表示 */}
        <div className="flex flex-wrap mt-2">
          {scoreChartConfigList.map((scoreType) => (
            <div key={scoreType.name} className="flex w-[50%] items-center mt-2">
              <scoreType.icon size="15" fill={scoreType.color} strokeWidth={0} />
              <span className="text-nowrap text-sm ml-[5px] mt-[2px] leading-4">
                {scoreType.name}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* ヘルスチェックAIの利用設定メニュー */}
      <MenuPanel
        title={APP_TEXT.HEALTH_SCORE_PAGE.TITLE}
        menuItems={SETTING_MENU_ITEMS}
        className="mb-12"
      />
    </>
  );
}

function judgmentResultIsFailed(data: HealthScoreJudgmentResponse | undefined) {
  if (data?.judgment_result?.judgment_result_data.judgment_result_type === '3') {
    return true;
  }
  return false;
}
