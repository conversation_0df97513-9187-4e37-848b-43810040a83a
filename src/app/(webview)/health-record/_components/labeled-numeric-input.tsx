import { NumberInput } from '@/components/shared/number-input';
import { cn } from '@/lib/utils';
import React from 'react';
interface LabeledNumericInputProps {
  label: string;
  className?: string;
  name: string;
  unit: string;
  maxIntLen?: number; // Maximum integer length
  maxDecLen?: number; // Minimum integer length
  disabled?: boolean;
  onChange?: (value: string) => void;
  value: string;
}

export const LabeledNumericInput = React.forwardRef<HTMLInputElement, LabeledNumericInputProps>(
  ({ label, className, ...props }, ref) => {
    return (
      <div className={cn('flex flex-col mt-4 gap-1', className)}>
        <div className="text-sm">{label}</div>
        <NumberInput ref={ref} {...props} />
      </div>
    );
  },
);
