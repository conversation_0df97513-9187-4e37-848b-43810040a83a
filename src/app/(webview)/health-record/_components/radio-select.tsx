import { cn } from '@/lib/utils';
import React from 'react';

interface RadioSelectProps {
  selectedOption: string;
  onSelect: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

export function RadioSelect({ selectedOption, onSelect, children, className }: RadioSelectProps) {
  return (
    <div className={cn('flex gap-2', className)}>
      {React.Children.map(children, (child) =>
        React.isValidElement<{ value: string; isSelected?: boolean; onSelect?: () => void }>(child)
          ? React.cloneElement(child, {
              isSelected: child.props.value === selectedOption,
              onSelect: () => onSelect(child.props.value),
            })
          : null,
      )}
    </div>
  );
}
