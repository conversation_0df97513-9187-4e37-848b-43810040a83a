import { cn } from '@/lib/utils';

interface RadioOptionProps {
  value: string;
  label: string;
  className?: string;
  isSelected?: boolean;
  onSelect?: () => void;
}

export function RadioOption({
  value,
  label,
  className,
  isSelected = false,
  onSelect,
}: RadioOptionProps) {
  return (
    <button
      type="button"
      className={cn(
        'px-6 py-1 border border-primary rounded-md text-center',
        className,
        isSelected ? 'bg-primary text-primary-foreground' : 'text-primary',
      )}
      onClick={onSelect}
    >
      {label}
    </button>
  );
}
