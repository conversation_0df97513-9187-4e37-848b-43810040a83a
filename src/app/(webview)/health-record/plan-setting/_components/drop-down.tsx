import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ChevronDown } from 'lucide-react';
import type { ReactNode } from 'react';

interface DropdownOption {
  value: string;
  label: string | ReactNode;
}

interface DropdownProps {
  options: DropdownOption[];
  groupLabel?: string;
  placeholder?: string;
  width?: string;
  onValueChange?: (value: string) => void;
  defaultValue?: string;
}

export function Dropdown({
  options = [],
  groupLabel = '',
  width = 'w-[180px]',
  onValueChange = () => {},
  defaultValue = '',
}: DropdownProps) {
  return (
    <Select onValueChange={onValueChange} defaultValue={defaultValue}>
      <SelectTrigger className={`relative flex items-center justify-between ${width}`}>
        <SelectValue />
        <ChevronDown className="absolute right-2 w-4 h-4" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {groupLabel && <SelectLabel>{groupLabel}</SelectLabel>}
          {options.map((option: DropdownOption) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
