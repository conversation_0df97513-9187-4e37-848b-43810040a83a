import { Dropdown } from './drop-down';
import { InputFieldLabel } from './input-field-label';

interface LabeledDropDownProps {
  label: string;
  options: { value: string; label: string }[];
}

export function LabeledDropDown({ label, options }: LabeledDropDownProps) {
  return (
    <div className="flex flex-col mt-4 gap-1 mb-4 text-base">
      <InputFieldLabel>{label}</InputFieldLabel>
      <Dropdown options={options} />
    </div>
  );
}
