import { InputFieldLabel } from './input-field-label';
import { InputWithUnit } from './input-with-unit';

interface LabeledNumericInputProps {
  label: string;
  unit: string;
}

export function LabeledNumericInput({ label, unit }: LabeledNumericInputProps) {
  return (
    <div className="flex flex-col mt-4 mb-4 gap-1">
      <InputFieldLabel>{label}</InputFieldLabel>
      <InputWithUnit unit={unit} />
    </div>
  );
}
