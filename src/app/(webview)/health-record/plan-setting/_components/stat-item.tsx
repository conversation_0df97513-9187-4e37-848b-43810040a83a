'use client';

import { cn } from '@/lib/utils';
import type React from 'react';

type StatItemBaseProps = {
  label: string;
  value: number | number[];
  unit: string | string[];
  labelClassName?: string;
  valueClassName?: string;
};

export const StatItemHorizontal: React.FC<StatItemBaseProps> = ({
  label,
  value,
  unit,
  labelClassName,
  valueClassName,
}) => {
  return (
    <div className="flex justify-between items-center py-2">
      <span className={cn(labelClassName)}>{label}</span>
      <div className="flex items-center space-x-1">
        {Array.isArray(value) ? (
          value.map((val, idx) => (
            <div key={idx} className="flex items-center">
              <span className={cn(valueClassName)}>
                {typeof val === 'number' ? new Intl.NumberFormat().format(val) : val}
              </span>
              {Array.isArray(unit) ? (
                unit[idx] && <span className={cn(labelClassName, 'ml-[1px]')}>{unit[idx]}</span>
              ) : (
                <span className={cn(labelClassName, 'ml-[1px]')}>{unit}</span>
              )}
            </div>
          ))
        ) : (
          <div className="flex items-center">
            <span className={cn(valueClassName)}>
              {typeof value === 'number' ? new Intl.NumberFormat().format(value) : value}
            </span>
            <span className={cn(labelClassName, 'ml-[1px]')}>
              {Array.isArray(unit) ? unit[0] : unit}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export const StatItemVertical: React.FC<StatItemBaseProps> = ({
  label,
  value,
  unit,
  labelClassName,
  valueClassName,
}) => {
  return (
    <div className="flex flex-col items-start py-2">
      <span className={cn(labelClassName)}>{label}</span>

      <div className="flex flex-wrap items-baseline space-x-1 mt-1">
        {Array.isArray(value) ? (
          value.map((val, idx) => (
            <div key={idx} className="flex items-baseline">
              <span className={cn(valueClassName)}>
                {typeof val === 'number' ? new Intl.NumberFormat().format(val) : val}
              </span>
              {Array.isArray(unit) ? (
                unit[idx] && <span className={cn(labelClassName, 'ml-1')}> {unit[idx]}</span>
              ) : (
                <span className={cn(labelClassName, 'ml-1')}> {unit}</span>
              )}
            </div>
          ))
        ) : (
          <div className="flex items-baseline">
            <span className={cn(valueClassName)}>
              {typeof value === 'number' ? new Intl.NumberFormat().format(value) : value}
            </span>
            <span className={cn(labelClassName, 'ml-1')}>
              {Array.isArray(unit) ? unit[0] : unit}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
