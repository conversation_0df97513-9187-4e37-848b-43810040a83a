import { cn } from '@/lib/utils';

interface ConfirmButtonProps {
  text?: string;
  onClick?: () => void;
  className?: string;
}

export function ConfirmButton({ text = '保存', onClick, className }: ConfirmButtonProps) {
  return (
    <div className="mt-2 flex justify-center">
      <button
        type="button"
        onClick={onClick}
        className={cn(
          'w-full px-8 py-3 bg-primary text-primary-foreground font-bold rounded-full hover:bg-primary-600',
          className,
        )}
      >
        {text}
      </button>
    </div>
  );
}
