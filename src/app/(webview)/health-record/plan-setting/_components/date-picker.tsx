'use client';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CalendarDays } from 'lucide-react';
import { useState } from 'react';

export function DatePicker({
  value,
  onChange,
  className = '',
}: {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}) {
  const [selectedDate, setSelectedDate] = useState<string | undefined>(value);

  const handleDateChange = (date: string) => {
    setSelectedDate(date);
    onChange?.(date);
  };

  const formatDate = (date: string | undefined) => {
    if (!date) return '';
    const parsedDate = new Date(date);
    return format(parsedDate, 'yyyy年M月d日（E）', { locale: ja });
  };

  return (
    <div className={cn('relative', className)}>
      <Input
        type="date"
        value={selectedDate}
        onChange={(e) => handleDateChange(e.target.value)}
        className="flex relative z-10 border rounded-lg appearance-none h-12 flex-1 pl-4 pr-10 text-sm w-full text-transparent"
      />
      <div
        className={cn(
          'absolute left-2 top-1/2 -translate-y-1/2 text-md',
          selectedDate ? 'bg-primary-foreground px-2 rounded' : 'px-2 rounded',
        )}
      >
        {selectedDate ? formatDate(selectedDate) : '日付を選択'}
      </div>
      <CalendarDays className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none" />
    </div>
  );
}
