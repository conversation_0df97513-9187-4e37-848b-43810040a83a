import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { CircleHelp } from 'lucide-react';

interface ListRadioOptionProps {
  value: string;
  help?: string;
  title: string;
  tip: string;
  className?: string;
}

interface ListRadioGroupProps {
  selectedOption: string;
  onSelect: (value: string) => void;
  className?: string;
  children: React.ReactNode;
}

export function ListRadioGroup({
  selectedOption,
  onSelect,
  className,
  children,
}: ListRadioGroupProps) {
  return (
    <RadioGroup
      value={selectedOption}
      onValueChange={onSelect}
      className={cn('flex flex-col gap-2', className)}
    >
      {children}
    </RadioGroup>
  );
}

export function ListRadioOption({ value, help, title, tip, className }: ListRadioOptionProps) {
  return (
    <div
      className={cn(
        'flex items-center gap-4 p-4 border rounded-lg text-left w-full cursor-pointer transition-colors',
        'border-border',
        className,
      )}
    >
      <RadioGroupItem
        value={value}
        id={`radio-${value}`}
        className={cn('shrink-0', 'peer', 'border-2 border-border rounded-full w-5 h-5')}
      />
      <Label className="flex flex-col" htmlFor={`radio-${value}`}>
        <span className="text-lg font-bold flex items-center gap-2">
          {title}
          {help && <CircleHelp size={22} className="text-muted-foreground" />}
        </span>
        <span className="text-sm text-muted-foreground">{tip}</span>
      </Label>
    </div>
  );
}
