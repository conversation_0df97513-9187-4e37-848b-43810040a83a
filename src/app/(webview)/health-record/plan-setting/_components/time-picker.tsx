'use client';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import * as React from 'react';

export function TimePicker({ className }: { className?: string }) {
  const [time, setTime] = React.useState<string>('');

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTime(event.target.value);
  };

  return (
    <div
      className={cn(
        'relative flex items-center justify-center h-[46px] border rounded',
        'w-[70px]',
        className,
      )}
    >
      {/* 显示时间文本 */}
      <span className="absolute text-md text-center pointer-events-none">{time || '00:00'}</span>
      {/* 隐藏原生时间输入框 */}
      <Input
        type="time"
        value={time}
        onChange={handleChange}
        className="absolute opacity-0 w-full h-full inset-0 cursor-pointer"
      />
    </div>
  );
}
