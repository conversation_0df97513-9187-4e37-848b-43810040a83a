interface RecommendationCardProps {
  value?: string;
}

export function RecommendationCard({ value = '-' }: RecommendationCardProps) {
  return (
    <div className="w-full p-4 bg-secondary rounded-lg shadow-md">
      <div className="text-base text-black-700 ml-2">おすすめの目標歩数</div>
      <div className="flex justify-end items-end mt-2 mr-2">
        <span className="text-2xl font-extrabold text-primary">-</span>
        <span className="text-lg ml-2">歩 / 日</span>
      </div>
    </div>
  );
}
