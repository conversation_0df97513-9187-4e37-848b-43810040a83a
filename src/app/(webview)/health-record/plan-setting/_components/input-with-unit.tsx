'use client';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import type * as React from 'react';

interface InputWithUnitProps extends React.InputHTMLAttributes<HTMLInputElement> {
  unit: string;
}

export function InputWithUnit({ unit, className, ...props }: InputWithUnitProps) {
  const inputId = props.id || 'input-with-unit';

  return (
    <label htmlFor={inputId} className="relative w-full max-w-[155px] cursor-pointer">
      <Input
        {...props}
        id={inputId}
        maxLength={6}
        className={cn('border rounded-lg px-4 py-3 pr-10 w-full h-18', className)}
      />
      <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground pointer-events-none">
        {unit}
      </span>
    </label>
  );
}
