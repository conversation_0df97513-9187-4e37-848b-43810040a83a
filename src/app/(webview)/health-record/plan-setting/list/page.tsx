'use client';
import { Form, FormField, FormItem } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ListRadioGroup, ListRadioOption } from '@/components/shared/list-radio';
import { useRouter } from '@/hooks/use-next-navigation';
import { ConfirmButton } from '../_components/confirm-button';

const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .max(30, {
      message: 'Username must not be longer than 30 characters.',
    }),
  email: z
    .string({
      required_error: 'Please select an email to display.',
    })
    .email(),
  bio: z.string().max(160).min(4),
  urls: z
    .array(
      z.object({
        value: z.string().url({ message: 'Please enter a valid URL.' }),
      }),
    )
    .optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// This can come from your database or API.
const defaultValues: Partial<ProfileFormValues> = {
  bio: 'I own a computer.',
  urls: [{ value: 'https://shadcn.com' }, { value: 'http://twitter.com/shadcn' }],
};

export default function HealthRecordPage() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  const router = useRouter();
  const [selectedOption, setSelectedOption] = useState('option-one');

  const plans = [
    {
      value: 'option-one',
      title: 'おすすめ歩数プラン',
      tip: '性別・年齢にあわせた、健康で長生きのための歩数を自動で目標に設定します。',
    },
    {
      value: 'option-two',
      title: '＋10プラン',
      help: '＋10プラン',
      tip: '普段のあなたの歩数に＋１０のウォーキングを自動計算して目標に設定します。',
    },
    {
      value: 'option-three',
      title: '自分で設定プラン',
      tip: '目標歩数を自分で入力して設定します。',
    },
  ];

  const handleConfirmClick = () => {
    if (selectedOption === 'option-one') {
      router.push('/health-record/plan-setting/detail/profile-based');
    } else if (selectedOption === 'option-two') {
      router.push('/health-record/plan-setting/detail/history-based');
    } else if (selectedOption === 'option-three') {
      router.push('/health-record/plan-setting/detail/manual-input');
    }
  };

  return (
    <>
      <div className="min-h-screen">
        <div className="bg-primary-foreground p-6 shadow-md">
          <div>
            <Form {...form}>
              <form>
                <FormField
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <ListRadioGroup
                        selectedOption={selectedOption}
                        onSelect={setSelectedOption}
                        className="gap-4"
                      >
                        {plans.map((plan, index) => (
                          <ListRadioOption
                            key={index}
                            value={plan.value}
                            help={plan.help}
                            title={plan.title}
                            tip={plan.tip}
                            className={selectedOption === plan.value ? 'border-primary' : ''}
                          />
                        ))}
                      </ListRadioGroup>

                      <ConfirmButton
                        text="設定に進む"
                        className="mt-[273px]"
                        onClick={handleConfirmClick}
                      />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
