'use client';
import { Form, FormField, FormItem } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { StatItemVertical } from '../../_components/stat-item';

const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .max(30, {
      message: 'Username must not be longer than 30 characters.',
    }),
  email: z
    .string({
      required_error: 'Please select an email to display.',
    })
    .email(),
  bio: z.string().max(160).min(4),
  urls: z
    .array(
      z.object({
        value: z.string().url({ message: 'Please enter a valid URL.' }),
      }),
    )
    .optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// This can come from your database or API.
const defaultValues: Partial<ProfileFormValues> = {
  bio: 'I own a computer.',
  urls: [{ value: 'https://shadcn.com' }, { value: 'http://twitter.com/shadcn' }],
};

export default function HealthRecordPage() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  interface Option {
    value: string;
    name: string;
  }

  const plan_data = {
    value: '1',
    title: 'おすすめ歩数プラン',
    step: 8000,
    carori: 190,
    distance: 6.5,
    walk_time_hour: 1,
    walk_time_minite: 16,
  };

  plan_data;

  return (
    <>
      <div className="min-h-screen">
        <div className=" bg-primary-foreground p-6 shadow-md">
          <div>
            <Form {...form}>
              <form>
                <FormField
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex gap-8">
                        <StatItemVertical
                          label="想定歩行距離"
                          value={plan_data.distance}
                          unit="km / 日"
                          labelClassName="text-sm"
                          valueClassName="font-bold text-xl"
                        />
                        <StatItemVertical
                          label="想定歩行時間"
                          value={[plan_data.walk_time_hour, plan_data.walk_time_minite]}
                          unit={['時間', '分']}
                          labelClassName="text-sm"
                          valueClassName="font-bold text-xl"
                        />
                      </div>
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
