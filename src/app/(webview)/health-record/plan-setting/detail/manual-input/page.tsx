'use client';
import { NumberInput } from '@/components/shared/number-input';
import { Form, FormField, FormItem } from '@/components/ui/form';
import { useRouter } from '@/hooks/use-next-navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { ConfirmButton } from '../../_components/confirm-button';
import { InputFieldTip } from '../../_components/input-field-tip';
import { SubTitle } from '../../_components/sub-title';
const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .max(30, {
      message: 'Username must not be longer than 30 characters.',
    }),
  email: z
    .string({
      required_error: 'Please select an email to display.',
    })
    .email(),
  bio: z.string().max(160).min(4),
  urls: z
    .array(
      z.object({
        value: z.string().url({ message: 'Please enter a valid URL.' }),
      }),
    )
    .optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// This can come from your database or API.
const defaultValues: Partial<ProfileFormValues> = {
  bio: 'I own a computer.',
  urls: [{ value: 'https://shadcn.com' }, { value: 'http://twitter.com/shadcn' }],
};

export default function HealthRecordPage() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  interface Option {
    value: string;
    name: string;
  }

  const router = useRouter();
  const handleConfirmClick = () => {
    router.push('/health-record/plan-setting/detail/walking_plan');
  };

  return (
    <>
      <div className="min-h-screen">
        <div className=" bg-primary-foreground p-6 shadow-md">
          <div>
            <Form {...form}>
              <form>
                <FormField
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex　flex-col gap-2">
                        <SubTitle className="mb-4 text-base">目標歩数を入力してください。</SubTitle>
                        <div className="flex gap-2 w-32">
                          <NumberInput
                            unit="歩"
                            name="step"
                            value={field.value}
                            onChange={field.onChange}
                          />
                        </div>
                        <InputFieldTip className="text-sm mt-4">
                          <p>
                            継続のため8,000歩~15,000歩の目標をおすすめします。厚生労働省の健康日本21によると、20~64歳の男女は1日8,000歩以上、65歳以上の男女は1日6,000歩以上を目安にすることが推奨されています。
                            <br />
                          </p>
                        </InputFieldTip>
                      </div>

                      <ConfirmButton
                        text="目標を決定する"
                        className="mt-80"
                        onClick={handleConfirmClick}
                      />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
