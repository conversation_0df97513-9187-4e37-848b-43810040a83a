'use client';
import { But<PERSON> } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { Form, FormField, FormItem } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { MainTitle } from '../../_components/main-title';
import { StatItemHorizontal } from '../../_components/stat-item';

const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .max(30, {
      message: 'Username must not be longer than 30 characters.',
    }),
  email: z
    .string({
      required_error: 'Please select an email to display.',
    })
    .email(),
  bio: z.string().max(160).min(4),
  urls: z
    .array(
      z.object({
        value: z.string().url({ message: 'Please enter a valid URL.' }),
      }),
    )
    .optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const defaultValues: Partial<ProfileFormValues> = {
  bio: 'I own a computer.',
  urls: [{ value: 'https://shadcn.com' }, { value: 'http://twitter.com/shadcn' }],
};

export default function HealthRecordPage() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  interface Option {
    value: string;
    name: string;
  }

  const plan_data = {
    value: '1',
    title: 'おすすめ歩数プラン',
    step: 8000,
    carori: 190,
    distance: 6.5,
    walk_time_hour: 1,
    walk_time_minite: 16,
  };

  plan_data;

  return (
    <>
      <div className="min-h-screen">
        <div className=" bg-primary-foreground p-6 shadow-md">
          <div>
            <Form {...form}>
              <form>
                <FormField
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <div className="mx-auto w-full max-w-5xl rounded-2xl bg-secondary p-6 shadow-md">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                          <div className="w-fit bg-secondary-foreground text-primary-foreground text-xs rounded px-1 py-0.5 text-left">
                            選択中のプラン
                          </div>
                          <MainTitle className="mb-4 font-bold text-lg">
                            {plan_data.title}
                          </MainTitle>
                          <hr className="my-2 border-t border-gray-300" />
                          <StatItemHorizontal
                            label="目標歩数"
                            value={plan_data.step}
                            unit="歩 / 日"
                            labelClassName="text-base"
                            valueClassName="font-bold text-4xl mr-2"
                          />
                          <StatItemHorizontal
                            label="想定消費カロリー"
                            value={plan_data.carori}
                            unit="kcal / 日"
                            labelClassName="text-sm"
                            valueClassName=""
                          />
                          <StatItemHorizontal
                            label="想定歩行距離"
                            value={plan_data.distance}
                            unit="km / 日"
                            labelClassName="text-sm"
                            valueClassName=""
                          />
                          <StatItemHorizontal
                            label="想定歩行時間"
                            value={[plan_data.walk_time_hour, plan_data.walk_time_minite]}
                            unit={['時間', '分 / 日']}
                            labelClassName="text-sm"
                            valueClassName=""
                          />
                          <div className="flex justify-center">
                            <Button size="sm" className="flex-1" variant="outline">
                              プランを変更
                            </Button>
                          </div>
                          <TextButton
                            className="w-[280px] h-12 text-primary flex-none"
                            variant="muted"
                          >
                            目標歩数のみを変更
                          </TextButton>
                        </div>
                      </div>
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
