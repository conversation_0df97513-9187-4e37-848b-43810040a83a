'use client';
import {
  SelectDrawer,
  SelectDrawerClose,
  SelectDrawerContent,
  SelectDrawerHeader,
  SelectDrawerTrigger,
} from '@/components/shared/select-drawer';
import { Form, FormField, FormItem } from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useRouter } from '@/hooks/use-next-navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { ConfirmButton } from '../../_components/confirm-button';
import { InputFieldLabel } from '../../_components/input-field-label';
import { InputFieldTip } from '../../_components/input-field-tip';
import { RecommendationCard } from '../../_components/recommendation-card';
import { SubTitle } from '../../_components/sub-title';
import { ERA_OPTIONS, GENDER_OPTIONS } from '../../_const';
const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: 'Username must be at least 2 characters.',
    })
    .max(30, {
      message: 'Username must not be longer than 30 characters.',
    }),
  email: z
    .string({
      required_error: 'Please select an email to display.',
    })
    .email(),
  bio: z.string().max(160).min(4),
  urls: z
    .array(
      z.object({
        value: z.string().url({ message: 'Please enter a valid URL.' }),
      }),
    )
    .optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

// This can come from your database or API.
const defaultValues: Partial<ProfileFormValues> = {
  bio: 'I own a computer.',
  urls: [{ value: 'https://shadcn.com' }, { value: 'http://twitter.com/shadcn' }],
};

export default function HealthRecordPage() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  interface Option {
    value: string;
    name: string;
  }
  const router = useRouter();
  const [selectedGender, setSelectedGender] = useState('');
  const [selectedEra, setSelectedEra] = useState('');

  const handleConfirmClick = () => {
    router.push('/health-record/plan-setting/detail/walking_plan');
  };
  return (
    <>
      <div className="min-h-screen">
        <div className=" bg-primary-foreground p-6 shadow-md">
          <div>
            <Form {...form}>
              <form>
                <FormField
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <SubTitle className="mb-4 text-base">性別と年代を選択してください。</SubTitle>
                      <div className="flex">
                        <div className="w-[150px]">
                          <InputFieldLabel>性別</InputFieldLabel>
                          <SelectDrawer>
                            <SelectDrawerTrigger>
                              <span>
                                {GENDER_OPTIONS.find((option) => option.value === selectedGender)
                                  ?.name || ''}
                              </span>
                            </SelectDrawerTrigger>
                            <SelectDrawerContent className="bg-card">
                              <SelectDrawerHeader>性別</SelectDrawerHeader>
                              <RadioGroup
                                value={selectedGender}
                                onValueChange={(value) => setSelectedGender(value)}
                              >
                                {GENDER_OPTIONS.map((genderOption) => (
                                  <SelectDrawerClose key={genderOption.name} asChild>
                                    <Label
                                      htmlFor={`score-type-${genderOption.value}`}
                                      className="flex items-center h-14 px-6 cursor-pointer text-base"
                                    >
                                      <RadioGroupItem
                                        value={genderOption.value}
                                        id={`score-type-${genderOption.value}`}
                                      />
                                      <span className="ml-4">{genderOption.name}</span>
                                    </Label>
                                  </SelectDrawerClose>
                                ))}
                              </RadioGroup>
                            </SelectDrawerContent>
                          </SelectDrawer>
                        </div>

                        <div className="w-[150px] ml-4">
                          <InputFieldLabel>年代</InputFieldLabel>

                          <SelectDrawer>
                            <SelectDrawerTrigger>
                              <span>
                                {ERA_OPTIONS.find((option) => option.value === selectedEra)?.name ||
                                  ''}
                              </span>
                            </SelectDrawerTrigger>
                            <SelectDrawerContent className="bg-card">
                              <SelectDrawerHeader>年代</SelectDrawerHeader>
                              <RadioGroup
                                value={selectedGender}
                                onValueChange={(value) => setSelectedEra(value)}
                              >
                                {ERA_OPTIONS.map((eraOption) => (
                                  <SelectDrawerClose key={eraOption.name} asChild>
                                    <Label
                                      htmlFor={`score-type-${eraOption.value}`}
                                      className="flex items-center h-14 px-6 cursor-pointer text-base"
                                    >
                                      <RadioGroupItem
                                        value={eraOption.value}
                                        id={`score-type-${eraOption.value}`}
                                      />
                                      <span className="ml-4">{eraOption.name}</span>
                                    </Label>
                                  </SelectDrawerClose>
                                ))}
                              </RadioGroup>
                            </SelectDrawerContent>
                          </SelectDrawer>
                        </div>
                      </div>

                      <div className="space-y-6">
                        <RecommendationCard />
                        <InputFieldTip className="text-sm">
                          <p>
                            毎日この歩数を達成することで、心疾患や糖尿病、高血圧などのリスクを減らす効果があります。
                          </p>
                          <p>引用元：~~~~~~</p>
                        </InputFieldTip>
                      </div>
                      <ConfirmButton
                        text="目標を決定する"
                        className="mt-72"
                        onClick={handleConfirmClick}
                      />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
