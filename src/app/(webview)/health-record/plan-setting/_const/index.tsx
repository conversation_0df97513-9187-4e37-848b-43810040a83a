export interface WALK_PLAN {
  value: string;
  title: string;
  help?: string;
  tip: string;
}

export interface GENDER_OPTION {
  value: string;
  name: string;
}

export interface ERA_OPTION {
  value: string;
  name: string;
}

export interface TIME_RANGE_OPTION {
  value: string;
  name: string;
}

export const WALK_PLAN_LIST: WALK_PLAN[] = [
  {
    value: 'option-one',
    title: 'おすすめ歩数プラン',
    tip: '性別・年齢にあわせた、健康で長生きのための歩数を自動で目標に設定します。',
  },
  {
    value: 'option-two',
    title: '＋10プラン',
    help: '＋10プラン',
    tip: '普段のあなたの歩数に＋１０のウォーキングを自動計算して目標に設定します。',
  },
  {
    value: 'option-three',
    title: '自分で設定プラン',
    tip: '目標歩数を自分で入力して設定します。',
  },
];

export const GENDER_OPTIONS: GENDER_OPTION[] = [
  { value: 'm', name: '男性' },
  { value: 'f', name: '女性' },
];

export const ERA_OPTIONS: ERA_OPTION[] = [
  { value: '80', name: '80' },
  { value: '90', name: '90' },
  { value: '00', name: '00' },
];

export const TIME_RANGE_OPTIONS: TIME_RANGE_OPTION[] = [
  { value: '1W', name: '直近1週間' },
  { value: '1M', name: '直近1ヶ月' },
  { value: '3M', name: '直近3ヶ月' },
  { value: '1Y', name: '直近1年' },
];

export const ONOFF_OPTIONS: GENDER_OPTION[] = [
  { value: '1', name: '開く' },
  { value: '0', name: '閉じる' },
];
