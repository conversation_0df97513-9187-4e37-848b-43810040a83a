import type {
  BarChartData,
  BloodPressureThumbnailData,
  MultiBloodGlucoseThumbnailData,
  RangeBarChartData,
  SingleBloodGlucoseThumbnailData,
  SleepTimeThumbnailData,
  StepThumbnailData,
  WeightThumbnailData,
} from '@/types/health-record';

export function convertThumbnailData<
  T extends
    | StepThumbnailData
    | WeightThumbnailData
    | SleepTimeThumbnailData
    | BloodPressureThumbnailData
    | SingleBloodGlucoseThumbnailData
    | MultiBloodGlucoseThumbnailData,
>(data: T[] | undefined, key: keyof T, currentDate: string): BarChartData[] {
  if (!data || !currentDate) {
    return [];
  }
  const convertedData = data.map((item) => ({
    date: item.measureDate,
    value: item[key] as number,
  }));
  return convertDataTo7Days(convertedData, currentDate);
}

export function convertRangeBarChartData<T extends BloodPressureThumbnailData>(
  data: T[] | undefined,
  lowKey: keyof T,
  highKey: keyof T,
  currentDate: string,
): RangeBarChartData[] {
  if (!data || !currentDate) {
    return [];
  }
  const convertedData = data.map((item) => ({
    date: item.measureDate,
    low: item[lowKey] as number,
    high: item[highKey] as number,
  }));
  return convertRangeDataTo7Days(convertedData, currentDate);
}

function convertDataTo7Days(data: BarChartData[], currentDate: string): BarChartData[] {
  const last7Days = Array.from({ length: 7 }, (_, index) => {
    const date = new Date(currentDate);
    date.setDate(date.getDate() - index);
    return date.toISOString().split('T')[0];
  }).reverse();
  const dataMap = new Map(data.map((item) => [item.date, item.value]));
  return last7Days.map((date) => ({
    date,
    value: dataMap.get(date) ?? undefined,
  }));
}

function convertRangeDataTo7Days(
  data: RangeBarChartData[],
  currentDate: string,
): RangeBarChartData[] {
  const last7Days = Array.from({ length: 7 }, (_, index) => {
    const date = new Date(currentDate);
    date.setDate(date.getDate() - index);
    return date.toISOString().split('T')[0];
  }).reverse();
  const dataMap = new Map(data.map((item) => [item.date, item]));
  return last7Days.map((date) => ({
    date,
    low: dataMap.get(date)?.low ?? 0,
    high: dataMap.get(date)?.high ?? 0,
  }));
}
