'use client';

import { cn } from '@/lib/utils';
import { formatDate } from '@/utils/date-format';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import React, { useEffect, useState } from 'react';

type DateTitleProps = React.HTMLAttributes<HTMLDivElement> & {
  onDateChange: (date: Date) => void;
  date: Date | null;
};

export const DateMenu = React.forwardRef<HTMLDivElement, DateTitleProps>(
  ({ className, onDateChange, date, ...props }, ref) => {
    const handleDateChange = (offset: number) => {
      if (date) {
        const newDate = new Date(date);
        newDate.setDate(newDate.getDate() + offset);
        onDateChange(newDate);
      }
    };
    const [isToday, setIsToday] = useState(false);
    useEffect(() => {
      if (date) {
        setIsToday(date.toDateString() === new Date().toDateString());
      }
    }, [date]);
    return (
      <div
        className={cn('flex items-center justify-between mx-9 mt-5', className)}
        ref={ref}
        {...props}
      >
        <button className="p-1" type="button" onClick={() => handleDateChange(-1)}>
          <ChevronLeft className="h-5 w-5" />
        </button>

        <div className="flex-1 text-center">
          <p className="font-body-l-regular text-black">{formatDate(date, 'mm月dd日(d)')}</p>
        </div>

        <button
          className={cn('p-1', isToday && 'opacity-0')}
          type="button"
          onClick={() => handleDateChange(1)}
          disabled={isToday}
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>
    );
  },
);

DateMenu.displayName = 'DateMenu';
