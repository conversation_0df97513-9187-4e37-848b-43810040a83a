'use client';
import { COLORS } from '@/const/colors';
import { cn } from '@/lib/utils';
import type { RangeBarChartData } from '@/types/health-record';
import { Bar, type BarProps, BarChart as RechartsBarChart, YAxis } from 'recharts';
interface RangeBarChartProps {
  className?: string;
  data: RangeBarChartData[];
}

const minBloodPressure = 40;
const maxBloodPressure = 280;
// const maxDisplayBloodPressure = maxBloodPressure - minBloodPressure;

export default function RangeBarChart({ className, data }: RangeBarChartProps) {
  // const chartData = data.map((item) => {
  //   const systolic = Math.min(Math.max(item.high - minBloodPressure, 0), maxDisplayBloodPressure);
  //   const diastolic = Math.min(Math.max(item.low - minBloodPressure, 0), maxDisplayBloodPressure);
  //   return {
  //     date: item.date,
  //     systolic: systolic,
  //     diastolic: diastolic,
  //   };
  // });

  const chartData = data.map((item) => {
    const systolic = item.high;
    const diastolic = item.low;
    return {
      date: item.date,
      systolic: systolic,
      diastolic: diastolic,
    };
  });

  return (
    <div className={cn('w-[64px] h-[64px] flex items-center justify-center', className)}>
      <RechartsBarChart width={64} height={56} data={chartData} barSize={4}>
        <YAxis domain={[minBloodPressure, maxBloodPressure]} hide />
        <Bar
          dataKey="diastolic"
          stackId="a"
          fill={'transparent'}
          name="low"
          isAnimationActive={false}
        />
        <Bar
          dataKey="systolic"
          stackId="a"
          fill={COLORS.primary.foreground}
          name="high"
          isAnimationActive={false}
          shape={(props: BarProps) => {
            const item = data[props.index as number];
            if (item.high === 0 && item.low === 0) {
              return <span key={props.key} />;
            }
            return <CustomBar curIndex={6} props={props} />;
          }}
        />
      </RechartsBarChart>
    </div>
  );
}

const CustomBar = ({ curIndex, props }: { curIndex: number; props: BarProps }) => {
  const isActive = props.index === curIndex;
  const width = props.width as number;
  const height = props.height as number;
  const x = props.x as number;
  const y = props.y as number;
  if (height < 4) {
    return null;
  }

  return (
    <>
      <rect
        height="2"
        width={width}
        y={y}
        x={x}
        fill={isActive ? COLORS.primary.soft : COLORS.primary.DEFAULT}
      />
      <rect
        height={height - 4}
        width={width}
        y={y + 2}
        x={x}
        fill={isActive ? COLORS.primary.DEFAULT : COLORS.primary.soft}
      />
      <rect
        height="2"
        width={width}
        y={y + height - 2}
        x={x}
        fill={isActive ? COLORS.primary.soft : COLORS.primary.DEFAULT}
      />
    </>
  );
};
