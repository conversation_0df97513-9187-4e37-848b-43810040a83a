'use client';
import { COLORS } from '@/const/colors';
import { cn } from '@/lib/utils';
import type { BarChartData } from '@/types/health-record';
import { Line, LineChart as RechartsLineChart, YAxis } from 'recharts';

export default function LineChart({
  className,
  data,
  yAxisDomain,
}: { className: string; data: BarChartData[]; yAxisDomain?: [number, number] }) {
  return (
    <div className={cn('w-[64px] h-[64px] flex items-center justify-center', className)}>
      <RechartsLineChart
        width={64}
        height={40}
        data={data}
        // @ts-expect-error
        connectNulls
      >
        <Line
          dataKey="value"
          stroke={COLORS.primary.soft}
          isAnimationActive={false}
          dot={(props) => {
            const isLast = props.index === data.length - 1;
            const item = data[props.index];
            if (!item.value) {
              return <span key={props.key} className="w-4 h-4" />;
            }
            return <Circle key={props.key} cx={props.cx} cy={props.cy} isLast={isLast} />;
          }}
        />
        {yAxisDomain && <YAxis domain={yAxisDomain} hide />}
      </RechartsLineChart>
    </div>
  );
}

function Circle({ cx, cy, isLast }: { cx: number; cy: number; isLast: boolean }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={cx - 2}
      y={cy - 2}
      width={4}
      height={4}
      viewBox="0 0 28 28"
      strokeWidth="4"
      stroke={isLast ? COLORS.primary.DEFAULT : COLORS.primary.soft}
      fill={isLast ? COLORS.primary.DEFAULT : COLORS.primary.softer}
    >
      <circle cx="14" cy="14" r="10" />
    </svg>
  );
}
