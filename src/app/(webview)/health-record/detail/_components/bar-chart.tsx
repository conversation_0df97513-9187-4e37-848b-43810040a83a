'use client';
import { COLORS } from '@/const/colors';
import { cn } from '@/lib/utils';
import type { BarChartData } from '@/types/health-record';
import { Bar, Cell, BarChart as RechartsBarChart } from 'recharts';

export default function Bar<PERSON>hart({ className, data }: { className: string; data: BarChartData[] }) {
  return (
    <div className={cn('w-[64px] h-[64px] flex items-center justify-center', className)}>
      <RechartsBarChart width={64} height={40} data={data} barSize={4}>
        <Bar dataKey="value" fill={COLORS.primary.soft} isAnimationActive={false}>
          {data.map((entry, index, arr) => {
            const isActive = index === arr.length - 1;
            return (
              <Cell
                key={`cell-${index}`}
                fill={isActive ? COLORS.primary.DEFAULT : COLORS.primary.soft}
              />
            );
          })}
        </Bar>
      </RechartsBarChart>
    </div>
  );
}
