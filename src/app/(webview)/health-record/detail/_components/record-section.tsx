'use client';

import { TextButton } from '@/components/shared/text-button';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import type {
  HealthRecord,
  MultiBloodGlucoseThumbnailData,
  SingleBloodGlucoseThumbnailData,
  ThumbnailData,
} from '@/types/health-record';
import React from 'react';
import { convertRangeBarChartData, convertThumbnailData } from '../_utils/data-convert';
import BarChart from './bar-chart';
import LineChart from './line-chart';
import RangeBarChart from './range-bar-chart';
interface DataProps {
  data: HealthRecord;
  thumbnail?: ThumbnailData;
  date?: string;
}

// 数値を3桁ごとにカンマで区切る
function formatNumber(number: number | undefined) {
  if (!number) return '0';
  return number.toLocaleString();
}

function DataEmpty() {
  return <p className="text-text-muted mt-2">データがありません</p>;
}

const Step = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & DataProps>(
  ({ className, data, thumbnail, date, ...props }, ref) => {
    const step = data.step;
    const stepThumbnailData = thumbnail?.stepThumnData;
    const router = useRouter();
    return (
      <RecordCard {...props} className={className} ref={ref}>
        <RecordWrapper
          size="lg"
          title={APP_TEXT.HEALTH_RECORD_PAGE.STEPS}
          className="mb-5"
          onClick={() => {
            router.push(`/graph?tab=step&date=${date}`);
          }}
        >
          {step && (
            <div className="flex items-center">
              <RecordValue size="lg">{formatNumber(step)}</RecordValue>
              <RecordUnit size="lg">歩</RecordUnit>
            </div>
          )}
          <BarChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(stepThumbnailData, 'step', data.date || '')}
            key="step"
          />
          {!step && <DataEmpty />}
        </RecordWrapper>
        {step && (
          <RecordFlexWrapper
            onClick={() => {
              router.push(`/graph?tab=step&date=${date}`);
            }}
          >
            <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.STEPS_DISTANCE}>
              <RecordTextWrapper>
                <RecordValue>{data.distance}</RecordValue>
                <RecordUnit>{APP_TEXT.HEALTH_RECORD_PAGE.KILOMETERS}</RecordUnit>
              </RecordTextWrapper>
            </RecordWrapper>
            <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.STEPS_TIME}>
              <RecordTextWrapper>
                <RecordValue>
                  {data.exerciseTime ? Math.floor(data.exerciseTime / 60) : 0}
                </RecordValue>
                <RecordUnit>{APP_TEXT.HEALTH_RECORD_PAGE.HOURS}</RecordUnit>
                <RecordValue className="ml-1">
                  {data.exerciseTime ? data.exerciseTime % 60 : 0}
                </RecordValue>
                <RecordUnit>{APP_TEXT.HEALTH_RECORD_PAGE.MINUTES}</RecordUnit>
              </RecordTextWrapper>
            </RecordWrapper>
            <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.STEPS_CALORIES}>
              <RecordTextWrapper>
                <RecordValue>{formatNumber(data.energy)}</RecordValue>
                <RecordUnit>{APP_TEXT.HEALTH_RECORD_PAGE.KILOCALORIES}</RecordUnit>
              </RecordTextWrapper>
            </RecordWrapper>
          </RecordFlexWrapper>
        )}
        {/* <div className="flex justify-center mt-4">
          <TextButton
            size="sm"
            className="h-6"
            onClick={() => {
              router.push('/goal-calendar');
            }}
          >
            {APP_TEXT.HEALTH_RECORD_PAGE.TARGET_ACHIEVEMENT_CALENDAR}
          </TextButton>
        </div> */}
      </RecordCard>
    );
  },
);

const Weight = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & DataProps>(
  ({ className, data, thumbnail, ...props }, ref) => {
    const weight = data.weight;
    const bmi = data.bmi;
    const fatPercentage = data.fatPercentage;
    const weightThumbnailData = thumbnail?.weightThumnData;

    return (
      <RecordCard {...props} className={cn(className, 'pb-5')}>
        <RecordWrapper size="lg" title={APP_TEXT.HEALTH_RECORD_PAGE.WEIGHT} className="mb-5">
          {weight && (
            <RecordTextWrapper>
              <RecordValue size="lg">{weight}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.KG}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            yAxisDomain={[30, 150]}
            data={convertThumbnailData(weightThumbnailData, 'weight', data.date || '')}
          />
          {!weight && <DataEmpty />}
        </RecordWrapper>
        {weight && (
          <RecordFlexWrapper>
            {/* <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.BMI} className="flex-1">
              <RecordValue>{bmi}</RecordValue>
            </RecordWrapper> */}
            <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.BODY_FAT_RATE} className="flex-1">
              <RecordTextWrapper>
                <RecordValue>{fatPercentage}</RecordValue>
                <RecordUnit>{APP_TEXT.HEALTH_RECORD_PAGE.PERCENT}</RecordUnit>
              </RecordTextWrapper>
            </RecordWrapper>
          </RecordFlexWrapper>
        )}
      </RecordCard>
    );
  },
);

const SleepTime = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & DataProps
>(({ className, data, thumbnail, ...props }, ref) => {
  const sleepTime = data.sleepTime;
  const sleepTimeThumbnailData = thumbnail?.sleepTimeThumnData;
  return (
    <RecordCard {...props} className={cn(className, 'pb-5')} ref={ref}>
      <RecordWrapper size="lg" title={APP_TEXT.HEALTH_RECORD_PAGE.SLEEP_TIME}>
        {sleepTime && (
          <RecordTextWrapper>
            <RecordValue size="lg">{sleepTime ? Math.floor(sleepTime / 60) : 0}</RecordValue>
            <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.HOURS}</RecordUnit>
            <RecordValue size="lg" className="ml-1">
              {sleepTime ? sleepTime % 60 : 0}
            </RecordValue>
            <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MINUTES}</RecordUnit>
          </RecordTextWrapper>
        )}
        <BarChart
          className="absolute right-0 top-0"
          data={convertThumbnailData(sleepTimeThumbnailData, 'sleepTime', data.date || '')}
        />
        {!sleepTime && <DataEmpty />}
      </RecordWrapper>
    </RecordCard>
  );
});

const BloodPressure = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & DataProps
>(({ className, data, thumbnail, ...props }, ref) => {
  const lowBpAm = data.lowBpAm;
  const highBpAm = data.highBpAm;
  const lowBpPm = data.lowBpPm;
  const highBpPm = data.highBpPm;
  const bloodPressureThumbnailData = thumbnail?.bloodPressureThumnData;

  return (
    <RecordCard {...props} className={cn(className, 'pb-5')} ref={ref}>
      <RecordWrapper size="lg" title={APP_TEXT.HEALTH_RECORD_PAGE.BLOOD_PRESSURE}>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.MORNING} className="mt-2">
          {lowBpAm && (
            <RecordTextWrapper>
              <RecordValue size="lg">{highBpAm}</RecordValue>
              <RecordUnit size="lg">/</RecordUnit>
              <RecordValue size="lg" className="ml-1">
                {lowBpAm}
              </RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MMHG}</RecordUnit>
            </RecordTextWrapper>
          )}
          <RangeBarChart
            className="absolute right-0 top-0"
            data={convertRangeBarChartData(
              bloodPressureThumbnailData,
              'lowBpAm',
              'highBpAm',
              data.date || '',
            )}
          />
          {!lowBpAm && <DataEmpty />}
        </RecordWrapper>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.AFTERNOON} className="mt-5">
          {lowBpPm && (
            <RecordTextWrapper>
              <RecordValue size="lg">{highBpPm}</RecordValue>
              <RecordUnit size="lg">/</RecordUnit>
              <RecordValue size="lg" className="ml-1">
                {lowBpPm}
              </RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MMHG}</RecordUnit>
            </RecordTextWrapper>
          )}
          <RangeBarChart
            className="absolute right-0 top-0"
            data={convertRangeBarChartData(
              bloodPressureThumbnailData,
              'lowBpPm',
              'highBpPm',
              data.date || '',
            )}
          />
          {!lowBpPm && <DataEmpty />}
        </RecordWrapper>
      </RecordWrapper>
    </RecordCard>
  );
});

const BloodGlucose = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & DataProps
>(({ className, data, thumbnail, ...props }, ref) => {
  const frequency = data.bgMeasurementFrequency;
  if (frequency === 1) {
    return (
      <BloodGlucoseSingle
        {...props}
        className={className}
        data={data}
        thumbnail={thumbnail}
        ref={ref}
      />
    );
  }
  if (frequency === 2) {
    return (
      <BloodGlucoseMulti
        {...props}
        className={className}
        data={data}
        thumbnail={thumbnail}
        ref={ref}
      />
    );
  }
  return (
    <RecordCard {...props} className={cn(className, 'pb-5')} ref={ref}>
      <RecordWrapper size="lg" title={APP_TEXT.HEALTH_RECORD_PAGE.BLOOD_GLUCOSE}>
        <DataEmpty />
      </RecordWrapper>
    </RecordCard>
  );
});

const BloodGlucoseYAxisDomain = [30, 500] as [number, number];

const BloodGlucoseMulti = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & DataProps
>(({ className, data, thumbnail, ...props }, ref) => {
  const afterBfBg = data.afterBfBg;
  const beforeBfBg = data.beforeBfBg;
  const afterLnBg = data.afterLnBg;
  const beforeLnBg = data.beforeLnBg;
  const afterDnBg = data.afterDnBg;
  const beforeDnBg = data.beforeDnBg;
  const beforeSlBg = data.beforeSlBg;
  const bloodGlucoseThumbnailData =
    thumbnail?.bloodGlucoseThumnData as MultiBloodGlucoseThumbnailData[];
  return (
    <RecordCard {...props} className={cn(className, 'pb-5')} ref={ref}>
      <RecordWrapper size="lg" title={APP_TEXT.HEALTH_RECORD_PAGE.BLOOD_GLUCOSE}>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.BREAKFAST_BEFORE} className="mt-2">
          {beforeBfBg && (
            <RecordTextWrapper>
              <RecordValue size="lg">{beforeBfBg}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(bloodGlucoseThumbnailData, 'beforeBfBg', data.date || '')}
            yAxisDomain={BloodGlucoseYAxisDomain}
          />
          {!beforeBfBg && <DataEmpty />}
        </RecordWrapper>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.BREAKFAST_AFTER} className="mt-5">
          {afterBfBg && (
            <RecordTextWrapper>
              <RecordValue size="lg">{afterBfBg}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(bloodGlucoseThumbnailData, 'afterBfBg', data.date || '')}
            yAxisDomain={BloodGlucoseYAxisDomain}
          />
          {!afterBfBg && <DataEmpty />}
        </RecordWrapper>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.LUNCH_BEFORE} className="mt-5">
          {beforeLnBg && (
            <RecordTextWrapper>
              <RecordValue size="lg">{beforeLnBg}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(bloodGlucoseThumbnailData, 'beforeLnBg', data.date || '')}
            yAxisDomain={BloodGlucoseYAxisDomain}
          />
          {!beforeLnBg && <DataEmpty />}
        </RecordWrapper>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.LUNCH_AFTER} className="mt-5">
          {afterLnBg && (
            <RecordTextWrapper>
              <RecordValue size="lg">{afterLnBg}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(bloodGlucoseThumbnailData, 'afterLnBg', data.date || '')}
            yAxisDomain={BloodGlucoseYAxisDomain}
          />
          {!afterLnBg && <DataEmpty />}
        </RecordWrapper>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.DINNER_BEFORE} className="mt-5">
          {beforeDnBg && (
            <RecordTextWrapper>
              <RecordValue size="lg">{beforeDnBg}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(bloodGlucoseThumbnailData, 'beforeDnBg', data.date || '')}
            yAxisDomain={BloodGlucoseYAxisDomain}
          />
          {!beforeDnBg && <DataEmpty />}
        </RecordWrapper>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.DINNER_AFTER} className="mt-5">
          {afterDnBg && (
            <RecordTextWrapper>
              <RecordValue size="lg">{afterDnBg}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(bloodGlucoseThumbnailData, 'afterDnBg', data.date || '')}
            yAxisDomain={BloodGlucoseYAxisDomain}
          />
          {!afterDnBg && <DataEmpty />}
        </RecordWrapper>
        <RecordWrapper title={APP_TEXT.HEALTH_RECORD_PAGE.SLEEP_BEFORE} className="mt-5">
          {beforeSlBg && (
            <RecordTextWrapper>
              <RecordValue size="lg">{beforeSlBg}</RecordValue>
              <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
            </RecordTextWrapper>
          )}
          <LineChart
            className="absolute right-0 top-0"
            data={convertThumbnailData(bloodGlucoseThumbnailData, 'beforeSlBg', data.date || '')}
            yAxisDomain={BloodGlucoseYAxisDomain}
          />
          {!beforeSlBg && <DataEmpty />}
        </RecordWrapper>
      </RecordWrapper>
    </RecordCard>
  );
});

const BloodGlucoseSingle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & DataProps
>(({ className, data, thumbnail, ...props }, ref) => {
  const onceDailyBg = data.onceDailyBg;
  const bloodGlucoseThumbnailData =
    thumbnail?.bloodGlucoseThumnData as SingleBloodGlucoseThumbnailData[];
  return (
    <RecordCard {...props} className={cn(className, 'pb-5')} ref={ref}>
      <RecordWrapper size="lg" title={APP_TEXT.HEALTH_RECORD_PAGE.BLOOD_GLUCOSE}>
        {onceDailyBg && (
          <RecordTextWrapper>
            <RecordValue size="lg">{onceDailyBg}</RecordValue>
            <RecordUnit size="lg">{APP_TEXT.HEALTH_RECORD_PAGE.MGDL}</RecordUnit>
          </RecordTextWrapper>
        )}
        <LineChart
          className="absolute right-0 top-0"
          data={convertThumbnailData(bloodGlucoseThumbnailData, 'onceDailyBg', data.date || '')}
        />
        {!onceDailyBg && <DataEmpty />}
      </RecordWrapper>
    </RecordCard>
  );
});

function RecordTitle({
  children,
  size,
  className,
}: { children: React.ReactNode; size?: 'lg' | 'sm'; className?: string }) {
  if (size === 'lg') {
    return <h3 className={cn('text-base', className)}>{children}</h3>;
  }
  return <div className={cn('text-sm', className)}>{children}</div>;
}

const RecordCard = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    return (
      <div className={cn('bg-card rounded-2xl p-4 mx-6 mb-2', className)} ref={ref} {...props}>
        {children}
      </div>
    );
  },
);

function RecordValue({
  children,
  size,
  className,
}: { children: React.ReactNode; size?: 'lg' | 'sm'; className?: string }) {
  return (
    <span className={cn('font-bold ', size === 'lg' ? 'text-3xl' : 'text-xl', className)}>
      {children}
    </span>
  );
}

function RecordUnit({ children, size }: { children: React.ReactNode; size?: 'lg' | 'sm' }) {
  return <span className={cn('ml-1', size === 'lg' ? 'text-base' : 'text-sm')}>{children}</span>;
}

function RecordWrapper({
  children,
  size,
  title,
  className,
  onClick,
}: {
  children: React.ReactNode;
  size?: 'lg' | 'sm';
  title: string;
  className?: string;
  onClick?: () => void;
}) {
  return (
    // biome-ignore lint/a11y/useKeyWithClickEvents: just for app webview
    <div className={cn('relative', className)} onClick={onClick}>
      <RecordTitle size={size} className="mb-[2px]">
        {title}
      </RecordTitle>
      {children}
    </div>
  );
}

function RecordFlexWrapper({
  children,
  className,
  onClick,
}: { children: React.ReactNode; className?: string; onClick?: () => void }) {
  return (
    // biome-ignore lint/a11y/useKeyWithClickEvents: just for app webview
    <div onClick={onClick} className={cn('flex items-center justify-between', className)}>
      {children}
    </div>
  );
}

function RecordTextWrapper({ children }: { children: React.ReactNode }) {
  return <div className="flex items-center">{children}</div>;
}

export default { BloodGlucose, BloodPressure, SleepTime, Step, Weight };
