'use client';

import { healthR<PERSON>ordAPI } from '@/api/modules/health-record';
import { Button } from '@/components/shared/button';
import { Form } from '@/components/ui/form';
import { useLoading } from '@/hooks/use-loading';
import { useAuthStore } from '@/store/auth';
import {
  type HealthRecord,
  HealthRecordSource,
  type VitalDataRequest,
  type VitalDataResponse,
  type VitalFrom,
} from '@/types/health-record';
import { formatDate } from '@/utils/date-format';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import type { FieldErrors } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { PointDialogComponent, VITAL_DATA_POINT_LIST } from '../../home/<USER>/point-dialog';
import { DATA_FROM_TYPE } from '../_const';
import { BloodGlucoseField } from './_components/blood-glucose-field';
import { BloodPressureField } from './_components/blood-pressure-field';
import { DateField } from './_components/date-field';
import { SleepField } from './_components/sleep-field';
import { WeightField } from './_components/weight-field';
import { type RecordSchema, recordSchema } from './_form/form-schema';

const defaultValues: Partial<RecordSchema> = {
  vitalFromData: {
    bodyFrom: HealthRecordSource.MANUAL,
    sleepFrom: HealthRecordSource.MANUAL,
    bloodPressureFrom: HealthRecordSource.MANUAL,
    bloodGlucoseFrom: HealthRecordSource.MANUAL,
  },
  recordDate: formatDate(new Date(), 'yyyy-MM-dd'),
  weight: undefined,
  fatRate: undefined,
  sleepTime: undefined,
  wakeTime: undefined,
  amTime: undefined,
  amMaxBP: undefined,
  amMinBP: undefined,
  pmTime: undefined,
  pmMaxBP: undefined,
  pmMinBP: undefined,
  bloodGlucoseMeasurementFrequency: 1,
  bloodGlucoseOnceDaily: undefined,
  bloodGlucoseBeforeBf: undefined,
  bloodGlucoseAfterBf: undefined,
  bloodGlucoseBeforeLn: undefined,
  bloodGlucoseAfterLn: undefined,
  bloodGlucoseBeforeDn: undefined,
  bloodGlucoseAfterDn: undefined,
  bloodGlucoseBeforeSl: undefined,
};

// trans 12:00 to 12:00:00
function formatTime(time: string | undefined) {
  if (!time) {
    return '';
  }
  const [h, m] = time.split(':');
  return `${h}:${m}:00`;
}

interface HealthRecordPageProps {
  date?: string;
  onFormSubmit?: (dataUpdateState: boolean) => void;
}

export default function HealthRecordCreatePage({ date, onFormSubmit }: HealthRecordPageProps) {
  const {
    pointDialogTitle,
    setPointDialogTitle,
    pointList,
    scoreList,
    setPointList,
    setScoreList,
  } = useAuthStore();
  const { isLoading, setIsLoading } = useLoading();
  const [formIsEmpty, setFormIsEmpty] = useState(false);
  const [originalVitalData, setOriginalVitalData] = useState<HealthRecord>({
    bodyFrom: HealthRecordSource.MANUAL,
    sleepFrom: HealthRecordSource.MANUAL,
    bloodPressureFrom: HealthRecordSource.MANUAL,
    bloodGlucoseFrom: HealthRecordSource.MANUAL,
  });
  const [vitalFromData, setVitalFromData] = useState<VitalFrom>({
    bodyFrom: HealthRecordSource.MANUAL,
    sleepFrom: HealthRecordSource.MANUAL,
    bloodPressureFrom: HealthRecordSource.MANUAL,
    bloodGlucoseFrom: HealthRecordSource.MANUAL,
  });
  const [showPointDialog, setShowPointDialog] = useState(false);

  const form = useForm<RecordSchema>({
    resolver: zodResolver(recordSchema),
    defaultValues: {
      ...defaultValues,
      recordDate: date || formatDate(new Date(), 'yyyy-MM-dd'),
    },
    // mode: 'onChange',
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
  });

  const recordDate = form.watch('recordDate');
  useEffect(() => {
    if (recordDate) {
      healthRecordAPI
        .dailyVitalData({
          measureDate: formatDate(new Date(form.getValues('recordDate')), 'yyyyMMdd'),
        })
        .then((res) => {
          if (res) {
            setOriginalVitalData(res.vitalData);

            const vitalFromData = {
              bodyFrom: res.vitalData.bodyFrom
                ? String(res.vitalData.bodyFrom)
                : HealthRecordSource.MANUAL,
              sleepFrom: res.vitalData.sleepFrom
                ? String(res.vitalData.sleepFrom)
                : HealthRecordSource.MANUAL,
              bloodPressureFrom: res.vitalData.bloodPressureFrom
                ? String(res.vitalData.bloodPressureFrom)
                : HealthRecordSource.MANUAL,
              bloodGlucoseFrom: res.vitalData.bloodGlucoseFrom
                ? String(res.vitalData.bloodGlucoseFrom)
                : HealthRecordSource.MANUAL,
            };
            if (!res.vitalData.weight && !res.vitalData.fatPercentage) {
              vitalFromData.bodyFrom = HealthRecordSource.MANUAL;
            }
            if (!res.vitalData.sleepAtDate && !res.vitalData.wakeAtDate) {
              vitalFromData.sleepFrom = HealthRecordSource.MANUAL;
            }
            if (!res.vitalData.bpAmMeasureAt && !res.vitalData.bpPmMeasureAt) {
              vitalFromData.bloodPressureFrom = HealthRecordSource.MANUAL;
            }
            setVitalFromData(vitalFromData as VitalFrom);
            form.setValue('vitalFromData', vitalFromData);
            form.setValue('weight', res.vitalData.weight?.toString() || '');
            form.setValue('fatRate', res.vitalData.fatPercentage?.toString() || '');
            if (res.vitalData.sleepAtDate && res.vitalData.sleepAtTime) {
              form.setValue(
                'sleepTime',
                `${res.vitalData.sleepAtDate} ${res.vitalData.sleepAtTime}`,
              );
            } else {
              form.setValue('sleepTime', '');
            }
            if (res.vitalData.wakeAtDate && res.vitalData.wakeAtTime) {
              form.setValue('wakeTime', `${res.vitalData.wakeAtDate} ${res.vitalData.wakeAtTime}`);
            } else {
              form.setValue('wakeTime', '');
            }
            form.setValue('amTime', res.vitalData.bpAmMeasureAt);
            form.setValue('amMaxBP', res.vitalData.highBpAm);
            form.setValue('amMinBP', res.vitalData.lowBpAm);
            form.setValue('pmTime', res.vitalData.bpPmMeasureAt);
            form.setValue('pmMaxBP', res.vitalData.highBpPm);
            form.setValue('pmMinBP', res.vitalData.lowBpPm);
            form.setValue(
              'bloodGlucoseMeasurementFrequency',
              res.vitalData.bgMeasurementFrequency || 1,
            );
            form.setValue('bloodGlucoseOnceDaily', res.vitalData.onceDailyBg?.toString() || '');
            form.setValue('bloodGlucoseBeforeBf', res.vitalData.beforeBfBg?.toString() || '');
            form.setValue('bloodGlucoseAfterBf', res.vitalData.afterBfBg?.toString() || '');
            form.setValue('bloodGlucoseBeforeLn', res.vitalData.beforeLnBg?.toString() || '');
            form.setValue('bloodGlucoseAfterLn', res.vitalData.afterLnBg?.toString() || '');
            form.setValue('bloodGlucoseBeforeDn', res.vitalData.beforeDnBg?.toString() || '');
            form.setValue('bloodGlucoseAfterDn', res.vitalData.afterDnBg?.toString() || '');
            form.setValue('bloodGlucoseBeforeSl', res.vitalData.beforeSlBg?.toString() || '');
          }
        });
      form.clearErrors();
    }
  }, [recordDate, form]);

  useEffect(() => {
    const subscription = form.watch(() => {
      if (
        form.getValues('weight') ||
        form.getValues('fatRate') ||
        form.getValues('sleepTime') ||
        form.getValues('wakeTime') ||
        form.getValues('amTime') ||
        form.getValues('pmTime') ||
        form.getValues('amMaxBP') ||
        form.getValues('amMinBP') ||
        form.getValues('pmMaxBP') ||
        form.getValues('pmMinBP') ||
        form.getValues('bloodGlucoseOnceDaily') ||
        form.getValues('bloodGlucoseBeforeBf') ||
        form.getValues('bloodGlucoseAfterBf') ||
        form.getValues('bloodGlucoseBeforeLn') ||
        form.getValues('bloodGlucoseAfterLn') ||
        form.getValues('bloodGlucoseBeforeDn') ||
        form.getValues('bloodGlucoseAfterDn') ||
        form.getValues('bloodGlucoseBeforeSl')
      ) {
        setFormIsEmpty(false);
      } else {
        setFormIsEmpty(true);
      }
    });

    return () => subscription.unsubscribe();
  }, [form.watch, form.getValues]);

  function postVitalData(values: RecordSchema) {
    const vitalData: VitalDataRequest = {
      measureDate: formatDate(new Date(values.recordDate), 'yyyyMMdd'),
    };
    if (vitalFromData.bodyFrom === HealthRecordSource.MANUAL) {
      vitalData.weightData = {
        weight: values.weight?.toString() || '',
        fatPercentage: values.fatRate?.toString() || '',
        bodyFrom: DATA_FROM_TYPE.MANUAL,
      };
    } else {
      vitalData.weightData = {
        weight: originalVitalData.weight?.toString() || '',
        fatPercentage: originalVitalData.fatPercentage?.toString() || '',
        bodyFrom: Number(originalVitalData.bodyFrom),
      };
    }
    if (vitalFromData.sleepFrom === HealthRecordSource.MANUAL) {
      vitalData.sleepData = {
        sleepFrom: DATA_FROM_TYPE.MANUAL,
      };
      if (values.sleepTime) {
        vitalData.sleepData.sleepAt = formatDate(new Date(values.sleepTime), 'yyyy-MM-dd HH:mm:ss');
      }
      if (values.wakeTime) {
        vitalData.sleepData.wakeAt = formatDate(new Date(values.wakeTime), 'yyyy-MM-dd HH:mm:ss');
      }
    } else {
      vitalData.sleepData = {
        sleepFrom: Number(originalVitalData.sleepFrom),
        sleepAt: originalVitalData.sleepAtDate
          ? `${originalVitalData.sleepAtDate} ${originalVitalData.sleepAtTime || '00:00:00'}`
          : undefined,
        wakeAt: originalVitalData.wakeAtDate
          ? `${originalVitalData.wakeAtDate} ${originalVitalData.wakeAtTime || '00:00:00'}`
          : undefined,
      };
    }
    if (vitalFromData.bloodPressureFrom === HealthRecordSource.MANUAL) {
      vitalData.bloodPressureData = {
        bloodPressureFrom: DATA_FROM_TYPE.MANUAL,
      };
      if (values.amTime) {
        vitalData.bloodPressureData.bpAmMeasureAt = formatTime(values.amTime);
      }
      if (values.amMaxBP && values.amMinBP) {
        vitalData.bloodPressureData.highBpAm = values.amMaxBP;
        vitalData.bloodPressureData.lowBpAm = values.amMinBP;
      }
      if (values.pmTime) {
        vitalData.bloodPressureData.bpPmMeasureAt = formatTime(values.pmTime);
      }
      if (values.pmMaxBP && values.pmMinBP) {
        vitalData.bloodPressureData.highBpPm = values.pmMaxBP;
        vitalData.bloodPressureData.lowBpPm = values.pmMinBP;
      }
    } else {
      vitalData.bloodPressureData = {
        bloodPressureFrom: Number(originalVitalData.bloodPressureFrom),
        bpAmMeasureAt: originalVitalData.bpAmMeasureAt || undefined,
        highBpAm: originalVitalData.highBpAm || undefined,
        lowBpAm: originalVitalData.lowBpAm || undefined,
        bpPmMeasureAt: originalVitalData.bpPmMeasureAt || undefined,
        highBpPm: originalVitalData.highBpPm || undefined,
        lowBpPm: originalVitalData.lowBpPm || undefined,
      };
    }
    if (vitalFromData.bloodGlucoseFrom === HealthRecordSource.MANUAL) {
      if (values.bloodGlucoseMeasurementFrequency === 1) {
        vitalData.bloodGlucoseData = {
          bgMeasurementFrequency: 1,
          onceDailyBg: values.bloodGlucoseOnceDaily
            ? Number(values.bloodGlucoseOnceDaily)
            : undefined,
          bloodGlucoseFrom: DATA_FROM_TYPE.MANUAL,
        };
      } else {
        vitalData.bloodGlucoseData = {
          bgMeasurementFrequency: 2,
          beforeBfBg: values.bloodGlucoseBeforeBf ? Number(values.bloodGlucoseBeforeBf) : undefined,
          afterBfBg: values.bloodGlucoseAfterBf ? Number(values.bloodGlucoseAfterBf) : undefined,
          beforeLnBg: values.bloodGlucoseBeforeLn ? Number(values.bloodGlucoseBeforeLn) : undefined,
          afterLnBg: values.bloodGlucoseAfterLn ? Number(values.bloodGlucoseAfterLn) : undefined,
          beforeDnBg: values.bloodGlucoseBeforeDn ? Number(values.bloodGlucoseBeforeDn) : undefined,
          afterDnBg: values.bloodGlucoseAfterDn ? Number(values.bloodGlucoseAfterDn) : undefined,
          beforeSlBg: values.bloodGlucoseBeforeSl ? Number(values.bloodGlucoseBeforeSl) : undefined,
          bloodGlucoseFrom: DATA_FROM_TYPE.MANUAL,
        };
      }
    } else {
      vitalData.bloodGlucoseData = {
        bgMeasurementFrequency: originalVitalData.bgMeasurementFrequency || 1,
        onceDailyBg: originalVitalData.onceDailyBg || undefined,
        beforeBfBg: originalVitalData.beforeBfBg || undefined,
        afterBfBg: originalVitalData.afterBfBg || undefined,
        beforeLnBg: originalVitalData.beforeLnBg || undefined,
        afterLnBg: originalVitalData.afterLnBg || undefined,
        beforeDnBg: originalVitalData.beforeDnBg || undefined,
        afterDnBg: originalVitalData.afterDnBg || undefined,
        beforeSlBg: originalVitalData.beforeSlBg || undefined,
        bloodGlucoseFrom: Number(originalVitalData.bloodGlucoseFrom),
      };
    }
    // setIsLoading(true);
    healthRecordAPI
      .vitalData(vitalData)
      .then((res) => {
        if (res) {
          // onFormSubmit?.(true);
          console.log('vitalData====', res);
          if (res?.popupInfo?.isPopupShow) {
            setOutsideList(res);
            setShowPointDialog(true);
          } else {
            onFormSubmit?.(true);
            setShowPointDialog(false);
          }
        }
        // setIsLoading(false);
      })
      .catch(() => {
        onFormSubmit?.(true);
        setShowPointDialog(false);
        setIsLoading(false);
      });
  }

  const setOutsideList = (res: VitalDataResponse) => {
    // defaultValue
    const result = res?.popupInfo?.pointInfo?.details ?? [];
    if (result?.length > 0) {
      const list = result
        ?.map((item) => ({
          organizerName: item.organizerName,
          point: item.organizerTotalPoint,
        }))
        .filter((item) => item.point);

      setPointList(list);

      // Merge and remove duplicate data
      const mergedPointDetail = result?.reduce((acc, curr) => {
        return Object.assign(acc, curr.pointDetail);
      }, {});

      const mergedArray = Object.entries(mergedPointDetail)?.map(([key, value]) => ({
        key: VITAL_DATA_POINT_LIST[key ?? ''],
        // key,
        value,
      }));
      setScoreList(mergedArray);
      setPointDialogTitle('データを登録！');
    }
  };

  function onSubmit(values: RecordSchema) {
    postVitalData(values);
  }

  // エラーフィールドにスクロールする関数
  const scrollToError = (errors: FieldErrors<RecordSchema>) => {
    const firstErrorField = Object.keys(errors)[0];
    const errorElement = document.querySelector(`[name="${firstErrorField}"]`);
    if (errorElement) {
      errorElement.parentElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const handleCloseCreateDialog = () => {
    onFormSubmit?.(true);
    setShowPointDialog(false);
  };

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit, (errors) => {
            scrollToError(errors);
          })}
          className="bg-card px-6 pb-4 pt-3"
        >
          <DateField />
          <WeightField vitalFromData={vitalFromData} />
          <SleepField vitalFromData={vitalFromData} form={form} />
          <BloodPressureField vitalFromData={vitalFromData} />
          <BloodGlucoseField vitalFromData={vitalFromData} form={form} />
          <div className=" flex justify-center">
            <Button type="submit" className="w-full" disabled={isLoading || formIsEmpty}>
              保存
            </Button>
          </div>
        </form>
      </Form>
      <PointDialogComponent
        title={pointDialogTitle}
        pointList={pointList}
        scoreList={scoreList}
        open={showPointDialog}
        onOpenChange={handleCloseCreateDialog}
      />
    </>
  );
}
