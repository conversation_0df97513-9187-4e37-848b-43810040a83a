import { HealthRecordSource } from '@/types/health-record';
import { z } from 'zod';

const bloodGlucoseSchema = z
  .string()
  .refine(
    (val) => {
      if (!val) return true;
      const numVal = Number(val) || 0;
      return numVal >= 30 && numVal <= 500;
    },
    { message: '血糖値は30mg/dLから500mg/dLの範囲で設定してください。' },
  )
  .optional();

export const recordSchema = z
  .object({
    vitalFromData: z.object({
      bodyFrom: z.string(),
      sleepFrom: z.string(),
      bloodPressureFrom: z.string(),
      bloodGlucoseFrom: z.string(),
    }),
    recordDate: z.string().min(1, { message: '日付を入力してください。' }),

    weight: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const numVal = Number(val) || 0;
          return numVal >= 20 && numVal <= 300;
        },
        { message: '体重は20kgから300kgの範囲で設定してください。' },
      )
      .optional(),

    fatRate: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const numVal = Number(val) || 0;
          return numVal >= 5 && numVal <= 60;
        },
        { message: '体脂肪率は5%から60%の範囲で設定してください。' },
      )
      .optional(),

    amTime: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
          if (!timeRegex.test(val)) {
            return false;
          }
          const [hours] = val.split(':').map(Number);
          return hours >= 0 && hours <= 11;
        },
        {
          message: '午前の血圧計測時間は00:00～11:59の範囲で設定してください。',
        },
      )
      .optional(),

    amMaxBP: z.coerce
      .number()
      .refine(
        (val) => {
          if (!val) return true;
          return val >= 40 && val <= 280;
        },
        { message: '午前最高血圧は40mmHgから280mmHgの範囲で設定してください。' },
      )
      .optional(),

    amMinBP: z.coerce
      .number()
      .refine(
        (val) => {
          if (!val) return true;
          return val >= 40 && val <= 255;
        },
        { message: '午前最低血圧は40mmHgから255mmHgの範囲で設定してください。' },
      )
      .optional(),

    pmTime: z
      .string()
      .refine(
        (val) => {
          if (!val) return true;
          const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
          if (!timeRegex.test(val)) {
            return false;
          }
          const [hours] = val.split(':').map(Number);
          return hours >= 12 && hours <= 23;
        },
        {
          message: '午後の血圧計測時間は12:00～23:59の範囲で設定してください。',
        },
      )
      .optional(),

    pmMaxBP: z.coerce
      .number()
      .refine(
        (val) => {
          if (!val) return true;
          return val >= 40 && val <= 280;
        },
        { message: '午後最高血圧は40mmHgから280mmHgの範囲で設定してください。' },
      )
      .optional(),

    pmMinBP: z.coerce
      .number()
      .refine(
        (val) => {
          if (!val) return true;
          return val >= 40 && val <= 255;
        },
        { message: '午後最低血圧は40mmHgから255mmHgの範囲で設定してください。' },
      )
      .optional(),

    sleepTime: z.string().refine(
      (val) => {
        if (!val) return true;
        return getDateByString(val) <= new Date();
      },
      {
        message: '未来の日付は設定できません。',
      },
    ),

    wakeTime: z.string().refine(
      (val) => {
        if (!val) return true;
        return getDateByString(val) <= new Date();
      },
      {
        message: '未来の日付は設定できません。',
      },
    ),
    bloodGlucoseMeasurementFrequency: z.number().optional(),
    bloodGlucoseOnceDaily: bloodGlucoseSchema,
    bloodGlucoseBeforeBf: bloodGlucoseSchema,
    bloodGlucoseAfterBf: bloodGlucoseSchema,
    bloodGlucoseBeforeLn: bloodGlucoseSchema,
    bloodGlucoseAfterLn: bloodGlucoseSchema,
    bloodGlucoseBeforeDn: bloodGlucoseSchema,
    bloodGlucoseAfterDn: bloodGlucoseSchema,
    bloodGlucoseBeforeSl: bloodGlucoseSchema,
  })
  .superRefine((data, ctx) => {
    const sleep = getDateByString(data.sleepTime);
    const wake = getDateByString(data.wakeTime);
    const vitalFromData = data.vitalFromData;

    if (
      vitalFromData.sleepFrom === HealthRecordSource.MANUAL &&
      data.sleepTime &&
      data.wakeTime &&
      wake <= sleep
    ) {
      ctx.addIssue({
        path: ['wakeTime'],
        message: '「起床時間」を「就寝時間」以後の日時に設定してください。',
        code: z.ZodIssueCode.custom,
      });
      ctx.addIssue({
        path: ['sleepTime'],
        message: '「就寝時間」を「起床時間」以前の日時に設定してください。',
        code: z.ZodIssueCode.custom,
      });
    }

    // 午前
    if (
      vitalFromData.bloodPressureFrom === HealthRecordSource.MANUAL &&
      data.amMinBP &&
      data.amMaxBP
    ) {
      const minBP = data.amMinBP as number;
      const maxBP = data.amMaxBP as number;

      if (minBP > maxBP) {
        ctx.addIssue({
          path: ['amMinBP'],
          message: '「午前最低血圧」は「午前最高血圧」以下に設定してください。',
          code: z.ZodIssueCode.custom,
        });
        ctx.addIssue({
          path: ['amMaxBP'],
          message: '「午前最低血圧」は「午前最高血圧」以下に設定してください。',
          code: z.ZodIssueCode.custom,
        });
      }
    }

    if (
      vitalFromData.bloodPressureFrom === HealthRecordSource.MANUAL &&
      !data.amMaxBP &&
      data.amMinBP
    ) {
      ctx.addIssue({
        path: ['amMaxBP'],
        message: '午前最高血圧を入力してください。',
        code: z.ZodIssueCode.custom,
      });
    }

    if (
      vitalFromData.bloodPressureFrom === HealthRecordSource.MANUAL &&
      !data.amMinBP &&
      data.amMaxBP
    ) {
      ctx.addIssue({
        path: ['amMinBP'],
        message: '午前最低血圧を入力してください。',
        code: z.ZodIssueCode.custom,
      });
    }

    // 午後
    if (
      vitalFromData.bloodPressureFrom === HealthRecordSource.MANUAL &&
      data.pmMinBP &&
      data.pmMaxBP
    ) {
      const minBP = data.pmMinBP as number;
      const maxBP = data.pmMaxBP as number;

      if (minBP > maxBP) {
        ctx.addIssue({
          path: ['pmMinBP'],
          message: '「午後最低血圧」は「午後最高血圧」以下に設定してください。',
          code: z.ZodIssueCode.custom,
        });
        ctx.addIssue({
          path: ['pmMaxBP'],
          message: '「午後最低血圧」は「午後最高血圧」以下に設定してください。',
          code: z.ZodIssueCode.custom,
        });
      }
    }

    if (
      vitalFromData.bloodPressureFrom === HealthRecordSource.MANUAL &&
      !data.pmMaxBP &&
      data.pmMinBP
    ) {
      ctx.addIssue({
        path: ['pmMaxBP'],
        message: '午後最高血圧を入力してください。',
        code: z.ZodIssueCode.custom,
      });
    }

    if (
      vitalFromData.bloodPressureFrom === HealthRecordSource.MANUAL &&
      !data.pmMinBP &&
      data.pmMaxBP
    ) {
      ctx.addIssue({
        path: ['pmMinBP'],
        message: '午後最低血圧を入力してください。',
        code: z.ZodIssueCode.custom,
      });
    }

    const recordDate = getDateByString(data.recordDate);
    const sleepTime = getDateByString(data.sleepTime);
    const wakeTime = getDateByString(data.wakeTime);

    // 获取日期的年月日部分进行比较
    const getDateOnly = (date: Date) => {
      return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    };

    const recordDateOnly = getDateOnly(recordDate);
    const sleepTimeOnly = getDateOnly(sleepTime);
    const wakeTimeOnly = getDateOnly(wakeTime);
    const previousDay = new Date(recordDateOnly);
    previousDay.setDate(previousDay.getDate() - 1);

    //就寝時間は前日と当日のみ
    if (
      vitalFromData.sleepFrom === HealthRecordSource.MANUAL &&
      data.sleepTime &&
      sleepTimeOnly.getTime() !== previousDay.getTime() &&
      sleepTimeOnly.getTime() !== recordDateOnly.getTime()
    ) {
      ctx.addIssue({
        path: ['sleepTime'],
        message: '就寝時間は前日と当日のみ設定できます。',
        code: z.ZodIssueCode.custom,
      });
    }

    //起床時間は当日のみ
    if (
      vitalFromData.sleepFrom === HealthRecordSource.MANUAL &&
      data.wakeTime &&
      wakeTimeOnly.getTime() !== recordDateOnly.getTime()
    ) {
      ctx.addIssue({
        path: ['wakeTime'],
        message: '起床時間は当日のみ設定できます。',
        code: z.ZodIssueCode.custom,
      });
    }
  });

export type RecordSchema = z.infer<typeof recordSchema>;

//fix ios15 date bug
function getDateByString(str: string) {
  if (!str) return new Date();
  const newStr = str.replace(/\-/g, '/');
  return new Date(newStr);
}
