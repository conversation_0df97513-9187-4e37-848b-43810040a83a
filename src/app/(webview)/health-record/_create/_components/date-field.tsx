import { DatePicker } from '@/components/shared/datetime-picker';
import { FormError } from '@/components/shared/form-error';
import Link from '@/components/shared/router-link';
import { FormControl, FormField } from '@/components/ui/form';
import { useRouter } from '@/hooks/use-next-navigation';
import { formatDate } from '@/utils/date-format';
import { InputFieldLabel } from '../../_components/input-field-label';

export function DateField() {
  const router = useRouter();
  return (
    <FormField
      name="recordDate"
      render={({ field }) => (
        // 日付
        <>
          <InputFieldLabel>日付</InputFieldLabel>
          <div className="flex items-center mt-1">
            <FormControl>
              <DatePicker
                max={formatDate(new Date(), 'yyyy-MM-dd')}
                className="flex-1"
                {...field}
              />
            </FormControl>
            <Link
              prefetch={true}
              href="/data-connect"
              onClick={() => {
                router.push('/data-connect', { onceMetadata: { isFormHealthRecord: '1' } });
              }}
              className="flex font-bold justify-content ml-6 mr-[20px] text-primary no-underline whitespace-nowrap"
            >
              連携設定
            </Link>
          </div>
          <FormError className="mt-2" />
        </>
      )}
    />
  );
}
