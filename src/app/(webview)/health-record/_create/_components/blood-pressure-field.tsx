import type { VitalFrom } from '@/types/health-record';
import { HealthRecordSource } from '@/types/health-record';
import { BloodPressure } from './blood-pressure';
import { CollapsibleSection } from './collapsible-section';

export function BloodPressureField({
  vitalFromData,
}: {
  vitalFromData: VitalFrom | undefined;
}) {
  return (
    <CollapsibleSection
      title="血圧"
      healthRecordSource={vitalFromData?.bloodPressureFrom || HealthRecordSource.MANUAL}
    >
      <BloodPressure
        title="午前の血圧"
        timeName="amTime"
        maxName="amMaxBP"
        minName="amMinBP"
        timeTip="0:00～11:59の間で記入してください"
        disabled={vitalFromData?.bloodPressureFrom !== HealthRecordSource.MANUAL}
      />
      <BloodPressure
        title="午後の血圧"
        timeName="pmTime"
        maxName="pmMaxBP"
        minName="pmMinBP"
        timeTip="12:00～23:59の間で記入してください"
        disabled={vitalFromData?.bloodPressureFrom !== HealthRecordSource.MANUAL}
      />
    </CollapsibleSection>
  );
}
