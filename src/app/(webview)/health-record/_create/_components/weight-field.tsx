import { FormError } from '@/components/shared/form-error';
import { FormControl, FormField } from '@/components/ui/form';
import { HealthRecordSource, type VitalFrom } from '@/types/health-record';
import { LabeledNumericInput } from '../../_components/labeled-numeric-input';
import { CollapsibleSection } from './collapsible-section';

export function WeightField({
  vitalFromData,
}: {
  vitalFromData: VitalFrom | undefined;
}) {
  return (
    <CollapsibleSection
      title="体重・体脂肪率"
      healthRecordSource={vitalFromData?.bodyFrom || HealthRecordSource.MANUAL}
    >
      <div className="flex">
        <FormField
          name="weight"
          render={({ field }) => (
            <div className="flex-1">
              <FormControl>
                <LabeledNumericInput
                  className="w-full"
                  label="体重"
                  unit="kg"
                  maxDecLen={1}
                  maxIntLen={3}
                  {...field}
                  disabled={vitalFromData?.bodyFrom !== HealthRecordSource.MANUAL}
                />
              </FormControl>
              <FormError className="mt-2" />
            </div>
          )}
        />
        <FormField
          name="fatRate"
          render={({ field }) => (
            <div className="flex-1 ml-4">
              <FormControl>
                <LabeledNumericInput
                  className="w-full"
                  label="体脂肪率"
                  unit="%"
                  maxDecLen={1}
                  maxIntLen={2}
                  {...field}
                  disabled={vitalFromData?.bodyFrom !== HealthRecordSource.MANUAL}
                />
              </FormControl>
              <FormError className="mt-2" />
            </div>
          )}
        />
      </div>
    </CollapsibleSection>
  );
}
