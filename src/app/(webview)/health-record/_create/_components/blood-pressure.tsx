import { TimePicker } from '@/components/shared/datetime-picker';
import { FormError } from '@/components/shared/form-error';
import { FormControl, FormField } from '@/components/ui/form';
import { LabeledNumericInput } from '../../_components/labeled-numeric-input';
import { SubTitle } from '../../_components/sub-title';
import { InputFieldLabel } from '../../plan-setting/_components/input-field-label';
import { InputFieldTip } from '../../plan-setting/_components/input-field-tip';

interface BloodPressureProps {
  title: string;
  timeName: string;
  maxName: string;
  minName: string;
  timeTip: string;
  disabled: boolean;
}

export function BloodPressure({
  title,
  timeName,
  maxName,
  minName,
  timeTip,
  disabled = false,
}: BloodPressureProps) {
  return (
    <>
      <SubTitle className="mb-4 mt-4">{title}</SubTitle>

      <FormField
        name={timeName}
        render={({ field }) => (
          <>
            <InputFieldLabel className="mb-2">計測時間</InputFieldLabel>
            <InputFieldTip className="mb-1">{timeTip}</InputFieldTip>
            <FormControl>
              <TimePicker disabled={disabled} {...field} />
            </FormControl>
            <FormError className="mt-2" />
          </>
        )}
      />

      <div className="flex">
        <FormField
          name={maxName}
          render={({ field }) => (
            <div className="flex-1">
              <FormControl>
                <LabeledNumericInput
                  label="最高血圧"
                  unit="mmHg"
                  maxDecLen={0}
                  maxIntLen={3}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormError className="mt-2" />
            </div>
          )}
        />
        <FormField
          name={minName}
          render={({ field }) => (
            <div className="flex-1 ml-4">
              <FormControl>
                <LabeledNumericInput
                  label="最低血圧"
                  unit="mmHg"
                  maxDecLen={0}
                  maxIntLen={3}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormError className="mt-2" />
            </div>
          )}
        />
      </div>
    </>
  );
}
