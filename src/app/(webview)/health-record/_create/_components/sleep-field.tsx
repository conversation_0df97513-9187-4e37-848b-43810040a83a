import { FormControl } from '@/components/ui/form';

import { DateTimePicker } from '@/components/shared/datetime-picker';
import { FormError } from '@/components/shared/form-error';
import { FormField, FormItem } from '@/components/ui/form';
import { HealthRecordSource, type VitalFrom } from '@/types/health-record';
import { formatDate } from '@/utils/date-format';
import { useEffect, useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import type { RecordSchema } from '../_form/form-schema';
import { CollapsibleSection } from './collapsible-section';

export function SleepField({
  vitalFromData,
  form,
}: {
  vitalFromData: VitalFrom | undefined;
  form: UseFormReturn<RecordSchema>;
}) {
  const recordDate = form.watch('recordDate');
  const [previousDay, setPreviousDay] = useState<string>();

  useEffect(() => {
    if (!recordDate) {
      setPreviousDay(undefined);
      return;
    }
    const previousDay = new Date(recordDate);
    previousDay.setDate(previousDay.getDate() - 1);
    setPreviousDay(formatDate(previousDay, 'yyyy-MM-dd') || '');
  }, [recordDate]);

  return (
    <CollapsibleSection
      title="睡眠時間"
      healthRecordSource={vitalFromData?.sleepFrom || HealthRecordSource.MANUAL}
    >
      <div className="text-sm mb-1 mt-4">就寝時間</div>
      <FormField
        name="sleepTime"
        render={({ field }) => (
          <FormItem className="mb-0">
            <FormControl>
              <DateTimePicker
                defaultValue={previousDay}
                disabled={vitalFromData?.sleepFrom !== HealthRecordSource.MANUAL}
                {...field}
              />
            </FormControl>
            <FormError className="mt-2" />
          </FormItem>
        )}
      />
      <div className="text-sm mb-1 mt-4">起床時間</div>
      <FormField
        name="wakeTime"
        render={({ field }) => (
          <FormItem className="mb-0">
            <FormControl>
              <DateTimePicker
                defaultValue={recordDate}
                disabled={vitalFromData?.sleepFrom !== HealthRecordSource.MANUAL}
                {...field}
              />
            </FormControl>
            <FormError className="mt-2" />
          </FormItem>
        )}
      />
    </CollapsibleSection>
  );
}
