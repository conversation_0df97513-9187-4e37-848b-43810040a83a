import { FormError } from '@/components/shared/form-error';
import { NumberInput } from '@/components/shared/number-input';
import { FormControl, FormField } from '@/components/ui/form';
import type { VitalFrom } from '@/types/health-record';
import { HealthRecordSource } from '@/types/health-record';
import type { UseFormReturn } from 'react-hook-form';
import { LabeledNumericInput } from '../../_components/labeled-numeric-input';
import { RadioOption } from '../../_components/radio-option';
import { RadioSelect } from '../../_components/radio-select';
import type { RecordSchema } from '../_form/form-schema';
import { CollapsibleSection } from './collapsible-section';

export function BloodGlucoseField({
  vitalFromData,
  form,
}: {
  vitalFromData: VitalFrom;
  form: UseFormReturn<RecordSchema>;
}) {
  return (
    <CollapsibleSection
      border={false}
      title="血糖値"
      healthRecordSource={vitalFromData.bloodGlucoseFrom || HealthRecordSource.MANUAL}
    >
      <FormField
        name="bloodGlucoseMeasurementFrequency"
        render={({ field }) => (
          <RadioSelect
            className="mt-4"
            selectedOption={field.value === 1 ? 'single' : 'multiple'}
            onSelect={(value) => {
              field.onChange(value === 'single' ? 1 : 2);
            }}
          >
            <RadioOption value="single" label="1日1回測定" />
            <RadioOption value="multiple" label="1日複数回測定" />
          </RadioSelect>
        )}
      />
      <div className="mt-2">
        {form.watch('bloodGlucoseMeasurementFrequency') === 1 ? (
          <FormField
            name="bloodGlucoseOnceDaily"
            render={({ field }) => (
              <div className="flex-1">
                <FormControl>
                  <LabeledNumericInput
                    className="w-[156px]"
                    label="今日の血糖値"
                    unit="mg/dl"
                    maxDecLen={0}
                    maxIntLen={3}
                    {...field}
                    disabled={vitalFromData?.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
                  />
                </FormControl>
                <FormError className="mt-2" />
              </div>
            )}
          />
        ) : (
          <BloodGlucoseMulti vitalFromData={vitalFromData} />
        )}
      </div>
    </CollapsibleSection>
  );
}

function BloodGlucoseMulti({
  vitalFromData,
}: {
  vitalFromData: VitalFrom;
}) {
  return (
    <div className="flex flex-col mt-4">
      <BloodGlucoseMultiItem
        name="bloodGlucoseBeforeBf"
        index={1}
        label="朝食前の血糖値"
        disabled={vitalFromData.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
      />
      <BloodGlucoseMultiItem
        name="bloodGlucoseAfterBf"
        index={2}
        label="朝食後の血糖値"
        disabled={vitalFromData.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
      />
      <BloodGlucoseMultiItem
        name="bloodGlucoseBeforeLn"
        index={3}
        label="昼食前の血糖値"
        disabled={vitalFromData.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
      />
      <BloodGlucoseMultiItem
        name="bloodGlucoseAfterLn"
        index={4}
        label="昼食後の血糖値"
        disabled={vitalFromData.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
      />
      <BloodGlucoseMultiItem
        name="bloodGlucoseBeforeDn"
        index={5}
        label="夕食前の血糖値"
        disabled={vitalFromData.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
      />
      <BloodGlucoseMultiItem
        name="bloodGlucoseAfterDn"
        index={6}
        label="夕食後の血糖値"
        disabled={vitalFromData.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
      />
      <BloodGlucoseMultiItem
        name="bloodGlucoseBeforeSl"
        index={7}
        label="就寝前の血糖値"
        disabled={vitalFromData.bloodGlucoseFrom !== HealthRecordSource.MANUAL}
      />
    </div>
  );
}

function BloodGlucoseMultiItem({
  name,
  index,
  label,
  disabled,
}: {
  name: string;
  index: number;
  label: string;
  disabled: boolean;
}) {
  return (
    <FormField
      name={name}
      render={({ field }) => (
        <div className="flex mb-4 last:mb-0">
          <div className="text-[17px] mr-3 w-[22px] flex items-center justify-center">{index}</div>
          <div className="flex flex-col gap-1">
            <div className="text-sm">{label}</div>
            <FormControl>
              <NumberInput
                className="w-[156px]"
                unit="mg/dl"
                maxIntLen={3}
                maxDecLen={0}
                disabled={disabled}
                {...field}
              />
            </FormControl>
            <FormError className="mt-2" />
          </div>
        </div>
      )}
    />
  );
}
