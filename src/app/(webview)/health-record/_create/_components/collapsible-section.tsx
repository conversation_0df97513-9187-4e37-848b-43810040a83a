import { cn } from '@/lib/utils';
import { HealthRecordSource } from '@/types/health-record';
import { Minus, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

interface CollapsibleSectionProps {
  title: string;
  healthRecordSource?: HealthRecordSource;
  children: React.ReactNode;
  border?: boolean;
}

enum CollapseState {
  UNTOUCHED = 0,
  COLLAPSED = 1,
  EXPANDED = 2,
}

const healthRecordSourceNameMap: Record<HealthRecordSource, string> = {
  [HealthRecordSource.MANUAL]: '手入力',
  [HealthRecordSource.HEALTH_APP_IPHONE]: 'ヘルスケア',
  [HealthRecordSource.HEALTH_APP_APPLE_WATCH]: 'ヘルスケア',
  [HealthRecordSource.SMARTPHONE]: 'スマートフォン',
  [HealthRecordSource.HEALTH_CONNECT_SMARTPHONE]: 'ヘルスコネクト',
  [HealthRecordSource.HEALTH_CONNECT_SMARTWATCH]: 'ヘルスコネクト',
  [HealthRecordSource.FITBIT]: 'Fitbit',
};

function getHealthRecordSourceName(source: HealthRecordSource) {
  return healthRecordSourceNameMap[source] || '';
}

function determineCollapsedState(
  defaultCollapsed: boolean | undefined,
  collapseState: CollapseState,
) {
  if (collapseState === CollapseState.UNTOUCHED) return !!defaultCollapsed;
  if (collapseState === CollapseState.COLLAPSED) return true;
  return false;
}

export function CollapsibleSection({
  title,
  children,
  border = true,
  healthRecordSource,
}: CollapsibleSectionProps) {
  const [collapseState, setCollapseState] = useState<CollapseState>(CollapseState.UNTOUCHED);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [defaultCollapsed, setDefaultCollapsed] = useState(false);

  useEffect(() => {
    setDefaultCollapsed(healthRecordSource !== HealthRecordSource.MANUAL);
  }, [healthRecordSource]);

  useEffect(() => {
    const state = determineCollapsedState(defaultCollapsed, collapseState);
    setIsCollapsed(state);
  }, [collapseState, defaultCollapsed]);

  return (
    <div className={cn(border && 'border-b border-border', 'pb-6 mt-6')}>
      <div className="flex items-center">
        <h2 className={cn('text-lg font-bold whitespace-nowrap')}>{title}</h2>
        <div className="flex-1 flex items-center w-0">
          {defaultCollapsed && (
            <div className="bg-primary-5 max-w-full overflow-hidden whitespace-nowrap text-ellipsis h-5 rounded text-sm px-1 ml-4 text-primary">
              {getHealthRecordSourceName(healthRecordSource || HealthRecordSource.MANUAL)}から連携済
            </div>
          )}
        </div>
        <button
          onClick={() =>
            setCollapseState(isCollapsed ? CollapseState.EXPANDED : CollapseState.COLLAPSED)
          }
          type="button"
        >
          {isCollapsed ? <Plus className="w-6 h-6" /> : <Minus className="w-6 h-6" />}
        </button>
      </div>
      <div className={cn(isCollapsed && 'hidden')}>{children}</div>
    </div>
  );
}
