'use client';
import { Button } from '@/components/shared/button';
import { Select } from '@/components/shared/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { codeTypeList, useCommonCode } from '@/hooks/use-common-code';

import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import type { ListWalkingCourseRequest } from '@/types/walking-course';
import { useState } from 'react';

interface WalkingCourseFilterFormProps {
  initialFilters?: ListWalkingCourseRequest['filters'];
  onApplyFilters: (filters: ListWalkingCourseRequest['filters']) => void;
  onClearFilters: () => void;
  onClose: () => void;
}

export default function WalkingCourseFilterForm({
  initialFilters,
  onApplyFilters,
  onClearFilters,
  onClose,
}: WalkingCourseFilterFormProps) {
  const router = useRouter();
  const pathname = usePathname();

  const commonCode = useCommonCode();

  const hasPointsToKey = (hasPoints: boolean | undefined) => {
    const codeList = commonCode.getType(codeTypeList.WALKING_COURSE_SEARCH_POINT)?.codeList || [];
    const code = codeList.find((code) => code.metaData?.val === hasPoints);
    return code?.codeKey || '1';
  };
  const keyToHasPoints = (key: string) => {
    const boolValue = commonCode.getRealValue(codeTypeList.WALKING_COURSE_SEARCH_POINT, key);
    if (boolValue === true || boolValue === false) {
      return boolValue;
    }
  };
  const [hasPoints, setHasPoints] = useState(hasPointsToKey(initialFilters?.hasPoints));

  const challengeCountToKey = (challengeCount: string | undefined) => {
    const codeList =
      commonCode.getType(codeTypeList.WALKING_COURSE_SEARCH_CHALLENGE)?.codeList || [];
    const code = codeList.find((code) => code.metaData?.val === challengeCount);
    return code?.codeKey || '1';
  };
  const keyToChallengeCount = (key: string) => {
    return commonCode.getRealValue(codeTypeList.WALKING_COURSE_SEARCH_CHALLENGE, key)?.toString();
  };

  const [remainingChallenges, setRemainingChallenges] = useState(
    challengeCountToKey(initialFilters?.remainingChallenges),
  );

  const distanceFromHereToKey = (distanceFromHere: number | undefined | null) => {
    const codeList =
      commonCode.getType(codeTypeList.WALKING_COURSE_SEARCH_DISTANCE)?.codeList || [];
    const code = codeList.find((code) => code.metaData?.val === distanceFromHere);
    return code?.codeKey || '1';
  };

  const keyToDistanceFromHere = (key: string) => {
    const numberValue = Number(
      commonCode.getRealValue(codeTypeList.WALKING_COURSE_SEARCH_DISTANCE, key),
    );
    return Number.isNaN(numberValue) || numberValue <= 0 ? undefined : numberValue;
  };
  const [distanceFromHere, setDistanceFromHere] = useState(
    distanceFromHereToKey(initialFilters?.distanceFromHere),
  );
  const [hideCompletedCourses, setHideCompletedCourses] = useState(
    initialFilters?.hideCompletedCourses || false,
  );
  const hasPointOptions = commonCode.getOptions(codeTypeList.WALKING_COURSE_SEARCH_POINT);
  const remainingChallengesOptions = commonCode.getOptions(
    codeTypeList.WALKING_COURSE_SEARCH_CHALLENGE,
  );
  const distanceFromHereOptions = commonCode.getOptions(
    codeTypeList.WALKING_COURSE_SEARCH_DISTANCE,
  );

  const handleApplyFilters = () => {
    const filters: ListWalkingCourseRequest['filters'] = {};
    if (hasPoints && Boolean(hasPoints)) filters.hasPoints = keyToHasPoints(hasPoints);
    if (remainingChallenges) filters.remainingChallenges = keyToChallengeCount(remainingChallenges);
    if (distanceFromHere) filters.distanceFromHere = keyToDistanceFromHere(distanceFromHere);
    if (hideCompletedCourses) filters.hideCompletedCourses = hideCompletedCourses;
    onApplyFilters(filters);
    onClose();
  };

  const handleClearFilters = () => {
    setHasPoints('1');
    setRemainingChallenges('1');
    setDistanceFromHere('1');
    setHideCompletedCourses(false);
    onClearFilters();
    onClose();
  };

  return (
    <div className="bg-card">
      <div className="p-6 flex flex-col gap-6 pb-40">
        {/* ポイントの有無 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">ポイントの有無</h3>
          <RadioGroup value={hasPoints} onValueChange={setHasPoints} className="space-y-5">
            {hasPointOptions.map((option) => (
              <div key={option.label} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.label} />
                <Label htmlFor={option.value} className="cursor-pointer font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        {/* 挑戦可能回数 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">挑戦可能回数</h3>
          <RadioGroup
            value={remainingChallenges}
            onValueChange={setRemainingChallenges}
            className="space-y-5"
          >
            {remainingChallengesOptions.map((option) => (
              <div key={option.label} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.label} />
                <Label htmlFor={option.value} className="cursor-pointer font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        {/* 距離 */}
        <div>
          <h3 className="text-base font-bold mt-6 mb-4">現在地からの距離</h3>
          <Select
            defaultValue={distanceFromHere}
            options={distanceFromHereOptions.map((option) => ({
              name: option.label,
              value: option.value,
            }))}
            title="現在地からの距離"
            onChange={(value) => setDistanceFromHere(value)}
            className="flex items-center justify-between h-12 px-4 border border-input rounded-md  text-sm"
          />
        </div>
        <div>
          <Checkbox
            className="ml-0"
            checked={hideCompletedCourses}
            onCheckedChange={() => setHideCompletedCourses(!hideCompletedCourses)}
          />
          <span className="ml-2 text-[15px] text-text-primary">
            再挑戦できないコースを非表示にする
          </span>
        </div>

        <div className="fixed bottom-0 left-0 right-0 flex flex-col gap-4 p-4 bg-card border-t">
          <Button onClick={handleApplyFilters}>条件を適用する</Button>
          <Button variant="outline" onClick={handleClearFilters}>
            クリア
          </Button>
        </div>
      </div>
    </div>
  );
}
