'use client';
import { walkingCourseAPI } from '@/api/modules/walking-course';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import NoData from '@/components/shared/no-data';
import { SearchInput } from '@/components/shared/search-input';
import { Select } from '@/components/shared/select';

import KeywordSearch from '@/components/shared/keyword-search';
import { codeTypeList, useCommonCode } from '@/hooks/use-common-code';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useWalkingCourseStore } from '@/store/walking-course-store';
import type { ListWalkingCourseRequest, WalkingCourse } from '@/types/walking-course';
import { Tent } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { FilterIcon } from '../_components/svg-icon';
import WalkingCourseCard from './_components/walking-course-card';
import WalkingCourseFilter from './_components/walking-course-filter';

export default function WalkingCourseSearchResultPage() {
  const { setLoading } = useLoading();
  const router = useRouter();

  const commonCode = useCommonCode();
  const sortOptions = commonCode.getOptions(codeTypeList.WALKING_COURSE_SEARCH_SORT);
  const sortToKey = (sort: string) => {
    const codeList = commonCode.getType(codeTypeList.WALKING_COURSE_SEARCH_SORT)?.codeList || [];
    const code = codeList.find((code) => code.metaData?.val === sort);
    return code?.codeKey || '1';
  };
  const keyToSort = (key: string) => {
    return (
      commonCode.getRealValue(codeTypeList.WALKING_COURSE_SEARCH_SORT, key)?.toString() || 'popular'
    );
  };

  const { setSlidePage } = useSlidePage();

  const { location } = useGeolocation();

  const { searchParams, updateSearchSort, setGeolocation, updateSearchFilters, clearSearchParams } =
    useWalkingCourseStore();

  const [walkingCoursesList, setWalkingCourseList] = useState<WalkingCourse[]>([]);

  const handleBackClick = () => {
    router.back();
  };
  const handleCloseClick = () => {
    router.back();
  };

  useEffect(() => {
    const searchWalkingCoursesData = async () => {
      setLoading(true);
      try {
        if (!location) return;
        if (!searchParams.latitude) {
          setGeolocation(location);
          return;
        }
        const data = await walkingCourseAPI.listWalkingCourse(searchParams);
        if (data) {
          setWalkingCourseList(data.coursesList || []);
        }
      } catch (error) {
        console.log('検索に失敗しました:', error);
        toast.error('検索に失敗しました');
      } finally {
        setLoading(false);
      }
    };

    searchWalkingCoursesData();
  }, [searchParams]);

  // 处理筛选条件应用
  const handleApplyFilters = async (filters: ListWalkingCourseRequest['filters']) => {
    updateSearchFilters(filters, location);
  };

  // 处理清除筛选条件
  const handleClearFilters = async () => {
    clearSearchParams();
  };

  // 处理关键词搜索
  const handleKeywordSearch = (courseNameQuery: string) => {
    const filters = {
      ...searchParams.filters,
      courseNameQuery,
    };
    updateSearchFilters(filters, location);
  };

  // 处理清空关键词
  const handleClearKeyword = () => {
    const filters = {
      ...searchParams.filters,
      courseNameQuery: undefined,
    };
    updateSearchFilters(filters, location);
  };

  // 处理排序变更
  const handleSortChange = (sortType: string) => {
    updateSearchSort(keyToSort(sortType), location);
  };

  const openFilterSlidePage = () => {
    setSlidePage(true, {
      title: '絞り込み',
      content: (
        <WalkingCourseFilter
          initialFilters={searchParams.filters}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          onClose={() => setSlidePage(false)}
        />
      ),
      isOverAll: true,
      enableClose: true,
      enableBack: false,
      slideFrom: 'bottom',
    });
  };

  // 打开关键词搜索滑动页面
  const openKeywordSlidePage = () => {
    setSlidePage(true, {
      title: 'ウォーキングコース',
      content: (
        <KeywordSearch
          initialKeyword={searchParams.filters?.courseNameQuery || ''}
          onKeywordSelect={handleKeywordSearch}
          onClose={() => setSlidePage(false)}
          functionType="walkingCourse"
          functionTypeName="ウォーキングコース"
          suggestionsIcon={<Tent className="h-6 w-6 text-muted-foreground" />}
          detailUrl="/walking-course/detail/[courseId]"
        />
      ),
      isOverAll: true,
      enableClose: false,
      enableBack: true,
      slideFrom: 'bottom',
    });
  };

  return (
    <>
      <TopBar
        title="ウォーキングコース"
        enableBack={true}
        enableClose={false}
        onBack={handleBackClick}
        onClose={handleCloseClick}
      />

      <div className="p-6 flex flex-row gap-4 bg-card">
        <div onClick={openKeywordSlidePage} className="flex-1">
          <SearchInput
            placeholder="キーワードを入力"
            value={searchParams.filters?.courseNameQuery || ''}
            readOnly
            className="cursor-pointer"
            showClearButton={!!searchParams.filters?.courseNameQuery}
            onClear={handleClearKeyword}
          />
        </div>
        <Button
          onClick={openFilterSlidePage}
          variant="icon"
          className="flex flex-col items-center gap-0"
          type="button"
        >
          <FilterIcon size={24} />
          <span className="text-[10px] font-normal h-4">絞り込み</span>
        </Button>
      </div>

      <div className="p-4 flex flex-col gap-1 bg-card">
        {/* すべて */}
        <div className="font-medium flex items-center justify-between ml-1">
          <div>
            <div className="font-bold text-lg">
              {Object.keys(searchParams.filters).length === 0 ? 'すべて' : '検索結果'}
            </div>
            <p className="text-sm text-muted-foreground">{walkingCoursesList.length}件</p>
          </div>
          <Select
            defaultValue={sortToKey(searchParams.sort || 'popular')}
            options={sortOptions.map((option) => ({
              value: option.value,
              name: option.label,
            }))}
            title="並び替え"
            onChange={handleSortChange}
            className="flex items-center justify-between h-9  px-4 border border-input rounded-md w-[136px] text-sm mr-2"
          />
        </div>

        <div className="flex flex-col gap-0 py-0">
          {walkingCoursesList.length > 0 ? (
            walkingCoursesList.map((walkingCourse: WalkingCourse) => (
              <WalkingCourseCard
                key={walkingCourse.courseId}
                walkingCourse={walkingCourse}
                onClick={() => {
                  router.push(`/walking-course/detail/${walkingCourse.courseId}`);
                }}
              />
            ))
          ) : (
            <div className="text-center py-24">
              <NoData />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
