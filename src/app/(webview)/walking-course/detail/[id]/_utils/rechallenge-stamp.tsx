import type { ParticipationResponse, WalkingCourseDetailResponse } from '@/types/walking-course';

/**
 * 日付文字列をフォーマットする関数
 * "YYYY-MM-DD ..." => "YYYY年M月D日"
 */
export function formatDate(dateStr: string): string {
  if (!dateStr) return '';
  const [year, month, day] = dateStr.split('-');
  return `${year}年${Number.parseInt(month, 10)}月${Number.parseInt(day, 10)}日`;
}

/**
 * 参加情報配列から最大のチャレンジ回数を取得する関数
 */
export function getMaxChallengeCount(participations: ParticipationResponse[] | undefined): number {
  if (!participations || participations.length === 0) return 0;
  return Math.max(...participations.map((p) => p.challengeCount));
}

/**
 * WalkingCourseDetailResponse をコンポーネント用のデータ構造に変換する関数
 */
export function transformWalkingCourseDetail(detail: WalkingCourseDetailResponse) {
  const nextRunCount = getMaxChallengeCount(detail.participations);

  return {
    runCount: nextRunCount,
    courseName: detail.courseName,
    rechallengeDate: formatDate(detail.rechallengeDate ?? ''),
    spotCount: detail.stampSpotList?.length ?? 0,
  };
}
