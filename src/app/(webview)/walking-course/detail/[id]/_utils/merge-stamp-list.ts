// src/utils/merge-stamp-list.ts

import type { ParticipationStampSpotCheckData, StampSpot } from '@/types/walking-course';

// スタンプリストをマージする関数
export function mergeStampList(
  stampList: ParticipationStampSpotCheckData[],
  stampSpotList: StampSpot[],
): ParticipationStampSpotCheckData[] {
  const existingSpotIds = new Set(stampList.map((s) => s.spotId));

  const missingStamps = stampSpotList
    .filter((spot) => !existingSpotIds.has(spot.spotId))
    .map((spot) => ({
      spotId: spot.spotId,
      isChecked: false,
      createdAt: undefined,
    }));

  return [...stampList, ...missingStamps];
}
