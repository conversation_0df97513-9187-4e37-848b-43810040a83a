// ---------------------------
// ウォーキングコース詳細データ解析ユーティリティ
// ---------------------------
import type {
  ParticipationResponse,
  StampSpotResponse,
  WalkingCourseDetailResponse,
} from '@/types/walking-course';
import { mergeStampList } from './merge-stamp-list';

export function parseWalkingCourseDetail(
  detail: WalkingCourseDetailResponse | undefined,
  courseId: string,
) {
  const participations = detail?.participations ?? [];

  // 最大挑戦回数の参加データを取得
  const len = participations.length;
  const maxParticipation = participations[len - 1];

  const courseName = detail?.courseName ?? '';
  const stampPoints = detail?.stampPoints ?? 0;
  const completeBonus = detail?.completeBonus ?? 0;
  const rechallengePoints = detail?.rechallengePoints ?? 0;
  const rechallengeAllowed = detail?.rechallengeAllowed ?? 0;
  const rechallengeState = detail?.rechallengeState ?? 0;
  const rechallengeDate = detail?.rechallengeDate ?? '';
  const lastSpot: StampSpotResponse | undefined = detail?.stampSpotList?.at(-1);

  const stampCardData = {
    id: Number(courseId) || 0,
    courseName,
    runCount: maxParticipation?.challengeCount ?? 1,
    stampPoints,
    completeBonus,
    isCompleted: maxParticipation?.isCompleted || false,
    rechallengePoints,
    stampList: mergeStampList(maxParticipation?.stampList || [], detail?.stampSpotList || []),
  };

  return {
    courseName,
    stampPoints,
    completeBonus,
    rechallengePoints,
    rechallengeAllowed,
    rechallengeDate,
    rechallengeState,
    lastSpot,
    maxParticipation,
    stampCardData,
  };
}
