import { walkingCourseAPI } from '@/api/modules/walking-course';
import { ImageGrid } from '@/app/(webview)/home/<USER>/image-grid';
import { StampCard } from '@/app/(webview)/walking-course/_components/stamp-card';
import { Button } from '@/components/shared/button';
import { Drawer, DrawerContent, DrawerDescription, DrawerTitle } from '@/components/shared/drawer';
import QaIcon from '@/components/shared/qa-icon';
import { type QaData, QaPage } from '@/components/shared/qa-page';
import { ScrollArea } from '@/components/shared/scroll-area';
import SectionTitle from '@/components/shared/section-title';
import { TextButton } from '@/components/shared/text-button';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel';
import { COLORS } from '@/const/colors';
import { useGlobalVar } from '@/hooks/use-global-var';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useSlidePage } from '@/hooks/use-slide-page';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/store/auth';
import { useFixedCourseStore } from '@/store/fixed-course';
import {
  ChallengeType,
  type StampSpotCardData,
  type WalkingCourseDetailResponse,
} from '@/types/walking-course';
import { ChevronLeftIcon, ChevronRightIcon, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { StampSpotItem } from '../../../_components/course-item';
import { PinIcon, PointIcon, StampIcon, StarIcon, UnpinIcon } from '../../../_components/svg-icon';
import {
  ChallengeTimeTag,
  CourseInfoTag,
  SpotTag,
  WalkingCourseTypeTag,
} from '../../../_components/tag';
import { useStampSpotState } from '../../../_context/use-stamp-spot-state';
import { getStampSpotCardListData } from '../../../_utils';

function CourseBeginnerContent() {
  return (
    <div>
      <p className="text-gray-700 leading-relaxed text-base">
        アプリのマップ画面でウォーキングコースとスタンプの場所をチェック
      </p>
      <div className="w-[327px] h-[186px] overflow-hidden mt-2">
        <img
          src="/images/walking-course/sample-detail.png"
          alt="例"
          className="w-full h-full object-cover"
        />
      </div>
      <p className="text-gray-700 leading-relaxed text-base font-bold mt-6">歩数の目安について</p>
      <p className="text-gray-700 leading-relaxed text-base mt-2">
        1歩70cmとして、コースの距離から算出しています。実際の歩数と異なる場合があります。
      </p>
    </div>
  );
}

function StampPushContent() {
  return (
    <div>
      <p className="text-gray-700 leading-relaxed text-base">
        スタンプの場所にいくと、「今押せるスタンプがあります」ボタンが表示されます。タップしてスタンプを押しましょう！
      </p>{' '}
      <p className="text-muted-foreground leading-relaxed text-sm mt-2">
        ※スタンプを押す順番の指定はありません。
      </p>
      <div className="w-[327px] h-[186px] overflow-hidden mt-2">
        <img
          src="/images/walking-course/sample-detail.png"
          alt="例"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
}

function StampCompleteContent() {
  return (
    <div>
      <p className="text-gray-700 leading-relaxed text-base">
        ウォーキングコース上のすべてのスタンプを押すとコンプリートです。コンプリート後も何度でも同じウォーキングコースに挑戦できます。
      </p>{' '}
      <p className="text-muted-foreground leading-relaxed text-sm mt-2">
        ※連続して同じスタンプを押すことはできません。
      </p>{' '}
      <p className="text-muted-foreground dleading-relaxed text-sm mt-2">
        ※同じスタンプを押すには該当するコースをコンプリートする必要があります。
      </p>
      <div className="w-[327px] h-[186px] overflow-hidden mt-2">
        <img
          src="/images/walking-course/sample-detail.png"
          alt="例"
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
}

const qaData: QaData[] = [
  {
    id: 'course-beginner',
    title: 'ウォーキングコースの始め方',
    content: <CourseBeginnerContent />,
  },
  {
    id: 'stamp-push',
    title: 'スタンプの押し方',
    content: <StampPushContent />,
  },
  {
    id: 'stamp-complete',
    title: 'スタンプコンプリートとは',
    content: <StampCompleteContent />,
  },
];

const snapPoints = [0.6, 1];

export function CourseDetailDrawer({
  data,
  show,
}: { data?: WalkingCourseDetailResponse; show: boolean }) {
  const { setSlidePage } = useSlidePage();
  const { setShowCourseDetail } = useStampSpotState();
  const [snap, setSnap] = useState<number | string | null>(snapPoints[0]);
  const safeArea = useSafeArea();
  const top = useMemo(() => safeArea.top + 72 + 53 + 16, [safeArea]);
  const { setFooterMenuSettingOptions } = useGlobalVar();

  return (
    <Drawer
      open={show}
      onOpenChange={setShowCourseDetail}
      snapPoints={snapPoints}
      activeSnapPoint={snap}
      setActiveSnapPoint={setSnap}
    >
      <DrawerContent hideOverlay={true}>
        <DrawerTitle className="sr-only">{data?.courseName}</DrawerTitle>
        <DrawerDescription className="sr-only">Set your daily activity goal.</DrawerDescription>
        <div className="flex items-center h-[53px] shrink-0 px-6">
          <h1 className="text-[22px] font-bold flex-1 truncate mr-1">{data?.courseName}</h1>
          <Button
            aria-label="friend tips"
            size="xs"
            variant="icon"
            onClick={() => {
              setFooterMenuSettingOptions({ isShow: true, layerIndex: 51 });
              setSlidePage(true, {
                title: 'ウォーキングコースについて',
                isOverAll: true,
                content: <QaPage data={qaData} />,
                enableClose: false,
                enableBack: true,
                slideFrom: 'right',
                onClose: () => {
                  setFooterMenuSettingOptions({ isShow: false, layerIndex: 50 });
                },
              });
            }}
          >
            <QaIcon />
          </Button>
          <Button
            aria-label="course detail close"
            size="xs"
            className=" ml-4 relative"
            variant="icon"
            onClick={() => {
              setShowCourseDetail(false);
              setSnap(snapPoints[0]);
            }}
          >
            <X size={24} />
          </Button>
        </div>
        <ScrollArea
          disable={snap !== 1}
          className="w-full"
          style={{ height: `calc(100vh - ${top}px)` }}
          type="hover"
        >
          {data && <CourseDetail data={data} />}
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
}

export function CourseDetail({ data }: { data: WalkingCourseDetailResponse }) {
  const { fixedCourse, setFixedCourse, clearFixedCourse } = useFixedCourseStore();
  const { setShowStampSpotCardSlideDrawer, setShowCourseDetail } = useStampSpotState();
  const isMultiOrg = useAuthStore().getIsMultiOrg();

  const stampCardListData = useMemo(() => {
    if (!data) {
      return [];
    }
    // data.participations = [
    //   {
    //     challengeCount: 1,
    //     isCompleted: true,
    //     stampList: [
    //       {
    //         spotId: 1,
    //         isChecked: true,
    //         createdAt: '2025-01-01',
    //       },
    //       {
    //         spotId: 2,
    //         isChecked: true,
    //         createdAt: '2025-01-01',
    //       },
    //       {
    //         spotId: 3,
    //         isChecked: true,
    //         createdAt: '2025-01-01',
    //       },
    //     ],
    //   },
    //   {
    //     challengeCount: 2,
    //     isCompleted: false,
    //     stampList: [
    //       {
    //         spotId: 1,
    //         isChecked: true,
    //         createdAt: '2025-01-01',
    //       },
    //       {
    //         spotId: 2,
    //         isChecked: false,
    //       },
    //       {
    //         spotId: 3,
    //         isChecked: false,
    //       },
    //     ],
    //   },
    // ];
    return getStampSpotCardListData(data) || [];
  }, [data]);

  const handleShowStampDrawer = () => {
    setShowCourseDetail(false);
    setShowStampSpotCardSlideDrawer(true);
  };
  const handlePinCourse = () => {
    if (data) {
      setFixedCourse({
        id: data.courseId,
        name: data.courseName,
      });
    }
  };
  const handleUnpinCourse = () => {
    clearFixedCourse();
  };
  const challengeType = useMemo(() => {
    if (data?.rechallengeAllowed) {
      if (data.rechallengeInterval === 0) {
        return ChallengeType.DAILY;
      }
      if (data.rechallengeInterval === 1) {
        return ChallengeType.WEEKLY;
      }
      if (data.rechallengeInterval === 2) {
        return ChallengeType.MONTHLY;
      }
    }
    return ChallengeType.ONE_TIME;
  }, [data]);

  return (
    <>
      <div className="relative mx-6">
        <div className="flex items-center gap-2">
          <WalkingCourseTypeTag />
          <ChallengeTimeTag challengeType={challengeType} />
        </div>
        <div className="text-sm text-gray-60 mt-1">
          <span>{data?.estimatedDistance || '---'}km</span>
          <span>・</span>
          <span>{data?.endDate}</span>
        </div>
        <FavButton />
      </div>
      <div className="mx-6 mt-3 flex items-center justify-between">
        <SpotTag count={data?.stampSpotList?.length || 0} />
        {fixedCourse?.id === data?.courseId ? (
          <Button onClick={handleUnpinCourse} variant="outline" size="sm" className="gap-1 px-6">
            <UnpinIcon size={18} fill={COLORS.primary.DEFAULT} />
            固定表示を解除する
          </Button>
        ) : (
          <Button onClick={handlePinCourse} variant="outline" size="sm" className="gap-1 px-6">
            <PinIcon size={18} fill={COLORS.primary.DEFAULT} />
            地図に固定表示する
          </Button>
        )}
      </div>
      <CourseInfoTag
        walkingCourse={{
          estimatedTimeHours: data?.estimatedTimeHours || 0,
          estimatedTimeMinutes: data?.estimatedTimeMinutes || 0,
          estimatedDistance: data?.estimatedDistance?.toString() || '0',
          steps: data?.steps || 0,
        }}
        className="mx-6 mt-3 h-10"
      />
      {(data?.stampPoints || data?.completeBonus) && (
        <div className="mx-6 mt-3 bg-primary-5 rounded-2xl py-1">
          {isMultiOrg && (
            <div className="flex items-center text-sm mx-6 h-[46px]">
              <span>{data.organizerNm}</span>
            </div>
          )}
          <div className="flex items-center text-sm mx-6 border-b border-gray-30 h-[46px]">
            <PointIcon size={20} fill={COLORS.primary[100]} />
            <span className="ml-2">1スタンプ</span>
            <span className="flex-1" />
            <span className="text-primary text-xl font-bold">{data?.stampPoints || 0}</span>
            <span className="ml-1">P</span>
          </div>
          <div
            className={cn(
              'flex items-center text-sm mx-6 h-[46px]',
              data?.rechallengePoints === 0 ? 'mt-1 mb-1' : '',
            )}
          >
            <PointIcon size={20} fill={COLORS.primary[100]} />
            <div className="ml-2 flex-1">
              <div>スタンプコンプリート</div>
              {(data.participations?.length ?? 0) > 1 && data?.rechallengePoints === 0 && (
                <div className="text-xs text-gray-60">※2回目以降は獲得ポイントなし</div>
              )}
            </div>
            <span className="text-primary text-xl font-bold">{data?.completeBonus || 0}</span>
            <span className="ml-1">P</span>
          </div>
        </div>
      )}
      <div className="mt-3 mx-6">{data?.description || 'コースの説明がありません。'}</div>
      {data?.imagePath && (
        <div className="mx-6 mt-2 h-[216px] overflow-hidden flex items-center justify-center rounded-2xl">
          <img src={data?.imagePath} alt="course-item-img" className="w-full object-cover" />
        </div>
      )}
      <SectionTitle className="h-[27px] pt-0 pb-0 mt-6">スタンプ</SectionTitle>
      <StampSpotCardList data={stampCardListData} />
      <SectionTitle className="h-[27px] pt-0 pb-0 mt-6">スタンプスポット</SectionTitle>
      <div className="mx-6">
        {data?.stampSpotList?.map((spot, index) => (
          <StampSpotItem
            key={spot.spotId}
            className="py-3 border-b border-gray-30 last:border-b-0"
            data={spot}
          />
        )) || <div className="py-4 text-center text-gray-60">スタンプスポットがありません</div>}
      </div>
      <div className="mx-6 mt-3">
        <Button className="w-full" onClick={handleShowStampDrawer}>
          <StampIcon size={20} fill={'#ffffff'} />
          コース内で押せるスタンプがあります
        </Button>
      </div>
      <div className="h-12" />
      {/* <SectionTitle>
        <span className="flex-1">周辺の投稿</span>
        <TextButton className="gap-0">
          <span>もっと見る</span> <ChevronRightIcon size={22} />
        </TextButton>
      </SectionTitle> */}
      {/* <ImageGrid className="mt-0 pb-10" /> */}
    </>
  );
}

function StampSpotCardList({ data }: { data: StampSpotCardData[] }) {
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [api, setApi] = useState<CarouselApi>();
  useEffect(() => {
    if (!api) {
      return;
    }
    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);
    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
    api.scrollTo(api.scrollSnapList().length - 1);
  }, [api]);
  const marginLeft = (window.innerWidth - 320) / 2 - 16;

  return (
    <Carousel
      opts={{
        align: 'start',
        loop: false,
      }}
      orientation="horizontal"
      setApi={setApi}
    >
      <CarouselContent className="w-[320px] pt-2 pb-2" style={{ marginLeft: `${marginLeft}px` }}>
        {data.map((item, index) => (
          <CarouselItem key={item.runCount} className={cn(index > 0 ? 'ml-2' : '')}>
            <StampCard data={item} hideCourseName={true} />
          </CarouselItem>
        ))}
      </CarouselContent>
      <div className="h-[24px] flex items-center justify-center mx-6 px-2">
        <Button variant="icon" size="xs" onClick={() => api?.scrollPrev()}>
          <ChevronLeftIcon size={20} strokeWidth={1} />
        </Button>
        <div className="text-center text-sm flex-1">
          {current}/{count}
        </div>
        <Button variant="icon" size="xs" onClick={() => api?.scrollNext()}>
          <ChevronRightIcon size={20} strokeWidth={1} />
        </Button>
      </div>
    </Carousel>
  );
}

function FavButton() {
  return (
    <Button
      variant="icon"
      size="xs"
      className="absolute border border-gray-30 top-[6px] right-0 w-8 h-8"
    >
      <StarIcon size={16} stroke={COLORS.gray['30']} fill={'none'} />
      {/* <StarIcon size={16} /> */}
    </Button>
  );
}
