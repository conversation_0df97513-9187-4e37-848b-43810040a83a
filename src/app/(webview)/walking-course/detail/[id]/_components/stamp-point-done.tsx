'use client';

import { walkingCourseAPI } from '@/api/modules/walking-course';
import { PointIcon } from '@/app/(webview)/walking-course/_components/svg-icon';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/store/auth';
import type { StampSpotResponse } from '@/types/walking-course';
import { useQuery } from '@tanstack/react-query';
import { ChevronRight, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { StampSpotItem } from '../../../_components/course-item';
import { StampCard } from '../../../_components/stamp-card';
import { useStampSpotState } from '../../../_context/use-stamp-spot-state';
import { getStampSpotCardData } from '../../../_utils';
import { parseWalkingCourseDetail } from '../_utils/parse-walking-course-detail';

// ---------------------------
// メインコンポーネント
// ---------------------------
export function StampPointDone({
  onClose,
  courseId,
  onGoDetail,
}: {
  onClose: () => void;
  courseId: string;
  onGoDetail: () => void;
}) {
  const safeArea = useSafeArea();
  const safeTop = safeArea.top ?? 0;
  const [isComplete, setIsComplete] = useState(false);
  const [topBarHeight, setTopBarHeight] = useState(48);
  const { latestStampPointCheckedTime } = useStampSpotState();

  // APIからウォーキングコースの詳細を取得;
  const { data: walkingCourseDetail } = useQuery({
    queryKey: ['walkingCourseDetail', courseId, latestStampPointCheckedTime],
    queryFn: () => walkingCourseAPI.walkingCourseDetail(courseId),
    enabled: !!courseId,
  });

  // 共通データをまとめて取得（util 関数に移動）
  const {
    courseName,
    stampPoints,
    completeBonus,
    rechallengePoints,
    rechallengeAllowed,
    rechallengeState,
    rechallengeDate,
    lastSpot,
    maxParticipation,
  } = useMemo(
    () => parseWalkingCourseDetail(walkingCourseDetail, courseId),
    [walkingCourseDetail, courseId],
  );

  const stampCardData = useMemo(() => {
    if (!walkingCourseDetail) {
      return undefined;
    }
    return getStampSpotCardData(walkingCourseDetail, maxParticipation);
  }, [walkingCourseDetail, maxParticipation]);

  // 完了状態を管理
  useEffect(() => {
    if (stampCardData) {
      setIsComplete(stampCardData.isCompleted);
    }
  }, [stampCardData]);

  // TopBarの高さ設定
  useEffect(() => {
    setTopBarHeight(isComplete ? safeTop : safeTop + 48);
  }, [safeTop, isComplete]);

  const defaultCourseItemData: StampSpotResponse = {
    spotId: 1,
    spotName: '中島公園2kmコース',
    latitude: '35.681236',
    longitude: '139.767125',
    imagePath: 'https://placehold.co/56x56',
  };

  return (
    <div className="bg-card">
      {/* Top Bar スペース */}
      <div style={{ height: topBarHeight }} className="bg-card" />

      {/* 閉じるボタン */}
      <div className={cn('fixed top-0 right-0 z-50 w-[48px]')}>
        <div className="flex h-[48px] items-center relative">
          <button type="button" onClick={onClose} aria-label="close" className="absolute right-5">
            <X size={24} />
          </button>
        </div>
      </div>

      {/* ヘッダー */}
      {isComplete ? (
        <StampPointHeaderWithComplete
          title={courseName}
          maxRound={maxParticipation?.challengeCount ?? 0}
          stampPoints={stampPoints}
          rechallengePoints={rechallengePoints}
          completeBonus={completeBonus}
          organizerNm={walkingCourseDetail?.organizerNm}
        />
      ) : (
        <StampPointHeader
          maxRound={maxParticipation?.challengeCount ?? 0}
          stampPoints={stampPoints}
          rechallengePoints={rechallengePoints}
          organizerNm={walkingCourseDetail?.organizerNm}
        />
      )}

      {/* スタンプカード */}
      {stampCardData && <StampCard className="mt-4" data={stampCardData} />}

      {/* 再挑戦可能日 */}
      {isComplete && rechallengeAllowed && rechallengeDate && (
        <p className="text-sm text-gray-600 text-center mt-4">
          次は{rechallengeDate.slice(0, 4)}年{rechallengeDate.slice(5, 7)}月
          {rechallengeDate.slice(8, 10)}日以降にチャレンジ可能です
        </p>
      )}

      <div className="px-6 mt-6 pb-6">
        {/* スポット情報 */}
        {!isComplete && (
          <>
            <h2 className="text-lg font-medium mb-4">このコースの近くのスタンプスポット</h2>
            <StampSpotItem data={lastSpot ?? defaultCourseItemData} className="mt-3" />
          </>
        )}

        {/* ボタン */}
        <Button className="mt-12 w-full" onClick={onClose}>
          閉じる
        </Button>
        <TextButton className="mt-6 w-full" onClick={onGoDetail}>
          コースの詳細を見る
          <ChevronRight />
        </TextButton>
      </div>
    </div>
  );
}

// ---------------------------
// スタンプ獲得ヘッダー（未コンプリート）
// ---------------------------
function StampPointHeader({
  maxRound,
  stampPoints,
  rechallengePoints,
  organizerNm,
}: {
  maxRound: number;
  stampPoints: number;
  rechallengePoints: number;
  organizerNm?: string;
}) {
  const isMultiOrg = useAuthStore().getIsMultiOrg();
  const point =
    maxRound === 1 && stampPoints !== 0
      ? stampPoints
      : maxRound !== 1 && rechallengePoints !== 0
        ? rechallengePoints
        : 0;

  console.log('organizerNm', organizerNm);
  return (
    <div className="text-center text-xl font-bold">
      <img
        src={`/images/walking-course/stamp-${point === 0 ? 'nopoint' : 'point'}-done-icon.png`}
        alt="スタンプを獲得！"
        className="w-16 h-16 mx-auto"
      />
      <h1 className="text-[22px] font-bold mt-1">スタンプを獲得！</h1>
      {point !== 0 && (
        <p className="text-[22px] mt-1">
          <span className="text-primary mr-1">{isMultiOrg ? '' : point}</span>ポイントゲット
        </p>
      )}
      {isMultiOrg && (
        <div className="flex flex-col bg-primary-5 rounded-2xl ml-6 mr-6 mt-2">
          <div className="flex items-center justify-between mt-4 mb-4">
            <span className="text-gray-700 text-sm ml-4">{organizerNm}</span>
            <div className="flex items-center gap-0.5">
              <span className="text-primary font-bold text-xl">{point}</span>
              <span className="text-sm mr-6">p</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// ---------------------------
// スタンプ獲得ヘッダー（コンプリート）
// ---------------------------
function StampPointHeaderWithComplete({
  title,
  maxRound,
  stampPoints,
  rechallengePoints,
  completeBonus,
  organizerNm,
}: {
  title: string;
  maxRound: number;
  stampPoints: number;
  rechallengePoints: number;
  completeBonus: number;
  organizerNm?: string;
}) {
  const isMultiOrg = useAuthStore().getIsMultiOrg();

  return (
    <div className="relative h-[296px]">
      <div className="absolute flex justify-center items-center">
        <img
          src="/images/walking-course/stamp-complete-bg.png"
          alt="stamp-complete"
          className="w-full"
        />
      </div>
      <div className="absolute w-[318px] h-[64px] top-[8px] left-[50%] translate-x-[-50%]">
        <img
          src="/images/walking-course/course-name-bg.png"
          alt="course-name"
          className="w-full h-full"
        />
        <h1 className="absolute w-full top-[15px] text-center font-bold">{title}</h1>
      </div>
      {((maxRound === 1 && stampPoints !== 0) || (maxRound !== 1 && rechallengePoints !== 0)) && (
        <div className="absolute bottom-4 left-0 right-0 mx-[24px] text-center rounded-2xl bg-white shadow-lg px-6">
          <div className="text-lg font-bold mt-2">
            <b className="text-primary">
              {maxRound === 1 ? stampPoints + completeBonus : rechallengePoints}
            </b>
            ポイントゲット
          </div>
          <div className="flex flex-col items-center justify-center h-auto rounded-full bg-primary-5 px-4 py-1 mb-2 text-xs">
            {isMultiOrg && <span className="mb-1">{organizerNm}</span>}
            <div className="flex items-center justify-center gap-1">
              <PointIcon />
              <span>1スタンプ</span>
              <span className="text-primary ml-1">
                {maxRound === 1 ? stampPoints : rechallengePoints}
              </span>
              <span className="mr-1">P</span>
              {maxRound === 1 && completeBonus !== 0 && (
                <>
                  <span> / コンプリート</span>
                  <span className="text-primary ml-1">{completeBonus}</span>
                  <span> P</span>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
