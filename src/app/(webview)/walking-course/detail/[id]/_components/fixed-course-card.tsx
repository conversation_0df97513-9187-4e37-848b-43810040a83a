'use client';

import { PinIcon } from '@/app/(webview)/walking-course/_components/svg-icon';
import { TextButton } from '@/components/shared/text-button';
import { COLORS } from '@/const/colors';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { useFixedCourseStore } from '@/store/fixed-course';

interface FixedCourseCardProps {
  onUnpin?: () => void;
  className?: string;
}

export function FixedCourseCard({ onUnpin, className }: FixedCourseCardProps) {
  const { fixedCourse, clearFixedCourse } = useFixedCourseStore();
  const safeArea = useSafeArea();

  if (!fixedCourse) return null;
  const handleUnpin = () => {
    clearFixedCourse();
  };

  return (
    <div
      style={{
        boxShadow: '4px 8px 16px 0px rgba(0, 0, 0, 0.25)',
        top: safeArea.top + 8,
      }}
      className={cn(
        'fixed gap-2 h-[55px] px-4 top-0 w-[calc(100vw-48px)] left-1/2 -translate-x-1/2 flex items-center justify-between bg-card rounded-lg shadow-sm ',
        className,
      )}
    >
      <PinIcon size={20} fill={COLORS.primary.DEFAULT} />
      <div className="min-w-0 flex-1">
        <h3 className="text-sm truncate">{fixedCourse?.name}</h3>
        <p className="text-xs text-gray-60">固定表示中</p>
      </div>
      <TextButton onClick={handleUnpin}>解除</TextButton>
    </div>
  );
}
