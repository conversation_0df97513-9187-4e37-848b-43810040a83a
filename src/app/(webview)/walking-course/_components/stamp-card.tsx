'use client';

import { COLORS } from '@/const/colors';
import { cn } from '@/lib/utils';
import type { SortedStampSpotItemData, StampSpotCardData } from '@/types/walking-course';
import { useEffect, useMemo, useRef, useState } from 'react';
import { StampFinishIcon } from './svg-icon';

export function StampCard({
  data,
  className,
  hideCourseName,
}: { data: StampSpotCardData; className?: string; hideCourseName?: boolean }) {
  const [isStampComplete, setIsStampComplete] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setIsStampComplete(data.isCompleted);
  }, [data.isCompleted]);

  // 高さを計算（2行まで固定、それ以上はスクロール）
  const dynamicHeight = useMemo(() => {
    const rows = Math.ceil(data.stampList.length / 5);

    if (rows <= 2) {
      return 176; // 2行以内
    }
    return 188; // 3行以上でスクロール表示
  }, [data]);

  return (
    <div
      className={cn(
        'mx-auto w-[320px] rounded-[24px] overflow-hidden shadow-[0px_0px_8px_0px_rgba(0,0,0,0.15)]',
        className,
      )}
    >
      {/* ヘッダー */}
      <div className={cn('py-2 px-6', isStampComplete ? 'bg-primary' : 'bg-primary-5')}>
        <div className="flex items-center">
          <div
            className={cn('font-bold text-[22px]', isStampComplete ? 'text-white' : 'text-primary')}
          >
            {data.runCount}
          </div>
          <div className={cn('flex-1 ml-1', isStampComplete ? 'text-white' : '')}>周目</div>
          {isStampComplete && (
            <div className="bg-white text-primary px-4 py-0.5 rounded-2xl text-xs font-bold">
              STAMP COMPLETE!
            </div>
          )}
        </div>
        {!isStampComplete && !hideCourseName && (
          <div className={cn('text-sm', isStampComplete ? 'text-white' : 'text-gray-60')}>
            {data.courseName}
          </div>
        )}
      </div>

      <div className="border-t border-dashed border-gray-30" />

      {/* スタンプグリッド（スクロールエリア） */}
      <div className="bg-primary-5">
        <div
          ref={scrollAreaRef}
          className="overflow-y-auto mr-1"
          style={{
            height: `${dynamicHeight}px`,
            scrollbarWidth: 'thin',
            scrollbarColor: '#cbd5e1 transparent',
          }}
        >
          <div className="px-4 pt-4 pb-2">
            <div className="grid grid-cols-5 gap-2">
              {data.stampList.map((stamp, index) => (
                <Point key={`${stamp.spotId}-${index}`} index={index + 1} data={stamp} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function Point({ data, index }: { data: SortedStampSpotItemData; index: number }) {
  const { runCount, createdAt, stampPoints, rechallengePoints, completeBonus, isGoal, isChecked } =
    data;

  // ポイントがない場合
  if ((runCount !== 1 && !rechallengePoints) || (runCount === 1 && !stampPoints)) {
    // 最終日ではない & スタンプ未獲得
    if (!isGoal && !isChecked) {
      return (
        <div className="w-12">
          <div className="flex flex-col items-center border border-dashed border-gray-30 justify-center bg-white rounded-full w-12 h-12">
            <span className="text-gray-30 font-bold text-lg">{index}</span>
          </div>
          <div className="text-xs mt-0.5 text-center h-[18px]" />
        </div>
      );
    }

    // 最終日ではない & スタンプ獲得済み
    if (!isGoal && isChecked) {
      return (
        <div className="w-12">
          <div className="flex flex-col relative items-center border-gray-30 justify-center bg-primary rounded-full w-12 h-12">
            <span className="text-white text-xs font-bold transform -rotate-[20deg] scale-90">
              STAMP
            </span>
            <div className="border border-primary-50 rounded-full w-[44px] h-[44px] absolute top-[2px] left-[2px]" />
          </div>
          <div className="text-xs mt-0.5 text-center">{createdAt}</div>
        </div>
      );
    }

    // Bonusなし
    if (runCount !== 1 || !completeBonus) {
      // 最終日 & スタンプ未獲得
      if (isGoal && !isChecked) {
        return (
          <div className="w-12">
            <div className="flex flex-col items-center border border-dashed border-gray-30 justify-center bg-white rounded-full w-12 h-12">
              <span className="text-gray-30 font-bold text-sm">GOAL</span>
            </div>
            <div className="text-xs mt-0.5 text-center h-[18px]" />
          </div>
        );
      }

      // 最終日 & スタンプ獲得済み
      if (isGoal && isChecked) {
        return (
          <div className="w-12 flex flex-col items-center">
            <div className="w-12 flex flex-col items-center">
              <div className="relative w-12 h-12">
                <StampFinishIcon size={48} fill={COLORS.primary.DEFAULT} />
                <span className="absolute inset-0 flex items-center justify-center text-white text-xs font-bold transform -rotate-[20deg] scale-90">
                  CLEAR!
                </span>
              </div>
              <div className="text-xs mt-0.5 text-center">{createdAt}</div>
            </div>
          </div>
        );
      }
    }
  }

  // ポイントがある場合
  if (
    (runCount === 1 && (stampPoints !== 0 || completeBonus !== 0)) ||
    (runCount !== 1 && rechallengePoints !== 0)
  ) {
    const point = runCount === 1 ? stampPoints : rechallengePoints;
    const bonus = runCount === 1 ? completeBonus : 0;
    const tight = point && bonus;

    // 最終日ではない & スタンプ獲得済み
    if (isChecked && !isGoal) {
      return (
        <div className="w-12">
          <div className="flex flex-col relative items-center border-gray-30 justify-center bg-primary rounded-full w-12 h-12">
            <span className="text-white text-xs mt-[-2px]">{point}p</span>
            <span className="text-white text-xs font-bold">GET!</span>
            <div className="border border-primary-50 rounded-full w-[44px] h-[44px] absolute top-[2px] left-[2px]" />
          </div>
          <div className="text-xs mt-0.5 text-center">{createdAt}</div>
        </div>
      );
    }
    // 最終日ではない & スタンプ未獲得
    if (!isGoal && !isChecked) {
      return (
        <div className="w-12">
          <div className="flex flex-col items-center border border-dashed border-gray-30 justify-center bg-white rounded-full w-12 h-12">
            <span className="text-gray-30 font-bold">{index}</span>
            <span className="text-gray-60 text-xs mt-[-4px]">{point}p</span>
          </div>
          <div className="text-xs mt-0.5 text-center h-[18px]" />
        </div>
      );
    }
    // 最終日 & スタンプ未獲得
    if (isGoal && !isChecked) {
      return (
        <div className="w-12 flex flex-col items-center">
          <div className="relative w-12 h-12">
            <div className="flex flex-col items-center border border-dashed border-gray-30 justify-center bg-white rounded-full w-12 h-12">
              <span className="text-gray-30 font-bold text-xs">GOAL</span>
              {point !== 0 && (
                <span className={`text-gray-60 text-xs ${tight ? 'mt-[-2px]' : 'mt-[-4px]'}`}>
                  {point}p
                </span>
              )}
              {bonus !== 0 && (
                <span className={`text-gray-30 text-xs ${tight ? 'mt-[-2px]' : 'mt-[-4px]'}`}>
                  +{bonus}p
                </span>
              )}
            </div>
          </div>
        </div>
      );
    }
    // 最終日 & スタンプ獲得済み
    if (runCount === 1 && isChecked) {
      return (
        <div className="w-12 flex flex-col items-center">
          <div className="relative w-12 h-12">
            <StampFinishIcon size={48} fill={COLORS.primary.DEFAULT} />
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <span className="text-white text-xs mt-[-2px]">{point}p</span>
              <span className="text-white text-xs font-bold">GET!</span>
            </div>
          </div>
          <div className="text-xs mt-0.5 text-center">{createdAt}</div>
        </div>
      );
    }
  }
}
