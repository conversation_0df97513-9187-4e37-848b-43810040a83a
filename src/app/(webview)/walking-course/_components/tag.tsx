'use client';

import { COLORS } from '@/const/colors';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/store/auth';
import { ChallengeType } from '@/types/walking-course';
import { PointIcon, StampIcon, StepIcon, TimeIcon, WalkingIcon } from './svg-icon';

/**
 * ウォーキングコース用の橙色タグ
 */
export function WalkingCourseTypeTag() {
  const textColor = '#C2560E';
  const bgColor = '#FFFAF7';
  const iconColor = '#FF945F';
  const borderColor = '#FF945F';
  const className = cn(`bg-[${bgColor}] text-[${textColor}] border-[${borderColor}]`);
  return (
    <span
      className={cn(
        'inline-flex h-5 items-center border rounded-[4px] text-xs select-none px-1',
        className,
      )}
      aria-label="ウォーキングコース"
    >
      <WalkingIcon fill={iconColor} size={16} />
      ウォーキングコース
    </span>
  );
}

const challengeTypeTextMap = {
  [ChallengeType.ONE_TIME]: '1回のみ挑戦可能',
  [ChallengeType.DAILY]: '複数回挑戦可能(1日1回)',
  [ChallengeType.WEEKLY]: '複数回挑戦可能(1週1回)',
  [ChallengeType.MONTHLY]: '複数回挑戦可能(1月1回)',
};

export function ChallengeTimeTag({ challengeType }: { challengeType: ChallengeType }) {
  const text = challengeTypeTextMap[challengeType];
  return (
    <span
      className="inline-flex items-center px-1 h-5 rounded-[4px] text-primary bg-primary-5 text-xs select-none"
      aria-label={text}
    >
      {text}
    </span>
  );
}

/**
 * 6スポット用の白底标签
 */
export function SpotTag({ count }: { count: number }) {
  return (
    <span
      className="inline-flex items-center px-3 py-2 bg-primary-5 rounded-full select-none h-10 text-sm"
      aria-label={`${count}スポット`}
    >
      {/* 印章アイコン（emoji仮） */}
      <StampIcon fill={COLORS.primary[100]} />
      {count}スポット
    </span>
  );
}

export function CourseInfoTag({
  walkingCourse,
  className,
}: {
  walkingCourse: {
    estimatedTimeHours: number;
    estimatedTimeMinutes: number;
    estimatedDistance: string;
    steps: number;
  };
  className?: string;
}) {
  const estimatedTime =
    walkingCourse.estimatedTimeHours > 0
      ? `約${walkingCourse.estimatedTimeHours}時${walkingCourse.estimatedTimeMinutes}分`
      : `約${walkingCourse.estimatedTimeMinutes}分`;
  return (
    <div className={cn('flex items-center rounded-full text-sm bg-primary-5 px-2', className)}>
      <TimeIcon size={18} fill={COLORS.primary[100]} />
      <span className="ml-0.5" aria-label="time">
        {estimatedTime}
      </span>
      <span className="flex-1" />
      <WalkingIcon size={18} fill={COLORS.primary[100]} />
      <span className="ml-0.5" aria-label="time">
        約{walkingCourse.estimatedDistance}km
      </span>
      <span className="flex-1" />
      <StepIcon size={18} fill={COLORS.primary[100]} />
      <span className="ml-0.5" aria-label="time">
        約{walkingCourse.steps.toLocaleString()}歩
      </span>
    </div>
  );
}

export function CoursePointInfoTag({
  walkingCourse,
  className,
}: {
  walkingCourse: {
    stampPoints: number;
    completeBonus: number;
    organizerNm: string;
  };
  className?: string;
}) {
  const isMultiOrg = useAuthStore().getIsMultiOrg();
  return (
    <div
      className={cn(
        'flex flex-col rounded-full text-sm bg-primary-5 px-2 py-1 w-fit',
        isMultiOrg ? 'h-12' : 'h-6',
        className,
      )}
    >
      {isMultiOrg && <span className="leading-none mb-0.5 mt-1">{walkingCourse.organizerNm}</span>}
      <div className="flex items-center">
        <PointIcon size={18} fill={COLORS.primary[100]} />
        <span className="ml-0.5" aria-label="time">
          1スタンプ
          <b className="font-bold text-primary">{walkingCourse.stampPoints}</b>P / コンプリート
          <b className="text-primary font-bold">{walkingCourse.completeBonus}</b>P
        </span>
      </div>
    </div>
  );
}
