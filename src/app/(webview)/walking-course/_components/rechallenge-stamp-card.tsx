'use client';

import { cn } from '@/lib/utils';
import { useEffect, useRef } from 'react';

export interface RechanllengeStampCardData {
  runCount: number;
  courseName: string; // コース名
  rechallengeDate: string; // 周目
  spotCount: number;
}

export function RechanllengeStampCard({
  data,
  className,
}: { data: RechanllengeStampCardData; className?: string }) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 高さ計算（2行以内/それ以上）
  const dynamicHeight = data.spotCount <= 10 ? 176 : 188;

  return (
    <div
      className={cn(
        'mx-auto w-[320px] rounded-[24px] overflow-hidden shadow-[0px_0px_8px_0px_rgba(0,0,0,0.15)]',
        className,
      )}
    >
      {/* ヘッダー */}
      <div className="py-2 px-6 bg-primary-5">
        <div className="flex items-center">
          <div className="font-bold text-[22px] text-primary">{data.runCount + 1}</div>
          <div className="flex-1 ml-1 font-bold">周目</div>
        </div>
        <div className="text-sm  text-gray-60">{data.courseName}</div>
      </div>

      <div className="border-t border-dashed border-gray-30" />

      {/* スタンプグリッド*/}
      <div className="relative bg-primary-5">
        <div
          ref={scrollAreaRef}
          className="overflow-hidden mr-1" // overflow-hidden
          style={{
            height: `${dynamicHeight}px`,
            // scrollbarWidth: 'thin',
            // scrollbarColor: '#cbd5e1 transparent',
          }}
        >
          <div className="px-4 pt-4 pb-2">
            <div className="grid grid-cols-5 gap-2 relative">
              {Array.from({ length: data.spotCount }, (_, i) => (
                <Point key={`re-stp-cd-${i}`} index={i + 1} />
              ))}
            </div>
          </div>
        </div>

        <div
          className="absolute inset-0 bg-primary-100 font-bold opacity-60 rounded-b-[24px] pointer-events-none"
          // pointer-events-none
        />

        <div className="absolute top-[65px] left-[47px] right-[47px]">
          <div className="bg-primary-5 px-4 py-2 rounded-full text-sm select-none text-center leading-snug">
            次は{data.rechallengeDate}以降に
            <br />
            チャレンジ可能です
          </div>
        </div>
      </div>
    </div>
  );
}

// スタンプポイント
function Point({ index }: { index: number }) {
  return (
    <div className="w-12">
      <div className="flex flex-col items-center border border-dashed border-gray-30 justify-center bg-white rounded-full w-12 h-12">
        <span className="text-gray-30 font-bold text-lg">{index}</span>
      </div>
      <div className="text-xs mt-0.5 text-center h-[18px]" />
    </div>
  );
}
