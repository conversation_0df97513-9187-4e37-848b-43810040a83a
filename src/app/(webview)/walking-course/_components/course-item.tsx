import { Distance } from '@/components/shared/distance';
import { TextButton } from '@/components/shared/text-button';
import { cn } from '@/lib/utils';
import type { StampSpotResponse } from '@/types/walking-course';
import { useStampSpotState } from '../_context/use-stamp-spot-state';

interface StampSpotItemProps {
  data: StampSpotResponse;
  className?: string;
}

export function StampSpotItem({ data, className }: StampSpotItemProps) {
  const {
    setShowStampSpotCardSlideOverMap,
    setStampSpotCardSlideOverMapCurrentStampId,
    setShowCourseDetail,
  } = useStampSpotState();
  const handleClick = () => {
    setShowStampSpotCardSlideOverMap(true);
    setShowCourseDetail(false);
    setStampSpotCardSlideOverMapCurrentStampId(String(data.spotId));
  };
  return (
    <div className={cn('flex items-center justify-between', className)}>
      <div className="flex items-center">
        <div className="mr-3 w-16 h-16 rounded-lg overflow-hidden flex items-center justify-center">
          <img
            src={data.imagePath || '/images/walking-course/stamp-spot-default.png'}
            alt="course-item-img"
            className="w-full"
          />
        </div>
        <div>
          <div>{data.spotName}</div>
          <div className="text-sm text-gray-60 mt-1">
            <Distance
              targetLocation={{ lat: Number(data.latitude), lng: Number(data.longitude) }}
            />
          </div>
        </div>
      </div>
      <TextButton size="sm" className="ml-3" onClick={handleClick}>
        地図で見る
      </TextButton>
    </div>
  );
}
