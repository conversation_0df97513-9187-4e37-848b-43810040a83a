import type {
  ParticipationResponse,
  ParticipationStampSpotCheckData,
  SortedStampSpotItemData,
  StampSpotCardData,
  WalkingCourseDetailResponse,
} from '@/types/walking-course';

export function getStampSpotCardListData(
  data: WalkingCourseDetailResponse,
): StampSpotCardData[] | undefined {
  if (!data.participations || data.participations.length === 0) {
    return [getStampSpotCardData(data)];
  }
  return data.participations.map((item) => {
    return getStampSpotCardData(data, item);
  });
}

export function getStampSpotCardData(
  resData: WalkingCourseDetailResponse,
  participation?: ParticipationResponse,
): StampSpotCardData {
  const fullParticipationData = getSortedStampSpotDataList(resData, participation);
  return {
    ...resData,
    runCount: participation?.challengeCount ?? 1,
    isCompleted: participation?.isCompleted ?? false,
    stampList: fullParticipationData,
  };
}

export function getSortedStampSpotDataList(
  resData: WalkingCourseDetailResponse,
  participation?: ParticipationResponse,
): SortedStampSpotItemData[] {
  const runCount = participation?.challengeCount ?? 1;
  const rechallengePoints = resData.rechallengePoints;
  const { stampPoints, completeBonus } = resData;
  const fullParticipationData = mergeParticipationData(resData, participation?.stampList ?? []);

  return [...fullParticipationData]
    .sort((a, b) => {
      // チェック済みを優先
      if (a.isChecked !== b.isChecked) {
        return a.isChecked ? -1 : 1;
      }
      // チェック済み同士は日付順
      if (a.isChecked && b.isChecked) {
        return new Date(a.createdAt ?? 0).getTime() - new Date(b.createdAt ?? 0).getTime();
      }
      // それ以外は spotId 順
      return a.spotId - b.spotId;
    })
    .map((item, index, arr) => {
      const isLast = index === arr.length - 1;

      // createdAt を MM/dd 形式に変換（最終日は関係なし）
      const formattedCreatedAt = item.createdAt
        ? new Date(item.createdAt).toLocaleDateString('ja-JP', {
            month: '2-digit',
            day: '2-digit',
          })
        : undefined;

      return {
        spotId: item.spotId,
        createdAt: formattedCreatedAt, // フォーマット済み日付
        isChecked: item.isChecked,
        stampPoints: stampPoints,
        completeBonus: completeBonus,
        isGoal: isLast,
        runCount: runCount,
        rechallengePoints: rechallengePoints,
      };
    });
}

export function mergeParticipationData(
  resData: WalkingCourseDetailResponse,
  existingStampSpotCheckDate: ParticipationStampSpotCheckData[],
): ParticipationStampSpotCheckData[] {
  const { stampSpotList } = resData;
  return stampSpotList.map((spot) => {
    const participationStamp = existingStampSpotCheckDate.find((s) => s.spotId === spot.spotId);
    if (participationStamp) {
      return participationStamp;
    }
    return {
      spotId: spot.spotId,
      isChecked: false,
      createdAt: '',
    };
  });
}
