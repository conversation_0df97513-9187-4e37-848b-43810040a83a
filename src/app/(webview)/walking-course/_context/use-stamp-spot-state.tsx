'use client';

import { useFixedCourseStore } from '@/store/fixed-course';
import type { FixedCourse } from '@/types/walking-course';
import { type ReactNode, createContext, useContext, useState } from 'react';

// スタンプスポット状態の型定義
interface StampSpotState {
  showCourseDetail: boolean;
  showStampSpotCardSlideOverMap: boolean;
  stampSpotCardSlideOverMapCurrentStampId: string | null;
  showStampSpotCardSlideDrawer: boolean;
  showStampPointDoneSlidePage: boolean;
  fixedCourse?: FixedCourse;
  latestStampPointCheckedTime: number;
}

// 状態更新関数の型定義
interface StampSpotActions {
  setShowCourseDetail: (show: boolean) => void;
  setShowStampSpotCardSlideOverMap: (show: boolean) => void;
  setStampSpotCardSlideOverMapCurrentStampId: (stampId: string | null) => void;
  setShowStampSpotCardSlideDrawer: (show: boolean) => void;
  setFixedCourse: (course: FixedCourse) => void;
  setShowStampPointDoneSlidePage: (show: boolean) => void;
  setLatestStampPointCheckedTime: (time: number) => void;
}

// Context の型定義
interface StampSpotContextType extends StampSpotState, StampSpotActions {}

// Context の作成
const StampSpotContext = createContext<StampSpotContextType | undefined>(undefined);

// Provider の Props 型定義
interface StampSpotProviderProps {
  children: ReactNode;
}

/**
 * スタンプスポット状態管理 Provider
 * walking course detail ページの状態を管理する
 * fixedCourse は Zustand store で永続化される
 */
export const StampSpotProvider = ({ children }: StampSpotProviderProps) => {
  // UI 状態は useState で管理
  const [showCourseDetail, setShowCourseDetail] = useState(true);
  // スタンプスポットカード
  const [showStampSpotCardSlideOverMap, setShowStampSpotCardSlideOverMap] = useState(false);
  const [stampSpotCardSlideOverMapCurrentStampId, setStampSpotCardSlideOverMapCurrentStampId] =
    useState<string | null>(null);
  // コース内押せるスタンプスポット
  const [showStampSpotCardSlideDrawer, setShowStampSpotCardSlideDrawer] = useState(false);
  const [showStampPointDoneSlidePage, setShowStampPointDoneSlidePage] = useState(false);
  const [latestStampPointCheckedTime, setLatestStampPointCheckedTime] = useState(0);

  // fixedCourse は Zustand store で管理（永続化される）
  const { fixedCourse, setFixedCourse } = useFixedCourseStore();

  const value: StampSpotContextType = {
    showCourseDetail,
    showStampSpotCardSlideOverMap,
    stampSpotCardSlideOverMapCurrentStampId,
    showStampSpotCardSlideDrawer,
    showStampPointDoneSlidePage,
    fixedCourse,
    latestStampPointCheckedTime,
    setLatestStampPointCheckedTime,
    setShowCourseDetail,
    setShowStampSpotCardSlideOverMap,
    setStampSpotCardSlideOverMapCurrentStampId,
    setShowStampSpotCardSlideDrawer,
    setFixedCourse,
    setShowStampPointDoneSlidePage,
  };

  return <StampSpotContext.Provider value={value}>{children}</StampSpotContext.Provider>;
};

/**
 * スタンプスポット状態管理 Hook
 * Provider の外で使用した場合エラーを投げる
 */
export function useStampSpotState(): StampSpotContextType {
  const context = useContext(StampSpotContext);

  if (context === undefined) {
    throw new Error('useStampSpotState は StampSpotProvider の中で使用する必要があります');
  }

  return context;
}
