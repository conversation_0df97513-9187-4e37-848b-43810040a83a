'use client';

import { friendAP<PERSON> } from '@/api/modules/friend';
import { Button } from '@/components/shared/button';
import QaIcon from '@/components/shared/qa-icon';
import { TextButton } from '@/components/shared/text-button';
import { Input } from '@/components/ui/input';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import { useSlidePage } from '@/hooks/use-slide-page';
import { useAuthStore } from '@/store/auth';
import { type FriendRanking, ProcessType } from '@/types/friend';
import { getShareLink } from '@/utils/get-share-link';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import About from './_components/about';
import Empty from './_components/empty';
import FriendList from './_components/friend-list';
import Photos from './_components/photos';
import RemoveFriends from './_components/remove-friends';
import { FRIEND_COUNT_LIMIT } from './_const/index';

// グループ（フレンド）画面
export default function FriendPage() {
  const safeArea = useSafeArea();
  const [friendData, setFriendData] = useState<FriendRanking[]>([]);
  const [friendCount, setFriendCount] = useState(0);
  const { setDialog } = useMessageDialog();
  const { user } = useAuthStore();
  const { setSlidePage } = useSlidePage();
  const [pageSize, setPageSize] = useState(10);
  const fetchFriendData = useCallback((size: number) => {
    friendAPI
      .setpRanking({
        page: 1,
        size,
      })
      .then((res) => {
        if (res.friendList && res.friendList.length > 0) {
          setFriendData(res.friendList);
        } else {
          setFriendData([]);
        }
        if (res.friendInfo.friendCount) {
          // 自分を含める
          setFriendCount(res.friendInfo.friendCount + 1);
        } else {
          setFriendCount(0);
        }
      });
  }, []);

  useEffect(() => {
    fetchFriendData(10);
  }, [fetchFriendData]);

  const handleAfterDelete = useCallback(() => {
    fetchFriendData(pageSize);
    scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, [fetchFriendData, pageSize]);

  const handleYellClick = useCallback(
    (id: number, hasSentYell: boolean) => {
      friendAPI
        .sendCancelYell({
          friendUserId: id,
          processType: hasSentYell ? ProcessType.CANCEL_YELL : ProcessType.SEND_YELL,
        })
        .then(() => {
          setFriendData(
            friendData.map((item) =>
              item.friendUserId === id ? { ...item, hasSentYell: !hasSentYell } : item,
            ),
          );
        });
    },
    [friendData],
  );

  const handleSendInviteLink = useCallback(() => {
    friendAPI.inviteLink().then((res) => {
      const uuid = res.inviteInfo.uuid;
      // setDialog(true, {
      //   title: 'フレンド招待コード',
      //   content: <div className="text-center text-lg font-bold">{uuid}</div>,
      // });
      const shareLink = getShareLink({
        fcode: uuid,
      });
      sendMessageToNative({
        type: 'share',
        data: {
          shareType: 'text',
          title: 'フレンド招待',
          content: shareLink,
        },
        callback: () => {},
      });
    });
  }, [setDialog]);

  const handleShowMore = useCallback(() => {
    setPageSize(pageSize + 10);
    fetchFriendData(pageSize + 10);
  }, [fetchFriendData, pageSize]);

  const handleShowInviteCodeDialog = useCallback(() => {
    setDialog(true, {
      title: 'フレンド招待コード入力',
      content: <InviteCodeDialog />,
    });
  }, [setDialog]);

  return (
    <div className="mx-6" style={{ paddingTop: safeArea.top }}>
      <h1 className="flex items-center mt-4">
        <span className="font-bold text-[1.75rem] leading-[1.5]">フレンド</span>
        <Button
          aria-label="friend tips"
          size="xs"
          className="mr-4 ml-2 relative"
          variant="icon"
          onClick={() => {
            setSlidePage(true, {
              title: 'フレンドについて',
              content: <About />,
            });
          }}
        >
          <QaIcon />
        </Button>
      </h1>
      {friendData.length > 0 ? (
        <FriendList
          friendCount={friendCount}
          data={friendData}
          user={user}
          onYellClick={handleYellClick}
          onShowMore={handleShowMore}
        />
      ) : (
        <Empty />
      )}
      <div className="mt-6 pb-6">
        {friendCount < FRIEND_COUNT_LIMIT && (
          <Button className="w-full" onClick={handleSendInviteLink}>
            フレンドリクエストを送る
          </Button>
        )}
        {friendData.length > 0 ? (
          <TextButton
            variant="destructive"
            className="w-full mt-4"
            onClick={() => {
              setSlidePage(true, {
                title: 'フレンドを解除',
                content: (
                  <RemoveFriends
                    afterDelete={() => {
                      handleAfterDelete();
                    }}
                  />
                ),
                isOverAll: true,
                enableClose: true,
                enableBack: false,
                slideFrom: 'bottom',
              });
            }}
          >
            フレンドを解除
          </TextButton>
        ) : null}
        {/* <Button className="w-full mt-4" variant="muted" onClick={handleShowInviteCodeDialog}>
          フレンドを招待する
        </Button> */}
      </div>
      <Photos />
    </div>
  );
}

function InviteCodeDialog() {
  const [uuid, setUuid] = useState('');
  const addFriendByCode = (uuid: string) => {
    friendAPI.addFriendByUUID(uuid).then(() => {
      toast.success('フレンドを招待しました');
    });
  };
  return (
    <div className="text-center text-lg font-bold">
      <Input
        className="w-full"
        value={uuid}
        onChange={(e) => setUuid(e.target.value)}
        placeholder="フレンド招待コードを入力してください"
      />
      <Button className="w-full mt-4" onClick={() => addFriendByCode(uuid)}>
        フレンドを招待する
      </Button>
    </div>
  );
}
