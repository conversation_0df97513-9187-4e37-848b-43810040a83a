import { friend<PERSON><PERSON> } from '@/api/modules/friend';
import { Button } from '@/components/shared/button';
import { Checkbox } from '@/components/shared/checkbox';
import { TextButton } from '@/components/shared/text-button';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSlidePage } from '@/hooks/use-slide-page';
import type { Friend } from '@/types/friend';
import { useEffect, useState } from 'react';
import TextTrim from './text-trim';

// 解除するフレンドを選択するコンポーネント
export default function RemoveFriends({
  afterDelete,
}: {
  afterDelete?: () => void;
}) {
  const [data, setData] = useState<Friend[]>([]);
  const { setDialog } = useMessageDialog();
  const { setSlidePage } = useSlidePage();
  const [selectedFriends, setSelectedFriends] = useState<Friend[]>([]);
  const handleSelect = (friend: Friend) => {
    setSelectedFriends([...selectedFriends, friend]);
  };
  const handleUnselect = (friend: Friend) => {
    setSelectedFriends(selectedFriends.filter((f) => f.friendUserId !== friend.friendUserId));
  };
  const handleDelete = () => {
    friendAPI
      .cancelFriend({
        arrFriendUserId: selectedFriends.map((f) => f.friendUserId),
      })
      .then(() => {
        afterDelete?.();
        setSlidePage(false);
        setDialog(false);
      });
  };

  useEffect(() => {
    friendAPI.friends().then((res) => {
      setData(res.friendList);
    });
  }, []);

  return (
    <div className="mx-6">
      <div className="mt-6">解除するフレンドを選択してください。</div>
      <div className="bg-card rounded-2xl mt-2">
        {data.map((item, index) => (
          // biome-ignore lint/a11y/useKeyWithClickEvents: <explanation>
          <div
            className="flex items-center border-gray-200 border-b last:border-none"
            key={item.friendUserId}
            onClick={() => {
              if (selectedFriends.includes(item)) {
                handleUnselect(item);
              } else {
                handleSelect(item);
              }
            }}
          >
            <Checkbox className="ml-6" checked={selectedFriends.includes(item)} />
            <div className="flex items-center ml-4 py-3 flex-1">
              <div className="relative h-[51px]">
                {item.iconUrl && <img src={item.iconUrl} alt="level" width={40} height={40} />}
                {!item.iconUrl && (
                  <img src={'/images/friend/level3.png'} alt="level" width={40} height={40} />
                )}
                <div className="absolute bg-card bottom-0 left-0 right-0 height-4 text-[0.625rem] border-primary border rounded-sm text-center">
                  LV.{String(item.level || 1)}
                </div>
              </div>
              <div className="ml-2">
                <div className="flex items-center">
                  <span className="text-sm font-bold">
                    <TextTrim text={item.nickname} maxLength={10} allowLength={3} />
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className="flex flex-col mt-6 pb-6">
        <Button
          className="w-full"
          variant="destructive"
          disabled={selectedFriends.length === 0}
          onClick={() => {
            setDialog(true, {
              title: 'フレンドを解除してよろしいですか？',
              content: 'お互いにフレンドが解除されます。',
              outSideClickClose: true,
              footer: (
                <div className="flex flex-col">
                  <Button
                    className="w-full"
                    variant="destructive"
                    onClick={() => {
                      handleDelete();
                    }}
                  >
                    解除する
                  </Button>
                  <TextButton
                    className="w-full mt-4"
                    variant="muted"
                    onClick={() => {
                      setDialog(false);
                    }}
                  >
                    キャンセル
                  </TextButton>
                </div>
              ),
            });
          }}
        >
          フレンドを解除する ({selectedFriends.length}人)
        </Button>
        <TextButton
          variant="muted"
          className="w-full mt-4"
          onClick={() => {
            setSlidePage(false);
          }}
        >
          キャンセル
        </TextButton>
      </div>
    </div>
  );
}
