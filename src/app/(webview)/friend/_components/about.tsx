'use client';

import { TextButton } from '@/components/shared/text-button';
import { ChevronDown } from 'lucide-react';

const About = () => {
  const handleScrollTo = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  const sections = [
    {
      id: 'friend-what',
      title: 'フレンドとは',
      content:
        '身近な仲間と歩数を共有できる機能です。フレンドと切磋琢磨しながら健康を維持しましょう。',
    },
    {
      id: 'step-ranking',
      title: '歩数ランキングについて',
      content:
        '過去7日間の歩数が多い順にフレンドを並べたものです。お互いの歩数を見て、頑張ったり応援したりしましょう。',
    },
    {
      id: 'yell-what',
      title: 'エールとは',
      content:
        '追加したフレンドに、エール（応援）の気持ちを送ることができます。エールを送信すると相手に応援メッセージが届き、フレンドからエールを受け取ることも可能です。エールが届いた場合、フレンドリストにメッセージが表示されます。',
    },
    {
      id: 'add-friend',
      title: 'フレンドの追加方法',
      content:
        'QRコードスキャンまたはフレンドコードの入力でフレンドを追加できます。お互いにフレンド申請を送り合うことで、フレンド関係が成立します。',
    },
  ];

  return (
    <div className="bg-card pb-12 pt-4">
      <div className="flex flex-col gap-2 items-start px-6">
        {sections.map((section) => (
          <TextButton key={section.id} onClick={() => handleScrollTo(section.id)}>
            <span>{section.title}</span>
            <ChevronDown className="w-5 h-5" />
          </TextButton>
        ))}
      </div>

      <div className="px-6">
        {sections.map((section, index) => (
          <section key={section.id} id={section.id} className="py-6 border-b">
            <h2 className="text-2xl font-bold text-gray-700">{section.title}</h2>
            <p className="text-gray-700 leading-relaxed text-base">{section.content}</p>
          </section>
        ))}
      </div>
    </div>
  );
};

export default About;
