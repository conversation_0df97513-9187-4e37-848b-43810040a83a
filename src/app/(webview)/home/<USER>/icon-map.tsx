import {
  MenuCouponIcon,
  MenuEventIcon,
  MenuGraphIcon,
  MenuHealthCheckIcon,
  MenuMissionIcon,
  MenuRankingIcon,
  MenuRecordIcon,
  MenuResultIcon,
  MenuScoreIcon,
  MenuUserSettingIcon,
  MenuWalkingIcon,
} from '@/components/icons/menu-icons';
import type { IconKey } from '@/types/home-data';
import React from 'react';

export const iconMap: Record<IconKey, JSX.Element> = {
  graph: <MenuGraphIcon />,
  score: <MenuScoreIcon />,
  mission: <MenuMissionIcon />,
  ranking: <MenuRankingIcon />,
  walking: <MenuWalkingIcon />,
  event: <MenuEventIcon />,
  coupon: <MenuCouponIcon />,
  userSetting: <MenuUserSettingIcon />,
  healthCheck: <MenuHealthCheckIcon />,
  result: <MenuResultIcon />,
  record: <MenuRecordIcon />,
};
