import { cn } from '@/lib/utils';
import { Heart } from 'lucide-react'; // 或使用其他图标库
import { useState } from 'react';

export function ImageGrid({ className }: { className?: string }) {
  const bingImages = [
    {
      id: 1,
      url: 'https://tse4-mm.cn.bing.net/th/id/OIP-C.XwZyeu6zVK6Fv3Z7uU8mWAHaEo?w=284&h=180&c=7&r=0&o=5&dpr=1.1&pid=1.7',
      alt: '棕色小狗',
      likes: 42,
      liked: false,
    },

    {
      id: 3,
      url: 'data:image/jpeg;base64,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',
      alt: '美味披萨',
      likes: 28,
      liked: false,
    },
    {
      id: 4,
      url: 'https://tse2-mm.cn.bing.net/th/id/OIP-C.4wKHi3vlts7LYu0aqwcKbAHaE7?w=268&h=180&c=7&r=0&o=5&dpr=1.1&pid=1.7',
      alt: '湖景日落',
      likes: 65,
      liked: false,
    },

    {
      id: 6,
      url: 'https://tse1-mm.cn.bing.net/th/id/OIP-C.TgVSfsAd0qUWijRljisosgHaE7?w=283&h=188&c=7&r=0&o=5&dpr=1.1&pid=1.7',
      alt: '海滩风光',
      likes: 47,
      liked: false,
    },
    {
      id: 7,
      url: 'https://tse4-mm.cn.bing.net/th/id/OIP-C.ewYIAvoWnlojZplZ1ciyKwHaE7?w=283&h=189&c=7&r=0&o=5&dpr=1.1&pid=1.7',
      alt: '草莓甜点',
      likes: 31,
      liked: false,
    },
    {
      id: 8,
      url: 'https://img95.699pic.com/photo/30067/7524.jpg_wh860.jpg',
      alt: '秋季落叶',
      likes: 39,
      liked: false,
    },
    {
      id: 5,
      url: 'https://tse2-mm.cn.bing.net/th/id/OIP-C.VMRc6JbCbw7qh6vlrkLptQHaE7?w=296&h=197&c=7&r=0&o=5&dpr=1.1&pid=1.7',
      alt: '森林景观',
      likes: 53,
      liked: false,
    },
    {
      id: 9,
      url: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?ixlib=rb-4.0.3&w=600',
      alt: '儿童玩耍',
      likes: 24,
      liked: false,
    },
    {
      id: 2,
      url: 'https://tse1-mm.cn.bing.net/th/id/OIP-C.7uP5kfPBq2pXjr07AQLwjQHaHe?w=174&h=194&c=7&r=0&o=5&dpr=1.1&pid=1.7',
      alt: '橙色猫咪',
      likes: 36,
      liked: false,
    },
  ];
  const [images, setImages] = useState(bingImages);
  //「いいね!」のクリックを処理
  const handleLike = (id: number) => {
    setImages(
      images.map((img) =>
        img.id === id
          ? {
              ...img,
              liked: !img.liked,
              likes: img.liked ? img.likes - 1 : img.likes + 1,
            }
          : img,
      ),
    );
  };

  return (
    <div className={cn('grid grid-cols-3 gap-2 mt-2 max-w-2xl mx-auto', className)}>
      {images.map((image) => (
        <div key={image.id} className="relative aspect-square">
          <img src={image.url} alt={image.alt} className="w-full h-full object-cover" />
          <button
            type="button"
            onClick={() => handleLike(image.id)}
            className="absolute bottom-3 left-2 bg-white bg-opacity rounded-full px-1 py-px flex items-center justify-center shadow-sm"
          >
            <Heart
              fill={image.liked ? '#ff0000' : 'transparent'}
              stroke="#ff0000"
              className="w-3.5 h-3.5"
            />
            <span className="text-black text-xs ml-px font-medium leading-none">{image.likes}</span>
          </button>
        </div>
      ))}
    </div>
  );
}
