import { homePageAPI } from '@/api/modules/home-page';
import { Button } from '@/components/shared/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';
import { COMMON_TEXT } from '@/const/text/common';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import type {
  GetLoginBonusPointResponse,
  GetPhotosClaimResponse,
  PopupInfo,
  PopupInfoDetails,
} from '@/types/home-data';
import { formatNumberWithCommas } from '@/utils/string-format';
import type React from 'react';
import { useEffect, useMemo, useState } from 'react';

export interface ScoreListType {
  key: string;
  value: unknown;
}

// バイタルデータ入力ボーナスリスト
export const VITAL_DATA_POINT_LIST: { [key: string]: string } = {
  bloodPressure: '血圧',
  weight: '体重',
  fat: '体脂肪率',
  sleepTime: '睡眠時間',
  bloodGlucose: '血糖値',
};

// 起動ボーナ
export const LOGIN_BONUS_POINT: { [key: string]: string } = {
  firstLaunchPoint: '初回アプリ起動',
  dailyFirstLaunchPoint: '当日初回アプリ起動',
  conLaunchPointList: '連続{}日間アプリ起動',
};

export const PointDialogComponent = ({
  title,
  pointList = [],
  scoreList = [],
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  className = '',
}: {
  title: string;
  pointList?: PopupInfoDetails[];
  scoreList?: ScoreListType[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm?: () => void;
  onCancel?: () => void;
  className?: string;
}) => {
  const router = useRouter();
  const { setLoading } = useLoading();

  useEffect(() => {}, []);

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent
        className={`"sm:max-w-[425px] w-[calc(100vw-48px)] rounded-3xl bg-white pt-6 pb-8 px-8 ${className}`}
      >
        <div>
          <img
            className="w-[64px] mx-auto"
            src="/images/misson/point-finish.svg"
            alt="mission-info"
          />
          <div className="text-center font-bold text-[22px] mt-1">
            {title}
            <br />
            {pointList?.length < 2 && (
              <span className="font-bold text-primary ">
                {pointList?.length > 0 && pointList[0].point}
              </span>
            )}
            ポイントゲット
          </div>

          {/* 要因です */}
          {scoreList?.length > 0 && (
            <div className=" bg-[#F6F8FF] mt-2 px-6 py-4 rounded-2xl">
              {scoreList?.map((item, index) => (
                <div key={`score${index}`} className="flex items-start ">
                  <div className="h-2 w-2 bg-primary rounded-full mt-[7px] mr-3 flex-shrink-0" />
                  <div className="font-bold text-base text-primary">
                    {/* {VITAL_DATA_POINT_LIST[item.key ?? '']} */}
                    {item.key}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 団体です */}
          {pointList?.length > 1 && (
            <div className="bg-[#F6F8FF] mt-2 rounded-2xl ">
              {pointList?.map((item, index) => (
                <div
                  key={`pointDialog${index}`}
                  className="grid grid-cols-[1fr_71px] min-h-[46px] max-h-[58px] items-center border-border border-b mx-6 last:border-none"
                >
                  <div className="font-normal text-sm line-clamp-2 overflow-hidden text-ellipsis">
                    {item.organizerName}
                  </div>
                  <div className="text-right">
                    <span className="font-bold text-[20px] text-primary ">
                      {formatNumberWithCommas(Number(item.point))}
                    </span>
                    <span className="font-normal text-base ">p</span>
                  </div>
                </div>
              ))}
            </div>
          )}
          <Button onClick={handleCancel} className=" w-full  mt-4">
            {COMMON_TEXT.BUTTON.CLOSE}
          </Button>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
};
