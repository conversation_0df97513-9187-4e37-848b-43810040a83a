import { Card, CardContent } from '@/components/ui/card';
export function CouponSlider() {
  const promotions = [
    {
      id: 1,
      logo: 'ファミリーマート',
      title: '50円引きクーポン',
      description: '対象商品購入で50円割引',
      date: '2025/4/30まで',
      bgColor: 'bg-blue-50',
    },
    {
      id: 2,
      logo: 'マクドナルド',
      title: 'ポテトMサイズ 10円引',
      description: 'Mサイズポテト購入で10円割引',
      date: '2025/4/30まで',
      bgColor: 'bg-green-50',
    },
    {
      id: 3,
      logo: 'コメダ珈琲',
      title: 'モーニング ポン',
      description: '朝食メニュー対象割引',
      date: '2025/4/30まで',
      bgColor: 'bg-yellow-50',
    },
  ];

  return (
    <div className="relative">
      {/* 横向滚动容器 */}
      <div className="flex overflow-x-auto pb-4 px-6 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        <div className="flex space-x-1 mt-2">
          {promotions.map((promo) => (
            <Card key={promo.id} className={'border-0 rounded-2xl w-[134px] flex-shrink-0'}>
              <CardContent className="p-4 ">
                <div className="flex justify-center">
                  <div className="w-20 h-20">
                    <img
                      className="w-full h-full object-contain"
                      src="/images/mission-coin.png"
                      alt="coupon-info"
                    />
                  </div>
                </div>
                <div className="">
                  <p className="text-lg font-semibold">{promo.title}</p>
                  <p className="text-sm text-gray-500 mt-2">{promo.date}</p>
                  <p className="text-sm text-gray-500 truncate">{promo.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
