import { cn } from '@/lib/utils';
import { ChevronRight } from 'lucide-react';

interface TitleWithLinkProps {
  mainTitle: string;
  subTitle?: string;
  onSubTitleClick?: () => void;
  className?: string;
}

export function TitleWithLink({
  mainTitle,
  subTitle,
  onSubTitleClick,
  className,
}: TitleWithLinkProps) {
  return (
    <div className={cn('flex items-center justify-between  px-6 mt-6', className)}>
      <h2 className="text-lg font-bold text-black">{mainTitle}</h2>

      {subTitle && (
        <button
          type="button"
          onClick={onSubTitleClick}
          className="flex items-center gap-1 text-primary  transition-colors"
        >
          <span className="text-sm text-primary font-bold">{subTitle}</span>
          <ChevronRight className="text-primary h-4 w-4" />
        </button>
      )}
    </div>
  );
}
