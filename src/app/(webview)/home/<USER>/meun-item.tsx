export function MenuItemButtons() {
  const buttons = [
    { id: 1, label: '横浜スポーツ情報サイトハマ' },
    { id: 2, label: '外部サービスの名称が入ります' },
    { id: 3, label: '横浜スポーツ情報サイトハマ' },
  ];

  return (
    <div className="relative">
      <div className="flex overflow-x-auto pb-4 px-6 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        <div className="flex justify-between space-x-2">
          {buttons.map((button) => (
            <button
              type="button"
              key={button.id}
              className="flex items-center w-[152px] h-18 rounded-2xl p-3 bg-white shadow-sm"
            >
              <img
                className="w-12 w-12 object-contain"
                src="/images/mission-coin.png"
                alt="coupon-info"
              />
              <span className="text-ms text-gray-900 ml-2 line-clamp-2">{button.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
