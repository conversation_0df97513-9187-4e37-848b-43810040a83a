'use client';
import { useRouter } from '@/hooks/use-next-navigation';
import { usePointStore } from '@/store/point';
import type { OrganizerInfoBean } from '@/types/home-data';
import useEmblaCarousel from 'embla-carousel-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { IS_TRUE } from '../../point/_const';
import { formatNumberWithCommas } from '../../point/_utils';

interface CarouselProps {
  slides: OrganizerInfoBean[];
  initialIndex?: number;
  onSlideChange?: (index: number) => void;
}

interface DebouncedFunction<T extends (...args: any[]) => void> {
  (...args: Parameters<T>): void;
  cancel: () => void;
}

const debounce = <T extends (...args: any[]) => void>(
  fn: T,
  delay: number,
): DebouncedFunction<T> => {
  let timeoutId: NodeJS.Timeout;

  const debouncedFn = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };

  debouncedFn.cancel = () => {
    clearTimeout(timeoutId);
  };

  return debouncedFn;
};

export default function SwipingAreaBanner({
  slides,
  initialIndex = 0,
  onSlideChange,
}: CarouselProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'center',
    startIndex: initialIndex,
    skipSnaps: false,
    dragFree: false,
    loop: false,
  });
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const initializedRef = useRef(false);
  const router = useRouter();
  const { setHomeCardInfo } = usePointStore();

  // 使用防抖处理滑动变化
  const handleSlideChange = useCallback(
    debounce((newIndex: number) => {
      setCurrentIndex(newIndex);
      onSlideChange?.(newIndex);
    }, 150),
    [onSlideChange],
  );

  // 初始化Embla和事件监听
  useEffect(() => {
    if (!emblaApi) return;

    const updateIndex = () => {
      const newIndex = emblaApi.selectedScrollSnap();
      setCurrentIndex(newIndex);
      initializedRef.current = true;
    };

    updateIndex();

    const handleEmblaSelect = () => {
      const newIndex = emblaApi.selectedScrollSnap();
      handleSlideChange(newIndex);
    };

    emblaApi.on('select', handleEmblaSelect);

    return () => {
      emblaApi.off('select', handleEmblaSelect);
      (handleSlideChange as DebouncedFunction<typeof handleSlideChange>).cancel();
    };
  }, [emblaApi, handleSlideChange]);

  // 处理外部initialIndex变化
  useEffect(() => {
    if (!emblaApi || !initializedRef.current) return;
    if (emblaApi.selectedScrollSnap() !== initialIndex) {
      emblaApi.scrollTo(initialIndex);
    }
  }, [initialIndex, emblaApi]);

  const scrollTo = (index: number) => {
    emblaApi?.scrollTo(index);
  };

  // 生成稳定的key值
  const getStableKey = (slide: OrganizerInfoBean, index: number): string => {
    // 尝试使用organizerId作为key，如果不存在则使用index
    return slide.organizerId ? `slide-${slide.organizerId}` : `slide-index-${index}`;
  };

  return (
    <div className="max-w-3xl mx-auto pt-7">
      <div className="overflow-visible mb-0">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex gap-4">
            <div className="flex-[0_0_10%] min-w-0" aria-hidden="true" />
            {slides.map((slide, index) => (
              <div key={getStableKey(slide, index)} className="flex-[0_0_70%] min-w-0">
                {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                <div
                  className="bg-white rounded-2xl shadow-md p-2 h-16 flex items-center"
                  onClick={() => {
                    if (slide?.pointAuthority === IS_TRUE.YES && index === currentIndex) {
                      setHomeCardInfo(slide);
                      router.push('/point');
                    }
                  }}
                >
                  {slide?.availablePoint ? (
                    <>
                      <img
                        className="w-12 h-12 mr-1"
                        src={slide.characterUrl || '/images/mission-coin.png'}
                        alt="icon"
                      />
                      <div className="items-centerx w-full">
                        <div className="text-gray-600 text-sm font-medium">
                          {slide.organizerName || '-'}
                        </div>
                        <div className="flex items-baseline justify-end">
                          <span className="text-black text-2xl font-bold mr-2">
                            {formatNumberWithCommas(slide.availablePoint)}
                          </span>
                          <span className="text-black text-2xl font-bold">p</span>
                          <span className="text-xs text-gray-500 px-2 py-0.5">
                            {`(${Number(slide.diffPoint) <= 0 ? slide.diffPoint : `+${slide.diffPoint}`})`}
                          </span>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex items-center w-full">
                      <img
                        className="w-12 h-12 mr-1"
                        src={slide.characterUrl || '/images/mission-coin.png'}
                        alt="icon"
                      />
                      <div className="text-gray-600 text-sm font-medium">{slide.organizerName}</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div className="flex-[0_0_10%] min-w-0" aria-hidden="true" />
          </div>
        </div>
      </div>
      {/*インジケータ */}
      {slides.length > 1 ? (
        <div className="flex justify-center space-x-2 mt-2">
          {slides.map((_, index) => (
            <button
              key={index}
              type="button"
              onClick={() => scrollTo(index)}
              className={`w-1.5 h-1.5 rounded-full transition-all ${
                index === currentIndex ? 'bg-white opacity-100' : 'bg-white opacity-30'
              }`}
              aria-current={index === currentIndex}
            />
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}
