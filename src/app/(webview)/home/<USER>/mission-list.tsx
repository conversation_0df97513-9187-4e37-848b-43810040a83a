import { cn } from '@/lib/utils';
import { Progress } from '../../../../components/ui/progress';

export interface ProgressItem {
  id: string;
  title: string;
  subtitle: string;
  status: 'in-progress' | 'completed';
  current?: number;
  total?: number;
  iconType: 'mountain' | 'p' | 'chart';
  pointText?: string;
}

interface ProgressListProps {
  items: ProgressItem[];
  className?: string;
}

export function ProgressList({ items, className }: ProgressListProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {items.map((item) => (
        <div key={item.id} className="flex items-center p-3 bg-white rounded-2xl shadow-sm">
          {/* アイコン部分-青の枠と文字*/}
          <div className="relative flex flex-col items-center w-16">
            <div className={cn('flex items-center justify-center w-12 h-12 rounded-full')}>
              {item.iconType === 'mountain' && (
                <img className="w-full" src="/images/home/<USER>" alt="mission-info" />
              )}
              {item.iconType === 'p' && (
                <img className="w-full" src="/images/mission-coin.png" alt="mission-info" />
              )}
              {item.iconType === 'chart' && (
                <img className="w-full" src="/images/home/<USER>" alt="mission-info" />
              )}
            </div>

            {/* 青い枠と文字 */}
            {item.pointText && (
              <div className="-mt-2 relative w-full flex justify-center px-1">
                <div className="bg-white w-full rounded-full border-2 border-blue-500 h-5 px-1 flex items-center justify-center">
                  <span className="text-[10px] text-black font-bold leading-none">
                    {item.pointText}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* 内容部分 */}
          <div className="flex-1">
            <h3 className="text-x text-black font-bold">{item.title}</h3>
            {item.subtitle && (
              <span className="text-xs text-primary bg-slate-200 px-1 rounded-sm">
                {item.subtitle}
              </span>
            )}
            {item.status === 'in-progress' && (
              <div className="flex items-center mt-1">
                <Progress
                  value={50}
                  className="grow h-2.5 mr-1 rounded-2xl bg-blue-50	"
                  indicatorClassName="bg-primary-light"
                />
                <p className="text-xs text-gray-500">
                  {item.current}/{item.total}
                </p>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
