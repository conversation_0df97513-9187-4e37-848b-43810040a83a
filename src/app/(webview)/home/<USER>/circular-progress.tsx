'use client';

import { COMMON_TEXT } from '@/const/text/common';
import { type VariantProps, cva } from 'class-variance-authority';
import * as React from 'react';

const circularProgress = cva('relative flex items-center justify-center', {
  variants: {
    size: {
      sm: 'w-24 h-24',
      md: 'w-32 h-32',
      lg: 'w-40 h-40',
      xl: 'w-48 h-48',
      xxl: 'w-56 h-56',
    },
  },
  defaultVariants: {
    size: 'lg',
  },
});

export interface CircularProgressProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof circularProgress> {
  value: number;
  max?: number;
  strokeWidth?: number;
  icon?: React.ReactNode;
  label?: string;
  showValue?: boolean;
  showMax?: boolean;
}

export const CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(
  (
    {
      className,
      size,
      value = 0,
      max = 100,
      strokeWidth = 8,
      icon,
      label,
      showValue = true,
      showMax = true,
      ...props
    },
    ref,
  ) => {
    const radius = 49 - strokeWidth / 2;
    const circumference = 2 * Math.PI * radius;
    const progress = Math.min(value, max);
    const offset = circumference - (progress / max) * circumference;

    return (
      <div ref={ref} className={circularProgress({ size, className })} {...props}>
        <svg className="absolute w-full h-full" viewBox="0 0 100 100">
          {/* Background circle */}
          <circle
            className="text-gray-200"
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="transparent"
            r={radius}
            cx="49"
            cy="49"
          />
          {/* Progress circle */}
          <circle
            className="text-primary-light"
            stroke="currentColor"
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            fill="transparent"
            r={radius}
            cx="49"
            cy="49"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            transform="rotate(-90 49 49)"
            style={{
              transition: 'stroke-dashoffset 0.5s ease-in-out',
            }}
          />
        </svg>

        <div className="flex flex-col items-center z-10">
          {icon && (
            <div className="flex items-center gap-1 mb-1">
              {icon}
              {label && <span className="text-sm font-normal text-black">{label}</span>}
            </div>
          )}
          {showValue && (
            <div className="w-full flex justify-center text-3xl font-bold text-gray-900">
              {value.toLocaleString()}
            </div>
          )}
          {showMax && (
            <div className="w-full flex justify-center text-sm font-normal text-black">
              / {`${max.toLocaleString()} ${COMMON_TEXT.HOME.STEPS_UNIT}`}
            </div>
          )}
        </div>
      </div>
    );
  },
);

CircularProgress.displayName = 'CircularProgress';
