'use client';
import { COMMON_TEXT } from '@/const/text/common';
interface FloatingActionButtonProps {
  handleRecordClick?: () => void; //
}
export function FloatingActionButton({ handleRecordClick }: FloatingActionButtonProps) {
  return (
    <div className="fixed bottom-20 right-4 z-50 bg-primary rounded-full">
      <button
        type="button"
        onClick={handleRecordClick}
        className="text-primary h-12 pl-4 pr-3 flex rounded-full items-center justify-center "
      >
        <img src={'/images/plus.svg'} alt="icon" className="mr-1" />
        <span className="text-base text-white font-bold">{COMMON_TEXT.HOME.ADD_RECORD}</span>
      </button>
    </div>
  );
}
