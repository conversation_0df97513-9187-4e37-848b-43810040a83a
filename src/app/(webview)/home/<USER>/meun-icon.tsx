import { useMiniAppHealthCheckupLink } from '@/hooks/use-health-checkup-link';
import { useRouter } from '@/hooks/use-next-navigation';
import type { MenuButton } from '@/types/home-data';
import { Calculator } from 'lucide-react'; // 使用类似计算器的图标
import { iconMap } from '../_utils/icon-map';

interface MenuIconButtonsProps {
  buttonList: MenuButton[];
}

export function MenuIconButtons({ buttonList }: MenuIconButtonsProps) {
  const router = useRouter();

  return (
    <div className="relative mt-2">
      <div className="flex overflow-x-auto pb-4 px-6 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        <div className="flex justify-between space-x-2">
          {buttonList.map((button) => {
            const iconNode = iconMap[button.iconKey];
            return (
              <button
                type="button"
                key={button.id}
                className="flex flex-col items-center w-20 rounded-2xl pt-[12px] pb-[10px] bg-white shadow-sm"
                onClick={() => {
                  if (button.callback) {
                    button.callback();
                  } else if (button.url) {
                    router.push(button.url);
                  }
                }}
              >
                {iconNode}
                <span className="text-xs mt-[6px] font-medium text-gray-900 h-[28px] inline-flex items-center px-1">
                  {button.label}
                </span>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
}
