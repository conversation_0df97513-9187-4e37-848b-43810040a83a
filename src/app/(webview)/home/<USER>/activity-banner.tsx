'use client';
import type { slidesActivity } from '@/types/home-data';
import useEmblaCarousel from 'embla-carousel-react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface CarouselProps {
  slides: slidesActivity[];
  initialIndex?: number;
  autoPlayInterval?: number;
}

export default function SwipingActivityBanner({
  slides,
  initialIndex = 0,
  autoPlayInterval = 3000,
}: CarouselProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'center',
    startIndex: initialIndex,
    loop: true,
  });
  const [selectedIndex, setSelectedIndex] = useState(initialIndex);
  const [isPlaying, setIsPlaying] = useState(true);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleSlideChange = useCallback(() => {
    if (!emblaApi) return;
    const newIndex = emblaApi.selectedScrollSnap();
    setSelectedIndex(newIndex);
  }, [emblaApi]);

  const autoPlay = useCallback(() => {
    if (!emblaApi || !isPlaying) return;

    if (timeoutRef.current) clearTimeout(timeoutRef.current);

    timeoutRef.current = setTimeout(() => {
      if (!emblaApi || !isPlaying) return;
      emblaApi.scrollNext();
      autoPlay();
    }, autoPlayInterval);
  }, [emblaApi, isPlaying, autoPlayInterval]);

  const togglePlayPause = useCallback(() => {
    setIsPlaying((prev) => {
      const newState = !prev;

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      if (newState) {
        autoPlay();
      }
      return newState;
    });
  }, [autoPlay]);

  useEffect(() => {
    if (!emblaApi) return;

    setSelectedIndex(emblaApi.selectedScrollSnap());
    emblaApi.on('select', handleSlideChange);

    if (isPlaying) {
      autoPlay();
    }

    return () => {
      emblaApi.off('select', handleSlideChange);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [emblaApi, handleSlideChange, autoPlay, isPlaying]);

  return (
    <div className="max-w-3xl mx-auto pt-6">
      <div className="overflow-visible mb-2">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex gap-4">
            {slides.map((slide, index) => (
              <div key={index} className={`flex-[0_0_80%] min-w-0  ${index === 0 ? 'pl-4' : ''}`}>
                <img src={slide.imgUrl} alt="activity-banner" className="w-full h-auto" />
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <div className="bg-white rounded-full pl-1 pr-2 py-1.5  flex items-center space-x-3">
          <button
            type="button"
            onClick={togglePlayPause}
            className="w-6 h-6 rounded-full flex items-center justify-center transition-colors bg-primary"
            aria-label={isPlaying ? 'stop' : 'start'}
          >
            {isPlaying ? (
              <div className="flex space-x-1">
                <span className="w-0.5 h-2 bg-white" />
                <span className="w-0.5 h-2 bg-white" />
              </div>
            ) : (
              <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6 text-white ml-0.5">
                <path d="M8 5v14l11-7z" />
              </svg>
            )}
          </button>

          <div className="flex space-x-2">
            {slides.map((_, index) => (
              <button
                type="button"
                key={index}
                className={`w-1.5 h-1.5 rounded-full ${
                  index === selectedIndex ? 'bg-primary-light' : 'bg-gray-300'
                }`}
                aria-label={`jump ${index + 1} page`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
