'use client';

import { COMMON_TEXT } from '@/const/text/common';
import type { StepInfoResponse } from '@/types/home-data';
import { ChevronRight } from 'lucide-react';
import { CircularProgress } from './circular-progress';

export interface ActivitySummaryCardProps {
  data: StepInfoResponse;
  title?: string;
  onGraphClick?: () => void;
}
export const ActivitySummaryCard = ({
  data,
  title,
  onGraphClick,
}: ActivitySummaryCardProps): JSX.Element => {
  return (
    <section className="flex justify-center items-center mx-6 mt-6">
      <div className="w-full bg-white rounded-2xl shadow-sm p-4">
        {/* Title */}
        <h2 className=" text-black text-center leading-6 mb-4 font-bold">{title}</h2>

        <div className="flex flex-row md:flex-row">
          {/* Step Circle - Left Side */}
          <div className="basis-6/12 flex justify-center item-start min-w-[164px] mr-3">
            <div className="relative w-full h-full">
              {/* Step Info */}
              <div className="absolute inset-0 flex flex-col items-center">
                {/* CircularProgress */}
                <div className="flex justify-center">
                  <CircularProgress
                    value={data.step}
                    max={data.stepTarget}
                    strokeWidth={7}
                    icon={<StepsIcon />}
                    label={COMMON_TEXT.HOME.STEPS}
                    showValue
                    showMax
                  />
                </div>
                {/* Weekly Achievement */}
                <div className="flex items-center justify-center gap-1 text-[16px] text-[#333] font-normal leading-[15px] mt-1">
                  <span>{COMMON_TEXT.HOME.WEEKLY_ACHIEVEMENT}</span>
                  <span className="text-[18px] font-bold leading-[21px] text-primary">
                    {data.targetAchieveDays}
                  </span>
                  <span>{COMMON_TEXT.HOME.DAYS_COMPLETED}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Metrics */}
          <div className="basis-6/12 flex flex-col justify-end gap-3">
            <MetricItem
              icon={<DistanceIcon />}
              label={COMMON_TEXT.HOME.WALKING_DISTANCE}
              value={data.distance ? data.distance : '0'}
              unit={COMMON_TEXT.HOME.KILOMETERS}
            />

            <MetricItem
              icon={<TimeIcon />}
              label={COMMON_TEXT.HOME.WALKING_TIME}
              value={data.exerciseTime ? data.exerciseTime : '0'}
              unit={COMMON_TEXT.HOME.MINUTES}
            />

            <MetricItem
              icon={<CalorieIcon />}
              label={COMMON_TEXT.HOME.CALORIES_BURNED}
              value={data.energy ? data.energy.toLocaleString() : '0'}
              unit={COMMON_TEXT.HOME.KILOCALORIES}
            />
          </div>
        </div>
        <div className="flex justify-center mt-6">
          <button
            type="button"
            onClick={onGraphClick}
            className="flex items-center gap-1 text-primary  transition-colors "
          >
            <span className="text-sm text-primary font-bold">{COMMON_TEXT.HOME.GRAPH_DETAIL}</span>
            <ChevronRight className="text-primary h-4 w-4" />
          </button>
        </div>
      </div>
    </section>
  );
};

// Helper component for metrics
const MetricItem = ({
  icon,
  label,
  value,
  unit,
}: {
  icon: React.ReactNode;
  label: string;
  value: string;
  unit: string;
}) => (
  <div className="gap-2">
    <div className="flex items-center">
      <div className="w-6 h-6 flex items-center justify-center">{icon}</div>
      <div className="text-sm  text-[#000] font-normal leading-[18px]">{label}</div>
    </div>
    <div className="ml-auto flex items-baseline justify-end mt-1">
      <span className="text-2xl font-bold text-black leading-none">{value}</span>
      <span className="text-base font-normal text-black leading-none ml-1">{unit}</span>
    </div>
  </div>
);

// SVG Icons
const StepsIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 16 16"
    fill="none"
    className="text-primary"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7767 9.18002C14.14 8.19335 13.0967 7.58001 11.6733 7.35335C11.2067 7.06001 8.72 5.38668 8.43 2.78001C8.41333 2.62335 8.32333 2.48668 8.19 2.40668C8.05666 2.32668 7.89 2.31335 7.74667 2.37335C7.72333 2.38335 5.47 3.27668 3.36667 2.37335C3.11333 2.26335 2.82 2.38335 2.71 2.63668L0.706666 7.30335C0.613332 7.52001 0.683332 7.77001 0.876666 7.90668L9.04666 13.74C9.13 13.8 9.23333 13.8333 9.33667 13.8333H14.7633C14.9867 13.8333 15.18 13.6867 15.2433 13.4733C15.2733 13.3733 15.9433 10.9933 14.7733 9.18002H14.7767ZM3.43667 3.47001C5.09 4.01668 6.7 3.72335 7.53333 3.49001C7.63333 3.94335 7.79 4.36001 7.97333 4.75335L6.41667 5.00668C6.14333 5.05001 5.96 5.30668 6.00333 5.58001C6.04333 5.82668 6.25666 6.00001 6.49667 6.00001C6.52333 6.00001 6.55 6.00001 6.57666 5.99335L8.49333 5.68335C8.49333 5.68335 8.5 5.68335 8.50333 5.68335C8.61666 5.85001 8.73333 6.01001 8.85666 6.16335L7.75 6.34335C7.47667 6.38668 7.29333 6.64335 7.33667 6.91668C7.37667 7.16335 7.59 7.33668 7.83 7.33668C7.85666 7.33668 7.88333 7.33668 7.91 7.33001L9.66 7.04668C9.83667 7.21335 10.0067 7.36668 10.1667 7.50001L9.27666 7.64335C9.00333 7.68668 8.82 7.94335 8.86333 8.21668C8.90333 8.46335 9.11666 8.63668 9.35666 8.63668C9.38333 8.63668 9.41 8.63668 9.43666 8.63001L11.3533 8.32001C11.3533 8.32001 11.3667 8.31668 11.3733 8.31335C11.39 8.31668 11.4033 8.32668 11.4233 8.33001C12.5967 8.49668 13.4367 8.96335 13.9267 9.72001C14.2533 10.22 14.39 10.7967 14.4367 11.3367H10.3667L2.45667 5.76002L3.43667 3.47001ZM9.49667 12.8333L1.78333 7.32668L2.05333 6.70002L9.92667 12.2433C10.01 12.3033 10.11 12.3333 10.2133 12.3333H14.43C14.41 12.5267 14.3867 12.6967 14.36 12.8333H9.49333H9.49667Z"
      fill="currentColor"
    />
  </svg>
);

const DistanceIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 16 16"
    fill="none"
    className="text-primary"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.04667 3.52271C8.74667 3.52271 9.31001 2.95721 9.31001 2.26198C9.31001 1.56675 8.74334 1.00125 8.04667 1.00125C7.35001 1.00125 6.78334 1.56675 6.78334 2.26198C6.78334 2.95721 7.35001 3.52271 8.04667 3.52271Z"
      fill="currentColor"
    />
    <path
      d="M12.13 6.73276L10.4167 6.36352L8.18666 4.13812C8.05666 4.00839 7.86666 3.95849 7.69 4.01171L4.06 5.09614C3.82333 5.166 3.67666 5.39552 3.70666 5.63835L4.00333 7.88039C4.03666 8.12987 4.25 8.31283 4.5 8.31283C4.52333 8.31283 4.54333 8.31283 4.56666 8.3095C4.84 8.27291 5.03333 8.02342 4.99666 7.75065L4.75666 5.93108L6.33 5.46205L5.68333 8.8883C5.68333 8.8883 5.67667 8.90161 5.67333 8.91159L4.35 14.0144C4.28 14.2805 4.44 14.5533 4.71 14.6231C4.97666 14.693 5.25 14.5333 5.32 14.2639L6.46666 9.83967L8.35666 11.0937L8.66667 14.5133C8.69 14.7728 8.91 14.9657 9.16333 14.9657C9.18 14.9657 9.19333 14.9657 9.21 14.9657C9.48666 14.9391 9.68667 14.6996 9.66333 14.4235L9.33 10.7644C9.31666 10.6147 9.23333 10.4784 9.11 10.3952L7.92 9.60682L8.60666 5.96767L9.81333 7.17185C9.88 7.23838 9.96667 7.28495 10.06 7.30824L11.9167 7.71074C12.19 7.77061 12.4533 7.59764 12.51 7.32819C12.57 7.05875 12.3967 6.79263 12.1267 6.73608L12.13 6.73276Z"
      fill="currentColor"
    />
  </svg>
);

const TimeIcon = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 16 16"
    fill="none"
    className="text-primary"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1_185)">
      <path
        d="M8 0.498962C3.86333 0.498962 0.5 3.85536 0.5 7.9835C0.5 12.1116 3.86333 15.468 8 15.468C12.1367 15.468 15.5 12.1116 15.5 7.9835C15.5 3.85536 12.1367 0.498962 8 0.498962ZM8 14.4701C4.41667 14.4701 1.5 11.5594 1.5 7.9835C1.5 4.40755 4.41667 1.4969 8 1.4969C11.5833 1.4969 14.5 4.40755 14.5 7.9835C14.5 11.5594 11.5833 14.4701 8 14.4701Z"
        fill="currentColor"
      />
      <path
        d="M8.5 7.77726V3.16013C8.5 2.88404 8.27667 2.66116 8 2.66116C7.72333 2.66116 7.5 2.88404 7.5 3.16013V7.9835C7.5 8.11656 7.55333 8.24296 7.64667 8.3361L10.1467 10.8309C10.2433 10.9274 10.3733 10.9773 10.5 10.9773C10.6267 10.9773 10.7567 10.9274 10.8533 10.8309C11.05 10.6347 11.05 10.3187 10.8533 10.1257L8.5 7.77726Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_1_185">
        <rect width="16" height="15.967" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const CalorieIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-primary"
  >
    <path
      d="M11.02 6.08409C8.51 4.69363 9.56334 1.93599 9.61 1.81957C9.69334 1.60667 9.62334 1.36384 9.44 1.23078C9.25667 1.09773 9.00334 1.10438 8.82667 1.25074C4.98667 4.44414 5.73 6.5631 6.17667 7.83048C6.25 8.04338 6.36667 8.36937 6.34 8.4492C6.34 8.4492 6.28334 8.52571 6 8.60887C5.97 8.61885 5.95667 8.61885 5.92334 8.59557C5.53667 8.33278 5.18334 6.9523 5.08 5.82463C5.06 5.60508 4.89667 5.42212 4.68 5.38221C4.46334 5.34229 4.24334 5.44541 4.14 5.64167C2.62334 8.54234 2.39334 10.8975 3.45334 12.6439C3.87667 13.3391 4.46 13.8514 5.07667 14.2273C5.10334 14.2472 5.13667 14.2672 5.16667 14.2805C6.35 14.9691 7.63667 15.1653 8.17 15.1653C9.68 15.1653 10.9533 14.7429 11.88 13.9745C11.95 13.9379 12.0067 13.8846 12.05 13.8214C12.67 13.2493 13.12 12.5141 13.3467 11.6326C13.9067 9.47375 12.93 7.1419 11.0233 6.08409H11.02ZM5.91 13.552C6.02667 12.8035 6.51334 10.9274 8.49334 10.9274C10.4733 10.9274 10.98 12.6173 11.1033 13.2925C10.36 13.8514 9.35334 14.1641 8.16667 14.1641C7.83 14.1641 6.85334 14.0343 5.91 13.552ZM12.3767 11.3831C12.2767 11.769 12.12 12.1183 11.9133 12.431C11.51 11.2501 10.5167 9.93281 8.49334 9.93281C6.3 9.93281 5.33 11.606 5.00334 12.9532C4.73667 12.7204 4.49667 12.4476 4.30334 12.1283C3.60667 10.9806 3.62334 9.44381 4.35667 7.55438C4.55 8.30616 4.86 9.09121 5.35667 9.42718C5.63667 9.61679 5.95667 9.66669 6.28 9.57355C6.58667 9.48373 7.02 9.31741 7.22667 8.91158C7.45667 8.46584 7.29334 8.00013 7.12 7.50781C6.78667 6.5631 6.31 5.19592 8.35667 3.05368C8.30334 4.37429 8.75334 5.97432 10.5367 6.9656C12.05 7.80387 12.8267 9.66669 12.3767 11.3898V11.3831Z"
      fill="currentColor"
    />
  </svg>
);
