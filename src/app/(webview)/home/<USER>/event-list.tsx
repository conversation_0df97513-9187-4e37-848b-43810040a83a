import SimpleEventCard from '@/app/(webview)/event/_components/simple-event-card';

import { useRouter } from '@/hooks/use-next-navigation';
import type { SimpleEventListItem } from '@/types/event-types';

import { useCallback } from 'react';
interface EventListProps {
  events?: SimpleEventListItem[];
}

export function EventList({ events }: EventListProps) {
  const displayEvents = events || [];
  const router = useRouter();

  const handleEventClick = useCallback(
    (eventId?: string) => {
      if (eventId) {
        router.push(`/event/${eventId}?from=home`);
      }
    },
    [router],
  );
  return (
    <div className="max-w-md mx-auto space-y-2 px-6 mt-2">
      {displayEvents.map((event) => (
        <SimpleEventCard
          key={event.eventId}
          event={event}
          onClick={() => handleEventClick(event.eventId)}
        />
      ))}
    </div>
  );
}
