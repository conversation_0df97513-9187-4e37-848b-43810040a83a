'use client';
import { riskAPI } from '@/api/modules/health-score-init';
import { homePageAPI } from '@/api/modules/home-page';
import { missionAPI } from '@/api/modules/mission';
import { pointAPI } from '@/api/modules/point';
import { ActivitySummaryCard } from '@/app/(webview)/home/<USER>/activity-summary-card';
import SwipingAreaBanner from '@/app/(webview)/home/<USER>/area-banner';
import { FloatingActionButton } from '@/app/(webview)/home/<USER>/floating-action-button';
import { type ProgressItem, ProgressList } from '@/app/(webview)/home/<USER>/mission-list';
import { TitleWithLink } from '@/app/(webview)/home/<USER>/title-with-link';
import { LotteryAlertDialogComponent } from '@/app/lottery-result/_components/lottery-push-dialog';
import { Button } from '@/app/registration/_components/button';

import { eventAPI } from '@/api/modules/event-api';
import { orgGeoLocationAPI } from '@/api/modules/org-geo-location';
import HomeHeader from '@/components/layout/header';
import HealthRecordCreateButton from '@/components/shared/health-record-create-button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { THEMESCOLOR } from '@/const/themes';
import { useDynamicMenu } from '@/hooks/use-dynamic-menu';
import { useGeolocation } from '@/hooks/use-geolocation';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { usePathname, useRouter } from '@/hooks/use-next-navigation';
import { CONSTANTS } from '@/lib/constants';
import { useAuthStore } from '@/store/auth';
import { useGlobalStore } from '@/store/global';
import { useRegisterState } from '@/store/register';
import type { SimpleEventListItem } from '@/types/event-types';
import type {
  HomeEventList,
  MenuButton,
  OrganizerInfoBean,
  PopupInfoDetails,
  StepInfoResponse,
  StepPointInfo,
} from '@/types/home-data';
import type { MissionCardType, MissionPopupDetailResponse } from '@/types/mission';
import type { GetHomePointCardInfo } from '@/types/point';
import { formatDate } from '@/utils/date-format';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useTheme } from 'next-themes';
import { useCallback, useEffect, useRef, useState } from 'react';
import AlertDialogMission from '../mission/_components/alert-dialog-mission';
import StepAlertDialog from '../mission/_components/alert-dialog-step';
import MissionCard from '../mission/_components/mission-card';
import { mergeByOrganizerId } from '../point/_utils';
import SwipingActivityBanner from './components/activity-banner';
import { CouponSlider } from './components/coupon-list';
import { EventList } from './components/event-list';
import { ImageGrid } from './components/image-grid';
import { MenuIconButtons } from './components/meun-icon';
import { MenuItemButtons } from './components/meun-item';
import {
  LOGIN_BONUS_POINT,
  PointDialogComponent,
  type ScoreListType,
} from './components/point-dialog';

export default function HomePage() {
  const { theme, setTheme } = useTheme();
  const { setIsRiskEnabled } = useGlobalStore();
  const router = useRouter();
  const { user } = useAuthStore.getState();
  const { token } = useAuthStore.getState();
  const { refreshButtons, buttonList } = useDynamicMenu();
  const { setOrgGeoLocationData } = useGeolocation();

  const {
    pointDialogTitle,
    setPointDialogTitle,
    pointList,
    setScoreList,
    scoreList,
    setPointList,
    setUser,
  } = useAuthStore();
  const pathname = usePathname();
  const isInitialMount = useRef(true);
  //ミッション達成
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const popupContentRef = useRef<MissionPopupDetailResponse>();

  //歩数達成
  const [stepOpenDialog, setStepOpenDialog] = useState<boolean>(false);
  const stepPopupContentRef = useRef<StepPointInfo>();

  const [stepInfo, setStepInfo] = useState<StepInfoResponse>({
    step: 0,
    stepTarget: 6000,
  });
  const [missionList, setMissionList] = useState<MissionCardType[]>();
  const [organizationIndex, setOrganizationIndex] = useState(0);
  const [pointCardList, setPointCardList] = useState<GetHomePointCardInfo[]>([]);
  const organizerListRef = useRef<OrganizerInfoBean[]>([]);
  const [isSelectCity, setSelectCity] = useState<OrganizerInfoBean>({
    organizerId: 11,
    color: 1,
    organizerName: '',
  });

  const [homeEventList, setHomeEventList] = useState<SimpleEventListItem[]>([]);
  const organizerUseCodes = user?.organizerCode ? user?.organizerCode.split(',') : [];
  // const { openLink: openHealthCheckupLink } = useMiniAppHealthCheckupLink();
  // const refreshButtons = useMenuStore((s) => s.refreshButtons);
  // const { buttonList } = useMenuStore();

  const [show, setShow] = useState(false);
  function handleCloseDialog() {
    if (show) {
      setShow(!show);
    }
  }

  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showPhotoDialog, setShowPhotoDialog] = useState(false);
  const handleCloseCreateDialog = () => {
    setShowCreateDialog(false);
  };
  const handleClosePhotoDialog = () => {
    setShowPhotoDialog(false);
  };

  const { homeDialog, setHomeDialog } = useRegisterState();
  const { setDialog } = useMessageDialog();

  // ホームでのポイントカード情報取得API
  const getHomePointCardInfo = async () => {
    // setIsLoading(true);
    try {
      const result = await pointAPI.getHomePointCardInfo();
      console.log('result', result, organizerListRef.current);
      setPointCardList(result?.pointCardList ?? []);
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
      // setIsLoading(false);
    }
  };

  //歩数を更新 API
  const fetchStepInfo = useCallback(() => {
    nlog(`NEXT_PUBLIC_APP_ENV:${process.env.NEXT_PUBLIC_APP_ENV}`);
    // setIsLoading(true);
    homePageAPI
      .getStepInfo({})
      .then((response) => {
        if (response != null && response != null) {
          setStepInfo(response);
        }
      })
      .catch((error) => {});
  }, []);
  const setPresentOrgan = useCallback((orginizerID: number) => {
    homePageAPI
      .setOrganizerId({ defOrganizerId: orginizerID })
      .then((response) => {
        if (response != null && response.data != null) {
        }
      })
      .catch((error) => {});
  }, []);
  // 今日達成ミッション POPUP画面情報を取得する
  const getPopupData = () => {
    missionAPI.missionPopupDetail().then((res) => {
      if (res) {
        popupContentRef.current = res;
        setOpenDialog(true);
      }
    });
  };
  const fetchMissionInfo = useCallback(() => {
    // setIsLoading(true);
    homePageAPI
      .getHomeMission({ sourceType: 1 })
      .then((response) => {
        if (response != null && response != null) {
          if (response.achievedMissionExistFlg) {
            getPopupData();
          }
          setMissionList(response.missionList);
        }
      })
      .catch((error) => {});
  }, []);
  //ホームページ 主催団体
  const fetchData = useCallback(async () => {
    try {
      // setIsLoading(true);
      const response = await homePageAPI.getOrganizerList({});
      if (response?.organSettingInfo) {
        // const listData = response.organSettingInfo;
        const listData = [...mergeByOrganizerId(response.organSettingInfo, pointCardList)];
        // console.log('listData', response.organSettingInfo, listData);
        const groupIndex = listData.findIndex(
          (item) => item.organizerId.toString() === user?.useOrganizerID,
        );

        if (groupIndex > -1) {
          const normalData = listData[groupIndex];
          const color = THEMESCOLOR.get(normalData.color ?? 1) ?? 'theme-blue';
          setTheme(color);
          setOrganizationIndex(groupIndex);
          setSelectCity(normalData);
        }
        organizerListRef.current = listData;
        // setIsLoading(false);
      }
    } catch (error) {
      console.log('Error fetching organizer list:', error);
    }
  }, [pointCardList]);

  useEffect(() => {
    // if (isInitialMount.current) {
    //   isInitialMount.current = false;
    //   return; // 初回レンダリング時は実行しない
    // }

    if (pathname === '/home') {
      syncSteps(); // ルート変更時にトリガーされる
    }
  }, [pathname]);

  function syncSteps() {
    sendMessageToNative({
      type: 'sync-steps',
      callback: (data) => {
        nlog(JSON.stringify(data));
        if (data?.details) {
          setStepOpenDialog(true);
          stepPopupContentRef.current = data;
        } else {
          setStepOpenDialog(false);
        }

        //同期が完了したら歩数を更新
        fetchStepInfo();
      },
    });
  }
  //ホームページ 主催団体
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  //ホーム ミッション
  useEffect(() => {
    fetchMissionInfo();
  }, [fetchMissionInfo]);

  useEffect(() => {
    getHomePointCardInfo();
    getHomeEventCardInfo();
    getLoginBonusPoint();
    getPhotosClaim();
    // useMenuStore.setState({ openHealthCheckupLink });
    refreshButtons(organizerUseCodes);
    orgGeoLocationAPI.organizerLocations().then((res) => {
      setOrgGeoLocationData(res);
    });
  }, []);
  // ホームでのイベント情報取得API
  const getHomeEventCardInfo = async () => {
    // setIsLoading(true);
    try {
      const result = await eventAPI.getHomeEvents();
      if (result?.eventList) {
        setHomeEventList(result?.eventList);
      }
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
      // setIsLoading(false);
    }
  };
  useEffect(() => {
    nlog(`homeDialog ${homeDialog}`);
    if (homeDialog) {
      setDialog(true, {
        title: APP_TEXT.REGISTRATION_CREATE.CONFIRM_TITLE,
        content: (
          <div className="text-base font-normal">
            <img src="/images/create/finish.svg" alt="complete" className="mb-2" />
            {APP_TEXT.REGISTRATION_CREATE.CONFIRM_CONTENT}
          </div>
        ),
        outSideClickClose: true,
        footer: (
          <Button
            className="w-full"
            type="button"
            onClick={() => {
              setHomeDialog(false);
            }}
          >
            {COMMON_TEXT.BUTTON.CLOSE}
          </Button>
        ),
      });

      setDialog(false);
    }
  }, [homeDialog]);

  const activities = [
    {
      id: 1,
      url: '',
      imgUrl: '/images/header/banner_1.svg',
    },
    {
      id: 1,
      url: '',
      imgUrl: '/images/header/banner_2.svg',
    },
    { id: 1, url: '', imgUrl: '/images/header/banner_3.svg' },
  ];

  const handleSlideChange = (index: number) => {
    const selectOrgan = organizerListRef.current[index];

    if (selectOrgan) {
      const id = selectOrgan.organizerId;
      setSelectCity(selectOrgan);
      const color = THEMESCOLOR.get(selectOrgan.color ?? 1) ?? 'theme-blue';
      setTheme(color);

      if (user?.id !== undefined) {
        const updatedUser = {
          ...user,
          useOrganizerID: id.toString(),
        };
        setUser(updatedUser);
        //
        sendMessageToNative({
          type: 'user-info',
          data: {
            ...updatedUser,
            userOrganizerID: id.toString(),
            userID: user?.id,
            token: token,
          },
        });
      }
      setPresentOrgan(id);
    }
  };
  const handleGraphClick = () => {
    const today = new Date();
    const formattedDate = formatDate(today, 'yyyy-MM-dd');
    router.push(`/graph?tab=step&date=${formattedDate}`);
  };
  const handleClick = () => {};
  const missionHandleClick = () => {
    router.push('/mission');
  };
  const handleEventClick = () => {
    router.push('/event');
  };

  const handleMenuClick = (id: number) => {
    if (id === 5) {
      router.push(ROUTES.HEALTH_CHECKUP.HEALTH_CHECKUP);
    }
  };

  const [pointCreateDialogTitle, setPointCreateDialogTitle] = useState<string>('');
  const [pointCreateList, setPointCreateList] = useState<PopupInfoDetails[]>([]);
  const [scoreCreateList, setScoreCreateList] = useState<ScoreListType[]>([]);
  const processDetails = (details: PopupInfoDetails[]) => {
    // pointが未定義の場合は0として計算
    const detailsWithPoint = details?.map((item) => ({
      ...item,
      point: (item?.firstPoint ?? 0) + (item?.dailyPoint ?? 0) + (item?.continuousPoint ?? 0),
    }));
    setPointCreateList(detailsWithPoint);

    // Merge and remove duplicate data
    const seen = new Set<string>();
    const scoreList: ScoreListType[] = [];

    for (const item of detailsWithPoint) {
      const keys = ['firstPointName', 'dailyPointName', 'continuousPointName'];
      const values = ['firstPoint', 'dailyPoint', 'continuousPoint'];

      keys?.forEach((keyName, index) => {
        // 型安全のため、keyNameは'firstPointName' | 'dailyPointName' | 'continuousPointName'型であることを明示
        const key = item[keyName as 'firstPointName' | 'dailyPointName' | 'continuousPointName'];
        const value = item[values[index] as 'firstPoint' | 'dailyPoint' | 'continuousPoint'];

        if (typeof key === 'string' && key.trim() !== '' && !seen.has(key)) {
          seen.add(key);
          scoreList.push({
            key:
              key === 'conLaunchPointList'
                ? `連続${item?.continuousNum}日間アプリ起動`
                : LOGIN_BONUS_POINT[key ?? ''],
            value,
          });
        }
      });
    }
    setScoreCreateList(scoreList);
  };

  // 起動ボーナスAPI
  const getLoginBonusPoint = () => {
    homePageAPI
      .getLoginBonusPoint()
      .then((response) => {
        if (response?.isPopupShow) {
          const result = response?.pointInfo?.details ?? [];
          processDetails(result);
          setPointCreateDialogTitle('アプリを起動!');
          setShowCreateDialog(true);
        }
      })
      .catch((error) => {
        // setShowCreateDialog(false);
        // setPointCreateList([]);
        // setScoreCreateList([]);
      });
  };

  // 写真ポイント付与API
  const getPhotosClaim = () => {
    homePageAPI
      .getPhotosClaim()
      .then((response) => {
        if (response?.isPopupShow) {
          const result = response?.pointList ?? [];
          const list = result
            ?.filter((item) => item.point)
            .map((item) => ({
              organizerName: item.organizerNm,
              point: item.point,
            }));
          setScoreList([]);
          setPointList(list);
          setPointDialogTitle('「いいね」100回ボーナスを獲得！');
          setShowPhotoDialog(true);
        }
      })
      .catch((error) => {
        // setShowPhotoDialog(false);
        // setScoreList([]);
        // setPointList([]);
      });
  };

  return (
    <>
      <HomeHeader organInfo={isSelectCity} />
      <div className="relative flex-1 overflow-auto pb-16">
        <div className="bg-primary-light text-primary-foreground py-4 pt-2">
          <SwipingAreaBanner
            slides={organizerListRef.current}
            initialIndex={organizationIndex}
            onSlideChange={handleSlideChange}
          />
        </div>
        {/* TODO  banner hide */}
        {/* <SwipingActivityBanner slides={activities} /> */}
        <ActivitySummaryCard
          data={stepInfo}
          title={COMMON_TEXT.HOME.TODAY}
          onGraphClick={handleGraphClick}
        />
        {missionList && (
          <TitleWithLink
            mainTitle={COMMON_TEXT.HOME.MITSUYOSO}
            subTitle={COMMON_TEXT.HOME.MORE}
            onSubTitleClick={missionHandleClick}
          />
        )}
        <div className="px-6 mt-2">
          {missionList?.map((item: MissionCardType, i: number) => (
            <MissionCard
              key={i}
              missionCardItem={item}
              routerFlag="home"
              category={item?.category}
            />
          ))}

          {/* <ProgressList items={missionItems} /> */}
        </div>
        {/* <FloatingActionButton handleRecordClick={handleRecordClick} /> */}
        {/* TODO hide */}

        <TitleWithLink mainTitle={COMMON_TEXT.HOME.MENU} />
        <MenuIconButtons buttonList={buttonList} />
        {/* <MenuItemButtons /> */}
        {/* <TitleWithLink
          mainTitle={COMMON_TEXT.HOME.PHOTO_UPLOAD}
          subTitle={COMMON_TEXT.HOME.MORE}
          onSubTitleClick={handleClick}
        />
        <ImageGrid /> */}

        {homeEventList?.length > 0 && (
          <TitleWithLink
            mainTitle={COMMON_TEXT.HOME.EVENT}
            subTitle={COMMON_TEXT.HOME.MORE}
            onSubTitleClick={handleEventClick}
          />
        )}
        <EventList events={homeEventList} />

        {/* <TitleWithLink
          mainTitle={COMMON_TEXT.HOME.COUPON}
          subTitle={COMMON_TEXT.HOME.MORE}
          onSubTitleClick={handleClick}
        />
        <CouponSlider /> */}
        {/* TODO hide end */}
        <HealthRecordCreateButton />
      </div>
      <AlertDialogMission
        open={openDialog}
        popupContent={popupContentRef.current}
        onCancel={() => {
          setOpenDialog(false);
        }}
      />
      <StepAlertDialog
        open={stepOpenDialog}
        popupContent={stepPopupContentRef.current}
        onCancel={() => {
          setStepOpenDialog(false);
        }}
      />
      <LotteryAlertDialogComponent
        open={show}
        onOpenChange={handleCloseDialog}
        onConfirm={() => {}}
        onCancel={() => {}}
        lotteryId={'20001'}
      />
      {/* TODO: Put into 起動page */}
      {/* 起動ボーナス */}
      <PointDialogComponent
        title={pointCreateDialogTitle}
        pointList={pointCreateList}
        scoreList={scoreCreateList}
        open={showCreateDialog}
        onOpenChange={handleCloseCreateDialog}
      />
      {/* 写真ポイント付与 */}
      <PointDialogComponent
        title={pointDialogTitle}
        pointList={pointList}
        // scoreList={scoreList}
        open={showPhotoDialog}
        onOpenChange={handleClosePhotoDialog}
      />
    </>
  );
}
