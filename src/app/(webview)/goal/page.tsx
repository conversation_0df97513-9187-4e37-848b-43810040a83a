'use client';

import { stepGoalAPI } from '@/api/modules/step-goal';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import type { StepTarget } from '@/types/step-goal';
import { useCallback, useEffect, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';
function Goal() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const [stepTarget, setStepTarget] = useState<string | undefined>('');
  const [distanceTarget, setDistanceTarget] = useState<string | undefined>('');
  const [exciseTime, setExciseTime] = useState<string | undefined>('');
  const [energyTarget, setEnergyTarget] = useState<string | undefined>('0');
  const [targetPlan, setTargetPlan] = useState<string | undefined>('');
  const [plan, setPlan] = useState<number | undefined>(0);

  const fetchStepTarget = useCallback(() => {
    setLoading(true, { text: 'データを通信中...' });
    stepGoalAPI
      .getStepTargetInfo()
      .then((response: StepTarget) => {
        setLoading(false);
        if (response !== null) {
          if (response?.stepTarget != null) {
            const stepTargetStr = String(response?.stepTarget);
            setStepTarget(stepTargetStr.replace(/\B(?=(\d{3})+(?!\d))/g, ','));
          }

          if (response?.distanceTarget != null) {
            const distanceNum = Math.floor(Number(response?.distanceTarget));
            if (distanceNum !== undefined) {
              setDistanceTarget(String(distanceNum).replace(/\B(?=(\d{3})+(?!\d))/g, ','));
            }
          }

          if (response?.exerciseTimeTarget != null) {
            setExciseTime(getTimeStrFromMinu(response?.exerciseTimeTarget));
          }

          if (response?.energyTarget !== undefined) {
            setEnergyTarget(String(response?.energyTarget).replace(/\B(?=(\d{3})+(?!\d))/g, ','));
          }
          if (response?.targetPlan === 1) {
            setTargetPlan(APP_TEXT.GOAL.REC);
          } else if (response?.targetPlan === 2) {
            setTargetPlan(APP_TEXT.GOAL.PLUSTEN);
          } else if (response?.targetPlan === 3) {
            setTargetPlan(APP_TEXT.GOAL.CUS);
          }
          setPlan(response?.targetPlan);
        }
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        return Promise.reject();
      });
  }, [setLoading]);

  useEffect(() => {
    fetchStepTarget();
  }, [fetchStepTarget]);

  const getTimeStrFromMinu = (minute: number) => {
    let result = '';
    if (minute >= 60) {
      const hour = Math.floor(minute / 60);
      const minu = minute % 60;
      if (minu === 0) {
        result = `${hour}時間`;
      } else {
        result = `${hour}時間${minu}分`;
      }
    } else {
      result = `${minute}分`;
    }
    return result;
  };

  const goPlan = async () => {
    const params = {
      item: plan,
    };
    const paramsStr = btoa(JSON.stringify(params));
    router.push(`${ROUTES.GOAL.PLAN}?data=${paramsStr}`);
  };

  const goDetail = async () => {
    const params = {
      item: plan,
    };
    const paramsStr = btoa(JSON.stringify(params));
    if (plan === 1) {
      router.push(`${ROUTES.GOAL.RECMMEND}?data=${paramsStr}&isBack=true`);
    } else if (plan === 2) {
      router.push(`${ROUTES.GOAL.PLUSTEN}?data=${paramsStr}`);
    } else if (plan === 3) {
      router.push(`${ROUTES.GOAL.CUSTOM}?data=${paramsStr}`);
    }
  };

  const goBack = async () => {
    if (router.includePathInHistory(ROUTES.GOAL.HEALTH_RECORD)) {
      router.backTo(ROUTES.GOAL.HEALTH_RECORD);
      return;
    }
    if (router.includePathInHistory(ROUTES.MENU.SETTINGS)) {
      router.backTo(ROUTES.MENU.SETTINGS);
      return;
    }
    if (router.includePathInHistory(ROUTES.MENU.MENU)) {
      router.backTo(ROUTES.MENU.MENU);
      return;
    }
    router.replace(ROUTES.HOME);
  };

  return (
    <div className="bg-white min-h-[calc(100vh-9rem)]">
      <TopBar title={APP_TEXT.GOAL.TITLE} enableBack={true} onBack={goBack} />
      <div className="flex flex-col items-start  ml-6 mr-6">
        <div className="w-full mb-[18px] p-[15px]  items-center rounded-[16px]  bg-primary-5 mt-4">
          <div className="text-[11px] text-white text-center bg-primary p-1 rounded-md w-[90px] mt-1">
            {APP_TEXT.GOAL.CHOICE}
          </div>
          <div className="flex-1 font-bold mt-1">{targetPlan}</div>
          <div className="h-[2px] bg-gray-300 transition-all duration-300 hover:w-full mt-[20px] mb-[20px]" />
          <div className="flex justify-between  mb-4">
            <span className="text-[16px] text-center">{APP_TEXT.GOAL.STEP_TITLE}</span>
            <div>
              <span className="text-[26px] font-bold text-center">
                {stepTarget === null || stepTarget === undefined ? '' : stepTarget}
              </span>
              <span className="text-[16px] text-center">{APP_TEXT.GOAL.STEP_UNIT}</span>
            </div>
          </div>
          <div className="flex justify-between  mb-4">
            <span className="text-[16px] text-center">{APP_TEXT.GOAL.CALORIE_TITLE}</span>
            <div>
              <span className="text-[16px] text-center">
                {energyTarget === undefined ? '' : energyTarget}
              </span>
              <span className="text-[16px] text-center">{APP_TEXT.GOAL.CALORIE_UNIT}</span>
            </div>
          </div>
          <div className="flex justify-between  mb-4">
            <span className="text-[16px] text-center">{APP_TEXT.GOAL.DIST_TITLE}</span>
            <div>
              <span className="text-[16px] text-center">
                {distanceTarget === null || distanceTarget === undefined ? '' : distanceTarget}
              </span>
              <span className="text-[16px] text-center">{APP_TEXT.GOAL.DIST_UNIT}</span>
            </div>
          </div>
          <div className="flex justify-between  mb-4">
            <span className="text-[16px] text-center">{APP_TEXT.GOAL.TIME_TITLE}</span>
            <div>
              <span className="text-[16px] text-center">
                {exciseTime === null ? '' : exciseTime}
              </span>
              <span className="text-[16px] text-center">{APP_TEXT.GOAL.TIME_UNIT}</span>
            </div>
          </div>
          <Button
            onClick={() => {
              goPlan();
            }}
            className=" text-primary w-full border-[1px] border-primary bg-white rounded-[23px]  font-bold h-12 text-[16px]"
          >
            {APP_TEXT.GOAL.BTN_PLAN}
          </Button>
          {plan !== 2 && (
            <Button
              onClick={() => {
                goDetail();
              }}
              className=" text-primary w-full  bg-transparent rounded-[23px]  font-bold h-12 text-[16px]"
            >
              {APP_TEXT.GOAL.BTN_STEPS}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

export default Goal;
