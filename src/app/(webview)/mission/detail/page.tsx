'use client';

import { missionAP<PERSON> } from '@/api/modules/mission';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ScrollArea } from '@/components/shared/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useLoading } from '@/hooks/use-loading';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import { useMissionState } from '@/store/mission';
import type {
  ButtonTextType,
  ContentInterface,
  MissionListType,
  MissionPointInfoType,
  MissionPopupDetailResponse,
  OrganizerType,
} from '@/types/mission';
import { CircleAlert } from 'lucide-react';
import { Check, Palette } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import AlertDialogMission from '../_components/alert-dialog-mission';
import { progressValue } from '../_utils/data-convert';
import {
  CATEGORY_TYPE,
  CONTENT_TYPE,
  ICON_TYPE,
  JUDGMENT_MISSION_FLG,
  point_Limit_Type,
} from '../_utils/enums';
import Points from './_components/points';
import ResponsiveIframe from './_components/responsive-iframe';
import VideoPlayer from './_components/video-player';
export default function MissionDetail() {
  const { setLoading, isLoading } = useLoading();
  const router = useRouter();
  const { missionInfo } = useMissionState();
  const searchParams = useSearchParams().get('data');
  const scrollRef = useRef(null);
  const [isBottom, setIsBottom] = useState(true);
  const [contentDetail, setContentDetail] = useState<ContentInterface | undefined>();
  const [buttonText, setButtonText] = useState<ButtonTextType>();
  const categoryRef = useRef(0);
  const [origin, setOrigin] = useState<string>('');
  const [organizerIdArr, setOrganizerIdArr] = useState<number[]>([]);
  const itemMissionRef = useRef<MissionListType>();
  const contentTypeRef = useRef(0);
  const judgmentMissionFlgRef = useRef(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [isPointShow, setIsPointShow] = useState(false);
  const popupContentRef = useRef<MissionPopupDetailResponse>();
  const { top, bottom } = useSafeArea();
  // const [todayAchievedFlg, setTodayAchievedFlg] = useState<boolean>(false);
  const [pointLimitFlg, setPointLimitFlg] = useState<string>('');
  const [finishFlg, setFinishFlg] = useState<boolean>(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleScroll = () => {
    if (contentTypeRef.current === CONTENT_TYPE.CATEGORY_VIDEO) {
      setIsBottom(true);
      return;
    }
    if (judgmentMissionFlgRef.current === JUDGMENT_MISSION_FLG.COUNT) return;
    const threshold = 5;
    if (!scrollRef.current) return;
    const { scrollTop, clientHeight, scrollHeight } = scrollRef.current;
    if (clientHeight >= scrollHeight) {
      setIsBottom(false);
      return;
    }

    if (scrollTop + clientHeight >= scrollHeight - threshold) {
      // スクロールバーが下にスライドし、ボタンがアクティブになります
      if (
        itemMissionRef.current?.iconType !== ICON_TYPE.ICON_POINTS_INACTIVE &&
        itemMissionRef.current?.iconType !== ICON_TYPE.ICON_POINTS_LEVELUP
      ) {
        setIsBottom(false);
      }
    }
  };

  useEffect(() => {
    itemMissionRef.current = missionInfo;
    if (
      missionInfo.todayAchievedFlg ||
      itemMissionRef.current?.pointLimitFlg === point_Limit_Type.HAVE
    ) {
      setFinishFlg(true);
    } else {
      setFinishFlg(false);
    }
    if (itemMissionRef.current) {
      setOrigin(itemMissionRef.current?.origin ? itemMissionRef.current?.origin : '');
      const organizerIds: number[] = [];
      itemMissionRef.current?.organizerList?.map((element: OrganizerType) => {
        organizerIds.push(element.organizerId);
      });
      setOrganizerIdArr(organizerIds);
      setLoading(true, { text: COMMON_TEXT.MESSAGE.DATA_SYNC });
      missionAPI
        .missionDetail({
          missionId: itemMissionRef.current?.missionId,
          missionOrganizerIdList: organizerIds,
          // isRead: itemMissionRef.current?.isRead,
          missionType: itemMissionRef.current?.missionType,
        })
        .then((res) => {
          setLoading(false);
          if (res) {
            setContentDetail(res);
            categoryRef.current = res.category;
            contentTypeRef.current = res.contentType;
            judgmentMissionFlgRef.current = res.judgmentMissionFlg;
            if (res.missionPointInfoList) {
              const allIdsZero: boolean = res.missionPointInfoList.every(
                (item) => item.dailyPoint === 0 || !item.dailyPoint,
              );
              setIsPointShow(allIdsZero);
            }

            getButtonText();
          }
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [missionInfo]);

  useEffect(() => {
    // モニターのスクロールバー
    if (contentTypeRef.current === CONTENT_TYPE.CATEGORY_TEXT) {
      handleScroll();
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [contentTypeRef.current]);

  // カテゴリ 区分
  const getBadgeLab = () => {
    switch (categoryRef.current) {
      case CATEGORY_TYPE.CATEGORY_TYPE_IMPROVEMENT:
        return { text: APP_TEXT.MISSION_PAGE.JUDGMENT, color: 'bg-[#197A4B]' };

      case CATEGORY_TYPE.CATEGORY_TYPE_COLUMN:
        return { text: APP_TEXT.MISSION_PAGE.COLUMN, color: 'bg-[#4457D1]' };

      case CATEGORY_TYPE.CATEGORY_TYPE_VIDEO:
        return { text: APP_TEXT.MISSION_PAGE.VIDEO, color: 'bg-[#C43089]' };

      case CATEGORY_TYPE.CATEGORY_TYPE_HEALTH:
        return { text: APP_TEXT.MISSION_PAGE.HEALTH_GENERAL, color: 'bg-[#C2560E]' };
      default:
        return {};
    }
  };
  // 手動タスク完了ボタンのステータスを表示します
  const getButtonText = () => {
    if (judgmentMissionFlgRef.current === JUDGMENT_MISSION_FLG.COUNT) {
      setButtonText({
        textStart: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_WALK,
        textFinish: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_WALK,
      });
      return;
    }
    if (itemMissionRef.current?.iconType === ICON_TYPE.ICON_POINTS_LEVELUP) {
      setIsBottom(!itemMissionRef.current.todayAchievedFlg);
      setButtonText({
        textStart: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_SETTING,
        textFinish: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_SETTING,
      });
    } else if (itemMissionRef.current?.iconType === ICON_TYPE.ICON_POINTS_INACTIVE) {
      setButtonText({
        textStart: APP_TEXT.MISSION_PAGE.ACHIEVEMENT_DESCRIBE_NO,
        textFinish: APP_TEXT.MISSION_PAGE.ACHIEVEMENT_DESCRIBE_NO,
      });
    } else {
      if (contentTypeRef.current === CONTENT_TYPE.CATEGORY_TEXT) {
        setButtonText({
          textStart: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_FINISH,
          textFinish: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_FINISH,
        });
      } else if (contentTypeRef.current === CONTENT_TYPE.CATEGORY_VIDEO) {
        setButtonText({
          textStart: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_VIDEO,
          textFinish: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_TRUE,
        });
      } else if (contentTypeRef.current === CONTENT_TYPE.CATEGORY_COLUMN) {
        setButtonText({
          textStart: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_COLUMN,
          textFinish: APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_TRUE,
        });
      }
    }
  };

  // APIを呼び出してタスクを手動で完了する
  const openAlert = () => {
    const organizerIds: number[] = [];
    if (contentDetail) {
      contentDetail.missionPointInfoList?.map((element: OrganizerType) => {
        if (!element.pointGotFlg) {
          organizerIds.push(element.organizerId);
        }
      });
    }

    missionAPI
      .achieveUpdate({
        missionId: itemMissionRef.current?.missionId,
        missionOrganizerIdList: organizerIds,
        kind: contentDetail?.category,
        missionType: itemMissionRef.current?.missionType,
        judgmentMissionFlg: judgmentMissionFlgRef.current,
      })
      .then((res) => {
        if (res) {
          popupContentRef.current = res;
          // ポップアップを表示
          setOpenDialog(true);
        }
      });
  };

  const handleVideoEnd = () => {
    setIsBottom(false);
  };

  const linkToRouter = () => {
    router.push('/health-score');
  };

  const handlePlaybackRateChange = (rate: number) => {
    console.log(`再生レートの変更: ${rate}x`);
  };

  // ビデオが終了しました。ボタンを有効にしてください
  const handleStateChange = (state: number) => {
    if (itemMissionRef.current?.iconType !== ICON_TYPE.ICON_POINTS_INACTIVE) {
      if (state === 0) {
        setIsBottom(false);
      }
    }
  };

  return (
    <div className="bg-white min-h-full">
      <TopBar
        title={APP_TEXT.MISSION_PAGE.DETIAL_TITLE}
        className="shadow-none"
        onBack={() => {
          if (missionInfo.origin === 'mission') {
            router.push('/mission');
          } else if (missionInfo.origin === 'home') {
            router.push('/home');
          } else if (missionInfo.origin === 'score') {
            router.push('/health-score');
          } else {
            const str = missionInfo.origin;
            const result = str.split('-')[1] || ''; // 如果没找到 '-'，返回空字符串
            router.push(`/mission/list?data=${result}`);
          }
        }}
      />
      {finishFlg && (
        <div>
          <div
            className="text-[#666666] bg-background  w-full flex text-base font-bold py-2 z-50 justify-center fixed left-0 right-0 top-[48px] h-[40px]"
            style={{ top: `${top + 48}px` }}
          >
            <span>
              {/* このミッションはすでに達成済みです */}
              {APP_TEXT.MISSION_PAGE.ACQUIRED_AFTER_TOMORROW}
            </span>
          </div>
          <div className="w-full h-[40px]" />
        </div>
      )}

      <ScrollArea className="w-full" type="hover">
        <div className="bg-white  w-full" style={{ overflow: 'auto' }}>
          <div
            className="flex flex-col items-start w-full pt-6 px-6"
            ref={scrollRef}
            onScroll={handleScroll}
            style={{ height: 'calc(100vh - 182px)', overflow: 'auto' }}
          >
            <div className="w-full">
              <Badge
                className={`${getBadgeLab().color} font-bold text-sm text-white px-2 py-[2px] mb-2`}
              >
                {getBadgeLab().text}
              </Badge>
              <div className="text-xl font-bold mb-2">{contentDetail?.missionTitle}</div>
              {itemMissionRef.current?.iconType === ICON_TYPE.ICON_POINTS_INACTIVE &&
                itemMissionRef.current?.pointLimitFlg !== point_Limit_Type.HAVE && (
                  <div className="flex pb-4">
                    <div className="text-sm font-normal text-[#666666]">
                      {APP_TEXT.MISSION_PAGE.DETIAL_TOMORROW}
                    </div>
                    <div className="text-sm rounded-sm font-bold px-1 bg-primary-5 text-primary ml-2">
                      {APP_TEXT.MISSION_PAGE.POIONT_COUNT}
                    </div>
                  </div>
                )}
              {/* ポイントなし 以外*/}
              {itemMissionRef.current?.iconType !== ICON_TYPE.ICON_POINTS_NO && !isPointShow && (
                <div className="px-4 w-full bg-primary-5 rounded-2xl pt-1 pb-2 ">
                  <div className="divide-y divide-[#B2B2B2]">
                    {contentDetail?.missionPointInfoList?.map(
                      (item: MissionPointInfoType, i: number) =>
                        (item.dailyPoint ?? 0) > 0 && (
                          <Points
                            key={i}
                            itemPoint={item}
                            groupFlag={contentDetail?.missionPointInfoList?.length === 1}
                          />
                        ),
                    )}
                  </div>
                </div>
              )}
              <div className="w-full flex justify-between items-center mt-2">
                <Progress
                  value={progressValue(
                    itemMissionRef.current?.achievedDays,
                    itemMissionRef.current?.targetDays,
                  )}
                  className="bg-primary-10"
                  indicatorClassName="duration-700 bg-primary-light"
                />
                <div className="text-xs text-black ml-1">
                  {itemMissionRef.current?.achievedDays}/{itemMissionRef.current?.targetDays}
                </div>
              </div>
              <div className="mt-5 w-full">
                {/* 動画 youToBe  プレーヤー*/}
                {contentTypeRef.current === CONTENT_TYPE.CATEGORY_VIDEO &&
                  contentDetail?.missionUrl && (
                    <div>
                      <VideoPlayer
                        videoId={contentDetail?.missionUrl}
                        onPlaybackRateChange={handlePlaybackRateChange}
                        onStateChange={handleStateChange}
                      />
                    </div>
                  )}
                {/* コラム web url */}
                {contentTypeRef.current === CONTENT_TYPE.CATEGORY_COLUMN &&
                  contentDetail?.missionUrl && (
                    <div>
                      <ResponsiveIframe url={contentDetail?.missionUrl} />
                    </div>
                  )}
                {contentDetail?.missionContent && (
                  <div className="text-wrap">{contentDetail?.missionContent}</div>
                )}
              </div>
              {/* あなたの改善ミッション  表示*/}
              {CATEGORY_TYPE.CATEGORY_TYPE_IMPROVEMENT === contentDetail?.category && (
                <div className="w-full bg-[#F6F8FF] rounded-2xl  p-6 mb-6 mt-4 ">
                  <div className="">{APP_TEXT.MISSION_PAGE.DETIAL_BUTTON_CONTENT}</div>
                  {/* ソースはホーム画面xx概要画面です */}
                  {(origin === 'home' || origin === 'list' || origin === 'mission') && (
                    <Button
                      className="text-primary my-2 font-bold h-12 rounded-3xl w-full"
                      type="button"
                      variant="outline"
                      onClick={linkToRouter}
                    >
                      {APP_TEXT.MISSION_PAGE.HEALTH_BUTTON_CONFIRM}
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
      <div style={{ height: `${bottom + 60}px` }} />
      <div className="w-full bg-white fixed left-0 right-0 " style={{ bottom: `${60 + bottom}px` }}>
        <div className=" bg-white px-3 pb-2 w-full">
          {itemMissionRef.current?.iconType === ICON_TYPE.ICON_POINTS_LEVELUP &&
            !itemMissionRef.current.todayAchievedFlg && (
              <div className={cn('text-destructive flex justify-center items-center text-xs')}>
                <CircleAlert className="w-6 h-6 fill-destructive text-card flex-shrink-0 mr-2" />
                {APP_TEXT.MISSION_PAGE.DETIAL_SETTING_MESSAGE}
              </div>
            )}
          {!isLoading && (
            <Button
              disabled={finishFlg ? true : isBottom}
              className="bg-primary my-2 text-white  font-bold h-12  rounded-3xl w-full"
              type="button"
              onClick={openAlert}
            >
              {finishFlg ? (
                <div className="flex jutify-center items-center">
                  <Check className="w-5" />
                  {APP_TEXT.MISSION_PAGE.TODAY_ACHIEVED}
                </div>
              ) : (
                <div className="w-full  whitespace-normal break-words whitespace-pre-line">
                  {!isBottom ? buttonText?.textFinish : buttonText?.textStart}
                </div>
              )}
            </Button>
          )}
        </div>
      </div>
      {openDialog && (
        <AlertDialogMission
          open={openDialog}
          popupContent={popupContentRef.current}
          onCancel={() => {
            setOpenDialog(false);
            router.back();
          }}
        />
      )}
    </div>
  );
}
