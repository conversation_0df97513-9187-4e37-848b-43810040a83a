'use client';

import { APP_TEXT } from '@/const/text/app';
import type { PointInfoQuizType } from '@/types/mission';
import { formatNumber } from '../../../_utils/data-convert';

export default function MissionDetailPoints({
  itemPoint,
  groupFlag,
}: { itemPoint: PointInfoQuizType; groupFlag: boolean }) {
  return (
    <div className="pt-3">
      {!groupFlag && (
        <div>
          <div className="flex text-base font-normal  items-center ">
            <div className="w-[4px] text-base font-normal mr-2 h-5  bg-primary-soft" />
            {itemPoint.organizerName}
          </div>
        </div>
      )}
      <div className="py-2 flex justify-between items-center">
        <div className="flex items-center">
          <img className="w-[20px] mr-2" src="/images/misson/vector.svg" alt="mission-vector" />
          <p className="text-base font-normal text-black">{APP_TEXT.MISSION_PAGE.BONUS_COUNT}</p>
        </div>
        <div>
          {itemPoint.correctPoint !== undefined && itemPoint.correctPoint > 0 && (
            <div className="flex items-center ">
              <span className="text-xs mr-4">{APP_TEXT.MISSION_PAGE.CORRECTPOINT}</span>
              <div className="flex items-center justify-end flex-1">
                <span className="text-primary text-xl font-bold pr-[2px]">
                  {formatNumber(itemPoint.correctPoint)}
                </span>
                <p>{APP_TEXT.MISSION_PAGE.MISSION_P}</p>
              </div>
            </div>
          )}
          {itemPoint.incorrectPoint !== undefined && itemPoint.incorrectPoint > 0 && (
            <div className="flex items-center">
              <span className="text-xs mr-4">{APP_TEXT.MISSION_PAGE.INCORRECTPOINT}</span>
              <div className="flex items-center justify-end  flex-1">
                <span className="text-primary text-xl font-bold pr-[2px]">
                  {formatNumber(itemPoint.incorrectPoint)}
                </span>
                <p>{APP_TEXT.MISSION_PAGE.MISSION_P}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
