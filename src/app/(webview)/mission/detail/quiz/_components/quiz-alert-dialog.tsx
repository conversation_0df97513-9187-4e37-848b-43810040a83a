'use client';

import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import type { QuizTypeItem } from '@/types/mission';
import { CircleIcon, Cross2Icon } from '@radix-ui/react-icons';
import { useEffect } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from '../../../_components/alert-dialog';
import { QUIZ_TYPE } from '../../../_utils/enums';
export default function QuizAlertDialog({
  open,
  quizAlertItem,
  onCancel,
  onConfirm,
}: {
  open: boolean;
  quizAlertItem: QuizTypeItem | undefined;
  onCancel: () => void;
  onConfirm: () => void;
}) {
  const handleCancel = () => {
    onCancel();
  };

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <AlertDialog open={open}>
      <AlertDialogContent className="rounded-2xl shadow-none border-0 bg-white px-0 gap-0">
        <AlertDialogTitle>
          <div className="flex font-bold justify-center mb-4">{APP_TEXT.MISSION_PAGE.ANSWER}</div>
          <div className="text-primary flex items-center w-full flex-col ">
            {quizAlertItem?.quizType === QUIZ_TYPE.JUDGE ? (
              <div>
                {quizAlertItem.text === '1' ? (
                  <CircleIcon width="64px" height="64px" />
                ) : (
                  <Cross2Icon width="64px" height="64px" />
                )}
              </div>
            ) : (
              <div className="text-[56px] py-6">{quizAlertItem?.text}</div>
            )}
          </div>
        </AlertDialogTitle>

        <AlertDialogDescription className="hidden" />
        <div className="mt-5 flex items-center w-full flex-col text-base">
          {APP_TEXT.MISSION_PAGE.ANSWER_BETTER}
        </div>
        <div className="mt-1 mb-1 text-[#666666] flex items-center w-full flex-col text-sm">
          {APP_TEXT.MISSION_PAGE.ANSWER_CANNOT}
        </div>
        <AlertDialogFooter>
          <AlertDialogAction
            className="rounded-3xl mt-4 shadow-none text-primary ml-4 mr-4 border-primary border font-bold h-12 bg-white text-[16px]"
            onClick={handleCancel}
          >
            {COMMON_TEXT.BUTTON.CANCEL}
          </AlertDialogAction>
          <AlertDialogCancel
            className="rounded-3xl ml-4 mr-4 border-0  bg-primary font-bold h-12 text-white text-[16px]"
            onClick={handleConfirm}
          >
            {COMMON_TEXT.DIALOG.OK}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
