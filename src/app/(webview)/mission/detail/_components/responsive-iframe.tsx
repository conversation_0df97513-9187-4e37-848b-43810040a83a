import { useLoading } from '@/hooks/use-loading';
import React, { useRef, useEffect } from 'react';
const ResponsiveIframe = ({ url }: { url: string }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { setLoading, isLoading } = useLoading();
  useEffect(() => {
    setLoading(true);
    const iframe = iframeRef.current;
    if (!iframe) return;
    const observer = new ResizeObserver(() => {
      if (iframeRef.current) {
        // iframeRef.current.style.height = `${iframeRef.current.offsetHeight}px`;
      }
    });
    const handleScroll = () => {
      const iframeDoc = iframe.contentDocument;
      if (!iframeDoc) return;

      const scrollTop = iframeDoc.documentElement.scrollTop || iframeDoc.body.scrollTop;
    };
    const handleLoad = () => {
      setLoading(false);
      const iframeDoc = iframe.contentDocument;
      if (!iframeDoc) return;
      iframeDoc.addEventListener('scroll', handleScroll);
    };

    iframe.addEventListener('load', handleLoad);
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'iframeHeight' && iframeRef.current) {
        observer?.disconnect();
        // iframeRef.current.style.height = `${event.data.height}px`;
      }
    };

    window.addEventListener('message', handleMessage);
    iframeRef.current && observer.observe(iframeRef.current);
    iframe.addEventListener('load', handleLoad);
    return () => {
      iframe.addEventListener('load', handleLoad);
      window.removeEventListener('message', handleMessage);
      observer?.disconnect();
    };
  }, []);
  // height: 'calc(100vh - 413px)'
  return url.trim() !== '' ? (
    <div className="relative w-fulloverflow-hidden" style={{ height: 'calc(100vh - 213px)' }}>
      <iframe
        ref={iframeRef}
        src={url}
        width="100%"
        className="w-full h-full"
        style={{
          border: 'none',
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none', // IE/Edge
        }}
        onLoad={(e) => {
          const iframe = e.target as HTMLIFrameElement;
          iframe.contentWindow?.postMessage({ type: 'requestHeight' }, '*');
        }}
        title="Embedded Website"
      />
      <style jsx>{`
        iframe::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  ) : (
    <></>
  );
};

export default ResponsiveIframe;
