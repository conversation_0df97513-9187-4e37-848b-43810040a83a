import { useEffect, useRef, useState } from 'react';

interface YouTubePlayerProps {
  videoId: string;
  width?: number;
  height?: number;
  autoplay?: boolean;
  controls?: boolean;
  onReady?: (player: any) => void;
  onStateChange?: (state: number) => void;
  onPlaybackRateChange?: (rate: number) => void;
}

const YouTubePlayer: React.FC<YouTubePlayerProps> = ({
  videoId,
  width = 640,
  height = 360,
  autoplay = false,
  controls = true,
  onReady,
  onStateChange,
  onPlaybackRateChange,
}) => {
  const playerRef = useRef<any>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const [isApiReady, setIsApiReady] = useState(false);
  const [playerState, setPlayerState] = useState<number>(-1);
  const [playbackRate, setPlaybackRate] = useState<number>(1);

  // Extract video ID from URL if needed
  const getVideoId = (url: string) => {
    if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
      return url; // Assume it's already an ID
    }

    // Handle different URL formats
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : url;
  };

  // Initialize YouTube API
  useEffect(() => {
    if (window.YT) {
      setIsApiReady(true);
      return;
    }

    const tag = document.createElement('script');
    tag.src = 'https://www.youtube.com/iframe_api';
    const firstScriptTag = document.getElementsByTagName('script')[0];
    firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);

    window.onYouTubeIframeAPIReady = () => {
      setIsApiReady(true);
    };

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
      }
    };
  }, []);

  // Initialize player when API is ready
  useEffect(() => {
    if (!isApiReady || !playerContainerRef.current) return;

    const cleanVideoId = getVideoId(videoId);

    playerRef.current = new window.YT.Player(playerContainerRef.current, {
      width: '100%',
      height: '100%',
      videoId: cleanVideoId,
      playerVars: {
        autoplay: autoplay ? 1 : 0,
        controls: controls ? 1 : 0,
        mute: autoplay ? 1 : 0, // Required for autoplay in most browsers
        rel: 0,
        modestbranding: 1,
        enablejsapi: 1,
      },
      events: {
        onReady: (event: any) => {
          if (onReady) onReady(event.target);
          // Start checking playback rate
          const intervalId = setInterval(() => {
            try {
              const rate = event.target.getPlaybackRate();
              if (rate !== playbackRate) {
                setPlaybackRate(rate);
                if (onPlaybackRateChange) onPlaybackRateChange(rate);
              }
            } catch (e) {
              console.error('Error checking playback rate:', e);
            }
          }, 1000);

          return () => clearInterval(intervalId);
        },
        onStateChange: (event: any) => {
          const state = event.data;
          setPlayerState(state);
          if (onStateChange) onStateChange(state);
        },
      },
    });

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
      }
    };
  }, [isApiReady, videoId]);

  return (
    <div className="w-full" style={{ aspectRatio: `${width}/${height}` }}>
      <div ref={playerContainerRef} className="w-full h-full" />
    </div>
  );
};

export default YouTubePlayer;
