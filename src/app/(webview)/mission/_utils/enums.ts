// カテゴリ
export enum CATEGORY_TYPE {
  CATEGORY_TYPE_IMPROVEMENT = 1, // 1:あなたの改善ミッション
  CATEGORY_TYPE_COLUMN = 2, // 2:コラム
  CATEGORY_TYPE_VIDEO = 3, // 3:動画
  CATEGORY_TYPE_HEALTH = 4, //4:健康一般
}

// ミッションタイプ
export enum MISSION_TYPE {
  MISSION_TYPE_IMPROVEMENT = 1, //1：改善ミッション；
  MISSION_TYPE_FREEDOM = 2, // 2：自由設定ミッション；
  MISSION_TYPE_NORMAL = 3, // 3：クイズ以外の通常ミッション；
  MISSION_TYPE_QUIZ = 4, // 4：クイズ；
  MISSION_TYPE_LEVEL = 5, //5：レベルアップミッション；
}

// アイコンタイプ
export enum ICON_TYPE {
  ICON_POINTS_ACTIVITY = 1, //1：ポイントあり 活性；
  ICON_POINTS_INACTIVE = 2, // 2：ポイントあり（達成回数制御）　活性；
  ICON_POINTS_NO = 3, // 3：ポイントなし；
  ICON_POINTS_LEVELUP = 4, //4：レベルアップ；
}

// アイコンタイプ
export enum CONTENT_TYPE {
  CATEGORY_TEXT = 1, // 1:テキスト
  CATEGORY_VIDEO = 2, // 3:動画
  CATEGORY_COLUMN = 3, // 2:コラム
}

// 改善ミッションフラグ
export enum JUDGMENT_MISSION_FLG {
  COUNT = 1, // 歩数ミッション
  SLEEP = 2, // 睡眠ミッション
  OTHER = 3, // ほかのミッション
}

export enum ICON_FLG {
  HAVE = 1, // ポイントあり
  NOT_HAVE = 2, // ポイントなし
}

export enum QUIZ_TYPE {
  SINGLE_CHOICE = 'single_choice', // ポイントあり
  JUDGE = 'judge', // ポイントなし
}

export enum CHECK_STATUS {
  CORRECT = 1,
  MISTAKES = 2,
}

export enum point_Limit_Type {
  NOT_HAVE = '1', // なし
  HAVE = '2', // あり
  UNDETERMINED = '3', // 未定
}
