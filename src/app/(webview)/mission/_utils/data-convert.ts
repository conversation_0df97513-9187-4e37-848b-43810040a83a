import { APP_TEXT } from '@/const/text/app';
import { CATEGORY_TYPE } from './enums';

export const getRoleText = (val: number | undefined) => {
  switch (val) {
    case CATEGORY_TYPE.CATEGORY_TYPE_IMPROVEMENT:
      return APP_TEXT.MISSION_PAGE.IMPROVE;
    case CATEGORY_TYPE.CATEGORY_TYPE_COLUMN:
      return APP_TEXT.MISSION_PAGE.COLUMN;
    case CATEGORY_TYPE.CATEGORY_TYPE_VIDEO:
      return APP_TEXT.MISSION_PAGE.VIDEO;
    case CATEGORY_TYPE.CATEGORY_TYPE_HEALTH:
      return APP_TEXT.MISSION_PAGE.HEALTH_GENERAL;
    default:
      return '';
  }
};

export const formatNumber = (num: number): string => {
  return num.toLocaleString('en-US');
};

export const progressValue = (achieved: number | undefined, target: number | undefined): number => {
  return achieved && target ? (achieved / target) * 100 : 0;
};
