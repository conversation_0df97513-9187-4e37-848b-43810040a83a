'use client';

import { missionAPI } from '@/api/modules/mission';
import TopBar from '@/components/layout/top-bar';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { MissionCardType, MissionInterface, MissionType } from '@/types/mission';
import { useCallback, useEffect, useState } from 'react';
import MissionCard from '../_components/mission-card';
import { getRoleText } from '../_utils/data-convert';

export default function MissionList() {
  const searchParams = useSearchParams().get('data');
  const [title, setTitle] = useState<string>('');
  const [category, setCategory] = useState<number>(0);
  const [missionList, setMissionList] = useState<MissionCardType[]>([]);
  useEffect(() => {
    const category = Number(searchParams);
    setTitle(getRoleText(category));
    missionAPI.missionSectionList({ category: category }).then((res) => {
      if (res) {
        setMissionList(res.categoryMissionList);
        setCategory(res.category);
      }
    });
  }, [searchParams]);

  return (
    <div className="p-6">
      <TopBar title={title} />
      <div>
        {missionList?.map((item: MissionCardType, i: number) => (
          <MissionCard
            key={i}
            missionCardItem={item}
            category={category}
            routerFlag={`list-${category}`}
          />
        ))}
      </div>
    </div>
  );
}
