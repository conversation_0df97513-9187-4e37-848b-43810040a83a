'use client';

import { APP_TEXT } from '@/const/text/app';
import type { PointInfoType } from '@/types/mission';
import { formatNumber } from '../_utils/data-convert';

export default function AlertPoint({ pointInfo }: { pointInfo: PointInfoType }) {
  return (
    <div className="rounded-2xl bg-[#F6F8FF] px-6 divide-y divide-[#B2B2B2] py-1">
      <div className="flex justify-between items-center py-2">
        <div className="flex justify-between items-center flex-1">
          <div className="text-sm">{APP_TEXT.MISSION_PAGE.BONUS_COUNT}</div>
          <div className="text-end">
            <span className="text-xl font-bold text-primary">
              {formatNumber(pointInfo.getDailyPoint)}
            </span>
            <span>p</span>
          </div>
        </div>
        {pointInfo.getAchievePoint > 0 && <span className="mx-1">/</span>}
        {pointInfo.getAchievePoint > 0 && (
          <div className="text-sm flex items-center justify-between flex-1">
            <div>{APP_TEXT.MISSION_PAGE.BONUS}</div>
            <div>
              <span className="text-xl font-bold text-primary">
                {formatNumber(pointInfo.getAchievePoint)}
              </span>
              {APP_TEXT.MISSION_PAGE.MISSION_P}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
