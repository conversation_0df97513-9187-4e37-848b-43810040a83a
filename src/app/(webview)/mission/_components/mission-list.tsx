'use client';

import { COMMON_TEXT } from '@/const/text/common';
import { useRouter } from '@/hooks/use-next-navigation';
import type { MissionInterface, MissionType } from '@/types/mission';
import { ChevronRight } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { getRoleText } from '../_utils/data-convert';
import { CATEGORY_TYPE } from '../_utils/enums';
import MissionCard from './mission-card';
export default function MissionList({ missionItems }: { missionItems: MissionInterface }) {
  const [missionData, setMissionData] = useState<MissionInterface | undefined>();
  const router = useRouter();
  const [isMore, setIsMore] = useState<boolean>(false);
  useEffect(() => {
    if (missionItems) {
      setIsMore(
        missionItems.categoryMissionList?.length === 0 || !missionItems.categoryMissionList,
      );
      setMissionData(missionItems);
      // const totalAchievedValue =
      //   (organizerData.totalAchieved / organizerData.totalLimit) * 100;
      // setProgressValue(totalAchievedValue);
    }
  }, [missionItems]);

  // 詳細画面へジャンプ
  const linkRouter = () => {
    router.push(`/mission/list?data=${missionData?.category}`);
  };

  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-2">
        {!isMore && (
          <div className="font-bold text-lg text-black">{getRoleText(missionData?.category)}</div>
        )}
        {/* あなたの改善ミッション以外のステータスの場合は、「詳細を表示」ボタンが表示されます*/}
        {missionData?.category !== CATEGORY_TYPE.CATEGORY_TYPE_IMPROVEMENT && (
          <div>
            {!isMore && (
              <button
                type="button"
                className="flex items-center gap-1 text-primary transition-colors"
                onClick={linkRouter}
              >
                <span className="text-sm text-primary font-bold">{COMMON_TEXT.HOME.MORE}</span>
                <ChevronRight className="text-primary h-4 w-4" />
              </button>
            )}
          </div>
        )}
      </div>
      {/* ミッションリスト*/}
      {!isMore &&
        missionItems?.categoryMissionList?.map((item: MissionType, i: number) => (
          <MissionCard
            key={i}
            missionCardItem={item}
            category={missionData?.category}
            routerFlag="mission"
          />
        ))}
    </div>
  );
}
