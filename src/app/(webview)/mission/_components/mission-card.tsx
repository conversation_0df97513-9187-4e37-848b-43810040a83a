'use client';

import { missionAPI } from '@/api/modules/mission';
import TopBar from '@/components/layout/top-bar';
import { Progress } from '@/components/ui/progress';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { cn } from '@/lib/utils';
import { useMissionState } from '@/store/mission';
import type { MissionCardType, MissionType, OrganizerType } from '@/types/mission';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { progressValue } from '../_utils/data-convert';
import { ICON_TYPE, MISSION_TYPE, point_Limit_Type } from '../_utils/enums';

export default function MissionCard({
  missionCardItem,
  category,
  routerFlag,
}: {
  routerFlag: string;
  missionCardItem: MissionCardType;
  category: number | undefined;
}) {
  const router = useRouter();
  const { setMissionInfo } = useMissionState();
  const [missionData, setMissionData] = useState<MissionType | undefined>();
  const [organizerList, setOrganizerList] = useState<OrganizerType[]>();
  const [imgName, setImgName] = useState<string>('mission-coin.png');
  const [iconLabel, setIconLabel] = useState<string>('');

  useEffect(() => {
    if (missionCardItem) {
      setMissionData(missionCardItem);
      getIconType(missionCardItem.iconType);
      getMissionType(missionCardItem.missionType);
    }
  }, [missionCardItem]);

  const getMissionType = (val: number) => {
    const improvementData = [{ organizerId: 1, organizerName: APP_TEXT.MISSION_PAGE.IMPROVE }];
    if (val === MISSION_TYPE.MISSION_TYPE_IMPROVEMENT) {
      setOrganizerList(improvementData);
    } else if (val === MISSION_TYPE.MISSION_TYPE_FREEDOM) {
      setOrganizerList(missionCardItem?.organizerList);
    } else {
      setOrganizerList([]);
    }
  };

  const getIconType = (val: number) => {
    // レベルアップ；アイコン
    if (val === ICON_TYPE.ICON_POINTS_LEVELUP) {
      setImgName('home/mission_level.svg');
      setIconLabel(APP_TEXT.MISSION_PAGE.LEVEL);
    } else if (val === ICON_TYPE.ICON_POINTS_NO) {
      // ポイントなしアイコン
      setImgName('home/mission_flg.svg');
      setIconLabel('');
    } else {
      // ポイントあり
      setImgName('mission-coin.png');
      setIconLabel(APP_TEXT.MISSION_PAGE.POINT);
    }
  };

  const cardLinkDetail = () => {
    // ポイントあり　非活性  詳細画面にジャンプできません
    // if (missionData?.iconType === ICON_TYPE.ICON_POINTS_INACTIVE) return;
    setMissionInfo({ ...missionCardItem, origin: routerFlag });
    // 新しいタスク呼び出しAPI
    if (missionCardItem?.isRead === false) {
      //&& missionCardItem.missionType === MISSION_TYPE.MISSION_TYPE_FREEDOM
      missionAPI.missionReadDetail({ missionId: missionCardItem.missionId }).then((res) => {});
    }

    if (missionCardItem?.missionType === MISSION_TYPE.MISSION_TYPE_QUIZ) {
      // ミッション詳細（クイズ）
      // router.push(`/mission/detail/quiz?data=${encodeURIComponent(JSON.stringify(params))}`);
      router.push('/mission/detail/quiz');
    } else {
      // router.push(`/mission/detail?data=${encodeURIComponent(JSON.stringify(params))}`);
      router.push('/mission/detail');
    }
  };

  return (
    <div className="mb-2">
      <div className="w-full overflow-hidden bg-white rounded-2xl py-2 px-3  relative">
        {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
        <div className="flex items-center" onClick={cardLinkDetail}>
          <div
            className={`relative flex flex-col items-center w-16 flex-none ${
              missionData?.iconType === ICON_TYPE.ICON_POINTS_INACTIVE ||
              (
                missionCardItem.todayAchievedFlg === false &&
                  missionCardItem.pointLimitFlg === point_Limit_Type.HAVE
              )
                ? 'opacity-50'
                : ''
            }`}
          >
            <div className={cn('w-12 h-12 ')}>
              <img className="w-full" src={`/images/${imgName}`} alt="mission-info" />
            </div>
            {iconLabel && (
              <div className="-mt-[9px] relative w-full flex justify-center px-1">
                <div className="bg-white w-full rounded-full border-2 border-primary-100 h-5 px-1 flex items-center justify-center">
                  <span className="text-[10px] text-black font-bold leading-none">{iconLabel}</span>
                </div>
              </div>
            )}
          </div>
          <div className="flex-1 ml-2 ">
            <div className="text-base font-bold  line-clamp-2">{missionData?.missionTitle}</div>
            <div className=" w-full flex justify-between items-center min-h-[20px]">
              <div className="flex items-center flex-wrap">
                {organizerList?.map((item, i) => (
                  <div key={item.organizerId} className="mb-1">
                    <div className="font-normal py-[2px] mr-1 text-xs text-primary bg-primary-5 px-1 rounded-sm table ">
                      {item.organizerName}
                    </div>
                  </div>
                ))}
                {missionCardItem?.isRead === false && (
                  <div className="mb-1 font-normal py-[2px] mr-1 text-xs text-[#CE0000] bg-red-100 px-1 rounded-sm table ">
                    NEW
                  </div>
                )}
              </div>
              {/* 新規タスクフラグ */}
            </div>
            {/* ポイントゲットはまた明日 */}
            {missionData?.iconType === ICON_TYPE.ICON_POINTS_INACTIVE &&
              missionCardItem.pointLimitFlg !== point_Limit_Type.HAVE && (
                <div className="text-xs opacity-50 font-normal">
                  {APP_TEXT.MISSION_PAGE.ACHIEVEMENT_DESCRIBE_NO}
                </div>
              )}
            {/* タスクの進捗状況 */}
            {missionCardItem.todayAchievedFlg === false &&
            missionCardItem.pointLimitFlg === point_Limit_Type.HAVE ? (
              <div className="text-xs opacity-50 font-normal">
                このミッションはすでに達成済みです
              </div>
            ) : (
              <div className="w-full flex items-center">
                <Progress
                  value={progressValue(missionCardItem?.achievedDays, missionCardItem?.targetDays)}
                  className="bg-primary-10"
                  indicatorClassName="duration-700 bg-primary-light"
                />
                <div className="text-xs font-normal ml-1">
                  {missionCardItem?.achievedDays}/{missionCardItem?.targetDays}
                </div>
              </div>
            )}
          </div>
        </div>
        {/* 今日達成フラグ スタイル*/}
        {missionData?.todayAchievedFlg && (
          // biome-ignore lint/a11y/useKeyWithClickEvents: <explanation>
          <div
            className="absolute h-full top-0 left-0 w-full flex items-center justify-end"
            onClick={cardLinkDetail}
          >
            <div className="relative w-full h-full">
              <div className="bg-primary-light z-0 w-full opacity-60 absolute h-full top-0 left-0" />
              <div className="absolute z-1 right-0 justify-end items-center w-full h-full flex">
                <div className=" flex items-center justify-center text-base font-bold text-primary bg-white mx-5 border-4 size-12 rounded-full border-[var(--primary)]">
                  {APP_TEXT.MISSION_PAGE.ACHIEVE_TEXT}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
