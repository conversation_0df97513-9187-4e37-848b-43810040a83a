'use client';

import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import type {
  ExtendedQuizUpdateResponse,
  MissionOrganizerData,
  MissionPopupDetailResponse,
  PointInfoType,
  QuizAchieveUpdatelResponse,
} from '@/types/mission';
import { useEffect, useState } from 'react';
import { CHECK_STATUS, QUIZ_TYPE } from '../_utils/enums';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from './alert-dialog';
import AlertPoint from './alert-point';
import AlertPointMultiple from './alert-point-multiple';
import CategoryItem from './category-item';

export default function MissionAlertDialog({
  open,
  popupContent,
  popupQuizContent,
  onCancel,
}: {
  open: boolean;
  popupContent?: MissionPopupDetailResponse | undefined;
  popupQuizContent?: ExtendedQuizUpdateResponse | undefined;
  onCancel: () => void;
}) {
  const [pointInfoList, setPointInfoList] = useState<PointInfoType[]>();
  const [todayAchievedInfoList, setTodayAchievedInfoList] = useState<MissionOrganizerData[]>();
  const [pointAuthorityFlg, setPointAuthorityFlg] = useState<boolean>();
  const [pointDetailsShowFlg, setPointDetailsShowFlg] = useState<boolean>();
  const [imageName, setImageName] = useState<string>();
  const [answerTile, setAnswerTile] = useState<string>();
  const [isPointShow, setIsPointShow] = useState(false);

  useEffect(() => {
    if (popupQuizContent) {
      // ミッション詳細（クイズ）ポップアップウィンドウの場合
      const pointArr: PointInfoType[] = [];
      setTodayAchievedInfoList(popupQuizContent.todayAchievedInfoList);
      popupQuizContent?.getPointInfoList.map((item) => {
        pointArr.push({
          organizerId: item.organizerId,
          organizerName: item.organizerName,
          getDailyPoint: item.getPoint,
          getAchievePoint: 0,
        });
      });
      setPointInfoList(pointArr);
      setPointAuthorityFlg(popupQuizContent.pointAuthorityFlg);
      setPointDetailsShowFlg(popupQuizContent.pointDetailsShowFlg);
      getImgLable();
    } else {
      setPointInfoList(popupContent?.getPointInfoList);
      setTodayAchievedInfoList(popupContent?.todayAchievedInfoList);
      setPointAuthorityFlg(popupContent?.pointAuthorityFlg);
      setPointDetailsShowFlg(popupContent?.pointDetailsShowFlg);

      if (popupContent?.getPointInfoList) {
        const allIdsZero: boolean = popupContent.getPointInfoList.every(
          (item) => item.getDailyPoint === 0 || !item.getDailyPoint,
        );
        setIsPointShow(allIdsZero);
      }
    }
  }, [popupQuizContent, popupContent]);

  // ポップアップタイトルアイコンを表示
  const getImgLable = () => {
    if (popupQuizContent?.checkStatus === CHECK_STATUS.CORRECT) {
      setImageName(popupQuizContent?.iconFlg === 1 ? 'point-answer.svg' : 'answer-finish.svg');
      setAnswerTile(`${APP_TEXT.MISSION_PAGE.ANSWER_CORRECTPOINT}`);
    } else {
      setImageName(popupQuizContent?.iconFlg === 1 ? 'point-wrong.svg' : 'wrong.svg');
      setAnswerTile(`${APP_TEXT.MISSION_PAGE.ANSWER_SORRY}`);
    }
  };

  const handleCancel = () => {
    onCancel();
  };
  return (
    <AlertDialog open={open}>
      <AlertDialogContent className="rounded-2xl shadow-none border-0 bg-white px-0 gap-0">
        {popupQuizContent && (
          <AlertDialogTitle>
            <div className="flex flex-col items-center mb-2 font-bold ">
              <img className="w-[64px]" src={`/images/misson/${imageName}`} alt="point-answer" />
              <div>{answerTile}</div>
              {/* ミッション詳細（クイズ） ポイント権限フラグ  権限あり*/}
              {popupQuizContent?.iconFlg === 1 && (
                <div>
                  {popupQuizContent?.getPointInfoList &&
                    popupQuizContent?.getPointInfoList.length === 1 && (
                      <span className="font-bold text-2xl text-primary">
                        {popupQuizContent?.pointTitle}
                      </span>
                    )}
                  {APP_TEXT.MISSION_PAGE.DIALOG_SUB_TITLE}
                </div>
              )}
            </div>
          </AlertDialogTitle>
        )}
        {/* ミッション詳細（クイズ） 以外*/}
        {popupContent && (
          <div>
            {/* ポイント権限フラグ  権限あり*/}
            {popupContent?.iconFlg === 1 ? (
              <AlertDialogTitle>
                <div className="flex justify-center">
                  <img
                    className="w-[64px]"
                    src="/images/misson/point-finish.svg"
                    alt="mission-info"
                  />
                </div>
                <div className="flex font-bold justify-center">
                  {APP_TEXT.MISSION_PAGE.DETIAL_POPUP_WALK}
                </div>
                {popupContent?.iconFlg === 1 && (
                  <div className="flex justify-center items-center">
                    {!popupContent?.organizerNameShowFlg && (
                      <span className="font-bold text-2xl text-primary">
                        {popupContent?.pointTitle}
                      </span>
                    )}
                    {APP_TEXT.MISSION_PAGE.DIALOG_SUB_TITLE}
                  </div>
                )}
                {popupContent.stepMissionExistFlg && (
                  <div className="text-sm font-normal text-center mb-1">
                    {APP_TEXT.MISSION_PAGE.WALK_FINISH}
                  </div>
                )}
              </AlertDialogTitle>
            ) : (
              <AlertDialogTitle>
                <div className="flex justify-center">
                  <img className="w-[64px]" src="/images/misson/finish.svg" alt="mission-finish" />
                </div>
                <div className="flex font-bold justify-center mb-4">
                  {APP_TEXT.MISSION_PAGE.DETIAL_POPUP_WALK}
                </div>
              </AlertDialogTitle>
            )}
          </div>
        )}
        <AlertDialogDescription className="hidden" />

        <div className="overflow-auto max-h-[60vh]">
          {/* ポイント権限  ポイント内訳表示フラグ true*/}
          {(popupContent?.iconFlg === 1 || popupQuizContent?.iconFlg === 1) &&
            pointDetailsShowFlg && (
              <div className="px-6">
                {/* 単一グループ/多一グループのスコア表示 */}
                {pointInfoList && pointInfoList.length === 1
                  ? pointInfoList[0].getDailyPoint > 0 && (
                      <AlertPoint pointInfo={pointInfoList[0]} />
                    )
                  : !isPointShow && <AlertPointMultiple pointInfoList={pointInfoList} />}
              </div>
            )}
          {/* ミッション詳細（クイズ） POPUP画面詳細 */}
          {popupQuizContent && (
            <div className="m-6 rounded-2xl border border-[#B2B2B2] p-4">
              {/* クイズ 質問型判定 */}
              {popupQuizContent.quizType === QUIZ_TYPE.SINGLE_CHOICE ? (
                <div className="flex justify-center text-lg font-bold">
                  {APP_TEXT.MISSION_PAGE.CORRECT_ANSWER} {popupQuizContent.correctAnswer}
                </div>
              ) : (
                <div className="flex justify-center text-lg font-bold">
                  {/* {APP_TEXT.MISSION_PAGE.CORRECTPOINT} */}
                  {APP_TEXT.MISSION_PAGE.CORRECT_ANSWER}
                  {popupQuizContent.correctAnswer === '1' ? '〇' : '✕'}
                </div>
              )}
              {popupQuizContent.explanation && (
                <div className="my-2 text-sm break-all">{popupQuizContent.explanation}</div>
              )}
              {popupQuizContent.image && (
                <div>
                  <img className="w-full" src={popupQuizContent.image} alt="" />
                </div>
              )}
            </div>
          )}
          {/* ポイント権限 */}
          <div>
            {(popupContent?.iconFlg === 1 || popupQuizContent?.iconFlg === 1) &&
              todayAchievedInfoList && (
                <CategoryItem alertFlag={false} categoryData={todayAchievedInfoList} />
              )}
          </div>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel
            className="rounded-3xl ml-4 mr-4 border-0  bg-primary font-bold h-12 text-white text-[16px]"
            onClick={handleCancel}
          >
            {COMMON_TEXT.BUTTON.CLOSE}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
