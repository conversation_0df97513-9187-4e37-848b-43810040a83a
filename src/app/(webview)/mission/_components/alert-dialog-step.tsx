'use client';

import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import type { PointInfoDetails, StepPointInfo } from '@/types/home-data';

import { useEffect, useState } from 'react';
import { formatNumber } from '../_utils/data-convert';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from './alert-dialog';

export default function StepAlertDialog({
  open,
  popupContent,
  onCancel,
}: {
  open: boolean;
  popupContent?: StepPointInfo | undefined;
  onCancel: () => void;
}) {
  const [pointInfoList, setPointInfoList] = useState<PointInfoDetails[]>();

  useEffect(() => {
    setPointInfoList(popupContent?.details ?? undefined);
  }, [popupContent]);

  const handleCancel = () => {
    onCancel();
  };

  return (
    <AlertDialog open={open}>
      <AlertDialogContent className="rounded-2xl shadow-none border-0 bg-white px-0 gap-0">
        {/*歩数達成でポイントを獲得 */}
        {popupContent && (
          <div>
            {/* ポイント権限フラグ  権限あり*/}
            <AlertDialogTitle>
              <div className="flex justify-center">
                <img
                  className="w-[64px]"
                  src="/images/misson/point-finish.svg"
                  alt="mission-finish"
                />
              </div>
              <div className="flex flex-col items-center font-bold justify-center mb-4">
                <span>{APP_TEXT.HOME_DIALOG.STEP_POINT}</span>
                <span>{APP_TEXT.HOME_DIALOG.POINT}</span>
              </div>
            </AlertDialogTitle>
          </div>
        )}
        <AlertDialogDescription className="hidden" />

        <div className="overflow-auto max-h-[60vh]">
          {/* ポイント権限  ポイント内訳表示フラグ true*/}
          <div className="rounded-2xl bg-[#F6F8FF] mx-4 px-6 divide-y divide-[#B2B2B2] py-1">
            {pointInfoList?.map((item, i) => {
              return (
                <div key={item.organizerId} className="flex justify-between items-center py-2">
                  <div className="text-sm">{item.organizerName}</div>

                  <div className="text-end">
                    <span className="text-xl font-bold text-primary">
                      {formatNumber(item.point || 0)}
                    </span>
                    {APP_TEXT.MISSION_PAGE.MISSION_P}
                  </div>
                </div>
              );
            })}

            {/* pointInfoList */}
          </div>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel
            className="rounded-3xl m-4 border-0  bg-primary font-bold h-12 text-white text-[16px]"
            onClick={handleCancel}
          >
            {COMMON_TEXT.BUTTON.CLOSE}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
