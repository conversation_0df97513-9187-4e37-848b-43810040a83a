'use client';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import type { MissionOrganizerData } from '@/types/mission';
import { Minus, Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import Achievement from './achievement';

export default function CategoryItem({
  categoryData,
  alertFlag,
}: {
  alertFlag: boolean;
  categoryData: MissionOrganizerData[] | undefined;
}) {
  const [categoryList, setCategoryList] = useState<MissionOrganizerData[] | undefined>();
  const [isOpen, setIsOpen] = useState(false);
  const [teamFlag, setTeamFlag] = useState(false);
  const [totalRemainingFlag, setTotalRemainingFlag] = useState<boolean>(false);

  useEffect(() => {
    if (categoryData) {
      setTeamFlag(categoryData.length === 1);
      setIsOpen(categoryData.length === 1);
      setTotalRemainingFlag(allpointAreZero(categoryData));
      setCategoryList(categoryData);
    }
  }, [categoryData]);

  const allpointAreZero = (items: MissionOrganizerData[]): boolean => {
    return items.every((item) => item.totalRemaining === 0);
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full bg-white rounded-2xl pt-6">
        <div className="text-lg font-bold px-6">{APP_TEXT.MISSION_PAGE.ACHIEVEMENT_TITLE}</div>
        {/* オートコンプリートポップアップウィンドウに「ポイントゲットはまた明日」が表示されます */}
        {!teamFlag && alertFlag && (
          <div className="border-b border-solid px-6 border-[#F6F8FF] pb-6">
            <div className="inline-block text-xs py-[2px] text-primary rounded bg-primary-5 px-1">
              {totalRemainingFlag
                ? APP_TEXT.MISSION_PAGE.ACHIEVEMENT_DESCRIBE_NO
                : APP_TEXT.MISSION_PAGE.ACHIEVEMENT_DESCRIBE}
            </div>
          </div>
        )}
        {/* POPUP画面参照に「詳しく見る」が表示されない */}
        <Collapsible open={alertFlag ? isOpen : true} onOpenChange={setIsOpen}>
          <CollapsibleContent className="space-y-2">
            <div className={teamFlag ? 'mt-4' : 'mt-6'}>
              {categoryList?.map((item, i) => (
                <Achievement
                  organizerData={item}
                  key={i}
                  teamFlag={teamFlag}
                  alertFlag={alertFlag}
                />
              ))}
            </div>
          </CollapsibleContent>
          {/* POPUP画面参照にはアニメーション効果がありません */}
          {!teamFlag && alertFlag && (
            <div>
              {isOpen && <div className="border-b border-solid border-[#F6F8FF]" />}
              <CollapsibleTrigger asChild>
                {isOpen ? (
                  <div className="w-full flex items-center justify-center space-x-4 py-3 text-base font-bold text-primary">
                    <Minus className="pr-1" />
                    {COMMON_TEXT.BUTTON.CLOSE}
                  </div>
                ) : (
                  <div className="w-full flex items-center justify-center space-x-4 py-3 text-base font-bold text-primary">
                    <Plus className="pr-1" />
                    {APP_TEXT.MISSION_PAGE.MORE}
                  </div>
                )}
              </CollapsibleTrigger>
            </div>
          )}
        </Collapsible>
      </div>
    </div>
  );
}
