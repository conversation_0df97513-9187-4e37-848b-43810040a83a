'use client';

import { APP_TEXT } from '@/const/text/app';
import type { PointInfoType } from '@/types/mission';
import { formatNumber } from '../_utils/data-convert';

export default function AlertPointMultiple({
  pointInfoList,
}: { pointInfoList: PointInfoType[] | undefined }) {
  return (
    <div className="rounded-2xl bg-[#F6F8FF] px-6 divide-y divide-[#B2B2B2] py-1">
      {pointInfoList?.map(
        (item, i) =>
          item.getDailyPoint > 0 && (
            <div key={item.organizerId} className="flex justify-between items-center py-2">
              <div className="text-sm">{item.organizerName}</div>
              {item.getAchievePoint > 0 && (
                <div className="text-xs">
                  <div className="mb-3">{APP_TEXT.MISSION_PAGE.BONUS_COUNT}</div>
                  <div>{APP_TEXT.MISSION_PAGE.BONUS}</div>
                </div>
              )}
              {item.getAchievePoint > 0 ? (
                <div className="text-sm">
                  <div className="text-end">
                    <span className="text-xl font-bold text-primary text-end">
                      {formatNumber(item.getDailyPoint)}
                    </span>
                    {APP_TEXT.MISSION_PAGE.MISSION_P}
                  </div>
                  <div className="text-end">
                    <span className="text-xl font-bold text-primary">
                      {formatNumber(item.getAchievePoint)}
                    </span>
                    {APP_TEXT.MISSION_PAGE.MISSION_P}
                  </div>
                </div>
              ) : (
                <div className="text-end">
                  <span className="text-xl font-bold text-primary">
                    {formatNumber(item.getDailyPoint)}
                  </span>
                  {APP_TEXT.MISSION_PAGE.MISSION_P}
                </div>
              )}
            </div>
          ),
      )}

      {/* pointInfoList */}
    </div>
  );
}
