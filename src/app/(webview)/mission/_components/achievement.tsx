'use client';

import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { APP_TEXT } from '@/const/text/app';
import type { MissionOrganizerData } from '@/types/mission';
import { useCallback, useEffect, useState } from 'react';
import { getRoleText } from '../_utils/data-convert';
import { CATEGORY_TYPE } from '../_utils/enums';

export default function Achievement({
  teamFlag,
  alertFlag,
  organizerData,
}: {
  teamFlag: boolean;
  alertFlag: boolean;
  organizerData: MissionOrganizerData | undefined;
}) {
  const [organizationData, setOrganizationData] = useState<MissionOrganizerData | undefined>();
  const [progressValue, setProgressValue] = useState<number>(0);
  const [progressArr, setProgressArr] = useState<number[]>();

  // 奇数かどうかを判定し、区間数を取得する
  const getMultipleData = (num: number) => {
    if (num % 10 === 0) return 5;
    if (num % 6 === 0) return 3;
    if (num % 5 === 0) return 5;
    if (num % 3 === 0) return 3;
    if (num % 2 === 0) return 2;
    return null;
  };

  // タスク進捗バーの表示効果 奇数はすべて表示
  const generateArray = (max: number) => {
    const step: number | null = getMultipleData(max);
    if (step) {
      return Array.from({ length: Math.floor(max / step) + 1 }, (_, i) => i * step);
    }
    const stepOdd: number | null = getMultipleData(max + 1);
    if (stepOdd) {
      const oddArr = Array.from({ length: Math.floor(max / stepOdd) + 1 }, (_, i) => i * stepOdd);
      oddArr[oddArr.length - 1] = max;
      return oddArr;
    }
  };

  useEffect(() => {
    if (organizerData) {
      setOrganizationData(organizerData);
      const totalAchievedValue = (organizerData.totalAchieved / organizerData.totalLimit) * 100;
      const totalLimitArr = generateArray(organizerData.totalLimit);
      setProgressArr(totalLimitArr);
      setProgressValue(totalAchievedValue);
    }
  }, [organizerData]);

  return (
    <div className="pb-6">
      <div className="px-6">
        {!teamFlag && (
          <div className="flex text-base font-normal  items-center mb-4">
            <div className="w-[4px] text-base font-normal mr-2 h-5  bg-primary-soft" />
            {organizationData?.organizerName}
          </div>
        )}
        <div className="text-sm font-bold pb-1 ">{APP_TEXT.MISSION_PAGE.DESCRIBE}</div>
        {/* 団体別残ミッション数 */}
        <div className="text-base flex  items-center h-full pb-1 ">
          {APP_TEXT.MISSION_PAGE.AFTER}
          <span className="text-3xl font-bold px-1">{organizationData?.totalRemaining}</span>
          {APP_TEXT.MISSION_PAGE.INDIVIDUAL}
          {teamFlag && organizerData?.totalRemaining === 0 && alertFlag && (
            <div className="ml-1 inline-block text-xs py-[2px] text-primary rounded bg-[#F6F8FF] px-1">
              {APP_TEXT.MISSION_PAGE.ACHIEVEMENT_DESCRIBE_NO}
            </div>
          )}
        </div>
        {/* ミッション 完了タスクの進捗状況バー */}
        <Progress
          value={progressValue}
          className="bg-primary-10"
          indicatorClassName="duration-700 bg-primary-light"
        />
        <div className=" flex text-xs justify-between text-slate-400 ">
          {progressArr?.map((val: number, i: number) => (
            <div key={i}>{val}</div>
          ))}
        </div>
        <div className="flex text-xs justify-end text-slate-400 ">
          ({APP_TEXT.MISSION_PAGE.ACHIEVEMENTCEILING})
        </div>
        <ul className="rounded-lg divide-y divide-[#B2B2B2]">
          {/* カテゴリ一覧 */}
          {organizationData?.categoryList?.map((item, i) => (
            <li key={i} className="flex justify-between py-2  items-center">
              <div className=" text-sm">
                {getRoleText(item.category)}
                <span className="text-xs text-slate-400">({item.categoryLimit})</span>
              </div>
              {/* カテゴリ別残ミッション数 */}
              {item.categoryRemaining !== 0 ? (
                <div className="text-sm flex items-center ">
                  {APP_TEXT.MISSION_PAGE.AFTER}
                  <span className="text-xl font-bold text-primary">{item.categoryRemaining}</span>
                  {APP_TEXT.MISSION_PAGE.INDIVIDUAL}
                </div>
              ) : (
                // カテゴリ別残ミッション数 完了
                <Badge className="border-primary-100 font-normal text-textprimary text-white px-1  py-[2px] ">
                  {APP_TEXT.MISSION_PAGE.ACHIEVE_ALL}
                </Badge>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
