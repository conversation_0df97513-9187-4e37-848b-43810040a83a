'use client';

import { missionAPI } from '@/api/modules/mission';
import TopBar from '@/components/layout/top-bar';
import { APP_TEXT } from '@/const/text/app';
import type { MissionListResponse, MissionPopupDetailResponse } from '@/types/mission';
import { useEffect, useRef, useState } from 'react';
import AlertDialogMission from './_components/alert-dialog-mission';
import CategoryItem from './_components/category-item';
import MissionList from './_components/mission-list';
export default function Mission() {
  const [missionData, setMissionData] = useState<MissionListResponse>();
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const popupContentRef = useRef<MissionPopupDetailResponse>();

  useEffect(() => {
    missionAPI.missionList().then((res) => {
      if (res) {
        setMissionData(res);
      }
      if (res.achievedMissionExistFlg) {
        getPopupData();
      }
    });
  }, []);

  // 今日達成ミッション POPUP画面情報を取得する
  const getPopupData = () => {
    missionAPI.missionPopupDetail().then((res) => {
      if (res) {
        popupContentRef.current = res;
        setOpenDialog(true);
      }
    });
  };

  return (
    <div>
      <TopBar title={APP_TEXT.MISSION_PAGE.TITLE} />
      <div className="py-6 mx-6">
        {missionData?.detailList && (
          <CategoryItem alertFlag={true} categoryData={missionData.detailList} />
        )}
      </div>
      {/* 詳細一覧 */}
      <div className="mx-6">
        {missionData?.missionList?.map((item, i) => (
          <MissionList key={i} missionItems={item} />
        ))}
      </div>
      {/* ミッション達成POPUP画面詳細 */}
      <AlertDialogMission
        open={openDialog}
        popupContent={popupContentRef.current}
        onCancel={() => {
          setOpenDialog(false);
        }}
      />
    </div>
  );
}
