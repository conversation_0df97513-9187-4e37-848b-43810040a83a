import type { OrganizerInfoBean } from '@/types/home-data';
import type { GetHomePointCardInfo } from '@/types/point';

// Convert numbers to comma separated strings
export const formatNumberWithCommas = (num: number | undefined) => {
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// judge is it empty
export const isEmptyData = (v: any) => {
  if (v === '' || v === undefined || v === null || v === 'null' || v?.length === 0) {
    return false;
  }
  return true;
};

// Merge two arrays with the same organizeId
export const mergeByOrganizerId = (list1: OrganizerInfoBean[], list2: GetHomePointCardInfo[]) => {
  return list1?.map((item1: OrganizerInfoBean) => {
    const match = list2?.find(
      (item2: GetHomePointCardInfo) => item1?.organizerId === item2?.organizerId,
    );
    return match ? { ...item1, ...match } : item1;
  }) as OrganizerInfoBean[];
};
