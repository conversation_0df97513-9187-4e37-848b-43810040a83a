// 利用/獲得種別
export const POINT_TYPE: { [key: number]: string } = {
  0: '-',
  1: '利用',
  2: '獲得',
};

export enum POINT_TYPE_TEXT {
  USE = 1,
  GET = 2,
}

// 抽選表示フラグ
export const LOTTERY_SHOW_FLG: { [key: number]: string } = {
  0: '-',
  1: '公開日前',
  2: '開催予定',
  3: '応募中',
};

export enum LOTTERY_SHOW_FLG_TYPE {
  OPEN_DAY = 1,
  SCHEDULE = 2,
  APPLICATION = 3,
  CHANGE = 4,
}

// 表示カテゴリー
export const CATEGORY = [
  { value: 1, name: 'すべて' },
  { value: 2, name: '記録' },
  { value: 3, name: '抽選' },
  { value: 4, name: '歩数' },
  { value: 5, name: 'ミッション' },
  { value: 6, name: 'その他' },
];

// ポイントヒントタイトル
export const POINT_HINT_TITLE = [
  { value: '1', name: 'ポイント獲得について' },
  { value: '2', name: '保有ポイントについて' },
  { value: '3', name: '今年度獲得ポイントについて' },
  { value: '4', name: '通算獲得ポイントについて' },
  { value: '5', name: '交換可能ポイントについて' },
  { value: '6', name: '景品選択可能ポイントについて' },
];

export enum IS_TRUE {
  NO = 0, //なし、無
  YES = 1, //あり、有
}
