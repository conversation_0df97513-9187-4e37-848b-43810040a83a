import { Alert, AlertDescription } from '@/components/ui/alert';

interface AlertPropsType {
  imgSrc?: string;
  alertContent: string;
  handleClick?: () => void;
}

export const AlertPage = (props: AlertPropsType) => {
  const { imgSrc = '/images/point/warn.svg', alertContent } = props;
  return (
    <Alert
      variant="destructive"
      className="border-destructive-foreground rounded-2xl text-black bg-[#FDF2F2] mb-6"
    >
      <div className="flex items-center justify-between">
        <img src={imgSrc} alt="warn" width={24} height={24} />
        <AlertDescription className="ml-3">{alertContent}</AlertDescription>
      </div>
    </Alert>
  );
};
