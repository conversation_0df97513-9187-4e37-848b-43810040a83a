interface TableRowData {
  title: string;
  content: string;
}

interface TablePageProps {
  data: TableRowData[];
}

export const VerticalTablePage = (props: TablePageProps) => {
  return (
    <div className="border-t border-l border-border border-solid mt-2">
      {props?.data?.map((item, index) => (
        <div
          key={`verticaltable${index}`}
          className="grid grid-cols-2 h-10 text-center leading-10  "
        >
          <div
            className={`border-r border-b border-border  border-solid ${index === 0 ? 'bg-main-fix' : ''}`}
          >
            {item.title}
          </div>
          <div
            className={`border-r border-b  border-border border-solid ${index === 0 ? 'bg-main-fix' : ''}`}
          >
            {item.content}
          </div>
        </div>
      ))}
    </div>
  );
};
