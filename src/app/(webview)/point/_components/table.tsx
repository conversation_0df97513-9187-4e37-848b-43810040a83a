import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface TableRowData {
  bodyFirst: string;
  bodySecond: string;
}

interface TablePageProps {
  headerFirst: string;
  headerSecond: string;
  data: TableRowData[];
}

export const TablePage = (props: TablePageProps) => {
  return (
    <Table className="mt-2">
      <TableHeader className="bg-main-fix ">
        <TableRow className="border-none text-sm ">
          <TableHead className="text-black text-center border border-border border-solid">
            {props?.headerFirst}
          </TableHead>
          <TableHead className="text-black text-center border border-border border-solid">
            {props?.headerSecond}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {props?.data?.map((item) => (
          <TableRow key={`${item.bodyFirst}table`} className="border-none text-center text-base">
            <TableCell className="border border-border border-solid">{item.bodyFirst}</TableCell>
            <TableCell className="border border-border border-solid">{item.bodySecond}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
