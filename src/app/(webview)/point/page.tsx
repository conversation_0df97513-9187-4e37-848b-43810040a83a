'use client';

import { pointAPI } from '@/api/modules/point';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { APP_TEXT } from '@/const/text/app';
import { codeTypeList, useCodeOptions } from '@/hooks/use-common-code';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { usePointStore } from '@/store/point';
import type { CodeOption } from '@/types/code-types';
import type {
  GetLotteryInfoResponse,
  GetPointCardInfoResponse,
  GetPointHistoryList,
  GetResetInfoResponse,
} from '@/types/point';
import { format, parse, parseISO } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CircleParking } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AlertPage } from './_components/alert';
import { SelectCategoryDrawer } from './_components/category-drawer';
import { SelectDateDrawer } from './_components/date-drawer';
import { IS_TRUE, LOTTERY_SHOW_FLG_TYPE, POINT_TYPE, POINT_TYPE_TEXT } from './_const';
import { formatNumberWithCommas } from './_utils';
/**
 * 表示期間の型定義
 */
interface DateType {
  year: string;
  month: string;
}

export default function PointPage() {
  const router = useRouter();
  const pointTypeOptions = useCodeOptions(codeTypeList.POINT_TYPE);

  const { homeCardInfo } = usePointStore();
  const organizerId = Number(homeCardInfo?.organizerId) || 0;
  const { isLoading, setIsLoading } = useLoading();
  const [lotteryInfo, setLotteryInfo] = useState<GetLotteryInfoResponse>({
    lotteryShowFlg: 3,
    lotteryId: 0,
    prizeApplyEndDate: '',
  });
  const [pointInfo, setPointInfo] = useState<GetPointCardInfoResponse>({
    organizerId: 0,
    availablePoints: 0,
    premiumAvailablePoints: 0,
    currentYearPlusPoints: 0,
    totalPlusPoints: 0,
  });
  const [resetInfo, setResetInfo] = useState<GetResetInfoResponse>({
    resetFlg: 0,
    resetDate: '',
  });
  const [selectedGender, setSelectedGender] = useState<string>('0');
  const [selectedGenderOption, setSelectedGenderOption] = useState<CodeOption[]>([]);
  const [progress, setProgress] = useState(33);
  const dataContainerRef = useRef<HTMLDivElement>(null);
  const [pointHistory, setPointHistory] = useState<GetPointHistoryList[]>([]);
  const [selectedYearMonth, setSelectedYearMonth] = useState<DateType>({
    year: String(new Date().getFullYear()),
    month: String(new Date().getMonth() + 1),
  });
  const minYear = 2000;
  const maxYear = new Date().getFullYear();

  const handleSelectDateDrawer = (selected: DateType) => {
    // console.log('Selected Year and Month:', selected);
    setSelectedYearMonth(selected);
  };

  const handleSelectCategoryDrawer = (value: string) => {
    // console.log('value', value);
    setSelectedGender(value);
  };

  const linkHint = () => {
    router.push('/point/hint');
  };

  // ポイント履歴一覧情報取得API
  const getPointHistoryList = useCallback(async () => {
    setIsLoading(true);
    try {
      // Safari互換性のため、日付文字列を安全に構築
      const yearMonthString = `${selectedYearMonth.year}-${selectedYearMonth.month.padStart(2, '0')}`;
      const result = await pointAPI.getPointHistoryList({
        showMonth: yearMonthString,
        categoryId: Number(selectedGender),
      });
      setPointHistory(result?.pointHistoryList ?? []);
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedYearMonth, selectedGender, setIsLoading]);

  useEffect(() => {
    getPointHistoryList();
  }, [getPointHistoryList]);

  useEffect(() => {
    getCommonCode();
    fetchData();
    // getPointCardInfo();
    // getLotteryInfo();
    // getResetInfo();
  }, []);

  const getCommonCode = () => {
    const genderOptions = pointTypeOptions || [];
    setSelectedGenderOption([{ value: '0', label: 'すべて' }, ...genderOptions]);
  };

  const getCategoryName = (id: number | undefined) => {
    let list = [];
    list = selectedGenderOption
      ?.filter((item) => Number(item.value) === id)
      .map((item) => item.label);
    return list?.length > 0 ? list[0] : '';
  };

  const fetchData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([getPointCardInfo(), getResetInfo()]);
    } catch (error) {
      // console.log('One or more API requests failed:', error)
    } finally {
      setIsLoading(false);
    }
  };

  // 保有ポイントカード情報取得API
  const getPointCardInfo = async () => {
    // setLoading(true, { text: 'データを通信中...' });
    try {
      const result = await pointAPI.getPointCardInfo();
      console.log('result', result);
      setPointInfo(result);
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
    }
  };

  // リセット情報取得API
  const getResetInfo = async () => {
    // setLoading(true, { text: 'データを通信中...' });
    try {
      const result = await pointAPI.getResetInfo();
      console.log('result', result);
      setResetInfo(result);
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
    }
  };

  // 抽選応募情報取得API
  const getLotteryInfo = async () => {
    try {
      const result = await pointAPI.getLotteryInfo();
      setLotteryInfo(result);
    } catch (error) {
      console.log('Error fetching directions:', error);
    } finally {
    }
  };

  // 抽選情報を見る
  const linkLotteryResult = () => {
    router.push(`/lottery-result?status=${lotteryInfo.lotteryShowFlg}`);
  };

  // Display day of the week unit
  const getWeekday = (v: string) => {
    const weekday = format(parse(v, 'yyyy/MM/dd', new Date()), 'eeee', { locale: ja })[0];
    return weekday;
  };

  return (
    <div>
      <TopBar title={APP_TEXT.POINT.TITLE} />
      {/* ポイント履歴 */}
      <div className="p-6">
        {resetInfo.resetFlg === IS_TRUE.YES && (
          <AlertPage
            alertContent={
              resetInfo.resetDate
                ? `${format(resetInfo.resetDate, 'yyyy年MM月dd日')}に保有ポイントがリセットされます`
                : '-'
            }
          />
        )}

        <Card>
          <CardHeader className="pb-0">
            <CardTitle className="flex">
              <div className="font-bold text-lg"> {APP_TEXT.POINT.HOLD_POINT}</div>
              {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
              <img
                src="/images/point/hint.svg"
                alt="hint"
                width={20}
                height={20}
                onClick={linkHint}
              />
            </CardTitle>
            <CardDescription className="!mt-1">{homeCardInfo?.organizerName}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mt-1">
              {/* <img src="/images/point/stop.svg" alt="stop" width={32} height={32} /> */}
              <CircleParking className="text-primary-light" size={32} />
              <span className="font-bold text-[34px]">
                {formatNumberWithCommas(pointInfo.availablePoints)}{' '}
                <span className="font-normal text-[22px]">p</span>
              </span>
            </div>
            {/* TODO: Customer feedback dev environment error, comment out [selection] 08/05 */}
            {/* {lotteryInfo.lotteryShowFlg === LOTTERY_SHOW_FLG_TYPE.APPLICATION && (
              <Badge
                variant="secondary"
                className="w-full h-12 text-black flex items-center justify-between rounded-2xl bg-main-fix my-4 px-6"
              >
                <span className="font-normal text-sm">{APP_TEXT.POINT.GIFT_SELECTABLE}</span>
                <span className="font-bold text-base">
                  {formatNumberWithCommas(pointInfo.premiumAvailablePoints)}{' '}
                  <span className="font-normal text-sm">p</span>
                </span>
              </Badge>
            )} */}
            {/* 受付中 */}
            {/* {(lotteryInfo.lotteryShowFlg === LOTTERY_SHOW_FLG_TYPE.APPLICATION ||
              lotteryInfo.lotteryShowFlg === LOTTERY_SHOW_FLG_TYPE.SCHEDULE) && (
              <div className="w-full mt-4 bg-primary rounded-2xl text-center text-white px-6">
                <img src="/images/point/gift.svg" alt="gift" className="m-auto pt-4 pb-2" />
                {lotteryInfo.lotteryShowFlg === LOTTERY_SHOW_FLG_TYPE.APPLICATION ? (
                  <>
                    <div className="font-bold text-lg">{APP_TEXT.POINT.RUNNING}</div>
                    {lotteryInfo.prizeApplyEndDate && (
                      <div className="font-normal text-sm mt-[2px]">
                        {format(lotteryInfo.prizeApplyEndDate, 'yyyy年MM月dd日')}(
                        {getWeekday(lotteryInfo.prizeApplyEndDate)}) まで
                      </div>
                    )}
                  </>
                ) : (
                  <div className="font-bold text-lg mb-2">{APP_TEXT.POINT.START_SOON}</div>
                )}

                <Button
                  className="bg-main-color text-primary font-bold h-10 rounded-3xl w-full mt-2 mb-6"
                  onClick={linkLotteryResult}
                >
                  {APP_TEXT.POINT.DRAWING}
                </Button>
              </div>
            )} */}
            {/* ません */}
            {/* {lotteryInfo.lotteryShowFlg === LOTTERY_SHOW_FLG_TYPE.OPEN_DAY && (
              <div className="w-full  mt-4 bg-muted rounded-2xl text-center text-black px-6">
                <div className="font-normal text-sm pt-6 pb-4">{APP_TEXT.POINT.NO_START}</div>
                <Button
                  className="bg-main-color text-primary font-bold h-10 rounded-3xl w-full mb-6"
                  type="button"
                  variant="outline"
                  onClick={linkLotteryResult}
                >
                  {APP_TEXT.POINT.DRAWING_HISTORY}
                </Button>
              </div>
            )} */}
            {/* その他 */}
            {/* {lotteryInfo.lotteryShowFlg === LOTTERY_SHOW_FLG_TYPE.CHANGE && (
              <>
                <div className="w-full  mt-4 bg-main-color rounded-2xl  text-black px-6">
                  <div className="font-bold text-base text-left py-4 ">
                    {APP_TEXT.POINT.CHANGE_POINT}
                  </div>
                  <Progress value={progress} className="w-full bg-primary-softer" />
                  <div className="flex justify-between items-center font-normal text-[10px]">
                    <div>0</div>
                    <div>1,000</div>
                    <div>2,000</div>
                    <div>3,000</div>
                  </div>
                  <div className="text-right font-normal text-[10px] pt-[2px]">
                    {APP_TEXT.POINT.MAX_POINT}
                  </div>
                  <div className="w-full h-10 flex items-center justify-between font-normal my-2">
                    <span className=" text-sm">{APP_TEXT.POINT.POSSIBLE_POINT}</span>
                    <span className=" text-base">
                      {formatNumberWithCommas(pointInfo.premiumAvailablePoints)}
                      <span className=" text-sm">p</span>
                    </span>
                  </div>
                  <Button
                    className="bg-primary text-white font-bold h-10 rounded-3xl w-full mb-6"
                    type="button"
                    variant="outline"
                    onClick={linkLotteryResult}
                  >
                    {APP_TEXT.POINT.OTHER_POINT}
                  </Button>
                </div>
                <div className="font-normal text-sm text-muted-foreground py-4 border-b border-border border-solid">
                  {APP_TEXT.POINT.POINT_RESET_DESC}
                  {APP_TEXT.POINT.POINT_RESET_DESC2}
                </div>
              </>
            )} */}

            <div className="flex items-center w-full mt-4 font-normal">
              <div className="w-1/2">
                <div className="text-xs text-left">{APP_TEXT.POINT.YEAR_POINT}</div>
                <div className="text-base text-right mt-[2px]">
                  {formatNumberWithCommas(pointInfo.currentYearPlusPoints)}p
                </div>
              </div>
              <div className="mx-4 h-11 border-r border-border border-solid" />
              <div className="w-1/2">
                <div className="text-xs text-left">{APP_TEXT.POINT.ACQUISTION_POINT}</div>
                <div className="text-base text-right mt-[2px]">
                  {formatNumberWithCommas(pointInfo.totalPlusPoints)}p
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ポイント履歴 */}
      <div className="font-bold text-lg ml-6 pb-2"> {APP_TEXT.POINT.HISTORY}</div>
      <div className="bg-white pt-4 mb-4">
        <div className="grid grid-cols-2 gap-4  px-6">
          {/* 表示期間 */}
          <SelectDateDrawer
            title={APP_TEXT.POINT.DATE}
            minYear={minYear}
            maxYear={maxYear}
            value={selectedYearMonth}
            onSelect={handleSelectDateDrawer}
          />
          {/* 表示カテゴリー */}
          <SelectCategoryDrawer
            defaultValue={selectedGender}
            options={selectedGenderOption}
            title={APP_TEXT.POINT.CATEGORY}
            onChange={handleSelectCategoryDrawer}
          />
        </div>

        {pointHistory?.length > 0 ? (
          <div ref={dataContainerRef} className=" px-6">
            {pointHistory?.map((item: GetPointHistoryList, index: number) => (
              <div
                key={index}
                // className={`flex justify-between items-center border-border py-3 ${index === pointHistory.length - 1 ? 'border-none' : 'border-b'}`}
                className="flex justify-between items-center border-border py-3  border-b last:border-none"
              >
                <div className="font-normal ">
                  <span className="bg-main-fix text-xs text-button py-[3px] px-1 rounded-sm text-center">
                    {getCategoryName(item.categoryId)}
                  </span>
                  <div className="text-base my-[2px] pr-3">{item.pointName}</div>
                  <div className="text-sm !text-[#666666]">
                    {item.historyDate
                      ? `${format(new Date(item.historyDate), 'yyyy年MM月dd日')} (${getWeekday(item.historyDate)})`
                      : '-'}
                  </div>
                </div>
                <div
                  className={`text-right ${item.changeKind === POINT_TYPE_TEXT.USE ? 'text-destructive-foreground' : 'text-switch'}`}
                >
                  <div className="font-bold text-lg">
                    {item.changeKind === POINT_TYPE_TEXT.USE ? '-' : '+'}
                    {formatNumberWithCommas(item.point)}
                    <span className="font-bold text-base">p</span>
                  </div>
                  <div className="font-bold text-sm">{POINT_TYPE[item.changeKind ?? 0]}</div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="h-[152px] pb-4 bg-white text-base font-normal text-muted-foreground flex items-center justify-center">
            {APP_TEXT.POINT.NO_HISTORY}
          </div>
        )}
      </div>
    </div>
  );
}
