'use client';

import { ServiceSettingRequest, dataConnectAPI } from '@/api/modules/data-connect';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import MenuPanel2 from '@/components/shared/menu-panel2';
import { Switch } from '@/components/shared/switch';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { DeviceType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { COMMON_TEXT } from '@/const/text/common';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter } from '@/hooks/use-next-navigation';
import type { OptionInfo } from '@/types/data-connect';
import { isAndroid, isIOS } from '@/utils/device-detect';
import { sendMessageToNative } from '@/utils/native-bridge';
import { useCallback, useEffect, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';
import fitbitIcon from '../../../images/fitbit.png';
import healthconnect from '../../../images/healthconnect.png';
import usageImage from '../../../images/heathcare.png';

function Goal() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const { isShow, setDialog } = useMessageDialog();
  const [fitbit, setFitBit] = useState(false);
  const [healthConnect, setHealthConnect] = useState(false);
  const [omron, setOmron] = useState(false);
  const [useOmron, setUseOmron] = useState(false);
  const [weight, setWeight] = useState(0);
  const [press, setPress] = useState(0);
  const [sleep, setSleep] = useState(0);
  const [sleepOptions, setSleepOptions] = useState<OptionInfo[]>();
  const [weightOptions, setWeightOptions] = useState<OptionInfo[]>();
  const [pressOptions, setPressOptions] = useState<OptionInfo[]>();
  const fetchData = useCallback(() => {
    // const isFormHealthRecord = router.getOnceMetadata()?.isFormHealthRecord;
    setLoading(true, { text: COMMON_TEXT.MESSAGE.DATA_SYNC });
    // setTimeout(
    //   () => {
    dataConnectAPI
      .deviceSetting()
      .then((response) => {
        setLoading(false);
        if (response.deviceConInfo) {
          setFitBit(response.deviceConInfo.fitbitConFlg === 1);
          setHealthConnect(response.deviceConInfo.healthConConFlg === 1);
          // setUseOmron(response.deviceConInfo.omronUsingFlg === 1);
          // setOmron(response.deviceConInfo.omronConFlg === 1);
        }

        if (response.vitalDataInfo) {
          if (response.vitalDataInfo.weightFrom !== undefined) {
            setWeight(response.vitalDataInfo.weightFrom);
          }
          if (response.vitalDataInfo.bpFrom !== undefined) {
            setPress(response.vitalDataInfo.bpFrom);
          }
          if (response.vitalDataInfo.sleepFrom !== undefined) {
            setSleep(response.vitalDataInfo.sleepFrom);
          }
        }

        if (response.sleepOptionInfo) {
          setSleepOptions(response.sleepOptionInfo);
        }
        if (response.bpOptionInfo) {
          setPressOptions(response.bpOptionInfo);
        }
        if (response.weightOptionInfo) {
          setWeightOptions(response.weightOptionInfo);
        }
        // ... existing code ...
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
    //   }
    //   ,isFormHealthRecord ? 0 : 1500,
    // );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const goBack = () => {
    const path = router.getBackPath([
      ROUTES.MENU.SETTINGS,
      ROUTES.MENU.MENU,
      ROUTES.GOAL.HEALTH_RECORD,
    ]);
    if (path !== '') {
      router.backTo(path);
      return;
    }

    router.replace(ROUTES.HOME);
  };

  const onChangeOmron = () => {
    if (omron === true) {
      //true -> false omron連携を解除
      if (weight === 7 || sleep === 7 || press === 7) {
        //show popup
        let str = '';
        if (weight === 7) {
          str = `${str}・${APP_TEXT.DATA_CONNECT.WEIGHT}`; //
        }
        if (sleep === 7) {
          if (str !== '') {
            str = `${str}<br/>・${APP_TEXT.DATA_CONNECT.SLEEP}`; //
          } else {
            str = `${str}・${APP_TEXT.DATA_CONNECT.SLEEP}`; //
          }
        }
        if (press === 7) {
          if (str !== '') {
            str = `${str}<br/>・${APP_TEXT.DATA_CONNECT.PRESS}`; //
          } else {
            str = `${str}・${APP_TEXT.DATA_CONNECT.PRESS}`; //
          }
        }
        const strs = str.split('<br/>');
        showOFFDisableConfirm(7, strs);
      } else {
        //確認ポップアップ
        showOFFConfirm(7);
      }
    } else {
      //false -> true　omron連携
      router.push(ROUTES.DATA_CONNECT.OMRON);
    }
  };

  const onChangeConnect = () => {
    if (healthConnect === true) {
      if (weight === 4 || sleep === 4 || press === 4) {
        //show popup
        let str = '';
        if (weight === 4) {
          str = `${str}・${APP_TEXT.DATA_CONNECT.WEIGHT}`; //
        }
        if (sleep === 4) {
          if (str !== '') {
            str = `${str}<br/>・${APP_TEXT.DATA_CONNECT.SLEEP}`; //
          } else {
            str = `${str}・${APP_TEXT.DATA_CONNECT.SLEEP}`; //
          }
        }
        if (press === 4) {
          if (str !== '') {
            str = `${str}<br/>・${APP_TEXT.DATA_CONNECT.PRESS}`; //
          } else {
            str = `${str}・${APP_TEXT.DATA_CONNECT.PRESS}`; //
          }
        }
        const strs = str.split('<br/>');
        showOFFDisableConfirm(4, strs);
      } else {
        //確認ポップアップ
        showOFFConfirm(4);
      }
      //true -> false healthConnect連携を解除
    } else {
      //false -> true　healthConnect連携
      router.push(ROUTES.DATA_CONNECT.HEATHCON);
    }
  };

  const showOFFConfirm = (itemIndex: number) => {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-2">
          <div className="text-[14px]">
            {APP_TEXT.DATA_CONNECT.CONFIRM_OFF_PREFIX}
            {APP_TEXT.DATA_CONNECT.SRCITEMS[itemIndex]}
            {APP_TEXT.DATA_CONNECT.CONFIRM_OFF_SURFIX}
          </div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="destructive"
            className="w-full font-bold"
            onClick={() => {
              setDialog(false);
              if (itemIndex === 4) {
                //ヘルスコネクト
                sendToServer(DeviceType.HealthConnect);
              } else if (itemIndex === 6) {
                //Fitbit
                sendToServer(DeviceType.Fitbit);
              } else if (itemIndex === 7) {
                //OMRON
                sendToServer(DeviceType.Omron);
              }
            }}
          >
            {APP_TEXT.DATA_CONNECT.CONFIRM_OFF_BTN}
          </Button>
          <DialogClose asChild>
            <TextButton className="mt-2 w-full" variant="muted">
              {APP_TEXT.DATA_CONNECT.CONFIRM_CLOSE}
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  };

  const sendToServer = async (deviceType: number) => {
    const request = new ServiceSettingRequest();
    request.deviceType = deviceType;
    request.connectFlg = 0;
    setLoading(true);
    dataConnectAPI
      .serviceSetting(request)
      .then((response) => {
        setLoading(false);
        if (deviceType === DeviceType.Omron) {
          setOmron(!omron);
        } else if (deviceType === DeviceType.Fitbit) {
          setFitBit(!fitbit);
          //修正完了ソース状態を更新します。
          sendMessageToNative({
            type: 'sync-step-source',
            callback: (data) => {},
          });
        } else if (deviceType === DeviceType.HealthConnect) {
          setHealthConnect(!healthConnect);
          //修正完了ソース状態を更新します。
          sendMessageToNative({
            type: 'sync-step-source',
            callback: (data) => {},
          });
        }
        //リフレッシュ状態です
        setTimeout(() => {
          fetchData();
        }, 1000);
      })
      .catch((response) => {
        setLoading(false);
        return Promise.reject();
      });
  };

  const showOFFDisableConfirm = (itemIndex: number, items: string[]) => {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-2">
          <div className="font-bold text-[20px] text-center">
            {APP_TEXT.DATA_CONNECT.CONFIRM_OFF_DISABLE_TITLE}
          </div>
          <div className="text-[16px]">
            {APP_TEXT.DATA_CONNECT.CONFIRM_OFF_DISABLE_PREFIX}
            {APP_TEXT.DATA_CONNECT.SRCITEMS[itemIndex]}
            {APP_TEXT.DATA_CONNECT.CONFIRM_OFF_DISABLE_SURFIX}
            <br />
            {APP_TEXT.DATA_CONNECT.CONFIRM_OFF_DISABLE_TXT2}
          </div>

          <div className="bg-primary-5 rounded-xl  p-4">
            <div className="mb-2">{APP_TEXT.DATA_CONNECT.CONFIRM_ITEMS_TITLE}</div>
            {items.map((line, index) => (
              <div className="text-primary font-bold" key={index}>
                {line}
              </div>
            ))}
          </div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <DialogClose asChild>
            <TextButton
              className="mt-2 w-full rounded-3xl bg-primary h-12 text-white font-bold"
              variant="muted"
            >
              {APP_TEXT.DATA_CONNECT.CONFIRM_CLOSE}
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  };

  const onChangeFitbit = () => {
    if (fitbit === true) {
      if (weight === 6 || sleep === 6 || press === 6) {
        //show popup
        let str = '';
        if (weight === 6) {
          str = `${str}・${APP_TEXT.DATA_CONNECT.WEIGHT}`; //
        }
        if (sleep === 6) {
          if (str !== '') {
            str = `${str}<br/>・${APP_TEXT.DATA_CONNECT.SLEEP}`; //
          } else {
            str = `${str}・${APP_TEXT.DATA_CONNECT.SLEEP}`; //
          }
        }
        if (press === 6) {
          if (str !== '') {
            str = `${str}<br/>・${APP_TEXT.DATA_CONNECT.PRESS}`; //
          } else {
            str = `${str}・${APP_TEXT.DATA_CONNECT.PRESS}`; //
          }
        }
        const strs = str.split('<br/>');
        showOFFDisableConfirm(6, strs);
      } else {
        //確認ポップアップ
        showOFFConfirm(6);
      }
      //true -> false fitbit連携を解除
    } else {
      //false -> true　fitbit連携
      router.push(ROUTES.DATA_CONNECT.FITBIT);
    }
  };
  const changeWeight = () => {
    goChangeSrc('weight', weight, weightOptions);
  };

  const changeSleep = () => {
    goChangeSrc('sleep', sleep, sleepOptions);
  };

  const changePress = () => {
    goChangeSrc('press', press, pressOptions);
  };

  const goChangeSrc = (item: string, value: number, options: OptionInfo[] | undefined) => {
    setTimeout(() => goToDataSrc(item, value, options), 1000);
  };

  const goToDataSrc = (item: string, value: number, options: OptionInfo[] | undefined) => {
    const params = {
      item: item,
      value: value,
      options: options,
    };
    const paramsStr = btoa(JSON.stringify(params));
    const path = `${ROUTES.DATA_CONNECT.SRCSET}?data=${paramsStr}`;
    router.push(path);
  };

  const openConnectionHelp = () => {
    //open HealthConnnection Help
    router.push(ROUTES.DATA_CONNECT.HEATHCON_INTRO);
  };
  const menuItems = [
    {
      label: APP_TEXT.DATA_CONNECT.WEIGHT,
      subLabel: APP_TEXT.DATA_CONNECT.SRCITEMS[weight],
      onClick: changeWeight,
      href: '',
    },
    {
      label: APP_TEXT.DATA_CONNECT.SLEEP,
      subLabel: APP_TEXT.DATA_CONNECT.SRCITEMS[sleep],
      onClick: changeSleep,
      href: '',
    },
    {
      label: APP_TEXT.DATA_CONNECT.PRESS,
      subLabel: APP_TEXT.DATA_CONNECT.SRCITEMS[press],
      onClick: changePress,
      href: '',
    },
  ];

  const ios = APP_TEXT.DATA_CONNECT.IOS_DESC3.split('<br/>');

  return (
    <div className="">
      <TopBar title={APP_TEXT.DATA_CONNECT.TITLE} onBack={goBack} enableBack={true} />
      <div className="flex flex-col items-left">
        <div className="text-[18px] ml-6 mr-6 mt-6 mb-1 font-bold">
          {APP_TEXT.DATA_CONNECT.SUB_TITLE1}
        </div>

        <div className="rounded-2xl bg-white pt-4  pl-4 pr-6 ml-6 mr-6 pb-3">
          {isIOS() && (
            <div className="flex justify-between border-b border-text-muted last:border-b-0  mb-4 pb-2">
              <div className="flex items-center gap-2">
                <img className="h-10 w-10" alt="desc" src={usageImage.src} />
                <div>
                  <div className="w-full text-left text-[18px]">
                    {APP_TEXT.DATA_CONNECT.HEALTHCARE}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[15px] text-gray-600">
                      {APP_TEXT.DATA_CONNECT.HEALTHCARE_DESC}
                    </span>
                  </div>
                </div>
              </div>
              <Switch checked={true} disabled={true} />
            </div>
          )}
          {isAndroid() && (
            <div className="flex justify-between border-b border-text-muted last:border-b-0  mb-4 pb-2">
              <div className="flex items-center gap-2">
                <img className="h-10 w-10" alt="desc" src={healthconnect.src} />
                <div>
                  <div className="text-[16px] text-left">{APP_TEXT.DATA_CONNECT.HEALTCONNECT}</div>
                  <TextButton
                    className="w-[140px] flex-none"
                    variant="default"
                    size="sm"
                    onClick={openConnectionHelp}
                  >
                    {APP_TEXT.DATA_CONNECT.HEALTCONNECT_CONNECT}
                  </TextButton>
                </div>
              </div>
              <Switch checked={healthConnect} onCheckedChange={onChangeConnect} />
            </div>
          )}
          <div className={'flex justify-between border-b border-text-muted last:border-b-0  pb-1'}>
            <div className="flex items-center gap-2">
              <img className="h-10 w-10" alt="desc" src={fitbitIcon.src} />
              <span className="text-[16px] text-left">{APP_TEXT.DATA_CONNECT.FITBIT}</span>
            </div>
            <Switch checked={fitbit} onCheckedChange={onChangeFitbit} />
          </div>
          {useOmron && (
            <div className="flex justify-between border-b border-text-muted last:border-b-0 mt-4 pb-1">
              <div className="flex items-center gap-2">
                <img className="h-10 w-10" alt="desc" src={usageImage.src} />
                <span className="text-[16px] text-left">{APP_TEXT.DATA_CONNECT.OMRON}</span>
              </div>
              <Switch checked={omron} onCheckedChange={onChangeOmron} />
            </div>
          )}
        </div>

        <MenuPanel2 title={APP_TEXT.DATA_CONNECT.SUB_TITLE2} menuItems={menuItems} />
        {isIOS() && (
          <div className="text-gray-400 text-xs ml-6 mr-6 mb-4 mt-6 ">
            <div>{APP_TEXT.DATA_CONNECT.IOS_DESC1}</div>
            <div>{APP_TEXT.DATA_CONNECT.IOS_DESC2}</div>
            <br />
            {ios.map((line, index) => (
              <div key={index}>{line}</div>
            ))}
          </div>
        )}
        {isAndroid() && (
          <div className="text-gray-400 text-xs ml-6 mr-6 mb-4 mt-6 ">
            <div>{APP_TEXT.DATA_CONNECT.ANDROID_DESC1}</div>
            <div>{APP_TEXT.DATA_CONNECT.ANDROID_DESC2}</div>
            <br />
            <div>{APP_TEXT.DATA_CONNECT.ANDROID_DESC3}</div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Goal;
