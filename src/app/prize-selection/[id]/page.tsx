'use client';

import { prizeSelectPageAPI } from '@/api/modules/prize-select';
import TopBar from '@/components/layout/top-bar';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useParams, useRouter } from '@/hooks/use-next-navigation';
import type { PrizeDetailInfo } from '@/types/prize-select';
import { CircleParking } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const PrizeDetail = () => {
  const router = useRouter();
  const params = useParams();
  const [prize, setPrize] = useState<PrizeDetailInfo>();
  const prizeId = params?.id as string;
  const { setLoading } = useLoading();

  const handleBack = () => {
    router.back();
  };

  const fetchPrizeDetail = useCallback(() => {
    setLoading(true);
    prizeSelectPageAPI
      .getPrizeDetail({ prizeId: +prizeId })
      .then((response) => {
        if (response !== null && response !== undefined) {
          setPrize(response.prizeDetail);
        }
      })
      .catch((error) => {})
      .finally(() => setLoading(false));
  }, [prizeId, setLoading]);

  useEffect(() => {
    fetchPrizeDetail();
  }, [fetchPrizeDetail]);

  return (
    <div className="min-h-screen bg-white">
      <TopBar title="景品詳細" enableBack={true} onBack={handleBack} />

      {/* Prize Image */}
      {prize?.prizeImageFile && (
        <div className="flex justify-center px-7 pb-0 py-4">
          <div className="w-80 h-80 bg-muted rounded-lg overflow-hidden">
            <img
              src={prize.prizeImageFile ?? '/images/lottery-result/deafult-prize.png'}
              alt={prize?.prizeNm ?? ''}
              width={320}
              height={320}
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      )}

      <div className="px-6 pb-12 pt-4">
        <div className="space-y-2 mb-4">
          <div className="flex flex-col items-start justify-between">
            <h2 className="text-lg font-bold flex-1 text-text-primary">{prize?.prizeNm}</h2>
            {prize?.prizeDonation !== 2 && (
              <div className="text-xs text-text-secondary bg-background px-1 py-0.5 my-2 rounded">
                {APP_TEXT.PRICE_SELECTION.UNABLE_MAIL}
              </div>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <CircleParking size={32} className="text-primary-light" />
              <span className="text-base text-text-primary">
                {APP_TEXT.PRICE_SELECTION.NEED_POINT}
              </span>
            </div>
            <div className="flex items-baseline gap-1">
              <span className="text-2xl font-bold text-text-primary">
                {prize?.needPoint?.toLocaleString()}
              </span>
              <span className="text-base text-text-primary">{APP_TEXT.PRICE_SELECTION.UNIT}</span>
            </div>
          </div>

          <div className="rounded-2xl px-0 py-1 bg-card-mainLight3">
            <div className="flex items-center justify-between px-6 py-2">
              <span className="text-sm text-text-primary">
                {APP_TEXT.PRICE_SELECTION.WINNING_NUM}
              </span>
              <div className="flex items-baseline gap-1">
                <span className="text-base text-text-primary">{prize?.prizeNum}</span>
                <span className="text-sm text-text-primary">{APP_TEXT.PRICE_SELECTION.NAME}</span>
              </div>
            </div>
          </div>

          {prize?.prizeDonation === 2 && (
            <p className="text-sm mt-2 text-text-secondary">
              {APP_TEXT.PRICE_SELECTION.DETAIL_REMARK}
            </p>
          )}
        </div>

        <div className="mt-4">
          <p className="text-base leading-6 whitespace-pre-line text-text-primary">
            {prize?.prizeDet}
          </p>
        </div>
      </div>
    </div>
  );
};

export default PrizeDetail;
