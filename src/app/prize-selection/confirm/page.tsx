'use client';

import { prizeSelectPageAPI } from '@/api/modules/prize-select';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { Checkbox } from '@/components/ui/checkbox';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import type {
  LotteryChoiceParam,
  LotteryChoiceParamInfo,
  PrizeListInfo,
  RecordData,
} from '@/types/prize-select';
import { base64ToArrayBuffer, utf8ArrayToBase64 } from '@/utils/string-format';
import { Trash2Icon } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import QuantitySelector from '../_components/quantity-selector';

interface SelectedPrizesParam {
  isModify: boolean;
  prizesList: PrizesInfo[];
  remainingPoints: number;
  lotteryId: number;
  lotteryMode: number;
  selectedPrizes: Record<number, number>;
}
interface PrizesInfo {
  prizeId: number | undefined;
  prizeType: number | undefined;
  prizeNm: string | undefined;
  prizeNum: number | undefined;
  prizeImageFile: string | undefined;
  prizeDonation: number | undefined;
  needPoint: number;
  quantity: number;
}

export default function PrizeConfirmPage() {
  const [prizeList, setPrizeList] = useState<PrizeListInfo[]>([]);
  const [donations, setDonations] = useState<Record<string, boolean>>({});
  const router = useRouter();
  const safeArea = useSafeArea();

  const [remainingPoints, setRemainingPoints] = useState<number>(0);
  const [selectedPoints, setSelectedPoints] = useState<number>(0);
  const [isOverPoints, setIsOverPoints] = useState<boolean>(false);
  const lotteryId = useRef<number | undefined>(undefined);
  const lotteryMode = useRef<number | null>(null);
  const [recordData, setRecordData] = useState<RecordData>();
  const isModify = useRef<boolean | undefined>(false);
  const { setLoading } = useLoading();
  const availablePoints = 100000;

  useEffect(() => {
    const totalSelectPoints = prizeList.reduce((accumulator: number, prize: PrizeListInfo) => {
      return accumulator + Number(prize.needPoint ?? 0) * Number(prize.quantity ?? 0);
    }, 0);

    console.log('totalSelectPoints', totalSelectPoints);
    const prizeIdToQuantityMap = prizeList.reduce(
      (accumulator, prize) => {
        const prizeId = Number(prize.prizeId);
        const quantity = prize.quantity;

        if (!Number.isNaN(prizeId) && quantity !== undefined && !Number.isNaN(quantity)) {
          if (accumulator[prizeId] !== undefined) {
            accumulator[prizeId] += quantity; // 累加 quantity
          } else {
            accumulator[prizeId] = quantity;
          }
        }

        return accumulator;
      },
      {} as Record<number, number>,
    );
    setSelectedPoints(totalSelectPoints);
    setRemainingPoints(availablePoints - totalSelectPoints);
    setIsOverPoints(availablePoints - totalSelectPoints < 0);

    console.log('prizeIdToQuantityMap', prizeIdToQuantityMap);
    setRecordData((p: RecordData | undefined): RecordData => {
      if (p === undefined) {
        return { selectedPrizes: prizeIdToQuantityMap } as RecordData;
      }
      return { ...p, selectedPrizes: prizeIdToQuantityMap } as RecordData;
    });
  }, [prizeList]);

  const handleQuantityChange = (prizeId: number | undefined, newQuantity: number) => {
    setPrizeList((prev) =>
      prev.map((prize) =>
        prize.prizeId === prizeId ? { ...prize, quantity: newQuantity } : prize,
      ),
    );
  };

  const handleRemovePrize = (prizeId: string | undefined) => {
    setPrizeList((prev) => prev.filter((prize) => prize.prizeId?.toLocaleString() !== prizeId));
    const prize = prizeList.find((prize) => String(prize.prizeId) === prizeId);
    console.log('prizeIdToQuantityMap', prize);
    setSelectedPoints((p) => p - (prize?.needPoint ?? 0) * (prize?.quantity ?? 0));
    setRemainingPoints((p) => p + (prize?.needPoint ?? 0) * (prize?.quantity ?? 0));
  };

  const handleDonationChange = (prizeId: string | undefined, isDonation: boolean) => {
    if (prizeId === undefined) return;
    setDonations((prev) => ({
      ...prev,
      [prizeId]: isDonation,
    }));
  };

  const handleSubmit = async () => {
    // 応募処理
    const updatedPrizeList = prizeList.map((prize) => {
      if (donations[String(prize.prizeId)]) {
        return {
          ...prize,
          prizeDonation: 2,
        };
      }
      return {
        ...prize,
        prizeDonation: 1,
      };
    });

    const prizesListParam: LotteryChoiceParamInfo[] = updatedPrizeList.map((prize) => ({
      prizeId: prize.prizeId,
      prizeAppNum: prize.quantity,
      prizeDonation: prize.prizeDonation,
    }));

    const params: LotteryChoiceParam = {
      LotteryId: lotteryId.current,
      PrizeData: prizesListParam,
    };

    try {
      if (isModify.current) {
        setLoading(true);
        console.log('応募更新', params);
        await prizeSelectPageAPI.setUpdLotteryChoice(params);
      } else {
        setLoading(true);
        console.log('応募登录', params);
        await prizeSelectPageAPI.setRegLotteryChoice(params);
      }
      const toCompleteParams = {
        lotteryId: lotteryId.current,
      };
      console.log('応募処理', toCompleteParams);
      const encodeStr = utf8ArrayToBase64(
        new TextEncoder().encode(JSON.stringify(toCompleteParams)),
      );
      const encodedParamsStr = encodeURIComponent(encodeStr);
      // 応募完了ページへ遷移
      router.push(`/prize-selection/completion?data=${encodedParamsStr}`);
    } catch (e) {
    } finally {
      setLoading(false);
    }
  };

  // 応募内容を修正する遷移
  const handleEdit = () => {
    // router.back();
    const encodeStr = utf8ArrayToBase64(new TextEncoder().encode(JSON.stringify(recordData)));
    const encodedParamsStr = encodeURIComponent(encodeStr);
    // 応募完了ページへ遷移
    router.push(`/prize-selection?data=${encodedParamsStr}`);
  };

  const searchParams = useSearchParams();
  useEffect(() => {
    if (searchParams != null) {
      const encodedData = searchParams.get('data') || '';
      if (encodedData.length !== 0) {
        const decodedData = decodeURIComponent(encodedData as string);
        const arrayBuffer = base64ToArrayBuffer(decodedData);
        const prizesJson = new TextDecoder().decode(arrayBuffer);
        const selectedPrizesParam: SelectedPrizesParam = JSON.parse(prizesJson);
        if (
          selectedPrizesParam &&
          Array.isArray(selectedPrizesParam.prizesList) &&
          selectedPrizesParam.prizesList.length > 0
        ) {
          isModify.current = selectedPrizesParam.isModify;
          setPrizeList(selectedPrizesParam.prizesList);

          const result: { [key: number]: boolean } = {};
          for (const prize of selectedPrizesParam.prizesList) {
            if (prize.prizeId !== undefined && prize.prizeId !== null) {
              if (prize.prizeDonation === 2) {
                result[prize.prizeId] = true;
              } else {
                result[prize.prizeId] = false;
              }
            }
          }
          setDonations(result);
          // 計算
          const selectedPointsNum = selectedPrizesParam.prizesList.reduce(
            (total, prize) => total + prize.needPoint * prize.quantity,
            0,
          );
          setSelectedPoints(selectedPointsNum);
          setRemainingPoints(selectedPrizesParam.remainingPoints);
          setIsOverPoints(selectedPrizesParam.remainingPoints < 0);
          lotteryId.current = selectedPrizesParam.lotteryId;
          lotteryMode.current = selectedPrizesParam.lotteryMode;
          setRecordData({
            sourceFlag: '2',
            isModify: true,
            lotteryId: lotteryId.current,
            lotteryMode: lotteryMode.current,
            prizeList: selectedPrizesParam.prizesList,
            selectedPrizes: selectedPrizesParam.selectedPrizes,
          });
        }
      }
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-white">
      <TopBar
        title={
          isModify.current
            ? APP_TEXT.PRICE_SELECTION.CONFIRM_MODIFY
            : APP_TEXT.PRICE_SELECTION.CONFIRM_CONTENT_MODIFY
        }
        enableBack={true}
        enableClose={true}
        onBack={() => router.back()}
        onClose={() => router.push('/')}
      />

      <div className="flex flex-col gap-4 pb-[128px]">
        {/* 確認メッセージ */}
        <div className="px-6 pt-4">
          <p className="text-base text-text-primary">
            {APP_TEXT.PRICE_SELECTION.CONFIRM_LOTTERY_CONTENT}
          </p>
        </div>

        {/* ポイント情報 */}
        <div
          className={cn(
            'mx-6 rounded-2xl',
            isOverPoints ? 'bg-invalid-background' : 'bg-card-mainLight3',
          )}
        >
          <div className="px-6 py-4">
            <div className="flex items-center justify-between pb-4">
              <span className="text-base text-text-primary">
                {APP_TEXT.PRICE_SELECTION.CONFIRM_SELECTED_POINTS}
              </span>
              <div className="flex items-end gap-2">
                <span
                  className={cn(
                    'text-[34px] font-bold leading-[1.5]',
                    isOverPoints ? 'text-invalid-message' : 'text-primary',
                  )}
                >
                  {selectedPoints.toLocaleString()}
                </span>
                <span className="text-base text-text-primary">p</span>
              </div>
            </div>

            <div className="h-px bg-border" />

            <div className="flex items-center justify-between pt-2">
              <span className="text-sm text-text-primary">
                {APP_TEXT.PRICE_SELECTION.REMAINING_POINTS}
              </span>
              <div className="flex items-end gap-0.5">
                <span
                  className={cn(
                    'text-base',
                    isOverPoints ? 'text-invalid-message' : 'text-text-primary',
                  )}
                >
                  {remainingPoints.toLocaleString()}
                </span>
                <span className="text-sm text-text-primary">p</span>
              </div>
            </div>

            {/* エラーメッセージ */}
            {isOverPoints && (
              <div className="flex items-center gap-1 pt-3">
                <div className="w-4 h-4 bg-invalid-message rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xs font-bold leading-none">!</span>
                </div>
                <span className="text-xs text-invalid-message leading-[1.5]">
                  {APP_TEXT.PRICE_SELECTION.OUT_OF_POINT}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 選択中の景品 */}
        <div className="px-6">
          <h2 className="text-lg font-bold text-text-primary mb-2">
            {APP_TEXT.PRICE_SELECTION.SELECTED_PRIZE}
          </h2>

          <div className="space-y-0">
            {prizeList.map((prize, index) => {
              return (
                <div key={prize.prizeId ?? `default-key-${index}`}>
                  <div className="py-3">
                    {/* 景品情報 */}
                    <div className="flex items-center gap-3 py-3">
                      <div className="w-14 h-14 rounded overflow-hidden">
                        <img
                          src={prize.prizeImageFile}
                          alt={prize.prizeNm}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      <div className="flex-1 space-y-0.5">
                        <h3 className="text-base text-text-primary">{prize.prizeNm}</h3>
                        <p className="text-sm font-bold text-primary">
                          {typeof prize.needPoint === 'number'
                            ? prize.needPoint.toLocaleString()
                            : prize.needPoint}
                          p / 1口
                        </p>
                      </div>

                      <QuantitySelector
                        quantity={prize.quantity ?? 0}
                        disabled={false}
                        onQuantityChange={(newQuantity) =>
                          handleQuantityChange(prize.prizeId, newQuantity)
                        }
                        min={0}
                      />
                    </div>

                    {/* アクション & 小計 */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <button
                          type="button"
                          onClick={() => handleRemovePrize(String(prize.prizeId))}
                          className="flex items-center gap-1 text-sm font-bold text-destructive"
                        >
                          <Trash2Icon className="w-[18px] h-[18px]" />
                          削除
                        </button>

                        <div className="flex items-center gap-10">
                          <span className="text-sm text-text-primary">小計</span>
                          <div className="flex items-end gap-0.5">
                            <span className="text-xl font-bold text-primary">
                              {(
                                Number(prize.needPoint ?? 0) * Number(prize.quantity)
                              ).toLocaleString()}
                            </span>
                            <span className="text-xl text-text-primary">p</span>
                          </div>
                        </div>
                      </div>

                      {/* 寄付オプション */}
                      {prize.prizeId && prize.prizeDonation === 2 && (
                        <div className="flex items-center justify-end gap-2">
                          <Checkbox
                            checked={donations[prize.prizeId]}
                            onCheckedChange={() => {
                              if (prize.prizeId === undefined) return;
                              handleDonationChange(
                                String(prize.prizeId),
                                !donations[prize.prizeId],
                              );
                            }}
                            className="border border-border ring-none shadow-none"
                          />
                          <span className="text-[15px] text-text-primary">
                            {APP_TEXT.PRICE_SELECTION.DONATE_PRIZE}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 区切り線 */}
                  {index < prizeList.length - 1 && <div className="h-px bg-border" />}
                </div>
              );
            })}
          </div>
        </div>

        {/* 注意事項 */}
        <div className="px-6">
          <p className="text-sm text-text-secondary leading-[1.5]">
            {APP_TEXT.PRICE_SELECTION.REMARK_ONE}
            <br />
            {APP_TEXT.PRICE_SELECTION.REMARK_TWO}
            <br />
            {APP_TEXT.PRICE_SELECTION.REMARK_THREE}
          </p>
        </div>
      </div>

      {/* 固定フッター */}
      <div
        className="fixed bottom-0 left-0 right-0 bg-white"
        style={{ paddingBottom: safeArea.bottom }}
      >
        <div className={cn('px-6 py-2')}>
          <Button
            onClick={handleSubmit}
            disabled={isOverPoints || selectedPoints === 0}
            variant="default"
            className="w-full"
          >
            {APP_TEXT.PRICE_SELECTION.APPLY_CONTENT}
          </Button>

          <Button
            onClick={handleEdit}
            variant="secondary"
            className="w-full bg-transparent text-text-secondary"
          >
            {APP_TEXT.PRICE_SELECTION.MODIFY_APPLICATION}
          </Button>
        </div>
      </div>
    </div>
  );
}
