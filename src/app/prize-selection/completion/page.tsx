'use client';

import { prizeSelectPageAPI } from '@/api/modules/prize-select';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { AppLotteryResponse, PrizeCompletionParam } from '@/types/prize-select';
import { formatDate } from '@/utils/date-format';
import { base64ToArrayBuffer, utf8ArrayToBase64 } from '@/utils/string-format';
import { ChevronRight, ParkingCircle } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

export default function LotteryCompletionPage() {
  const router = useRouter();
  const [mockLotteryInfo, setMockLotteryInfo] = useState<PrizeCompletionParam>();
  const lotteryId = useRef<number>();
  const pushSendFlg = useRef<number>(1);
  const lotteryOn = useRef<string>('');
  const { setLoading } = useLoading();

  const handleEditApplication = () => {
    // 応募完了ページへ遷移
    const params = {
      sourceFlag: 1,
      isModify: true,
      lotteryId: lotteryId.current,
    };
    const encodeStr = utf8ArrayToBase64(new TextEncoder().encode(JSON.stringify(params)));
    const encodedParamsStr = encodeURIComponent(encodeStr);
    const path = `/prize-selection?data=${encodedParamsStr}`;
    router.push(path);
  };

  const handlePushNotificationSetting = () => {
    // プッシュ通知設定へ遷移
    console.log('プッシュ通知設定へ遷移');
  };

  const searchParams = useSearchParams();
  useEffect(() => {
    (async () => {
      const encodedData = searchParams.get('data') || '';
      if (encodedData) {
        try {
          setLoading(true);
          const appLotteryResponse: AppLotteryResponse = await prizeSelectPageAPI.getAppLottery();
          if (!appLotteryResponse) {
            throw new Error('APIからの応答がありません。');
          }
          pushSendFlg.current = appLotteryResponse.pushSendFlg ?? 1;
          const decodedData = decodeURIComponent(encodedData);
          const arrayBuffer = base64ToArrayBuffer(decodedData);
          const prizesJson = new TextDecoder().decode(arrayBuffer);
          const selectedPrizesParam = JSON.parse(prizesJson);
          if (selectedPrizesParam) {
            lotteryId.current = selectedPrizesParam.lotteryId;
            if (appLotteryResponse?.EntryInfo?.lotteryOn) {
              selectedPrizesParam.announcementDate = formatDate(
                new Date(appLotteryResponse.EntryInfo.lotteryOn),
                'yyyy年MM月dd日(d)',
              );
            } else {
              selectedPrizesParam.announcementDate = undefined;
            }
            selectedPrizesParam.selectedPoints = appLotteryResponse.entryDetail?.choicedPoint;
            selectedPrizesParam.remainingPoints = appLotteryResponse.entryDetail?.remainPoint;
            selectedPrizesParam.prizeList = appLotteryResponse.entryDetail?.choiceList;
            lotteryOn.current = formatDate(
              appLotteryResponse.EntryInfo?.lotteryOn ?? '',
              'yyyy年M月d日',
            );
            setMockLotteryInfo(selectedPrizesParam);
          }
        } catch (error) {
          console.error('デコードまたはJSONパースに失敗しました:', error);
        } finally {
          setLoading(false);
        }
      }
    })();
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-background pb-12">
      <TopBar
        title={APP_TEXT.PRICE_SELECTION.LOTTERY_APPLI_COMPLETE}
        enableBack={true}
        onBack={() => router.back()}
      />

      <div className="flex flex-col gap-6">
        {/* 完了メッセージ */}
        <div className="px-6 pt-4">
          <p className="text-base text-text-primary">
            {APP_TEXT.PRICE_SELECTION.ACCEPTE_FOR_LOTTERY}
          </p>
        </div>

        {/* メインカード */}
        <div className="mx-6 bg-white rounded-2xl p-6 space-y-4">
          {/* 当選発表日 */}
          <div className="space-y-2">
            <h3 className="text-lg font-bold text-text-primary">
              {APP_TEXT.PRICE_SELECTION.ELECT_ANNO_DAY}
            </h3>
            <p className="text-base text-text-primary">{mockLotteryInfo?.announcementDate}</p>
          </div>

          {/* 発表方法 */}
          <div className="space-y-2">
            <h3 className="text-lg font-bold text-text-primary">
              {APP_TEXT.PRICE_SELECTION.ANNO_METHOD}
            </h3>
            <p className="text-base text-text-primary">{APP_TEXT.PRICE_SELECTION.CONTACT_US}</p>

            {/* プッシュ通知案内 */}
            {pushSendFlg.current === 0 && (
              <div className="bg-card-mainLight3 rounded-2xl p-4">
                <p className="text-sm text-text-primary leading-[1.5]">
                  {APP_TEXT.PRICE_SELECTION.PUSH_LOTTERY_RESULT}
                </p>
                <Button
                  onClick={handlePushNotificationSetting}
                  className="bg-transparent text-primary text-sm font-bold p-0 mt-0 h-6 w-full justify-end"
                >
                  {APP_TEXT.PRICE_SELECTION.SET_PUSH_NOTIFICATION}
                  <ChevronRight className="w-[18px] h-[18px]" />
                </Button>
              </div>
            )}
          </div>

          {/* 抽選応募内容 */}
          <div className="space-y-2">
            <h3 className="text-lg font-bold text-text-primary">
              {APP_TEXT.PRICE_SELECTION.LOTTERY_APPLI_CONTENT}
            </h3>

            {/* ポイント情報 */}
            <div className="bg-card-mainLight3 rounded-2xl p-4 px-6 space-y-2">
              {/* 選択中のポイント */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ParkingCircle className="w-5 h-5 text-primary-light" />
                  <span className="text-sm text-text-primary">
                    {APP_TEXT.PRICE_SELECTION.CONFIRM_SELECTED_POINTS}
                  </span>
                </div>
                <div className="flex items-end gap-0.5">
                  <span className="text-base text-text-primary">
                    {mockLotteryInfo?.selectedPoints?.toLocaleString()}
                  </span>
                  <span className="text-sm text-text-primary">p</span>
                </div>
              </div>

              <div className="h-px bg-border" />

              {/* 残りの選択可能ポイント */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ParkingCircle className="w-5 h-5 text-primary-light" />
                  <span className="text-sm text-text-primary">
                    {APP_TEXT.PRICE_SELECTION.REMAINING_POINTS}
                  </span>
                </div>
                <div className="flex items-end gap-0.5">
                  <span className="text-base text-text-primary">
                    {mockLotteryInfo?.remainingPoints?.toLocaleString()}
                  </span>
                  <span className="text-sm text-text-primary">p</span>
                </div>
              </div>
            </div>

            {/* 選択中の景品 */}
            <div className="bg-card-mainLight3 rounded-2xl p-4 px-6 space-y-4">
              <h4 className="text-sm font-bold text-text-primary">
                {APP_TEXT.PRICE_SELECTION.SELECTED_PRIZE}
              </h4>

              <div className="space-y-4">
                {mockLotteryInfo?.prizeList?.map((prize, index) => (
                  <div key={prize.prizeId}>
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <p className="text-sm text-text-primary">{prize.prizeNm}</p>
                        <div className="flex items-center gap-2">
                          <p className="text-xs text-text-secondary">
                            {APP_TEXT.PRICE_SELECTION.APPLI_NUM}
                            {prize.prizeAppNum}
                          </p>
                          {prize.prizeDonation !== 1 && (
                            <span className="px-1 py-0.5 bg-muted text-xs text-text-secondary rounded">
                              {APP_TEXT.PRICE_SELECTION.DONATION}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-end gap-0.5">
                        <span className="text-base text-text-primary">
                          {(prize.needPoint ?? 0) * (prize.prizeAppNum ?? 0)}
                        </span>
                        <span className="text-sm text-text-primary">p</span>
                      </div>
                    </div>
                    {mockLotteryInfo?.prizeList &&
                      index >= 0 &&
                      index < mockLotteryInfo?.prizeList.length - 1 && (
                        <div className="h-px bg-border mt-4" />
                      )}
                  </div>
                ))}
              </div>
            </div>

            {/* 注意事項 */}
            <p className="text-sm text-text-secondary leading-[1.5]">
              {APP_TEXT.PRICE_SELECTION.COMPLETE_REMARK_ONE}
              <br />
              {APP_TEXT.PRICE_SELECTION.COMPLETE_REMARK_TWO(lotteryOn.current)}
            </p>
          </div>

          {/* 変更ボタン */}
          <div className="pt-2">
            <Button onClick={handleEditApplication} variant="outline" className="w-full">
              {APP_TEXT.PRICE_SELECTION.LOTTERY_CHANGE}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
