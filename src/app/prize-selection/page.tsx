'use client';

import { prizeSelectPageAPI } from '@/api/modules/prize-select';
import TopBar from '@/components/layout/top-bar';
import { AlertDialogContent } from '@/components/shared/alert-dialog';
import { Button } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import {
  type ReadonlyURLSearchParams,
  useRouter,
  useSearchParams,
} from '@/hooks/use-next-navigation';
import { useSafeArea } from '@/hooks/use-safe-area';
import { cn } from '@/lib/utils';
import type {
  PrizeListInfo,
  PrizeListResponse,
  UserLotteryChoiceInfo,
  UserLotteryChoiceResponse,
} from '@/types/prize-select';
import { base64ToArrayBuffer, utf8ArrayToBase64 } from '@/utils/string-format';
import {
  AlertDialog,
  AlertDialogDescription,
  AlertDialogOverlay,
  AlertDialogPortal,
} from '@radix-ui/react-alert-dialog';
import { CircleParking, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import QuantitySelector from './_components/quantity-selector';

export default function PrizeSelectionPage() {
  const [selectedPrizes, setSelectedPrizes] = useState<Record<number, number>>({});
  const [prizeList, setPrizeList] = useState<PrizeListInfo[]>([]);
  const availablePoints = 100000;
  const router = useRouter();
  const safeArea = useSafeArea();
  const [isSingleWaring, setIsSingleWaring] = useState(false);
  const lotteryId = useRef<number | undefined>(undefined);
  const lotPartFlg = useRef<number | undefined>(undefined);
  const lotteryMode = useRef<number | undefined>(undefined);
  const isModify = useRef<boolean | undefined>(false);
  const hasSelected = useRef<boolean | undefined>(false);
  const { setLoading } = useLoading();

  const [isOpen, setIsOpen] = React.useState(false);
  const openClearDialog = () => setIsOpen(true);
  const closeClearDialog = () => setIsOpen(false);
  const deleteSelectPoint = async () => {
    try {
      setIsOpen(false);
      await prizeSelectPageAPI.setDelLotteryChoice(lotteryId.current?.toString() || '');
      closeClearDialog();
      router.push(`/lottery-result?lotteryId=${lotteryId}&status=2`);
    } catch (e) {
    } finally {
      setLoading(false);
    }
  };

  const selectedPoints = Object.entries(selectedPrizes).reduce((total, [prizeId, count]) => {
    const prize = prizeList.find((p) => p.prizeId === Number.parseInt(prizeId, 10));
    return total + (prize?.needPoint ? prize.needPoint * count : 0);
  }, 0);

  const remainingPoints = availablePoints - selectedPoints;
  const selectedCount = Object.values(selectedPrizes).reduce((sum, count) => sum + count, 0);

  // selectedPrizes オブジェクト中に prizeId が異なる且つ数量が 0 でない賞品が存在するかどうかをチェック
  const hasDifferentNonZeroPrize = (prizeId: number): boolean => {
    return (
      Object.entries(selectedPrizes).reduce((acc: string[], [key, count]) => {
        if (Number.parseInt(key, 10) !== prizeId && count !== 0 && lotteryMode.current === 1) {
          acc.push(key);
        }
        return acc;
      }, []).length > 0
    );
  };

  const handlePrizeQuantityChange = (prizeId: number | undefined, quantity: number) => {
    if (prizeId === undefined) return;
    const prize = prizeList.find((p) => p.prizeId === prizeId);
    if (!prize) return;

    hasDifferentNonZeroPrize(prizeId);
    setSelectedPrizes((prev) => {
      const currentQuantity = prev[prizeId] || 0;

      if (currentQuantity === quantity) {
        return prev;
      }

      // 移除点数限制，允许超出可用点数
      if (quantity === 0) {
        const { [prizeId]: _, ...rest } = prev;
        return rest;
      }

      return {
        ...prev,
        [prizeId]: quantity,
      };
    });
  };

  const handleClearAll = () => {
    setSelectedPrizes({});
  };

  const handleConfirm = () => {
    if (!(selectedCount === 0 || remainingPoints < 0)) {
      const prizesList: Record<number, number>[] = prizeList.reduce(
        (acc: Record<number, number>[], prize: PrizeListInfo): Record<number, number>[] => {
          if (prize.prizeId && Object.hasOwn(selectedPrizes, prize.prizeId)) {
            const updatedPrize = { ...prize };
            updatedPrize.quantity = selectedPrizes[prize.prizeId];
            acc.push(updatedPrize);
          }
          return acc;
        },
        [] as Record<number, number>[],
      );

      const params = {
        isModify: isModify.current,
        prizesList,
        remainingPoints,
        selectedPrizes,
        lotteryId: lotteryId.current,
        lotteryMode: lotteryMode.current,
      };
      console.log('params: ', params);
      const paramsStr = utf8ArrayToBase64(new TextEncoder().encode(JSON.stringify(params)));
      const encodedParamsStr = encodeURIComponent(paramsStr);
      router.push(`/prize-selection/confirm?data=${encodedParamsStr}`);
      console.log('応募内容を確認する');
    } else {
      openClearDialog();
    }
  };

  const handlePrizeItemClick = (prizeId: string) => {
    router.push(`/prize-selection/${prizeId}`);
  };

  const searchParams: ReadonlyURLSearchParams = useSearchParams();
  useEffect(() => {
    const fetchData = async () => {
      if (searchParams) {
        const encodedData = searchParams.get('data') || '{}';
        const decodedData = decodeURIComponent(encodedData as string);
        const arrayBuffer = base64ToArrayBuffer(decodedData);
        const prizesJson = new TextDecoder().decode(arrayBuffer);
        const data = JSON.parse(prizesJson);

        lotteryId.current = data.lotteryId;
        isModify.current = data.isModify;

        try {
          setLoading(true);
          const prizeListResponse: PrizeListResponse = await prizeSelectPageAPI.getPrizeList({
            lotteryId: lotteryId.current?.toString() || '',
          });
          lotteryMode.current = prizeListResponse.lotteryMode;
          if (lotteryMode.current === 1) {
            setIsSingleWaring(true);
          }
          lotPartFlg.current = prizeListResponse.lotPartFlg;
          const selectPrizesRes: UserLotteryChoiceResponse =
            await prizeSelectPageAPI.getUserLotteryChoice({
              lotteryId: lotteryId.current?.toString() || '',
            });
          if (selectPrizesRes.userLotteryChoiceList) {
            hasSelected.current = true;
          } else {
            hasSelected.current = false;
          }
          if (data.sourceFlag === '2') {
            if (prizeListResponse != null && prizeListResponse.prizeList !== undefined) {
              setPrizeList(prizeListResponse.prizeList);
              setSelectedPrizes(data.selectedPrizes);
            }
          } else {
            if (prizeListResponse?.prizeList) {
              const fullPrizes: PrizeListInfo[] = prizeListResponse.prizeList;

              if (selectPrizesRes?.userLotteryChoiceList) {
                isModify.current = true;
                const selectPrizes: UserLotteryChoiceInfo[] | undefined =
                  selectPrizesRes.userLotteryChoiceList;
                console.log(selectPrizes);
                const prizeMap = selectPrizes?.reduce(
                  (acc, prize) => {
                    if (prize.prizeId !== undefined && prize.prizeAppNum !== undefined) {
                      acc[prize.prizeId] = prize.prizeAppNum;
                    }
                    return acc;
                  },
                  {} as Record<number, number>,
                );
                setSelectedPrizes(prizeMap || {});

                const donationMap: { [key: number]: number } = {};
                if (selectPrizesRes?.userLotteryChoiceList) {
                  for (const prize of selectPrizesRes.userLotteryChoiceList) {
                    if (prize.prizeId !== undefined && prize.prizeDonation !== undefined) {
                      donationMap[prize.prizeId] = Number(prize.prizeDonation);
                    }
                  }
                }
                const fullPrizesArray = fullPrizes.map((prize) => {
                  if (prize.prizeId !== undefined && prize.prizeId !== null) {
                    return {
                      ...prize,
                      prizeDonation: donationMap[prize.prizeId] || prize.prizeDonation,
                    };
                  }
                  return prize;
                });
                setPrizeList(fullPrizesArray);
              } else {
                isModify.current = false;
                setPrizeList(fullPrizes);
              }
            }
          }
        } catch (e) {
        } finally {
          setLoading(false);
        }
      }
    };
    fetchData();
  }, [searchParams, setLoading]);

  return (
    <div className="min-h-screen bg-white">
      <TopBar
        title={
          !isModify.current
            ? APP_TEXT.PRICE_SELECTION.PRICE_SELECTION_TITLE
            : APP_TEXT.PRICE_SELECTION.PRICE_UPDATE_TITLE
        }
        enableClose={true}
        onClose={() => {
          router.back();
        }}
      />

      {/* Prize List Content */}
      <div className="flex flex-col h-full">
        {/* Points Summary Section */}
        <div
          className={cn(
            'w-full px-6',
            remainingPoints < 0 ? 'bg-invalid-background' : 'bg-card-mainLight3',
          )}
        >
          <div className="flex items-center justify-between py-2 h-11">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 flex items-center justify-center">
                <CircleParking className="w-5 h-5 text-primary" />
              </div>
              <span className="text-sm leading-6 text-text-primary">
                {APP_TEXT.PRICE_SELECTION.SELECTED_POINTS}
              </span>
            </div>
            <div className="flex items-center">
              <span
                className={cn(
                  'text-xl font-bold',
                  remainingPoints < 0 ? 'text-invalid-message' : 'text-primary',
                )}
              >
                {selectedPoints.toLocaleString()}
              </span>
              <span className="text-sm text-text-primary ml-1">p</span>
            </div>
          </div>

          <div className="h-px bg-border" />

          <div className="flex items-center justify-between py-2 h-11">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 flex items-center justify-center">
                <CircleParking className="w-5 h-5 text-primary" />
              </div>
              <span className="text-sm leading-6 text-text-primary">
                {APP_TEXT.PRICE_SELECTION.REMAINING_POINTS}
              </span>
            </div>
            <div className="flex items-center">
              <span
                className={cn(
                  'text-xl font-bold',
                  remainingPoints < 0 ? 'text-invalid-message' : 'text-primary',
                )}
              >
                {remainingPoints.toLocaleString()}
              </span>
              <span className="text-sm text-text-primary ml-1">p</span>
            </div>
          </div>

          {/* Error Message */}
          {remainingPoints < 0 && (
            <div className="flex items-center gap-1 py-3 pt-1 transition-all duration-300">
              <div className="w-4 h-4 bg-invalid-message rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold leading-4">!</span>
              </div>
              <span className="text-xs text-invalid-message leading-[1.5]">
                {APP_TEXT.PRICE_SELECTION.OVERSELECT_WARING}
              </span>
            </div>
          )}
          {isSingleWaring && (
            <div className="flex items-center gap-1 py-3 pt-1 transition-all duration-300">
              <div className="w-4 h-4 bg-invalid-message rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold leading-4">!</span>
              </div>
              <span className="text-xs text-invalid-message leading-[1.5]">
                {APP_TEXT.PRICE_SELECTION.SINGLE_WARING}
              </span>
            </div>
          )}
        </div>

        {/* Selection Count and Clear All */}
        <div className="flex items-center justify-between px-6 py-4 pb-0 bg-white">
          <span className="text-sm text-text-secondary">
            {selectedCount}
            {APP_TEXT.PRICE_SELECTION.INSELECT_COUNT}
          </span>

          <div
            onClick={handleClearAll}
            onKeyUp={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleClearAll();
              }
            }}
            className="flex items-center gap-1 text-sm font-bold text-destructive cursor-pointer"
          >
            <X className="w-[18px] h-[18px]" />
            {APP_TEXT.PRICE_SELECTION.ALL_DONE}
          </div>
        </div>

        {/* Prize Items List */}
        <div className="flex flex-col bg-white">
          {prizeList.map((prize, index) => {
            if (prize.prizeId === undefined) return null;
            const selectedQuantity = selectedPrizes[prize.prizeId] || 0;
            // const canDecrease = selectedQuantity > 0;
            // const canIncrease = true; // 移除最大限制

            return (
              <React.Fragment key={prize.prizeId}>
                {/* Prize Item */}
                <div className="flex items-center gap-3 px-6 py-3">
                  <div
                    className="w-14 h-14 rounded-md overflow-hidden cursor-pointer"
                    onClick={() => handlePrizeItemClick(String(prize.prizeId))}
                  >
                    <img
                      src={prize.prizeImageFile ?? '/images/lottery-result/deafult-prize.png'}
                      alt={prize.prizeNm}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // (e.target as HTMLImageElement).src =
                        //   '/images/lottery-result/deafult-prize.png';
                      }}
                    />
                  </div>

                  <div
                    className="flex-1 flex flex-col gap-0.5 cursor-pointer"
                    onClick={() => handlePrizeItemClick(String(prize.prizeId))}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        handlePrizeItemClick(String(prize.prizeId));
                      }
                    }}
                  >
                    <h3 className="text-base text-text-primary">{prize.prizeNm}</h3>
                    <div className="flex items-baseline gap-1">
                      <span className="text-base text-text-secondary">{prize?.prizeNum}</span>
                      <span className="text-sm text-text-secondary">
                        {APP_TEXT.PRICE_SELECTION.NAME}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-bold text-primary">
                        {typeof prize.needPoint === 'number'
                          ? prize.needPoint.toLocaleString()
                          : prize.needPoint}
                        {APP_TEXT.PRICE_SELECTION.UNIT}
                      </span>
                      {prize.prizeDonation !== 2 && (
                        <span className="px-1 py-0.5 bg-background text-xs text-text-secondary rounded">
                          {APP_TEXT.PRICE_SELECTION.UNABLE_MAIL}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Quantity Selector */}
                  <QuantitySelector
                    disabled={hasDifferentNonZeroPrize(prize.prizeId)}
                    quantity={selectedQuantity}
                    onQuantityChange={(quantity: number) =>
                      handlePrizeQuantityChange(prize.prizeId, quantity)
                    }
                    min={0}
                  />
                </div>

                {/* Divider */}
                {index < prizeList.length - 1 && (
                  <div className="px-6">
                    <div className="h-px bg-border" />
                  </div>
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>

      {/* 固定フッター */}
      <div
        className="fixed bottom-0 left-0 right-0 bg-white"
        style={{ paddingBottom: safeArea.bottom }}
      >
        <div className="px-6 py-2 relative top-[-1px]">
          <Button
            onClick={handleConfirm}
            disabled={
              (!hasSelected.current && (selectedCount === 0 || remainingPoints < 0)) ||
              lotPartFlg.current === 0
            }
            variant="default"
            size="default"
            className="w-full"
          >
            {APP_TEXT.PRICE_SELECTION.CONTENT_CONFIRM}
          </Button>
        </div>
      </div>
      <div>
        <AlertDialog open={isOpen} onOpenChange={closeClearDialog}>
          <AlertDialogPortal>
            <AlertDialogOverlay className="AlertDialogOverlay" />
            <AlertDialogContent className="AlertDialogContent rounded-2xl" style={{ width: '80%' }}>
              <AlertDialogDescription className="AlertDialogDescription">
                {APP_TEXT.PRICE_SELECTION.CLEAR_WARNING}
              </AlertDialogDescription>
              <div className="flex flex-col items-center gap-2.5 mt-4">
                <Button
                  onClick={deleteSelectPoint}
                  variant="default"
                  size="sm"
                  className="w-full border-2 border-red-500 bg-transparent text-red-500 normal-weight"
                >
                  {APP_TEXT.PRICE_SELECTION.NO_LOTTERY}
                </Button>
                <Button
                  className="w-full bg-transparent text-black-500"
                  variant="default"
                  size="sm"
                  style={{ fontWeight: 'normal' }}
                  onClick={closeClearDialog}
                >
                  {APP_TEXT.PRICE_SELECTION.BACK}
                </Button>
              </div>
            </AlertDialogContent>
          </AlertDialogPortal>
        </AlertDialog>
      </div>
    </div>
  );
}
