import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Minus, Plus } from 'lucide-react';
import type React from 'react';

interface QuantitySelectorProps {
  /** 現在選択されている数量 */
  quantity: number;
  /** 数量変更時のコールバック関数 */
  onQuantityChange: (quantity: number) => void;
  /** 最小値、デフォルトは0 */
  min?: number;
  /** 最大値、オプション */
  max?: number;
  /** コンポーネントを無効にするかどうか */
  disabled?: boolean;
  /** カスタムクラス名 */
  className?: string;
}

/**
 * 数量選択コンポーネント
 * 数量の増減機能を提供します
 */
const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  quantity,
  onQuantityChange,
  min = 0,
  max,
  disabled = false,
  className = '',
}) => {
  const canDecrease = quantity > min && !disabled;
  const canIncrease = (!max || quantity < max) && !disabled;

  const handleIncrease = () => {
    if (!canIncrease) return;
    onQuantityChange(quantity + 1);
  };

  const handleDecrease = () => {
    if (!canDecrease) return;
    onQuantityChange(quantity - 1);
  };

  return (
    <div
      className={cn('flex items-center border border-border rounded-[20px] p-1 gap-0.5', className)}
    >
      <Button
        onClick={handleDecrease}
        disabled={!canDecrease}
        className="p-0 w-6 h-6 rounded-full flex items-center justify-center transition-colors select-none"
        variant="default"
      >
        <Minus className="w-4 h-4" />
      </Button>

      <div className="w-[34px] text-center text-lg text-text-primary">{quantity}</div>

      <Button
        onClick={handleIncrease}
        disabled={!canIncrease}
        className="p-0 w-6 h-6 rounded-full flex items-center justify-center transition-colors select-none"
        variant="default"
      >
        <Plus className="w-4 h-4" />
      </Button>
    </div>
  );
};

export default QuantitySelector;
