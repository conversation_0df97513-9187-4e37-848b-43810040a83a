'use client';

import TopBar from '@/components/layout/top-bar';
import Link from '@/components/shared/router-link';
import { Button } from '@/components/ui/button';
import { useRouter } from '@/hooks/use-next-navigation';
import { ArrowLeft, Home } from 'lucide-react';

export default function NotFound() {
  const router = useRouter();

  return (
    <main className="min-h-screen bg-card flex items-center justify-center px-4">
      <TopBar title="" />
      <div className="max-w-md w-full mx-auto space-y-8 text-center">
        <div className="space-y-2">
          <h1 className="text-7xl font-bold text-primary">404</h1>
          <h2 className="text-xl text-muted-foreground">ページが見つかりません</h2>
          <p className="text-sm text-muted-foreground mt-2">
            お探しのページは存在しないか、移動された可能性があります。
          </p>
        </div>

        <div className="h-px bg-border w-full my-6" />

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Button
            onClick={() => router.back()}
            variant="outline"
            className="w-full sm:w-auto flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            戻る
          </Button>

          <Link href="/" passHref>
            <Button variant="default" className="w-full sm:w-auto flex items-center gap-2">
              <Home className="w-4 h-4" />
              ホームへ
            </Button>
          </Link>
        </div>
      </div>
    </main>
  );
}
