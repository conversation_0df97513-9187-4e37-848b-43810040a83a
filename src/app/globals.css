@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

input,
textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

img,
a {
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
}

body {
  font-family: "SF Pro", Arial, Helvetica, sans-serif;
}

.android {
  font-family: "Roboto", Arial, Helvetica, sans-serif;
}

.ios {
  font-family: "SF Pro", Arial, Helvetica, sans-serif;
}

.height-auto-important {
  height: auto !important;
}

@layer base {
  :root,
  .theme-blue {
    --background: 0 0% 94.9%;
    --foreground: 334 55% 1%;
    --popover: 334 62% 100%;
    --popover-foreground: 334 55% 1%;
    --card: 334 62% 100%;
    --card-foreground: 334 55% 1%;
    --border: 0 0% 70%;
    --border: 0 0% 70%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --button: 232 61% 54%;
    --primary: 232 61% 54%;
    --primary-light: 227 93% 64%;
    --primary-soft: 227 93% 82%;
    --primary-softer: 227 93% 89%;
    --primary-light: 227 93% 64%;
    --primary-soft: 227 93% 82%;
    --primary-softer: 227 93% 89%;
    --primary-foreground: 0 0% 100%;
    --primary-5: 227 100% 98%;
    --primary-10: 226 89% 96%;
    --primary-20: 228 94% 93%;
    --primary-30: 227 93% 89%;
    --primary-40: 227 94% 86%;
    --primary-50: 227 93% 82%;
    --primary-60: 227 93% 78%;
    --primary-70: 227 94% 75%;
    --primary-80: 227 93% 71%;
    --primary-90: 227 94% 68%;
    --primary-100: 227 93% 64%;
    --secondary: 227 69% 97%;
    --secondary-foreground: 232 61% 54%;
    --switch: 151 66% 29%;
    --accent: 214.29 31.82% 91.37%;
    --accent-foreground: 334 20% 22%;
    --destructive: 348.37 78.4% 49.02%;
    --destructive-foreground: 0 100% 40%;
    --muted: 0 0% 95%;
    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 40%;
    --ring: 228.33 94.74% 62.75%;
    --text-primary: 0 0 0%;
    --text-secondary: 0 0 40%;
    --radius: 0.5rem;
    --sidebar-background: 334 62% 100%;
    --sidebar-foreground: 334 55% 1%;
    --sidebar-primary: 229 100% 62%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 214.74 100% 96.27%;
    --sidebar-accent-foreground: 229 100% 62%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 228.33 94.74% 62.75%;
    --badge-border: 227 93% 64%;
    --risk-score-1: 151 54% 47%;
    --risk-score-2: 32 100% 55%;
    --risk-score-3: 359 75% 61%;
    --risk-score-history-1: 184 64% 44%;
    --risk-score-history-2: 160 79% 39%;
    --risk-score-history-3: 227 93% 64%;
    --risk-score-history-4: 329 95% 74%;
    --risk-score-history-5: 255 100% 71%;
    --risk-score-history-6: 20 100% 69%;
    --risk-score-history-7: 199 80% 63%;
    --chart-1: 232 61% 54%;
    --chart-2: 226 83% 79%;
    --chart-3: 216 92% 60%;
    --chart-4: 210 98% 78%;
    --chart-5: 212 97% 87%;
    --main-color: 227, 100%, 98%;
    --main-fix-color: 227, 100%, 98%;
    --primary-ranking: 227, 93%, 89%;
  }

  .theme-light-blue {
    --primary: 199, 100%, 36%;
    --primary-light: 199, 80%, 63%;
    --primary-soft: 199 81% 82%;
    --primary-softer: 199 79% 89%;
    --primary-foreground: 0 0% 100%;
    --main-color: 195, 80%, 98%;
    --primary-5: 195 80% 98%;
    --primary-10: 200 79% 96%;
    --primary-20: 199 79% 93%;
    --primary-30: 199 79% 89%;
    --primary-40: 199 79% 85%;
    --primary-50: 199 81% 82%;
    --primary-60: 199 81% 78%;
    --primary-70: 199 80% 74%;
    --primary-80: 199 80% 70%;
    --primary-90: 199 80% 67%;
    --primary-100: 199 80% 63%;
    --primary-ranking: 199, 79%, 89%;
  }

  .theme-cyan {
    --primary: 184 97% 28%;
    --primary-light: 184 64% 44%;
    --primary-soft: 183 50% 72%;
    --primary-softer: 184 51% 83%;
    --primary-foreground: 0 0% 100%;
    --main-color: 180 47% 97%;
    --primary-5: 180 47% 97%;
    --primary-10: 184 50% 95%;
    --primary-20: 184 51% 89%;
    --primary-30: 184 51% 83%;
    --primary-40: 184 51% 78%;
    --primary-50: 184 50% 72%;
    --primary-60: 184 50% 66%;
    --primary-70: 184 50% 61%;
    --primary-80: 184 50% 55%;
    --primary-90: 184 51% 50%;
    --primary-100: 184 64% 44%;
    --primary-ranking: 199, 79%, 89%, 1;
  }

  .theme-green {
    --primary: 151 66% 29%;
    --primary-light: 160 79% 39%;
    --primary-soft: 160 51% 70%;
    --primary-softer: 160 51% 70%;
    --primary-foreground: 0 0% 100%;
    --main-color: 165 50% 97%;
    --primary-5: 165 50% 97%;
    --primary-10: 160 48% 94%;
    --primary-20: 159 52% 88%;
    --primary-30: 160 52% 82%;
    --primary-40: 160 52% 76%;
    --primary-50: 160 51% 70%;
    --primary-60: 160 51% 64%;
    --primary-70: 160 52% 58%;
    --primary-80: 160 51% 52%;
    --primary-90: 160 62% 45%;
    --primary-100: 160 79% 39%;
    --primary-ranking: 160, 52%, 82%, 1;
  }

  .theme-orange {
    --primary: 24, 87%, 41%;
    --primary-light: 20, 100%, 69%;
    --primary-soft: 20 100% 84%;
    --primary-softer: 20 100% 91%;
    --primary-foreground: 0 0% 100%;
    --main-color: 23, 100%, 98%;
    --primary-5: 23, 100%, 98%;
    --primary-10: 19, 100%, 97%;
    --primary-20: 21, 100%, 94%;
    --primary-30: 20, 100%, 91%;
    --primary-40: 20, 100%, 87%;
    --primary-50: 20, 100%, 84%;
    --primary-60: 20, 100%, 81%;
    --primary-70: 20, 100%, 78%;
    --primary-80: 20, 100%, 75%;
    --primary-90: 20, 100%, 72%;
    --primary-100: 20, 100%, 69%;
    --primary-ranking: 20, 100%, 91%, 1;
  }
  .theme-pink {
    --primary: 324 61% 48%;
    --primary-light: 329 95% 74%;
    --primary-soft: 330 100% 88%;
    --primary-softer: 330 100% 95%;
    --primary-foreground: 0 0% 100%;
    --main-color: 326, 100%, 99%;
    --primary-5: 326, 100%, 99%;
    --primary-10: 328, 100%, 97%;
    --primary-20: 329, 93%, 95%;
    --primary-30: 328, 95%, 92%;
    --primary-40: 329, 96%, 90%;
    --primary-50: 329, 94%, 87%;
    --primary-60: 328, 95%, 84%;
    --primary-70: 329, 96%, 82%;
    --primary-80: 329, 96%, 79%;
    --primary-90: 329, 95%, 76%;
    --primary-100: 329, 95%, 74%;
    --primary-ranking: 328, 95%, 92%, 1;
  }
  .theme-purple {
    --primary: 258, 45%, 50%;
    --primary-light: 255, 100%, 71%;
    --primary-soft: 255 100% 88%;
    --primary-softer: 256 100% 91%;
    --primary-foreground: 0 0% 100%;
    --main-color: 249, 100%, 99%;
    --primary-5: 249, 100%, 99%;
    --primary-10: 256, 100%, 97%;
    --primary-20: 256, 100%, 94%;
    --primary-30: 256, 100%, 91%;
    --primary-40: 255, 100%, 88%;
    --primary-50: 255, 100%, 85%;
    --primary-60: 255, 100%, 83%;
    --primary-70: 255, 100%, 80%;
    --primary-80: 255, 100%, 77%;
    --primary-90: 255, 100%, 74%;
    --primary-100: 255, 100%, 71%;
    --primary-ranking: 256, 100%, 91%, 1;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    color: transparent;
    background: transparent;
  }

  input[type="time"]::-webkit-calendar-picker-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    color: transparent;
    background: transparent;
  }
}
