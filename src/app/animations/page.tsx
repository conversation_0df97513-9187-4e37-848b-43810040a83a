'use client';
import { AnimationJumpLoading, AnimationStampComplete } from '@/hooks/use-lottie-animation';

export default function AnimationPage() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold mb-8">Lottie 动画测试页面</h1>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">跳跃加载动画</h2>
        <div className="border rounded-lg p-4">
          <AnimationJumpLoading width={200} height={200} />
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">印章完成动画</h2>
        <div className="border rounded-lg p-4">
          <AnimationStampComplete width={300} height={200} />
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">印章完成动画 (实际使用尺寸)</h2>
        <div className="border rounded-lg p-4">
          <AnimationStampComplete height={296} className="w-full" />
        </div>
      </div>
    </div>
  );
}
