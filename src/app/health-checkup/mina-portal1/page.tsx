'use client';

import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';

interface Props {
  sidemenuVariant: 'default';
  className?: string;
  frameClassName?: string;
  frameClassNameOverride?: string;
  syntheticCloneForWrapperClassName?: string;
}

// サイドメニューコンポーネントを内部コンポーネントとして定義
function SideMenu({
  sidemenuVariant,
  className = '',
  frameClassName = '',
  frameClassNameOverride = '',
  syntheticCloneForWrapperClassName = '',
}: Props) {
  return (
    <aside
      className={`mt-4 flex flex-col w-full max-w-md items-start gap-6 px-6 py-0 relative ${className}`}
      aria-label="Side information menu"
    >
      {/* Introductory Notice */}
      <section className={`flex flex-col items-start gap-2 w-full ${frameClassName}`}>
        <p className="text-black font-body-l-regular text-[length:var(--body-l-regular-font-size)] leading-[var(--body-l-regular-line-height)] tracking-[var(--body-l-regular-letter-spacing)] [font-style:var(--body-l-regular-font-style)]">
          マイナポータル連携を進める前に、以下の点にご注意ください。
        </p>
      </section>

      {/* Detailed Instructions */}
      <section className={`flex flex-col items-start gap-2 w-full ${frameClassNameOverride}`}>
        <ol className="list-decimal pl-5 text-black text-base leading-6 font-sans">
          <li>本機能をご利用いただくには、マイナンバーカードが必要です。</li>
          <li>
            健康診断の情報がマイナポータルから取得できない方は、お手数ですが手動で健康診断の情報をご登録ください。
          </li>
          <li>
            マイナンバーカードについて詳しく知りたい方は以下をご参照ください。
            <br />
            <a
              href="https://www.kojinbango-card.go.jp/"
              className="text-[#4457d1] underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              https://www.kojinbango-card.go.jp/
            </a>
          </li>
          <li>
            マイナポータルについて詳しく知りたい方は以下をご参照ください。
            <br />
            <a
              href="https://myna.go.jp/"
              className="text-[#4457d1] underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              https://myna.go.jp/
            </a>
          </li>
        </ol>
      </section>

      {/* Confirmation Prompt */}
      <section
        className={`flex flex-col items-start gap-2 w-full ${syntheticCloneForWrapperClassName}`}
      >
        <p className="text-black font-body-l-regular text-[length:var(--body-l-regular-font-size)] leading-[var(--body-l-regular-line-height)] tracking-[var(--body-l-regular-letter-spacing)] [font-style:var(--body-l-regular-font-style)]">
          上記注意事項を了承の上、本機能をご利用されますか？
        </p>
      </section>
    </aside>
  );
}

export default function Minaportal1() {
  const router = useRouter();
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <TopBar title={APP_TEXT.HEALTH_CHECKUP.TITLE} />
      <div className="flex-1 flex flex-col">
        <SideMenu sidemenuVariant="default" />
        <div className="px-6 mt-4 w-full flex flex-col gap-4">
          <Button
            className="w-full h-14 rounded-full"
            onClick={() => router.push(ROUTES.HEALTH_CHECKUP.MINA_PORTAL2)}
          >
            はい
          </Button>
          <Button
            className="w-full h-14 rounded-full"
            variant="outline"
            onClick={() => router.push(ROUTES.HEALTH_CHECKUP.MINA_PORTAL2)}
          >
            いいえ
          </Button>
        </div>
      </div>
    </div>
  );
}
