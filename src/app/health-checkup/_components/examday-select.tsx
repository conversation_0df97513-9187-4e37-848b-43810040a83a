import {
  SelectDrawer,
  SelectDrawerClose,
  SelectDrawerContent,
  SelectDrawerHeader,
  SelectDrawerTrigger,
} from '@/components/shared/select-drawer';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { CalendarDays } from 'lucide-react';
import { useEffect, useState } from 'react';
export function ExamdaySelect<T extends { value: string; name: string }>({
  className,
  defaultValue,
  options,
  title,
  onSelect,
  onChange,
  renderOption,
  id,
}: {
  className?: string;
  defaultValue: string;
  options: T[];
  title: string;
  onSelect?: (value: string) => void;
  onChange?: (value: string) => void;
  renderOption?: (option: T) => React.ReactNode;
  id?: string;
}) {
  const defaultItem = options.find((option) => option.value === defaultValue);
  const [selectedItem, setSelectedItem] = useState(defaultItem);

  useEffect(() => {
    const newDefaultItem = options.find((option) => option.value === defaultValue);
    setSelectedItem(newDefaultItem);
  }, [defaultValue, options]);

  const handleSelect = (item: T) => {
    setSelectedItem(item);
    onSelect?.(item.value);
    onChange?.(item.value);
  };

  return (
    <SelectDrawer>
      <SelectDrawerTrigger
        className={cn('rounded-xl border-border bg-white relative', className)}
        id={id}
        hideChevron={true}
      >
        <div className="flex justify-between items-center w-full pr-2">
          {renderOption && selectedItem ? (
            renderOption(selectedItem)
          ) : (
            <span>
              {options.find((option) => option.value === selectedItem?.value)?.name || ''}
            </span>
          )}
          <CalendarDays className="h-5 w-5 absolute right-3" />
        </div>
      </SelectDrawerTrigger>
      <SelectDrawerContent className="bg-white">
        <SelectDrawerHeader>{title}</SelectDrawerHeader>
        <div className="max-h-[70vh] overflow-y-auto">
          <RadioGroup defaultValue={selectedItem?.value}>
            {options.map((item) => (
              <SelectDrawerClose
                key={item.value}
                asChild
                onClick={() => {
                  handleSelect(item);
                }}
              >
                <div className="flex items-center h-14 px-6">
                  <RadioGroupItem
                    value={item.value}
                    id={`select-${item.value}`}
                    className="h-5 w-5 border-2 data-[state=checked]:border-primary data-[state=checked]:text-primary"
                  />
                  <Label htmlFor={`select-${item.value}`} className="text-base  ml-4 flex-1">
                    {renderOption ? renderOption(item) : item.name}
                  </Label>
                </div>
              </SelectDrawerClose>
            ))}
          </RadioGroup>
        </div>
      </SelectDrawerContent>
    </SelectDrawer>
  );
}
