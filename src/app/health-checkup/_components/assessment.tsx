// ランクタイプを定義
type AssessmentRank = 'A' | 'B' | 'C' | 'D' | string;

// 表示モードを定義
type AssessmentVariant = 'button' | 'record';

// コンポーネントプロパティタイプ
interface AssessmentProps {
  rank: AssessmentRank;
  variant?: AssessmentVariant;
  className?: string;
  label?: string; // オプションのlabelプロパティを追加
  onClick?: () => void; // クリックイベントハンドラー関数を追加
}

// ランクスタイル設定
const rankConfig: Record<
  string,
  {
    bg: string;
    text: string;
    label: string;
    hasImage?: boolean;
  }
> = {
  A: {
    bg: 'bg-[#D1E4DB]',
    text: 'text-[#197A4B]',
    label: 'A Rank',
    hasImage: true,
  },
  B: {
    bg: 'bg-[#EEF8FD]',
    text: 'text-[#0770C2]',
    label: 'B Rank',
    hasImage: true,
  },
  C: {
    bg: 'bg-[#F7EDE7]',
    text: 'text-[#C2560E]',
    label: 'C Rank',
    hasImage: true,
  },
  D: {
    bg: 'bg-[#FDF2F2]',
    text: 'text-[#CE0000]',
    label: 'D Rank',
    hasImage: true,
  },
  // デフォルトはNoDataスタイル
  default: {
    bg: 'bg-white',
    text: 'text-gray-700',
    label: 'No Data',
    hasImage: false,
  },
};

export function Assessment({
  rank,
  variant = 'button',
  className = '',
  label,
  onClick,
}: AssessmentProps) {
  // ランク設定を取得、A、B、C、D以外の場合はデフォルト設定を使用
  const config = ['A', 'B', 'C', 'D'].includes(rank) ? rankConfig[rank] : rankConfig.default;

  // ボタンモード
  if (variant === 'button') {
    return (
      <div
        className={`flex items-center justify-center flex-col ${config.bg} p-2 rounded-md border-border border ${className} ${onClick ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}`}
        onClick={onClick}
      >
        {config.hasImage ? (
          <img src={`/images/health-checkup/${rank}.svg`} alt={`${rank}等级`} />
        ) : (
          <span className="bg-gray-500 text-white text-xs px-2.5 rounded-xl mb-5 mt-1">
            データなし
          </span>
        )}
        <span className={`font-bold ${config.text}`}>{label || config.label}</span>
      </div>
    );
  }

  // レコードモード
  return (
    <div
      className={`w-6 h-6 ${config.bg} ${config.text} flex items-center justify-center rounded-md font-bold text-sm ${className}`}
    >
      {label || (['A', 'B', 'C', 'D'].includes(rank) ? rank : '-')}
    </div>
  );
}

// 全てのランクのボタンコンポーネントを表示
export function AssessmentButtonGroup() {
  return (
    // 1行3個、スペースを均等分割
    <div className="grid grid-cols-3 gap-2">
      <Assessment rank="A" />
      <Assessment rank="B" />
      <Assessment rank="C" />
      <Assessment rank="D" />
      <Assessment rank="NoData" />
    </div>
  );
}

// 後方互換性を保つため、元のコンポーネント名を保持
export function AssessmentButton() {
  return <AssessmentButtonGroup />;
}

// 全てのランクのレコードコンポーネントを表示
export function AssessmentRecordGroup() {
  return (
    <div className="flex items-center gap-2">
      <Assessment rank="A" variant="record" />
      <Assessment rank="B" variant="record" />
      <Assessment rank="C" variant="record" />
      <Assessment rank="D" variant="record" />
      <Assessment rank="NoData" variant="record" />
    </div>
  );
}

// 後方互換性を保つため、元のコンポーネント名を保持
export function AssessmentRecord() {
  return <AssessmentRecordGroup />;
}
