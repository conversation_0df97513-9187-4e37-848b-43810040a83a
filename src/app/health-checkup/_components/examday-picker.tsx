'use client';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { ja } from 'date-fns/locale';
import { CalendarDays } from 'lucide-react';
import { useRef, useState } from 'react';

export function ExamdayPicker({
  value,
  onChange,
  className = '',
  autoFlag,
}: {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  autoFlag?: number;
}) {
  const [selectedDate, setSelectedDate] = useState<string | undefined>(value);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDateChange = (date: string) => {
    setSelectedDate(date);
    onChange?.(date);
  };

  const handleContainerClick = () => {
    if (inputRef.current) {
      inputRef.current.focus();
      inputRef.current.showPicker?.();
    }
  };

  const formatDate = (date: string | undefined) => {
    if (!date) return '';
    const parsedDate = new Date(date);
    return format(parsedDate, 'yyyy年M月d日（E）', { locale: ja });
  };

  return (
    <div className={cn('relative cursor-pointer', className)} onClick={handleContainerClick}>
      <Input
        ref={inputRef}
        type="date"
        value={selectedDate}
        onChange={(e) => handleDateChange(e.target.value)}
        className="flex relative z-10 border rounded-lg appearance-none h-12 flex-1 pl-4 pr-10 text-sm w-full text-transparent cursor-pointer"
      />
      <div
        className={cn(
          'absolute left-2 top-1/2 -translate-y-1/2 text-md pointer-events-none z-20',
          selectedDate ? 'bg-primary-foreground px-2 rounded' : 'px-2 rounded',
        )}
      >
        {selectedDate ? formatDate(selectedDate) : '日付を選択'}
      </div>
      <div className="absolute right-12 top-1/2 -translate-y-1/2 pointer-events-none z-20">
        <span className="text-xs px-2 py-1 rounded-md bg-primary-5 text-primary ">
          {autoFlag === 1 ? '自動連携' : '手入力'}
        </span>
      </div>
      <CalendarDays className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none z-20" />
    </div>
  );
}
