'use client';
import { Button as SharedButton } from '@/components/shared/button';
import { DateSelect } from '@/components/shared/date-select';
import { Drawer, DrawerClose, DrawerContent, DrawerHeader } from '@/components/shared/drawer';
import { cn } from '@/lib/utils';
import { CalendarDays, X } from 'lucide-react';
import * as React from 'react';
import { useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { ExamdayPicker } from './examday-picker';
interface ExamdayUpdateDrawerProps {
  className?: string;
  selectedExamDay: string;
  examDaysData: Array<{ examDay: string; autoFlag: number }>;
  onSave: (newDate: string) => void;
  children?: React.ReactNode;
}

export default function ExamdayUpdateDrawer({
  className,
  selectedExamDay,
  examDaysData,
  onSave,
  children,
}: ExamdayUpdateDrawerProps) {
  const [date, setDate] = React.useState<string>(selectedExamDay || '');
  const [isOpen, setIsOpen] = React.useState(false);

  // selectedExamDayが変更された時、選択された日付を更新する
  useEffect(() => {
    // 当天日期
    const today = new Date().toISOString().split('T')[0];

    // 检查当天日期是否已经存在于examDays中
    const todayExists = examDaysData.some(
      (item: { examDay: string; autoFlag: number }) => item.examDay === today,
    );

    if (todayExists) {
      // 如果当天日期已存在，设置examDay为空字符串
      setDate('');
    } else {
      // 如果当天日期不存在，设置为当天日期
      setDate(today);
    }
  }, []);

  const autoFlag = examDaysData.find((exam) => exam.examDay === selectedExamDay)?.autoFlag;

  const handleSave = () => {
    if (!date) return;

    // 新しく選択された日付がexamDaysDataに既に存在するかチェックする
    const dateExists = examDaysData.some((exam) => exam.examDay === date);

    if (dateExists) {
      // 日付が既に存在する場合、エラーメッセージを表示する
      toast.error('『受診日』は既に登録されています', {
        position: 'top-center',
      });
      return;
    }

    // 日付が存在しない場合、onSaveコールバックを呼び出す
    onSave(date);
    setIsOpen(false);
  };

  const handleButtonClick = () => {
    setIsOpen(true);
  };

  return (
    <>
      <SharedButton variant="outline" className="w-full" onClick={handleButtonClick}>
        受診日を変更する
        <CalendarDays className=" h-4 w-4" />
      </SharedButton>

      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerContent className="bg-white">
          <DrawerHeader className="text-center font-bold">
            受診日を変更
            <DrawerClose asChild>
              <button type="button" className="absolute right-5 top-3">
                <X size={24} />
              </button>
            </DrawerClose>
          </DrawerHeader>
          <div className="p-4 space-y-4">
            <DateSelect
              value={date}
              onChange={setDate}
              className="mx-auto"
              disabledDates={examDaysData.map((item) => item.examDay)}
              max={new Date()}
              appendInner={
                <span className="text-xs px-2 py-1 rounded-md bg-primary-5 text-primary ">
                  {autoFlag === 1 ? '自動連携' : '手入力'}
                </span>
              }
            />
            <SharedButton className="w-full" onClick={handleSave}>
              保存
            </SharedButton>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
}
