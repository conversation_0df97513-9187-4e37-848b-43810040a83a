'use client';
import { Button } from '@/components/ui/button';
import { ChevronLeft, X } from 'lucide-react';

export default function CommonHeader({
  title,
  handleBackClick,
  handleCloseClick,
  showBackButton = false,
  showCloseButton = false,
}: {
  title: string;
  showBackButton?: boolean;
  showCloseButton?: boolean;
  handleBackClick?: () => void;
  handleCloseClick?: () => void;
}) {
  return (
    <div className="w-full bg-white  py-4 px-6 flex items-center">
      <div className="w-12">
        {showBackButton && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBackClick}
            className="mr-2"
            aria-label="返回"
          >
            <ChevronLeft className="h-10 w-10" />
          </Button>
        )}
      </div>
      <h1 className="text-xl font-medium text-center flex-1">{title}</h1>
      <div className="w-12">
        {showCloseButton && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleCloseClick}
            className="mr-2"
            aria-label="閉じる"
          >
            <X className="h-10 w-10" />
          </Button>
        )}
      </div>
    </div>
  );
}
