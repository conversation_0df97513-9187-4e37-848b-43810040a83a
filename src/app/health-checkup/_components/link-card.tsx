import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface LinkCardProps {
  children: React.ReactNode;
  direction?: 'row' | 'col';
  density?: 'default' | 'dense';
  onClick?: () => void;
  href?: string;
  className?: string;
  contentClassName?: string;
}

export default function LinkCard({
  children,
  direction = 'row',
  density = 'default',
  onClick,
  href,
  className,
  contentClassName,
}: LinkCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (href) {
      window.location.href = href;
    }
  };

  const isVertical = direction === 'col';

  return (
    <Card
      className={cn(
        'border border-primary bg-card cursor-pointer hover:bg-accent/50 transition-colors w-full',
        density === 'dense' ? 'p-1' : 'p-4',
        className,
      )}
      onClick={handleClick}
    >
      <CardContent
        className={cn(
          'p-1 flex gap-2',
          isVertical ? 'flex-col items-center' : 'flex-row items-center justify-start',
          contentClassName,
        )}
      >
        {children}
      </CardContent>
    </Card>
  );
}
