'use client';
import { healthCheckupAPI } from '@/api/modules/health-checkup';
import TopBar from '@/components/layout/top-bar';
import { Button as SharedButton } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';

import { But<PERSON> } from '@/components/ui/button';
import { DialogClose } from '@/components/ui/dialog';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useHealthCheckupStore } from '@/store/health-checkup';
import type { DailyExamData } from '@/types/health-checkup-input';
import { CalendarDays, Pencil } from 'lucide-react';

import { useRouter } from '@/hooks/use-next-navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { Assessment } from './_components/assessment';
import { ExamdaySelect } from './_components/examday-select';
import ExamdayUpdateDrawer from './_components/examday-update-drawer';
import LinkCard from './_components/link-card';
import { formatDate } from './checkup-common';
const title = '健康診断';

export default function ExamResultPage() {
  const { isLoading, setIsLoading } = useLoading();
  const router = useRouter();
  const { selectedExamDay, setSelectedExamDay } = useHealthCheckupStore();
  const { isShow, setDialog } = useMessageDialog();
  const [examDaysData, setExamDaysData] = useState<Array<{ examDay: string; autoFlag: number }>>(
    [],
  );
  const [examDaysOptions, setExamDaysOptions] = useState<
    Array<{ value: string; name: string; autoFlag: number }>
  >([]);
  const [dailyExamData, setDailyExamData] = useState<DailyExamData | null>(null);

  const [examResultMenu, setExamResultMenu] = useState<
    { label: string; value: string; key: string }[]
  >([
    { label: '身体測定', value: '', key: 'body-measure' },
    { label: '血圧', value: '', key: 'blood-pressure' },
    { label: '脂質', value: '', key: 'blood-lipid' },
    { label: '肝機能', value: '', key: 'liver-function' },
    { label: '血糖', value: '', key: 'blood-glucose' },
    { label: '尿', value: '', key: 'urinalysis' },
    { label: '貧血', value: '', key: 'anemia' },
    { label: '腎機能', value: '', key: 'kidney-function' },
  ]);

  const menuOther = { label: 'そのほか', text: '検査項目など', key: 'other' };

  const handleBackClick = () => {
    router.push('/health-record/detail');
  };

  const handleExamDayChange = (value: string) => {
    setSelectedExamDay(value);
  };

  // 受診日リストを取得
  const fetchExamDays = async (targetExamDay?: string) => {
    setIsLoading(true);

    try {
      const response = await healthCheckupAPI.getExamDays();
      console.log(response);
      if (response?.examDays) {
        const examDays = response.examDays;

        setExamDaysData(examDays);

        // examDaysをSelectコンポーネント用の形式に変換
        const options = examDays.map((day) => ({
          value: day.examDay,
          name: formatDate(day.examDay),
          autoFlag: day.autoFlag,
        }));

        setExamDaysOptions(options);

        // 目標日付が指定されている場合、優先的に目標日付を設定
        if (targetExamDay && options.some((option) => option.value === targetExamDay)) {
          setSelectedExamDay(targetExamDay);
        } else if (options.length > 0) {
          // データがある場合、現在選択されている日付がまだ存在するかチェック、存在しない場合は最初の日付を選択
          const currentSelectedExists = options.some((option) => option.value === selectedExamDay);
          if (!selectedExamDay || !currentSelectedExists) {
            setSelectedExamDay(options[0].value);
          }
        }
      } else {
        toast.error('受診日データが見つかりません');
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 日付に対応する検査データを取得
  const fetchDailyExamData = async (date: string) => {
    if (!date) return;

    setIsLoading(true);

    try {
      const response = await healthCheckupAPI.getDailyExamData({
        examDay: date,
      });
      setDailyExamData(response);
      console.log(response);
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // ページ初期化時に受診日リストを取得
  useEffect(() => {
    fetchExamDays();
  }, []);

  // 選択された日付が変更された時、対応する検査データを取得
  useEffect(() => {
    if (selectedExamDay) {
      fetchDailyExamData(selectedExamDay);
    }
  }, [selectedExamDay]);

  // dailyExamDataが変更された時、examResultMenuのvalue値を更新
  useEffect(() => {
    if (dailyExamData?.examResultData) {
      const { examResultData } = dailyExamData;

      // examResultMenuのvalue値を更新
      const updatedMenu = examResultMenu.map((item) => {
        let judgeValue = '';

        // マッピング関係に基づいて対応する評価を取得
        switch (item.key) {
          case 'body-measure':
            judgeValue = examResultData.bodyMeasJudge || '';
            break;
          case 'blood-pressure':
            judgeValue = examResultData.bpJudge || '';
            break;
          case 'blood-lipid':
            judgeValue = examResultData.blJudge || '';
            break;
          case 'liver-function':
            judgeValue = examResultData.lfJudge || '';
            break;
          case 'blood-glucose':
            judgeValue = examResultData.bgJudge || '';
            break;
          case 'urinalysis':
            judgeValue = examResultData.urinalysisJudge || '';
            break;
          case 'anemia':
            judgeValue = examResultData.anemiaJudge || '';
            break;
          case 'kidney-function':
            judgeValue = examResultData.kfJudge || '';
            break;
          default:
            judgeValue = '';
        }

        return { ...item, value: judgeValue };
      });

      setExamResultMenu(updatedMenu);
    }
  }, [dailyExamData]);

  // メニュー項目クリックイベントを処理
  const handleMenuItemClick = (key: string) => {
    if (selectedExamDay) {
      router.push(`/health-checkup/exam-detail/${key}?examDay=${selectedExamDay}`);
    }
  };

  const handleDeleteExamDay = async () => {
    await healthCheckupAPI.deleteDailyExamData(selectedExamDay);
    // Refresh the page
    window.location.reload();
  };

  const handleExamDayUpdate = async (examDayFrom: string, examDayTo: string) => {
    await healthCheckupAPI.updateExamDay({ examDayFrom, examDayTo });
    toast.success('受診日を変更しました');
    // Refresh the page
    window.location.reload();
  };

  // 日付更新保存を処理
  const handleExamDayUpdateSave = (newDate: string) => {
    const examDayFrom = selectedExamDay;
    const examDayTo = newDate;
    const cutoffDate = '2024-04-01';

    // 日付比較：両方が2024-04-01前、または両方が2024-04-01以降の場合
    const fromBeforeCutoff = examDayFrom < cutoffDate;
    const toBeforeCutoff = examDayTo < cutoffDate;

    // 両方が同じ期間（前または後）にある場合、Dialogを表示せずに直接更新
    if (fromBeforeCutoff === toBeforeCutoff) {
      handleExamDayUpdate(examDayFrom, examDayTo);
      return;
    }

    // 2024年4月の前後を跨ぐ受診日の編集をしたときにだけ、このモーダル画面が出てくる想定です。（2024-03-31から2024-04-01 ）（2024-04-01から2024-04-30 ）
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-2 text-left ">
          <p>
            特定健診は2024年4月より検査項目が改定されています。受診日変更により再登録が必要な項目が発生しますが、受診日を変更しますか？
          </p>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <SharedButton
            className="w-full"
            onClick={() => {
              handleExamDayUpdate(selectedExamDay, newDate);
              setDialog(false);
            }}
          >
            受診日を変更する
          </SharedButton>
          <DialogClose asChild>
            <TextButton className="mt-2 w-full" variant="muted">
              キャンセル
            </TextButton>
          </DialogClose>
        </div>
      ),
    });
  };

  return (
    <div className="bg-card ">
      {/* Header */}
      <TopBar enableBack={true} onBack={handleBackClick} title={title} />
      {/* Content */}
      {examDaysData.length > 0 && (
        <div className="p-6">
          {/* 受診日 */}
          <div className="">
            <label htmlFor="exam-day-select" className="block mb-2 text-base font-medium">
              受診日
            </label>

            <div>
              <ExamdaySelect
                key={selectedExamDay} // selectedExamDayが変更された時にコンポーネントが再レンダリングされることを保証するためのkey追加
                id="exam-day-select"
                defaultValue={selectedExamDay}
                options={examDaysOptions}
                title="受診日"
                onChange={(value) => handleExamDayChange(value)}
                className="flex justify-between items-center w-full p-4 text-base bg-white rounded-xl border border-border"
                renderOption={(option) => (
                  <div className="flex justify-between items-center w-full">
                    <span>{option.name}</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-primary-5 text-primary mr-4">
                      {option.autoFlag === 1 ? '自動連携' : '手入力'}
                    </span>
                  </div>
                )}
              />
            </div>
          </div>

          {/* Alert Text */}
          <div className="p-2 flex flex-col gap-4">
            <p>健康診断情報をまだ登録されていない場合は、手入力で登録をしてください。</p>
          </div>

          {/* Content */}
          <div className=" flex flex-col gap-4">
            {/* examResultMenu */}
            <div className="grid grid-cols-3 gap-2">
              {examResultMenu.map((item) => (
                <div key={item.key} className="flex flex-col items-center">
                  <Assessment
                    rank={item.value}
                    variant="button"
                    className="w-full h-24"
                    label={item.label}
                    onClick={() => handleMenuItemClick(item.key)}
                  />
                </div>
              ))}
              {/* menuOther */}
              <LinkCard
                direction="col"
                className="h-full p-0 rounded-md border-border border"
                contentClassName="gap-3 h-full pt-6"
                onClick={() =>
                  router.push(`/health-checkup/exam-detail/others?examDay=${selectedExamDay}`)
                }
              >
                <span className="font-bold">{menuOther.label}</span>
                <span className="text-sm">{menuOther.text}</span>
              </LinkCard>
            </div>
            {/* Question */}
            <Button
              onClick={() =>
                router.push(`/health-checkup/exam-detail/question?examDay=${selectedExamDay}`)
              }
              variant="outline"
              className="w-full bg-white font-bold h-12"
              size="lg"
            >
              <span>質問票</span>
            </Button>
            {/* Remarks */}
            <div className="flex flex-wrap gap-2 bg-white rounded-lg py-2">
              <span className="flex items-center">
                <Assessment rank="A" variant="record" />
                <span className="text-gray-700 ml-1 text-sm">異常なし</span>
              </span>
              <span className="flex items-center">
                <Assessment rank="B" variant="record" />
                <span className="text-gray-700 ml-1 text-sm">軽度異常</span>
              </span>
              <span className="flex items-center">
                <Assessment rank="C" variant="record" />
                <span className="text-gray-700 ml-1 text-sm">要再検査・生活改善</span>
              </span>
              <span className="flex items-center">
                <Assessment rank="D" variant="record" />
                <span className="text-gray-700 ml-1 text-sm">要精密検査・治療</span>
              </span>
            </div>

            {/* Notioce */}
            <div className=" space-y-2 text-sm text-gray-700 leading-relaxed ">
              <div className="flex">
                <span className="text-gray-500 mr-1 flex-shrink-0">※</span>
                <p>
                  健診結果の判定は、日本人間ドック学会の判定区分表2023年度版に従い表示しております。健診を受けられた医療機関の判定とは異なる場合があります。
                </p>
              </div>

              <div className="flex">
                <span className="text-gray-500 mr-1 flex-shrink-0">※</span>
                <p>
                  上記に表示している判定は総合判定ではありません。各項目の検査項目の中で一番悪い判定を表示しています。
                </p>
              </div>
            </div>
            <div>
              <ExamdayUpdateDrawer
                selectedExamDay={selectedExamDay}
                examDaysData={examDaysData}
                onSave={handleExamDayUpdateSave}
                className="w-full mb-4 p-4 text-base"
              />
              <SharedButton
                variant="destructive"
                className="w-full mt-2"
                onClick={() => {
                  setDialog(true, {
                    content: (
                      <div className="flex flex-col gap-2 text-left py-4">
                        <p>「{formatDate(selectedExamDay)}」の記録を削除してよろしいですか？</p>
                        <p className="text-sm text-muted-foreground">
                          ※一度削除すると元に戻すことはできません
                        </p>
                      </div>
                    ),
                    outSideClickClose: false,
                    footer: (
                      <div className="flex-col ">
                        <SharedButton
                          variant="destructive"
                          className="w-full"
                          onClick={() => {
                            handleDeleteExamDay();
                            setDialog(false);
                          }}
                        >
                          削除
                        </SharedButton>
                        <DialogClose asChild>
                          <TextButton className="mt-2 w-full" variant="muted">
                            キャンセル
                          </TextButton>
                        </DialogClose>
                      </div>
                    ),
                  });
                }}
              >
                この日の健診結果を削除する
              </SharedButton>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="p-6 bg-background">
        <div>
          <h1 className="text-xl font-bold  flex-1 pr-8 mb-4">健康診断情報登録</h1>
          <div className="flex gap-2 items-stretch">
            {/* <LinkCard
              direction="col"
              density="dense"
              onClick={() => console.log('マイナポータル連携')}
            >
              <img src="/images/health-checkup/icon-mynumber.svg" alt="" />
              <div className="text-primary text-center font-bold">マイナポータル連携</div>
            </LinkCard> */}
            <LinkCard
              direction="col"
              density="dense"
              className="w-1/2"
              onClick={() => router.push('/health-checkup/exam-create')}
            >
              <div className="w-[41px] h-[41px] flex items-center justify-center">
                <Pencil className="text-primary" width={36} height={36} />
              </div>

              <div className="text-primary text-center font-bold">手入力</div>
            </LinkCard>
          </div>
          {/* <p className="text-sm text-gray-600 my-4">
            マイナポータル連携は一度連携してもその後は自動で連携されないので、最新の情報は都度マイナポータル連携を実施して取得してください。
          </p> */}
        </div>
        {/* <div>
          <h1 className="text-xl font-bold flex-1 pr-8 my-4">アドバイス</h1>
          <LinkCard
            density="dense"
            onClick={() => router.push('/health-checkup/services/ebhs-gateway')}
          >
            <img src="/images/health-checkup/icon-health-score.svg" alt="" />
            <div className="text-primary font-bold">
              科学的に予測された寿命やヘルススコアをチェック
            </div>
          </LinkCard>

          <LinkCard
            density="dense"
            className="my-4"
            onClick={() => router.push('/health-checkup/services/wellcle-gateway')}
          >
            <img src="/images/health-checkup/icon-health-check.svg" alt="" />
            <div className="text-primary font-bold"> 生活習慣病の進行度をチェック</div>
          </LinkCard>

          <LinkCard density="dense" onClick={() => router.push('/health-score')}>
            <div className="w-[64px] h-[64px] bg-gray-200" />
            <span className="text-primary font-bold">ヘルスチェックAIをチェック</span>
          </LinkCard>
        </div> */}
      </div>
    </div>
  );
}
