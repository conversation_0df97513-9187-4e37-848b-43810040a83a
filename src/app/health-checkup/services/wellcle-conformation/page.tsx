'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { Select } from '@/components/shared/select';
import { useRouter } from '@/hooks/use-next-navigation';
import { useState } from 'react';
export default function WellcleConformationPage() {
  const router = useRouter();
  const handleClick = () => {
    router.push('/health-checkup/services/wellcle-conformation');
  };

  const SELECT_OPTIONS = [
    { value: 'open', name: '開く' },
    { value: 'close', name: '閉じる' },
  ];

  const [selectedOption, setSelectedOption] = useState('open');
  const handleOptionChange = (value: string) => {
    setSelectedOption(value);
  };

  return (
    <div className="bg-card h-screen">
      <TopBar
        title="他サービスと連携する"
        enableBack={true}
        enableClose={true}
        onBack={() => {
          router.back();
        }}
        onClose={() => {
          router.push('/health-checkup');
        }}
      />
      <div className="p-6">
        <p>【ドコモヘルスケアデモ】は、下記の事業者へ情報を提供します。</p>
        <div className="flex justify-center">
          <img className="my-6" src="/images/health-checkup/logo-wellcle.svg" alt="" />
        </div>
        <div className="flex flex-col gap-2">
          <div className="font-bold text-sm">提供先事業者名</div>
          <div>株式会社ウェルクル</div>
          <hr />
        </div>

        <div className="flex flex-col gap-2 mt-6">
          <div className="font-bold text-sm">サービス名</div>
          <div>生活習慣病チェック</div>
          <hr />
        </div>
        <div className="flex flex-col gap-2 mt-6">
          <div className="font-bold text-sm">提供情報</div>
          <div className="mb-2">
            {' '}
            <Select
              className="w-full"
              defaultValue={selectedOption}
              options={SELECT_OPTIONS}
              title="提供情報"
              onChange={(value) => handleOptionChange(value)}
            />
          </div>

          <hr />
        </div>
        <div className="flex flex-col gap-2 mt-6">
          <div className="font-bold text-sm">提供先における利用目的</div>
          <div>
            健康マイレージで取得した健康診断情報をもとに、生活習慣病の進行度チェックおよび動脈硬化性疾患の発症リスク推定をするとともに健康アドバイスを行うため。
          </div>
          <div>上記の事項に同意しますか？</div>
          <div className="text-gray-500 text-sm">
            ※「同意する」ボタンを押すと外部サービスに移動します。
          </div>
          <hr />
        </div>
        <div className="flex flex-col gap-3">
          <Button>同意する</Button>
          <Button variant="outline">同意しない</Button>
        </div>
      </div>
    </div>
  );
}
