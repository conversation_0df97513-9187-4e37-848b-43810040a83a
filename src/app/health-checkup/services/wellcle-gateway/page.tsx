'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { useRouter } from '@/hooks/use-next-navigation';
import LinkCard from '../../_components/link-card';
export default function WellcleGatewayPage() {
  const router = useRouter();
  const handleClick = () => {
    router.push('/health-checkup/services/wellcle-conformation');
  };
  return (
    <div className="flex flex-col h-scree n">
      <TopBar
        title="健康診断"
        enableBack={true}
        enableClose={true}
        onBack={() => {
          router.back();
        }}
        onClose={() => {
          router.push('/health-checkup');
        }}
      />
      <div className="p-6">
        <p>健康診断情報を外部サービスに連携することができます。</p>
        <LinkCard direction="col" onClick={handleClick}>
          <span className="text-primary font-medium text-lg">生活習慣病チェック</span>
          <span className="text-primary ">生活習慣病の進行度をチェックする</span>
          <span className="text-primary font-medium bg-muted rounded-full px-2 text-sm">
            (株)ウェルクル
          </span>
        </LinkCard>
      </div>
      <div className="flex-1 " />
      <div className="p-6 ">
        <Button variant="outline" className="w-full" onClick={() => router.back()}>
          健康診断情報画面に戻る
        </Button>
      </div>
    </div>
  );
}
