'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { ConnectionServiceResponse, MycardResponseSendRequest } from '@/types/health-checkup';
import React, { useEffect, useState } from 'react';

import { healthCheckupAPI } from '@/api/modules/health-checkup-alignment';

export default function HealthCompleted() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [services, setServices] = useState<ConnectionServiceResponse['serviceList']>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showEBHS, setShowEBHS] = useState(false);
  const [showWellcle, setShowWellcle] = useState(false);
  const [showHealthScore, setShowHealthScore] = useState(false);

  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const loggedInType = searchParams.get('logged_in_type');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (error || errorDescription) {
      router.push(ROUTES.HEALTH_CHECKUP.LINK_OTHER_SERVICES);
      return;
    }

    if (code && state) {
      initializeData({
        code,
        state,
        logged_in_type: loggedInType || '',
        error: error || '',
        error_description: '',
      });
    } else {
      setIsLoading(false);
    }
  }, [searchParams, router]);

  const initializeData = (params: {
    code: string;
    state: string;
    logged_in_type: string;
    error: string;
    error_description: string;
  }) => {
    setIsLoading(true);
    healthCheckupAPI
      .postMycard({
        user_id: 0,
        code: params.code,
        state: params.state,
        logged_in_type: params.logged_in_type,
        error: params.error,
        error_description: params.error_description,
        connectionAt: new Date().toISOString(),
      })
      .then((mycardResponse) => {
        mycardResponse.sex = 1;
        return healthCheckupAPI.postMedicalExam(mycardResponse);
      })
      .then(() => {
        return fetchServices();
      })
      .catch((error) => {
        console.log('err:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const fetchServices = async () => {
    try {
      const response: ConnectionServiceResponse = await healthCheckupAPI.getConnectionService();
      const ebhsService = response.serviceList.find((service) => service.fcnId === 1);
      const wellcleService = response.serviceList.find((service) => service.fcnId === 2);
      const healthScoreService = response.serviceList.find((service) => service.fcnId === 3);

      setShowEBHS(ebhsService?.useSts === 1);
      setShowWellcle(wellcleService?.useSts === 1);
      setShowHealthScore(healthScoreService?.useSts === 1);
      setServices(response.serviceList);
    } catch (error) {
      console.log('err:', error);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleServiceClick = (type: string) => {
    router.push(`/health-checkup/link-services?type=${type}`);
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <TopBar title={APP_TEXT.HEALTH_CHECKUP.TITLE} />
      <div className="flex-1 flex flex-col">
        <div className="px-6 pt-6 pb-6 mb-6">
          <div className="text-lg font-bold mb-2">健康診断情報の取得が完了しました。</div>

          <div className="mt-4 text-base text-[#222] leading-[1.5] mb-2">
            健康診断情報を外部サービスに連携することができます。
          </div>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700" />
            </div>
          ) : (
            <div className="flex flex-col gap-4 mt-6">
              {showEBHS && (
                <button
                  type="button"
                  onClick={() => handleServiceClick('1')}
                  className="relative flex items-center justify-center border border-[#2947D7] rounded-xl px-4 py-4 bg-white hover:shadow-md transition"
                >
                  <div className="flex-1 text-center">
                    <div className="text-lg font-bold text-blue-700">EBHS Life</div>
                    <div className="text-sm text-[#4457D1] mt-1">
                      寿命予測とヘルススコアを確認する
                    </div>
                    <div className="inline-block mt-2 px-2 py-0.5 bg-[#EAEDFB] text-xs text-gray-500 rounded-full">
                      エムスリー(株)
                    </div>
                  </div>
                </button>
              )}
              {showWellcle && (
                <button
                  type="button"
                  onClick={() => handleServiceClick('2')}
                  className="relative flex items-center justify-center border border-[#2947D7] rounded-xl px-4 py-4 bg-white hover:shadow-md transition"
                >
                  <div className="flex-1 text-center">
                    <div className="text-lg font-bold text-blue-700">生活習慣病チェック</div>
                    <div className="text-sm text-[#4457D1] mt-1">
                      生活習慣病の進行度をチェックする
                    </div>
                    <div className="inline-block mt-2 px-2 py-0.5 bg-[#EAEDFB] text-xs text-gray-500 rounded-full">
                      (株)ウェルクル
                    </div>
                  </div>
                </button>
              )}
              {showHealthScore && (
                <button
                  type="button"
                  onClick={() => handleServiceClick('3')}
                  className="relative flex items-center justify-center border border-[#2947D7] rounded-xl px-4 py-4 bg-white hover:shadow-md transition"
                >
                  <div className="flex-1 text-center">
                    <div className="text-lg font-bold text-blue-700">
                      ヘルスチェックAI
                      <br />
                      血糖値・中性脂肪改善習慣
                    </div>
                    <div className="text-sm text-[#4457D1] mt-1">
                      あなた専用の改善ミッションに挑戦する
                    </div>
                  </div>
                </button>
              )}
              {!showEBHS && !showWellcle && !showHealthScore && (
                <div className="text-center text-gray-500 mt-6">
                  利用可能なサービスはありません。
                </div>
              )}
            </div>
          )}
        </div>
        <div className="px-6 mt-4 w-full flex flex-col gap-4">
          <Button className="w-full h-14 rounded-full" onClick={handleBack}>
            健康診断情報画面に戻る
          </Button>
        </div>
      </div>
    </div>
  );
}
