'use client';
import { healthCheckupAPI } from '@/api/modules/health-checkup-alignment';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import {
  registerMessageHandler,
  removeMessageHandler,
  sendMessageToNative,
} from '@/utils/native-bridge';
import React, { useEffect, useState } from 'react';

export default function Minaportal2() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    registerMessageHandler('receive-message-from-native', (data) => {
      const url = data?.url;
      router.push(url);
    });
    return () => {
      removeMessageHandler('receive-message-from-native');
    };
  }, []);
  const handleConsent = async () => {
    try {
      setIsLoading(true);
      healthCheckupAPI
        .postHealthInfoRequest({
          inquiryInfomationType: 'Date',
          infoInquiryCond: '20250603',
          certifiedToken: '**********',
        })
        .then((res) => {
          setIsLoading(false);
          router.push(
            `${ROUTES.HEALTH_CHECKUP.HEALTH_COMPLETED}?state=302&code=xxxxxxx&logged_in_type=1`,
          );
          //TODO:  wait mycard
          // sendMessageToNative({
          //   type: 'start-other-link',
          //   data: {
          //     url: res.location,
          //   },
          // });
        });
    } catch (error) {
      console.error('Error in handleConsent:', error);
      // TODO: エラーメッセージを表示する
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <TopBar title={APP_TEXT.HEALTH_CHECKUP.TITLE} />
      <div className="flex-1 flex flex-col">
        <div className="px-6 pt-6 pb-6 mb-6">
          <div className="text-base font-bold mb-2">情報の取得について</div>

          <div className="mt-4 text-base text-[#222] leading-[1.5] mb-2">
            特定健診等の情報をマイナポータルから取得します。
            <br />
            ドコモヘルスケアデモは、マイナポータル連携により特定健診等の情報を、株式会社NTTドコモの提供するシステムを利用し、本サービスの運営受託者（NTTコミュニケーションズ株式会社）を介して取得します。取得した情報はアプリの健康診断情報に自動的に反映されます。
          </div>
          <div className="text-base text-[#222] leading-[1.5] mb-2">
            当健康診断の情報は以下の目的で活用します。
          </div>
          <ul className="list-disc pl-5 text-base text-[#222] leading-[1.5] mb-2 space-y-1">
            <li>ご自身の健康診断情報の推移をわかりやすく把握いただくため</li>
            <li>ドコモヘルスケアデモが参加者の健康増進のための分析・施策実施のため</li>
            <li>ドコモヘルスケアデモの事業効果の分析で統計情報として活用するため</li>
          </ul>
          <div className="text-base text-[#222] leading-[1.5] mt-4">
            上記の目的のために取得してもよろしいですか？
          </div>
        </div>
        <div className="px-6 mt-4 w-full flex flex-col gap-4">
          <Button className="w-full h-14 rounded-full" onClick={handleConsent} disabled={isLoading}>
            {isLoading ? '処理中...' : 'はい'}
          </Button>
          <Button
            className="w-full h-14 rounded-full"
            variant="outline"
            onClick={() => router.back()}
          >
            いいえ
          </Button>
        </div>
      </div>
    </div>
  );
}
