'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useHealthExamData } from '@/hooks/use-exam-data';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useHealthCheckupStore } from '@/store/health-checkup';
import type { TestResultItem } from '@/types/health-checkup-input';
import { useEffect, useState } from 'react';
import { Assessment } from '../../_components/assessment';
import { formatDate } from '../../checkup-common';
import BaseChart from '../_components/base-chart';
import BaseTable from '../_components/base-table';

export default function BloodPressurePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setSelectedExamDay } = useHealthCheckupStore();
  const urlExamDay = searchParams.get('examDay');
  const examDay = urlExamDay || '2025-06-11';
  const categories = 'bloodPressure';
  const [selectedIndex, setSelectedIndex] = useState(0);

  const { examData, examGraphData, isLoading, error, refetch } = useHealthExamData({
    examDay,
    categories,
  });

  // ページ読み込み時に、現在のexamDayを選択された受診日として設定
  useEffect(() => {
    if (examDay) {
      setSelectedExamDay(examDay);
    }
  }, [examDay, setSelectedExamDay]);

  return (
    <div>
      <TopBar
        title="血圧"
        enableBack={true}
        enableClose={false}
        onBack={() => {
          router.push('/health-checkup');
        }}
      />
      {/* ページヘッダー */}
      <div className="p-6 flex items-end">
        <div className="flex-1">
          <div className="">受診日</div>
          <div className="text-xl font-bold">{formatDate(examDay)}</div>
        </div>
        <Button
          variant="link"
          className="text-lg font-bold"
          onClick={() => {
            router.push(`/health-checkup/exam-edit?examDay=${examDay}&category=bloodPressure`);
          }}
        >
          編集する
        </Button>
      </div>

      <TableContent
        examData={examData}
        selectedIndex={selectedIndex}
        onSelectedIndexChange={setSelectedIndex}
      />
      <GraphTabContent
        examGraphData={examGraphData}
        selectedIndex={selectedIndex}
        onSelectedIndexChange={setSelectedIndex}
      />
    </div>
  );
}

// 血圧測定項目のマッピングを定義
const bloodPressureItems = {
  bpSystolic: { label: '収縮期血圧', unit: 'mmHg' },
  bpDiastolic: { label: '拡張期血圧', unit: 'mmHg' },
};

// テーブルデータ項目を取得
const getBloodPressureTableItems = (examData: any) => {
  if (!examData) return [];

  return Object.entries(bloodPressureItems).map(([key, { label, unit }]) => {
    const testResultItem = examData[key as keyof typeof examData] as TestResultItem | undefined;
    return {
      key,
      label,
      unit,
      testResultItem,
    };
  });
};

function TableContent({
  examData,
  selectedIndex,
  onSelectedIndexChange,
}: {
  examData: any;
  selectedIndex: number;
  onSelectedIndexChange: (index: number) => void;
}) {
  const items = getBloodPressureTableItems(examData);

  return (
    <BaseTable items={items} selectedIndex={selectedIndex} onRowClick={onSelectedIndexChange} />
  );
}

function GraphTabContent({
  examGraphData,
  selectedIndex,
  onSelectedIndexChange,
}: {
  examGraphData: any;
  selectedIndex: number;
  onSelectedIndexChange: (index: number) => void;
}) {
  const tabItems = Object.keys(bloodPressureItems);

  // タブ切り替えを処理
  const handleTabChange = (value: string) => {
    const index = tabItems.indexOf(value);
    if (index !== -1) {
      onSelectedIndexChange(index);
    }
  };

  // 現在選択されているタブのチャートデータを取得
  const getCurrentGraphData = () => {
    if (!examGraphData) return null;

    const tabKey = tabItems[selectedIndex] as keyof typeof bloodPressureItems;
    return examGraphData[tabKey] || null;
  };

  // 現在選択されているタブの単位を取得
  const getUnit = (): string => {
    const tabKey = tabItems[selectedIndex] as keyof typeof bloodPressureItems;
    return bloodPressureItems[tabKey].unit;
  };

  const graphData = getCurrentGraphData();
  const unit = getUnit();
  const currentTabValue = tabItems[selectedIndex];

  return (
    <div className="bg-card mt-6 ">
      <Tabs value={currentTabValue} onValueChange={handleTabChange} className="w-full">
        <TabsList className="inline-flex h-auto bg-transparent border-b rounded-none p-0 overflow-x-auto w-full justify-start">
          {tabItems.map((key) => (
            <TabsTrigger
              key={key}
              value={key}
              className="!flex-none px-6 py-4 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-primary data-[state=active]:text-white data-[state=active]:font-bold bg-transparent text-foreground hover:bg-muted/50 whitespace-nowrap"
            >
              {bloodPressureItems[key as keyof typeof bloodPressureItems].label}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabItems.map((key) => (
          <TabsContent key={key} value={key} className="p-4 mt-0">
            {/* チャート領域 */}
            <div className="min-h-64 mt-4">
              {graphData?.itemExamData?.length > 0 ? (
                <div>
                  <BaseChart graphData={graphData} units={unit} />
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  データがありません
                </div>
              )}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
