import { Card, CardContent } from '@/components/ui/card';
import type { ChartConfig } from '@/components/ui/chart';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import type { GraphData } from '@/types/health-checkup-input';
import { formatDate } from '@/utils/date-format';
import {
  CartesianGrid,
  Line,
  LineChart,
  ReferenceArea,
  ReferenceLine,
  XAxis,
  YAxis,
} from 'recharts';
import { Assessment } from '../../_components/assessment';

const chartConfig = {
  value: {
    label: 'Value',
    color: 'var(--primary)',
  },
} satisfies ChartConfig;

interface ChartDataItem {
  date: string;
  value: number;
}

const CHART_COMMON_COLORS = {
  A: '#dcfce7',
  B: '#dbeafe',
  C: '#ffedd5',
  D: '#fee2e2',
};

// カスタムX軸目盛りコンポーネント - 2行表示
const CustomXAxisTick = (props: any) => {
  const { x, y, payload } = props;
  if (!payload?.value || payload.value === '') return null; // 空文字列の場合は何も表示しない
  const date = new Date(payload.value);

  // 年と月日を分離
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  return (
    <g transform={`translate(${x},${y})`}>
      {/* 年を上の行に表示 */}
      <text
        x={0}
        y={5}
        textAnchor="middle"
        fill="currentColor"
        fontSize="12"
        className="text-muted-foreground"
      >
        {year}年
      </text>
      {/* 月日を下の行に表示 */}
      <text
        x={0}
        y={20}
        textAnchor="middle"
        fill="currentColor"
        fontSize="12"
        className="text-muted-foreground"
      >
        {month}月{day}日
      </text>
    </g>
  );
};

export default function BaseChart({ graphData, units }: { graphData: GraphData; units: string }) {
  const config: ChartConfig = chartConfig;

  // チャートデータを変換し、開始と終了に空のデータポイントを追加
  const originalData: ChartDataItem[] = graphData.itemExamData.map((item) => ({
    date: item.date,
    value: Number.parseFloat(item.itemValue) || 0,
  }));

  // 開始と終了に空のデータポイントを追加してスペースを作る
  const chartData: ChartDataItem[] = [
    { date: '', value: null as any }, // 開始の空データポイント
    ...originalData,
    { date: '', value: null as any }, // 終了の空データポイント
  ];

  // GraphBaseからチャート設定を取得
  const graphMinValue = graphData.base.graphMinValue
    ? Number.parseFloat(graphData.base.graphMinValue)
    : undefined;
  const graphMaxValue = graphData.base.graphMaxValue
    ? Number.parseFloat(graphData.base.graphMaxValue)
    : undefined;
  const graphStepValue = graphData.base.graphStepValue
    ? Number.parseFloat(graphData.base.graphStepValue)
    : undefined;

  // Y軸のdomainを計算 - 統一して0から開始
  const yDomain: [number | 'auto', number | 'auto'] = [
    0,
    graphMaxValue !== undefined ? graphMaxValue : 'auto',
  ];

  // Y軸の目盛り値を計算 - 0から開始
  const yTicks = (() => {
    if (graphStepValue && graphMaxValue !== undefined) {
      const ticks: number[] = [];
      for (let i = 0; i <= graphMaxValue; i += graphStepValue) {
        ticks.push(i);
      }
      // 最大値を含むことを確保
      if (ticks[ticks.length - 1] !== graphMaxValue) {
        ticks.push(graphMaxValue);
      }
      return ticks;
    }
    return undefined;
  })();

  // すべてのReferenceAreaの境界値を収集し重複を除去
  const getReferenceLines = () => {
    const boundaryValues = new Set<number>();

    // A領域の境界値を収集
    if (graphData.base.A && graphData.base.A.length > 0) {
      for (const range of graphData.base.A) {
        if (range.from && range.from.trim() !== '') {
          const fromValue = Number.parseFloat(range.from);
          if (!Number.isNaN(fromValue) && fromValue !== 0) {
            boundaryValues.add(fromValue);
          }
        }
        if (range.to && range.to.trim() !== '') {
          const toValue = Number.parseFloat(range.to);
          if (!Number.isNaN(toValue) && toValue !== 0) {
            boundaryValues.add(toValue);
          }
        }
      }
    }

    // B領域の境界値を収集
    if (graphData.base.B && graphData.base.B.length > 0) {
      for (const range of graphData.base.B) {
        if (range.from && range.from.trim() !== '') {
          const fromValue = Number.parseFloat(range.from);
          if (!Number.isNaN(fromValue) && fromValue !== 0) {
            boundaryValues.add(fromValue);
          }
        }
        if (range.to && range.to.trim() !== '') {
          const toValue = Number.parseFloat(range.to);
          if (!Number.isNaN(toValue) && toValue !== 0) {
            boundaryValues.add(toValue);
          }
        }
      }
    }

    // C領域の境界値を収集
    if (graphData.base.C && graphData.base.C.length > 0) {
      for (const range of graphData.base.C) {
        if (range.from && range.from.trim() !== '') {
          const fromValue = Number.parseFloat(range.from);
          if (!Number.isNaN(fromValue) && fromValue !== 0) {
            boundaryValues.add(fromValue);
          }
        }
        if (range.to && range.to.trim() !== '') {
          const toValue = Number.parseFloat(range.to);
          if (!Number.isNaN(toValue) && toValue !== 0) {
            boundaryValues.add(toValue);
          }
        }
      }
    }

    // D領域の境界値を収集
    if (graphData.base.D && graphData.base.D.length > 0) {
      for (const range of graphData.base.D) {
        if (range.from && range.from.trim() !== '') {
          const fromValue = Number.parseFloat(range.from);
          if (!Number.isNaN(fromValue) && fromValue !== 0) {
            boundaryValues.add(fromValue);
          }
        }
        if (range.to && range.to.trim() !== '') {
          const toValue = Number.parseFloat(range.to);
          if (!Number.isNaN(toValue) && toValue !== 0) {
            boundaryValues.add(toValue);
          }
        }
      }
    }

    // 配列に変換してソート
    const sortedValues = Array.from(boundaryValues).sort((a, b) => a - b);

    // ReferenceLineコンポーネントを生成
    return sortedValues.map((value, index) => (
      <ReferenceLine
        key={`boundary-${index}`}
        y={value}
        stroke="#ccc"
        strokeWidth={1}
        strokeDasharray="none"
      />
    ));
  };

  // 参照領域を作成
  const renderReferenceAreas = () => {
    const areas: JSX.Element[] = [];

    // A領域を処理
    if (graphData.base.A && graphData.base.A.length > 0) {
      graphData.base.A.forEach((range, index) => {
        // 境界ケースを処理：空文字列はチャートの最小値または最大値を使用することを示す
        const fromValue =
          range.from && range.from.trim() !== '' ? Number.parseFloat(range.from) : 0; // A区域的from为空时，从0开始
        const toValue =
          range.to && range.to.trim() !== ''
            ? Number.parseFloat(range.to)
            : graphMaxValue !== undefined
              ? graphMaxValue
              : 100;

        // 有効な数値範囲があれば領域を追加
        if (!Number.isNaN(fromValue) && !Number.isNaN(toValue) && fromValue !== toValue) {
          areas.push(
            <ReferenceArea
              key={`A-${index}`}
              y1={fromValue}
              y2={toValue}
              fill={CHART_COMMON_COLORS.A}
            />,
          );
        }
      });
    }

    // B領域を処理
    if (graphData.base.B && graphData.base.B.length > 0) {
      graphData.base.B.forEach((range, index) => {
        // 境界ケースを処理：空文字列はチャートの最小値または最大値を使用することを示す
        const fromValue =
          range.from && range.from.trim() !== '' ? Number.parseFloat(range.from) : 0; // B区域的from为空时，从0开始
        const toValue =
          range.to && range.to.trim() !== ''
            ? Number.parseFloat(range.to)
            : graphMaxValue !== undefined
              ? graphMaxValue
              : 100;

        // 有効な数値範囲があれば領域を追加
        if (!Number.isNaN(fromValue) && !Number.isNaN(toValue) && fromValue !== toValue) {
          areas.push(
            <ReferenceArea
              key={`B-${index}`}
              y1={fromValue}
              y2={toValue}
              fill={CHART_COMMON_COLORS.B}
            />,
          );
        }
      });
    }

    // C領域を処理
    if (graphData.base.C && graphData.base.C.length > 0) {
      graphData.base.C.forEach((range, index) => {
        // 境界ケースを処理：空文字列はチャートの最小値または最大値を使用することを示す
        const fromValue =
          range.from && range.from.trim() !== ''
            ? Number.parseFloat(range.from)
            : graphMinValue !== undefined
              ? graphMinValue
              : 0;
        const toValue =
          range.to && range.to.trim() !== ''
            ? Number.parseFloat(range.to)
            : graphMaxValue !== undefined
              ? graphMaxValue
              : 100;

        // 有效な数値範囲があれば領域を追加
        if (!Number.isNaN(fromValue) && !Number.isNaN(toValue) && fromValue !== toValue) {
          areas.push(
            <ReferenceArea
              key={`C-${index}`}
              y1={fromValue}
              y2={toValue}
              fill={CHART_COMMON_COLORS.C}
            />,
          );
        }
      });
    }

    // D領域を処理
    if (graphData.base.D && graphData.base.D.length > 0) {
      graphData.base.D.forEach((range, index) => {
        // 境界ケースを処理：空文字列はチャートの最小値または最大値を使用することを示す
        const fromValue =
          range.from && range.from.trim() !== ''
            ? Number.parseFloat(range.from)
            : graphMinValue !== undefined
              ? graphMinValue
              : 0;
        const toValue =
          range.to && range.to.trim() !== ''
            ? Number.parseFloat(range.to)
            : graphMaxValue !== undefined
              ? graphMaxValue
              : 100;

        // 有効な数値範囲があれば領域を追加
        if (!Number.isNaN(fromValue) && !Number.isNaN(toValue) && fromValue !== toValue) {
          areas.push(
            <ReferenceArea
              key={`D-${index}`}
              y1={fromValue}
              y2={toValue}
              fill={CHART_COMMON_COLORS.D}
            />,
          );
        }
      });
    }

    return areas;
  };

  return (
    <Card className="rounded-lg overflow-hidden ">
      <CardContent className="p-0">
        <ChartContainer config={config} className="h-[400px] w-full">
          <LineChart
            accessibilityLayer
            data={chartData}
            margin={{
              top: 30,
              left: 0,
              right: 12,
              bottom: 10,
            }}
          >
            <CartesianGrid
              strokeDasharray="2 4"
              horizontal={true}
              vertical={false}
              stroke="hsl(var(--muted-foreground))"
            />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={true}
              tickMargin={20}
              height={80}
              tick={<CustomXAxisTick />}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              ticks={yTicks}
              domain={yDomain}
              width={50}
              tickFormatter={(value) => value.toLocaleString()}
              label={{
                value: units ? `(${units})` : '',
                position: 'top',
                offset: 20,
                style: { textAnchor: 'middle' },
              }}
            />

            {renderReferenceAreas()}
            {getReferenceLines()}

            <Line
              dataKey="value"
              type="linear"
              stroke="hsl(var(--primary))"
              strokeWidth={2}
              dot={{
                r: 6,
                fill: 'hsl(var(--primary))',
                stroke: 'hsl(var(--primary))',
                strokeWidth: 0,
                fillOpacity: 1,
              }}
              activeDot={{
                r: 8,
                fill: 'hsl(var(--primary))',
                stroke: 'hsl(var(--primary))',
                strokeWidth: 0,
                fillOpacity: 1,
              }}
              isAnimationActive={true}
              connectNulls={false}
              animationDuration={1000}
            />

            <ChartTooltip
              cursor={false}
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="bg-primary-10 shadow-md p-4 rounded-2xl relative min-w-[180px]">
                      <div className="text-sm font-medium mb-2">
                        {formatDate(new Date(payload[0].payload.date), 'yyyy年M月d日')}
                      </div>
                      <div className="text-2xl font-bold">
                        {payload[0]?.value ? payload[0].value.toLocaleString() : '-'}{' '}
                        <span className="text-sm ml-1">{units}</span>
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
          </LineChart>
        </ChartContainer>
        {/* チャート領域説明 */}
        <div className="flex flex-wrap gap-2 bg-white rounded-lg py-2 p-4">
          {/* A領域 - Aデータが存在する場合のみ表示 */}
          {graphData.base.A && graphData.base.A.length > 0 && (
            <span className="flex items-center">
              <Assessment rank="A" variant="record" />
              <span className="text-gray-700 ml-1 text-sm">異常なし</span>
            </span>
          )}

          {/* B領域 - Bデータが存在する場合のみ表示 */}
          {graphData.base.B && graphData.base.B.length > 0 && (
            <span className="flex items-center">
              <Assessment rank="B" variant="record" />
              <span className="text-gray-700 ml-1 text-sm">軽度異常</span>
            </span>
          )}

          {/* C領域 - Cデータが存在する場合のみ表示 */}
          {graphData.base.C && graphData.base.C.length > 0 && (
            <span className="flex items-center">
              <Assessment rank="C" variant="record" />
              <span className="text-gray-700 ml-1 text-sm">要再検査・生活改善</span>
            </span>
          )}

          {/* D領域 - Dデータが存在する場合のみ表示 */}
          {graphData.base.D && graphData.base.D.length > 0 && (
            <span className="flex items-center">
              <Assessment rank="D" variant="record" />
              <span className="text-gray-700 ml-1 text-sm">要精密検査・治療</span>
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
