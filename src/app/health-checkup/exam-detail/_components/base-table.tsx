import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { TestResultItem } from '@/types/health-checkup-input';
import { Assessment } from '../../_components/assessment';

interface TableItem {
  key: string;
  label: string;
  unit?: string;
  testResultItem: TestResultItem | undefined;
}

interface BaseTableProps {
  items: TableItem[];
  selectedIndex?: number;
  onRowClick?: (index: number) => void;
}

// 基準値をフォーマット
const formatBaseValue = (baseValue: string | null): string => {
  if (!baseValue) return '-';

  // まず単純な文字列として処理を試行（尿検の基準値など）
  if (baseValue.startsWith('(') && baseValue.endsWith(')')) {
    return baseValue;
  }

  // 括弧形式でない場合、JSONとして解析を試行（血液検査などの基準値）
  try {
    const baseObj = JSON.parse(baseValue);

    // 異なる判定レベルを処理
    for (const rank of ['A', 'B', 'C', 'D']) {
      if (baseObj[rank] && Array.isArray(baseObj[rank])) {
        return baseObj[rank]
          .map((range: { from: string; to: string }) => {
            if (range.from && range.to) {
              return `${range.from}～${range.to}`;
            }
            if (range.from) {
              return `${range.from}以上`;
            }
            if (range.to) {
              return `${range.to}以下`;
            }
            return '';
          })
          .join(', ');
      }
    }

    return '-';
  } catch (error) {
    // JSON解析が失敗した場合、元の文字列をそのまま返す
    return baseValue;
  }
};

export default function BaseTable({ items, onRowClick }: BaseTableProps) {
  return (
    <div className="bg-card">
      <Table>
        <TableHeader className="bg-primary/20">
          <TableRow>
            <TableHead className="w-[120px] text-black font-base text-center">検査項目</TableHead>
            <TableHead className="text-black font-base text-center">基準値</TableHead>
            <TableHead className="text-black font-base text-center bg-red-300/10">検査値</TableHead>
            <TableHead className="text-black font-base text-center">判定</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item, index) => (
            <TableRow
              key={item.key}
              className={`cursor-pointer ${index % 2 === 1 ? 'bg-primary/10 hover:bg-primary/10 focus:bg-primary/10' : 'hover:bg-transparent focus:bg-transparent'}`}
              onClick={() => onRowClick?.(index)}
            >
              <TableCell className="text-left pl-8">
                {item.label}
                {item.unit && (
                  <div className="text-xs text-muted-foreground">単位：{item.unit}</div>
                )}
              </TableCell>
              <TableCell className="text-center">
                {formatBaseValue(item.testResultItem?.base || null)}
              </TableCell>
              <TableCell className="bg-red-300/10 text-center">
                {item.testResultItem?.testResult || '-'}
              </TableCell>
              <TableCell>
                <div className="flex justify-center items-center py-2">
                  {item.testResultItem?.judge ? (
                    <Assessment rank={item.testResultItem.judge} variant="record" />
                  ) : (
                    '-'
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <hr />
    </div>
  );
}
