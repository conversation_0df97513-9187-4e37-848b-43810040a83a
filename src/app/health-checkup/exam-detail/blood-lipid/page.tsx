'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useHealthExamData } from '@/hooks/use-exam-data';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useHealthCheckupStore } from '@/store/health-checkup';
import type { TestResultItem } from '@/types/health-checkup-input';
import { useEffect, useState } from 'react';

import { formatDate } from '../../checkup-common';
import BaseChart from '../_components/base-chart';
import BaseTable from '../_components/base-table';

export default function BloodLipidPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setSelectedExamDay } = useHealthCheckupStore();
  const urlExamDay = searchParams.get('examDay');
  const examDay = urlExamDay || '2025-06-11';
  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');
  const categories = 'bloodLipid';
  const [selectedIndex, setSelectedIndex] = useState(0);

  const { examData, examGraphData, isLoading, error, refetch } = useHealthExamData({
    examDay,
    categories,
  });

  // ページ読み込み時に、現在のexamDayを選択された受診日として設定
  useEffect(() => {
    if (examDay) {
      setSelectedExamDay(examDay);
    }
  }, [examDay, setSelectedExamDay]);

  return (
    <div>
      <TopBar
        title="脂質"
        enableBack={true}
        enableClose={false}
        onBack={() => {
          router.push('/health-checkup');
        }}
      />
      {/* ページヘッダー */}
      <div className="p-6 flex items-end">
        <div className="flex-1">
          <div className="">受診日</div>
          <div className="text-xl font-bold">{formatDate(examDay)}</div>
        </div>
        <Button
          variant="link"
          className="text-lg font-bold"
          onClick={() => {
            router.push(`/health-checkup/exam-edit?examDay=${examDay}&category=bloodLipid`);
          }}
        >
          編集する
        </Button>
      </div>

      <TableContent
        examData={examData}
        selectedIndex={selectedIndex}
        onSelectedIndexChange={setSelectedIndex}
      />
      <GraphTabContent
        examGraphData={examGraphData}
        selectedIndex={selectedIndex}
        onSelectedIndexChange={setSelectedIndex}
      />
    </div>
  );
}

// 血脂測定項目のマッピングを定義
const getBloodLipidItems = (isBefore2024: boolean) => {
  let items: Record<string, { label: string; unit: string }> = {
    blTriglyceride: {
      label: isBefore2024 ? '中性脂肪' : '空腹時中性脂肪',
      unit: 'mg/dl',
    },
    blHdl: { label: 'HDLコレステロール', unit: 'mg/dl' },
    blLdl: { label: 'LDLコレステロール', unit: 'mg/dl' },
    blNonHdl: { label: 'Non-HDLコレステロール', unit: 'mg/dl' },
  };

  // 2024年度以後のみ随時中性脂肪を表示
  if (!isBefore2024) {
    items = {
      blTriglyceride: {
        label: isBefore2024 ? '中性脂肪' : '空腹時中性脂肪',
        unit: 'mg/dl',
      },
      blNormalTriglyceride: { label: '随時中性脂肪', unit: 'mg/dl' },
      blHdl: { label: 'HDLコレステロール', unit: 'mg/dl' },
      blLdl: { label: 'LDLコレステロール', unit: 'mg/dl' },
      blNonHdl: { label: 'Non-HDLコレステロール', unit: 'mg/dl' },
    };
  }

  return items;
};

// テーブルデータ項目を取得
const getBloodLipidTableItems = (examData: any, isBefore2024: boolean) => {
  if (!examData) return [];

  const bloodLipidItems = getBloodLipidItems(isBefore2024);
  return Object.entries(bloodLipidItems).map(([key, { label, unit }]) => {
    const testResultItem = examData[key as keyof typeof examData] as TestResultItem | undefined;
    return {
      key,
      label,
      unit,
      testResultItem,
    };
  });
};

function TableContent({
  examData,
  selectedIndex,
  onSelectedIndexChange,
}: {
  examData: any;
  selectedIndex: number;
  onSelectedIndexChange: (index: number) => void;
}) {
  const urlExamDay = new URLSearchParams(window.location.search).get('examDay');
  const examDay = urlExamDay || '2025-06-11';
  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');
  const items = getBloodLipidTableItems(examData, isBefore2024);

  return (
    <BaseTable items={items} selectedIndex={selectedIndex} onRowClick={onSelectedIndexChange} />
  );
}

function GraphTabContent({
  examGraphData,
  selectedIndex,
  onSelectedIndexChange,
}: {
  examGraphData: any;
  selectedIndex: number;
  onSelectedIndexChange: (index: number) => void;
}) {
  const urlExamDay = new URLSearchParams(window.location.search).get('examDay');
  const examDay = urlExamDay || '2025-06-11';
  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');
  const bloodLipidItems = getBloodLipidItems(isBefore2024);
  const tabItems = Object.keys(bloodLipidItems);

  // タブ切り替えを処理
  const handleTabChange = (value: string) => {
    const index = tabItems.indexOf(value);
    if (index !== -1) {
      onSelectedIndexChange(index);
    }
  };

  // 現在選択されているタブのチャートデータを取得
  const getCurrentGraphData = () => {
    if (!examGraphData) return null;

    const tabKey = tabItems[selectedIndex] as keyof typeof bloodLipidItems;
    return examGraphData[tabKey] || null;
  };

  // 現在選択されているタブの単位を取得
  const getUnit = (): string => {
    const tabKey = tabItems[selectedIndex] as keyof typeof bloodLipidItems;
    return bloodLipidItems[tabKey].unit;
  };

  const graphData = getCurrentGraphData();
  const unit = getUnit();
  const currentTabValue = tabItems[selectedIndex];

  return (
    <div className="bg-card mt-6">
      <Tabs value={currentTabValue} onValueChange={handleTabChange} className="w-full">
        <TabsList className="inline-flex h-auto bg-transparent border-b rounded-none p-0 overflow-x-auto w-full justify-start">
          {tabItems.map((key) => (
            <TabsTrigger
              key={key}
              value={key}
              className="!flex-none px-6 py-4 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-primary data-[state=active]:text-white data-[state=active]:font-bold bg-transparent text-foreground hover:bg-muted/50 whitespace-nowrap"
            >
              {bloodLipidItems[key as keyof typeof bloodLipidItems].label}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabItems.map((key) => (
          <TabsContent key={key} value={key} className="p-4 mt-0">
            {/* チャート領域 */}
            <div className="min-h-64 mt-4">
              {graphData?.itemExamData?.length > 0 ? (
                <div>
                  <BaseChart graphData={graphData} units={unit} />
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  データがありません
                </div>
              )}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
