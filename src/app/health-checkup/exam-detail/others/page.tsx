'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { getOthersOptionLabel } from '@/const/health-checkup';
import { useHealthExamData } from '@/hooks/use-exam-data';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { ExamData, InquiryData, OthersData } from '@/types/health-checkup-input';
import { formatDate } from '../../checkup-common';

export default function OthersPage() {
  const categories = 'others,inquiry';
  const router = useRouter();
  const searchParams = useSearchParams();
  const urlExamDay = searchParams.get('examDay');
  const examDay = urlExamDay || '2025-06-11';

  const { examData, inquiryData, isLoading, error } = useHealthExamData({
    examDay,
    categories,
  });

  return (
    <div>
      <TopBar
        title="そのほか"
        enableBack={true}
        enableClose={false}
        onBack={() => {
          router.push('/health-checkup');
        }}
      />
      {/* ページヘッダー */}
      <div className="p-6 flex items-end">
        <div className="flex-1">
          <div className="">受診日</div>
          <div className="text-xl font-bold">{examDay ? formatDate(examDay) : ''}</div>
        </div>
        <Button
          variant="link"
          className="text-lg font-bold"
          onClick={() => {
            router.push(`/health-checkup/exam-edit?examDay=${examDay}&category=others`);
          }}
        >
          編集する
        </Button>
      </div>

      <OthersTableContent examData={examData} />
      <InquiryTableContent inquiryData={inquiryData} />
      {/* その他セクション */}
      <OthersRemarksContent examData={examData} />
    </div>
  );
}

// Othersデータ項目マッピング
const othersItems = [
  { key: 'othersECGComment', label: '心電図検査' },
  { key: 'othersEyegroundComment', label: '眼底検査' },
  { key: 'othersMetabolicRank', label: 'メタボリックシンドローム判定' },
];

// 医師判断とその他項目
const othersSectionItems = [
  { key: 'othersDoctorComment', label: '医師の判断' },
  { key: 'othersUrineUncheckReason', label: '検査未実施の理由' },
  { key: 'othersRemarks', label: 'その他' },
];

// 検査未実施理由をフォーマット
const formatUrineUncheckReason = (value: string | undefined): string => {
  if (!value) return '-';
  return getOthersOptionLabel('othersUrineUncheckReason', value) || value;
};

// 既往歴と症状オプションをフォーマット
const formatYesNoOption = (value: string | undefined): string => {
  if (!value) return '-';
  return getOthersOptionLabel('past', value) || value;
};

function OthersTableContent({ examData }: { examData: ExamData | undefined }) {
  const othersData = examData as OthersData;

  return (
    <div className="bg-card">
      <Table>
        <TableHeader className="bg-primary/20">
          <TableRow>
            <TableHead className=" text-black font-base pl-6">検査項目</TableHead>
            <TableHead className="text-black font-base  pl-6">検査値</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {othersItems.map((item, index) => {
            const value = othersData?.[item.key as keyof OthersData] || '-';
            return (
              <TableRow key={item.key} className={index % 2 === 1 ? 'bg-primary/10' : ''}>
                <TableCell className=" pl-6 py-4">{item.label}</TableCell>
                <TableCell className=" pl-6">{value}</TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
      <hr />

      {/* 医師の判断セクション */}
      <Table>
        <TableHeader className="bg-primary/20">
          <TableRow>
            <TableHead className=" text-black font-base  pl-6">医師の判断</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell className=" pl-6 text-xs py-4">
              {othersData?.othersDoctorComment || '-'}
            </TableCell>
          </TableRow>
          {/* 検査未実施の理由 */}
          <TableRow>
            <TableCell className=" pl-6  py-4">
              <div>検査未実施の理由</div>
              <div className="text-xs mt-1">
                {formatUrineUncheckReason(othersData?.othersUrineUncheckReason || '-')}
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      <hr />

      <hr />
    </div>
  );
}

function InquiryTableContent({ inquiryData }: { inquiryData: InquiryData | undefined }) {
  return (
    <div className=" mt-6">
      {/* 診察項目ヘッダー */}

      <Table className="bg-card">
        <TableHeader className="bg-primary/20">
          <TableRow>
            <TableHead className="w-[100px] pl-6 text-black font-base ">診察項目</TableHead>
            <TableHead className="text-black font-base ">回答</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {/* 既往歴の有無 */}
          <TableRow>
            <TableCell className=" pl-6">既往歴の有無</TableCell>
            <TableCell className="text-sm">{formatYesNoOption(inquiryData?.past) || '-'}</TableCell>
          </TableRow>

          {/* 具体的な既往歴 */}
          <TableRow>
            <TableCell className=" pl-6">具体的な既往歴</TableCell>
            <TableCell className=" text-xs">
              <div className="">{inquiryData?.pastDetail || '-'}</div>
            </TableCell>
          </TableRow>

          {/* 自覚症状の有無 */}
          <TableRow className="bg-primary/10">
            <TableCell className=" pl-6">自覚症状の有無</TableCell>
            <TableCell className="text-sm ">{formatYesNoOption(inquiryData?.symptoms)}</TableCell>
          </TableRow>

          {/* 具体的な自覚症状 */}
          <TableRow className="bg-primary/10">
            <TableCell className=" pl-6">具体的な自覚症状</TableCell>
            <TableCell className=" ">
              <div className="text-xs ">{inquiryData?.symptomsDetail || '-'}</div>
            </TableCell>
          </TableRow>

          {/* 他覚症状の有無 */}
          <TableRow>
            <TableCell className=" pl-6">他覚症状の有無</TableCell>
            <TableCell className="text-sm">{formatYesNoOption(inquiryData?.objective)}</TableCell>
          </TableRow>

          {/* 具体的な他覚症状 */}
          <TableRow>
            <TableCell className=" pl-6">具体的な他覚症状</TableCell>
            <TableCell className=" text-xs">
              <div className="leading-relaxed">{inquiryData?.objectiveDetail || '-'}</div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      <hr />
    </div>
  );
}

function OthersRemarksContent({ examData }: { examData: ExamData | undefined }) {
  const othersRemarks = examData?.othersRemarks;

  return (
    <div className="bg-card mt-4">
      <Table>
        <TableHeader className="bg-primary/20">
          <TableRow>
            <TableHead className=" text-black font-base  pl-6">その他</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell className=" pl-6 text-xs py-4">{othersRemarks || '-'}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
}
