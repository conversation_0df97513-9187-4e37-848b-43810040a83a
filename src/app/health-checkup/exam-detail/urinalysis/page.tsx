'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useHealthExamData } from '@/hooks/use-exam-data';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { ExamData, GraphData, TestResultItem } from '@/types/health-checkup-input';
import { Assessment } from '../../_components/assessment';
import { formatDate } from '../../checkup-common';
import BaseTable from '../_components/base-table';

export default function UrinalysisPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const urlExamDay = searchParams.get('examDay');
  const examDay = urlExamDay || '2025-06-11';
  const categories = 'urinalysis';

  const { examData, examGraphData } = useHealthExamData({
    examDay,
    categories,
  });

  return (
    <div>
      <TopBar
        title="尿"
        enableBack={true}
        enableClose={false}
        onBack={() => {
          router.push('/health-checkup');
        }}
      />
      {/* ページヘッダー */}
      <div className="p-6 flex items-end">
        <div className="flex-1">
          <div className="">受診日</div>
          <div className="text-xl font-bold">{formatDate(examDay)}</div>
        </div>
        <Button
          variant="link"
          className="text-lg font-bold"
          onClick={() => {
            router.push(`/health-checkup/exam-edit?examDay=${examDay}&category=urinalysis`);
          }}
        >
          編集する
        </Button>
      </div>

      <TableContent examData={examData as ExamData} />
      <HistoryTableContent examGraphData={examGraphData as GraphData} />
    </div>
  );
}

// 尿検査項目のマッピングを定義
const urinalysisItems = {
  uaSugar: { label: '尿糖', unit: '' },
  uaProtein: { label: '尿蛋白', unit: '' },
};

// テーブルデータ項目を取得
const getUrinalysisTableItems = (examData: ExamData) => {
  if (!examData) return [];

  return Object.entries(urinalysisItems).map(([key, { label, unit }]) => {
    const testResultItem = examData[key as keyof typeof examData] as TestResultItem | undefined;
    return {
      key,
      label,
      unit,
      testResultItem,
    };
  });
};

function TableContent({ examData }: { examData: ExamData }) {
  const items = getUrinalysisTableItems(examData);

  return <BaseTable items={items} />;
}

// 尿検査値のスタイルを取得
const getUrinalysisValueStyle = (value: string) => {
  if (!value) return 'default';

  // 尿検査値に基づいて対応するレベルスタイルに直接マッピング
  switch (value) {
    case '(-)':
      return 'A'; // 緑色
    case '(±)':
      return 'B'; // 青色
    case '(+)':
      return 'C'; // オレンジ色
    default:
      return 'default'; // グレー色
  }
};

function HistoryTableContent({ examGraphData }: { examGraphData: GraphData }) {
  if (!examGraphData) return null;

  // すべての履歴データの日付を取得
  const allDates = new Set<string>();
  for (const [index] of Object.values(urinalysisItems).entries()) {
    const key = Object.keys(urinalysisItems)[index];
    const graphData = examGraphData[key as keyof typeof examGraphData] as GraphData | undefined;
    if (graphData?.itemExamData) {
      for (const item of graphData.itemExamData) {
        allDates.add(item.date);
      }
    }
  }

  const sortedDates = Array.from(allDates).sort(
    (a, b) => new Date(b).getTime() - new Date(a).getTime(),
  );

  if (sortedDates.length === 0) {
    return (
      <div className="bg-card mt-6 p-6">
        <div className="text-center text-muted-foreground">履歴データがありません</div>
      </div>
    );
  }

  return (
    <div className="bg-card mt-6 ">
      <div className="p-6">
        <Table>
          <TableHeader className="bg-gray-100">
            <TableRow>
              <TableHead className="w-[200px] text-black font-base text-left pl-4">
                受診日
              </TableHead>
              {Object.entries(urinalysisItems).map(([key, { label }]) => (
                <TableHead key={key} className="text-black font-base text-center">
                  {label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedDates.map((date, dateIndex) => {
              // 日付表示をフォーマット
              const formattedDate = new Date(date).toLocaleDateString('ja-JP', {
                year: 'numeric',
                month: 'numeric',
                day: 'numeric',
                weekday: 'short',
              });

              return (
                <TableRow key={date} className={dateIndex % 2 === 1 ? 'bg-primary/10' : ''}>
                  <TableCell className="text-left pl-4 font-medium">{formattedDate}</TableCell>
                  {Object.entries(urinalysisItems).map(([key]) => {
                    const graphData = examGraphData[key as keyof typeof examGraphData] as
                      | GraphData
                      | undefined;
                    const dataItem = graphData?.itemExamData?.find((item) => item.date === date);
                    const value = dataItem?.itemValue || '';
                    const styleRank = getUrinalysisValueStyle(value);

                    return (
                      <TableCell key={key} className="text-center">
                        <div className="flex justify-center items-center py-2">
                          {value ? (
                            <Assessment rank={styleRank} variant="record" label={value} />
                          ) : (
                            '-'
                          )}
                        </div>
                      </TableCell>
                    );
                  })}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        <hr />
      </div>
      {/* 凡例説明 */}
      <div className="p-6 pt-0">
        <div className="flex flex-wrap items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Assessment rank="A" variant="record" />
            <span>異常なし</span>
          </div>
          <div className="flex items-center gap-2">
            <Assessment rank="B" variant="record" />
            <span>軽度異常</span>
          </div>
          <div className="flex items-center gap-2">
            <Assessment rank="C" variant="record" />
            <span>要再検査・生活改善</span>
          </div>
          <div className="flex items-center gap-2">
            <Assessment rank="D" variant="record" />
            <span>要精密検査・治療</span>
          </div>
        </div>
      </div>
    </div>
  );
}
