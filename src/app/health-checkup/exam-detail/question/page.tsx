'use client';
import { TextButton } from '@/components/shared/text-button';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getQuestionOptionLabel } from '@/const/health-checkup';
import { useHealthExamData } from '@/hooks/use-exam-data';

import TopBar from '@/components/layout/top-bar';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { formatDate } from '../../checkup-common';
export default function QuestionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const urlExamDay = searchParams.get('examDay');
  const examDay = urlExamDay || '2025-06-11';

  const { question1Data, question2Data, question3Data, question4Data } = useHealthExamData({
    examDay,
    categories: 'question1,question2,question3,question4',
  });

  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');

  return (
    <div>
      <TopBar
        title="質問票"
        enableBack={true}
        enableClose={false}
        onBack={() => {
          router.push('/health-checkup');
        }}
      />

      {/* ページヘッダー */}
      <div className="px-6 pb-2 pt-8 flex items-end">
        <div className="flex-1">
          <div className="">受診日</div>
          <div className="text-xl font-bold">{examDay ? formatDate(examDay) : ''}</div>
        </div>
        <Button
          variant="link"
          className="text-lg font-bold"
          onClick={() => {
            router.push(`/health-checkup/exam-edit?examDay=${examDay}&category=question`);
          }}
        >
          編集する
        </Button>
      </div>

      <div className="p-6 pt-0 space-y-2">
        {/* 質問1 - a,b,c三つのサブ質問を含む */}
        <QuestionCard
          questionNumber="1"
          title="現在、aからcの薬の使用の有無"
          description={
            <p className="text-xs text-muted-foreground  my-2">
              （医師の判断・治療のもとで服薬中の人を指します。）
            </p>
          }
          subQuestions={[
            { key: 'mPressure', label: '血圧を下げる薬', subNumber: 'a' },
            { key: 'mSugar', label: '血糖を下げる薬又はインスリン注射', subNumber: 'b' },
            { key: 'mFat', label: 'コレステロールや中性脂肪を下げる薬', subNumber: 'c' },
          ]}
          data={question1Data || {}}
        />

        {/* 質問2 */}
        <QuestionCard
          questionNumber="2"
          title="医師から、脳卒中（脳出血、脳梗塞等）にかかっているといわれたり、治療を受けたことがありますか。"
          questionKey="brainDisease"
          data={question1Data || {}}
        />

        {/* 質問3 */}
        <QuestionCard
          questionNumber="3"
          title="医師から、心臓病（狭心症、心筋梗塞等）にかかっているといわれたり、治療を受けたことがありますか。"
          questionKey="heartDisease"
          data={question1Data || {}}
        />

        {/* 質問4 */}
        <QuestionCard
          questionNumber="4"
          title="医師から、慢性腎臓病や腎不全にかかっているといわれたり、治療（人工透析など）を受けていますか。"
          questionKey="kidneyDisease"
          data={question1Data || {}}
        />

        {/* 質問5 */}
        <QuestionCard
          questionNumber="5"
          title="医師から、貧血といわれたことがありますか。"
          questionKey="anemia"
          data={question1Data || {}}
        />

        {/* 質問6 */}
        <QuestionCard
          questionNumber="6"
          title="現在、たばこを習慣的に吸っていますか。"
          description={
            isBefore2024 ? (
              <div className="text-xs text-muted-foreground mb-4 my-2">
                <p>
                  ※「現在、習慣的に喫煙している人」とは、「合計100本以上、又は6ヶ月以上吸っている人。
                </p>
              </div>
            ) : (
              <div className="text-xs text-muted-foreground mb-4 my-2">
                <p>※「現在、習慣的に喫煙している者」とは、条件1と条件2を両方満たす者である。</p>
                <p>条件1：最近1ヶ月間吸っている</p>
                <p>条件2：生涯で6ヶ月以上吸っている、又は合計100本以上吸っている</p>
              </div>
            )
          }
          questionKey={isBefore2024 ? 'smokeBeforFY2024' : 'smokeAfterFY2024'}
          data={question2Data || {}}
        />

        {/* 質問7 */}
        <QuestionCard
          questionNumber="7"
          title="20歳の時の体重から10kg以上増加していますか。"
          questionKey="tenFrom20"
          data={question2Data || {}}
        />

        {/* 質問8 */}
        <QuestionCard
          questionNumber="8"
          title="1回30分以上の軽く汗をかく運動を週2日以上、1年以上実施していますか。"
          questionKey="sweatSport"
          data={question2Data || {}}
        />

        {/* 質問9 */}
        <QuestionCard
          questionNumber="9"
          title="日常生活において歩行又は同等の身体活動を1日1時間以上実施していますか。"
          questionKey="exercise1hour"
          data={question2Data || {}}
        />

        {/* 質問10 */}
        <QuestionCard
          questionNumber="10"
          title="ほぼ同じ年齢の同性と比較して歩く速度が速いですか。"
          questionKey="wsf"
          data={question2Data || {}}
        />

        {/* 質問11 */}
        <QuestionCard
          questionNumber="11"
          title="食事をかんで食べる時の状態はどれにあてはまりますか。"
          questionKey="eatEverything"
          data={question3Data || {}}
        />

        {/* 質問12 */}
        <QuestionCard
          questionNumber="12"
          title="人と比較して食べる速度が速いですか。"
          questionKey="eatSpeed"
          data={question3Data || {}}
        />

        {/* 質問13 */}
        <QuestionCard
          questionNumber="13"
          title="就寝前の2時間以内に夕食をとることが週に3回以上ありますか。"
          questionKey="eatNight3"
          data={question3Data || {}}
        />

        {/* 質問14 */}
        <QuestionCard
          questionNumber="14"
          title="朝昼夕の3食以外に間食や甘い飲み物を摂取していますか。"
          questionKey="eatSugar"
          data={question3Data || {}}
        />

        {/* 質問15 */}
        <QuestionCard
          questionNumber="15"
          title="朝食を抜くことが週に3回以上ありますか。"
          questionKey="noBreakfast3"
          data={question3Data || {}}
        />

        {/* 質問16 */}
        <QuestionCard
          questionNumber="16"
          title="お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度はどのくらいですか。"
          description={
            <p className="text-xs text-muted-foreground my-2">
              {isBefore2024
                ? ''
                : '（※「やめた」とは、過去に月1回以上の習慣的な飲酒歴があった者のうち、最近1年以上酒類を摂取していない者）'}
            </p>
          }
          questionKey={isBefore2024 ? 'wineFreBeforFY2024' : 'wineFreAfterFY2024'}
          data={question4Data || {}}
        />

        {/* 質問17 */}
        <QuestionCard
          questionNumber="17"
          title={
            isBefore2024
              ? '飲酒日の1日当たりの飲酒量はどれくらいですか。'
              : '飲酒日の1日当たりの飲酒量はどれくらいですか。'
          }
          description={
            isBefore2024 ? (
              <div className="text-xs text-muted-foreground mb-4 space-y-1 my-2">
                <p>日本酒1合（180ml）の目安</p>
                <p>ビール500ml</p>
                <p>焼酎25度（110ml）、</p>
                <p>ウイスキーダブル1杯（60ml）、</p>
                <p>ワイン2杯（240ml）</p>
              </div>
            ) : (
              <div className="text-xs text-muted-foreground mb-4 space-y-1 my-2">
                <p>日本酒1合（アルコール度数15度・180ml）の目安：</p>
                <p>ビール（同5度・500ml）、</p>
                <p>焼酎（同25度・約110ml）、</p>
                <p>ワイン（同14度・約180ml）、</p>
                <p>ウイスキー（同43度・60ml）、</p>
                <p>缶チューハイ（同5度・約500ml、同7度・約350ml）</p>
              </div>
            )
          }
          questionKey={isBefore2024 ? 'wineMountBeforFY2024' : 'wineMountAfterFY2024'}
          data={question4Data || {}}
        />

        {/* 質問18 */}
        <QuestionCard
          questionNumber="18"
          title="睡眠で休養が十分とれていますか。"
          questionKey="sleepEnough"
          data={question4Data || {}}
        />

        {/* 質問19 */}
        <QuestionCard
          questionNumber="19"
          title="運動や食生活等の生活習慣を改善してみようと思いますか。"
          questionKey="habitImprove"
          data={question4Data || {}}
        />

        {/* 質問20 */}
        <QuestionCard
          questionNumber="20"
          title={
            isBefore2024
              ? '生活習慣の改善について保健指導を受ける機会があれば、利用しますか。 '
              : '生活習慣の改善について、これまでに特定保健指導を受けたことがありますか。'
          }
          questionKey={isBefore2024 ? 'habitLessonBeforeFY2024' : 'habitLessonAfterFY2024'}
          data={question4Data || {}}
        />
      </div>
    </div>
  );
}

// 汎用質問カードコンポーネント
interface SubQuestion {
  key: string;
  label: string;
  subNumber: string;
}

interface QuestionCardProps {
  questionNumber: string;
  title: string;
  description?: string | React.ReactNode;
  questionKey?: string;
  subQuestions?: SubQuestion[];
  data: any;
}

function QuestionCard({
  questionNumber,
  title,
  description,
  questionKey,
  subQuestions,
  data,
}: QuestionCardProps) {
  return (
    <Card>
      <CardContent className="p-5">
        <div className="flex items-start gap-2 mb-6">
          <div className="font-bold">{questionNumber}.</div>
          <div>
            <div className="text-base">{title}</div>
            <div>
              {description && (
                <div className="text-sm text-muted-foreground mb-6 whitespace-pre-line">
                  {description}
                </div>
              )}
            </div>
            <div>
              {/* サブ質問がある場合（質問1の場合） */}
              {subQuestions && (
                <div className="space-y-4">
                  {subQuestions.map(({ key, label, subNumber }) => {
                    const value = data[key];
                    return (
                      <div key={key}>
                        <div className="flex gap-1">
                          <div className="text-sm font-medium min-w-[1rem] text-right ">
                            {subNumber}.
                          </div>
                          <div className="flex-1">
                            <p className="text-sm mb-2">{label}</p>
                            {value && (
                              <p className="font-bold mt-2">
                                {getQuestionOptionLabel(key as any, value)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* 単一質問の場合 */}
              {questionKey && (
                <div>
                  {data[questionKey] && (
                    <p className="font-bold mt-2">
                      {getQuestionOptionLabel(questionKey as any, data[questionKey])}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
