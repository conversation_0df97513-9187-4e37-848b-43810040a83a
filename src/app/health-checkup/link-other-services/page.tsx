'use client';
import { healthCheckupAPI } from '@/api/modules/health-checkup-alignment';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useAuthStore } from '@/store/auth';
import type {
  ConnectionServiceResponse,
  GetMedicalExamsResponse,
  MedicalExams,
  MycardResponseSendRequest,
  WellcleUploadRequest,
} from '@/types/health-checkup';
import { sendMessageToNative } from '@/utils/native-bridge';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';

export default function LinkOtherServices() {
  const router = useRouter();
  const [infoOpen, setInfoOpen] = useState('開く');
  const [isOpen, setIsOpen] = useState(false);
  const { setIsLoading } = useLoading();
  const handleBack = () => {
    router.back();
  };

  const handleAgree = () => {
    const { user } = useAuthStore.getState();
    healthCheckupAPI.getMedicalExam().then((res) => {
      console.log(res);
      const list: GetMedicalExamsResponse = res;
      const m = list.medicalExams[0];
      const exam = m.examData;
      const question = m.questionData;
      // res 组织request 调用getWellcleUpload

      const request: WellcleUploadRequest = {
        phb: {
          age: '', // ユーザー情報.年齢
          sex: '', // ユーザー情報.男女区分
          dateExam: m.examDay ?? '', // 情報を返 . 検診日
          height: exam.bodyMeasHeight?.toString() ?? '', // 情報を返 . 検査データ. 身長
          weight: exam.bodyMeasWeight?.toString() ?? '', // 情報を返 . 検査データ. 体重
          bmi: exam.bodyMeasBmi?.toString() ?? '', // 情報を返 . 検査データ. BMI
          waist: exam.bodyMeasWaistCircumference?.toString() ?? '', // 情報を返 . 検査データ. 腹囲
          sbp: exam.bpSystolic?.toString() ?? '', // 情報を返 . 検査データ. 収縮期血圧
          dbp: exam.bpDiastolic?.toString() ?? '', // 情報を返 . 検査データ. 拡張期血圧
          fnfat: exam.blTriglyceride?.toString() ?? '', // 情報を返 . 検査データ. 中性脂肪（字段名修正）
          hdl: exam.blHdl?.toString() ?? '', // 情報を返 . 検査データ. HDLコレステロール
          ldlUv: exam.blLdl?.toString() ?? '', // 情報を返 . 検査データ. LDLコレステロール
          nhdl: exam.blNonHdl?.toString() ?? '', // 情報を返 . 検査データ. non - HDLコレステロール
          ast: exam.lfGot?.toString() ?? '', // 情報を返 . 検査データ. GOT（AST）
          alt: exam.lfGtp?.toString() ?? '', // 情報を返 . 検査データ. GPT（ALT）
          ygt: exam.lfyGtp?.toString() ?? '', // 情報を返 . 検査データ. γ - GT（γ - GTP）
          scre: exam.kfSerumCreatinine?.toString() ?? '', // 情報を返 . 検査データ. 血清クレアチニン
          egfr: exam.kfeGFR?.toString() ?? '', // 情報を返 . 検査データ. eGFR
          fbg: exam.bgFbg?.toString() ?? '', // 情報を返 . 検査データ. 空腹時血糖
          cbg: exam.bgCbg?.toString() ?? '', // 情報を返 . 検査データ. 随時血糖
          hba1c: exam.bgHba1c?.toString() ?? '', // 情報を返 . 検査データ. HbA1c
          us: exam.uaSugar?.toString() ?? '', // 情報を返 . 検査データ. 尿糖
          up: exam.uaProtein?.toString() ?? '', // 情報を返 . 検査データ. 尿蛋白
          hct: exam.anHematocrit?.toString() ?? '', // 情報を返 . 検査データ. ヘマトクリット値
          mch: exam.anHb?.toString() ?? '', // 情報を返 . 検査データ. 血色素量（ヘモグロビン値）
          rbc: exam.anRbc?.toString() ?? '', // 情報を返 . 検査データ. 赤血球数
          ecgFindings: exam.othersECGComment ?? '', // 情報を返 . 検査データ. 心電図検査
          funduscopyFindings: exam.othersEyegroundComment ?? '', // 情報を返 . 検査データ. 眼底検査
          shgMetaboLevel: (() => {
            // メタボリックシンドローム判定
            switch (exam.othersMetabolicRank) {
              case '基準該当':
                return '1';
              case '予備群該当':
                return '2';
              case '非該当':
                return '3';
              case '判定不能':
                return '4';
              default:
                return exam.othersMetabolicRank ?? '';
            }
          })(),
          doctorcomment: exam.othersDoctorComment ?? '', // 情報を返 . 検査データ. 医師の診断（判定）
          qsnMedicineBp: question.m_pressure?.toString() ?? '', // 情報を返 . 問診データ. 血圧
          qsnMedicineBg: question.m_sugar?.toString() ?? '', // 情報を返 . 問診データ. 血糖検査
          qsnMedicineFat: question.m_fat?.toString() ?? '', // 情報を返 . 問診データ. 血中脂質検査
          qsnPastCvd: question.brain_disease?.toString() ?? '', // 情報を返 . 問診データ. brain_disease
          qsnPastMace: question.heart_disease?.toString() ?? '', // 情報を返 . 問診データ. heart_disease
          qsnPastPrihd: question.kidney_disease?.toString() ?? '', // 情報を返 . 問診データ. kidney_disease
          qsnAnemia: question.anemia?.toString() ?? '', // 情報を返 . 問診データ. anemia
          qsnSmoking: question.smoke_beforFY2024?.toString() ?? '', // 情報を返 . 問診データ. smoke
          qsnWeightchange: question.ten_from20?.toString() ?? '', // 情報を返 . 問診データ. ten_from20
          qsnExercisehabits: question.sweat_sport?.toString() ?? '', // 情報を返 . 問診データ. sweat_sport
          qsnActivity: question.exercise_1hour?.toString() ?? '', // 情報を返 . 問診データ. exercise_1hour
          qsnWalkingspeed: question.wsf?.toString() ?? '', // 情報を返 . 問診データ. wsf
          qsnChew: question.eat_everything?.toString() ?? '', // 情報を返 . 問診データ. eat_everything
          qsnDietVde: question.eat_speed?.toString() ?? '', // 情報を返 . 問診データ. eat_speed
          qsnDietVds: question.eat_night3?.toString() ?? '', // 情報を返 . 問診データ. eat_night3
          qsnDietZde: question.eat_suger?.toString() ?? '', // 情報を返 . 問診データ. eat_suger
          qsnDietHabit: question.no_breakfast3?.toString() ?? '', // 情報を返 . 問診データ. no_breakfast3
          qsnDrinking: question.wine_fre_afterFY2024?.toString() ?? '', // 情報を返 . 問診データ. wine_fre
          qsnDrinkingAmount: question.wine_mount_afterFY2024?.toString() ?? '', // 情報を返 . 問診データ. wine_mount
          qsnSleep: question.sleep_enough?.toString() ?? '', // 情報を返 . 問診データ. sleep_enough
          qsnLifestyle: question.habit_improve?.toString() ?? '', // 情報を返 . 問診データ. habit_improve
          qsnShg: question.habit_lesson_beforFY2024?.toString() ?? '', // 情報を返 . 問診データ. habit_lesson
        },
        providerUserId: 'provider_001',
      };

      healthCheckupAPI.getWellcleUpload(request).then((res) => {
        console.log(res);
        // 跳转 res.url
        sendMessageToNative({
          type: 'start-other-link',
          data: {
            link: res.url,
            title: '',
            callbackLink: '',
          },
          callback: (data) => {
            console.log('666');
            console.log(data);
          },
        });
      });
    });
  };

  const handleDisagree = () => {
    handleBack();
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <TopBar title="他サービスと連携する" />
      <div className="flex-1 flex flex-col px-6 pt-6 pb-6 mb-6">
        <div className="text-base  mb-10">
          【ドコモヘルスケアデモ】は、下記の事業者へ情報を提供します。
        </div>
        <div className="flex flex-col items-center mb-10">
          <Image
            src="/images/checkup/wellcle.png"
            alt="wellcle logo"
            width={279}
            height={48}
            className="mb-4"
          />
        </div>
        <div className="mb-2 text-sm font-bold">提供先事業者名</div>
        <div className="mb-4 text-base font-normal">株式会社ウェルクル</div>
        <div className="h-[1px] w-full bg-[#B2B2B2] mb-4" />
        <div className="mb-2 text-sm font-bold">サービス名</div>
        <div className="mb-4 text-base font-normal">生活習慣病チェック</div>

        <div className="h-[1px] w-full bg-[#B2B2B2] mb-4" />
        <div className="mb-2 text-sm font-bold">提供情報</div>
        <button
          type="button"
          className="w-full border rounded-lg px-3 py-2 mb-4 text-base flex items-center justify-between"
          onClick={() => setIsOpen((prev) => !prev)}
          aria-expanded={isOpen}
          aria-controls="provide-info-detail"
        >
          {isOpen ? '閉じる' : '開く'}
          <span className={`ml-2 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <polyline
                points="6 9 12 15 18 9"
                stroke="black"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </span>
        </button>
        {isOpen && (
          <div
            id="provide-info-detail"
            className="border border-[#E5E7EB] rounded-lg bg-[#F8F9FB] p-4 mb-4 text-sm text-gray-700"
          >
            <ul className="list-disc pl-5 space-y-1">
              <li>年齢</li>
              <li>性別</li>
              <li>
                健康診断画面に入力または連携された健診結果および問診結果情報（健診受診日や身長・体重等の情報を含む）
              </li>
            </ul>
          </div>
        )}

        <div className="h-[1px] w-full bg-[#B2B2B2] mb-4" />
        <div className="mb-2 text-sm font-bold">提供先における利用目的</div>
        <div className="mb-6 text-base  leading-relaxed">
          けんこうマイレージで取得した健康診断情報をもとに、生活習慣病の進行度チェックおよび動脈硬化性疾患の発症リスク推定をするとともに健康アドバイスを行うため。
        </div>
        <div className="mb-4 text-base ">上記の事項に同意しますか？</div>
        <div className="mb-2 text-sm text-[#666666]">
          ※「同意する」ボタンを押すと外部サービスに移動します。
        </div>
        <Button
          className="w-full h-12 rounded-full mb-3"
          onClick={handleAgree}
          aria-label="同意する"
        >
          同意する
        </Button>
        <Button
          className="w-full h-14 rounded-full"
          onClick={handleDisagree}
          aria-label="同意しない"
          variant="outline"
        >
          同意しない
        </Button>
      </div>
    </div>
  );
}
