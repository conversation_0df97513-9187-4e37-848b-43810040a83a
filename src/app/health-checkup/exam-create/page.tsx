'use client';
import { healthCheckupAPI } from '@/api/modules/health-checkup';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { DateSelect } from '@/components/shared/date-select';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { FORM_STEPS, useHealthCheckupStore } from '@/store/health-checkup';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';

import AnemiaForm from './_components/anemia-form';
import BloodGlucoseForm from './_components/blood-glucose-form';
import BloodLipidForm from './_components/blood-lipid-form';
import BloodPressureForm from './_components/blood-pressure-form';
import BodyMeasureForm from './_components/body-measure-form';
import InquiryForm from './_components/inquiry-form';
import KidneyFunctionForm from './_components/kidney-function-form';
import LiverFunctionForm from './_components/liver-function-form';
import OthersForm from './_components/others-form';
import Question1Form from './_components/question1-form';
import Question2Form from './_components/question2-form';
import Question3Form from './_components/question3-form';
import Question4Form from './_components/question4-form';
import UrinalysisForm from './_components/urinalysis-form';

export default function ExamEditPage() {
  const { selectedExamDay, setSelectedExamDay } = useHealthCheckupStore();
  const [examDaysData, setExamDaysData] = useState<Array<{ examDay: string; autoFlag: number }>>(
    [],
  );
  const { setLoading } = useLoading();
  const router = useRouter();

  const {
    currentStep,
    totalSteps,
    currentCategory,
    examDay,
    formData,
    isLoading,
    error,
    setCurrentStep,
    setExamDay,
    updateFormData,
    reset,
    setError,
    nextStep,
    prevStep,
    submitFormData,
  } = useHealthCheckupStore();

  useEffect(() => {
    const fetchExamDays = async () => {
      try {
        const response = await healthCheckupAPI.getExamDays();
        const examDays = response.examDays;
        console.log(examDays);
        setExamDaysData(examDays);

        // 当天日期
        const today = new Date().toISOString().split('T')[0];

        // 检查当天日期是否已经存在于examDays中
        const todayExists = examDays.some(
          (item: { examDay: string; autoFlag: number }) => item.examDay === today,
        );

        if (todayExists) {
          // 如果当天日期已存在，设置examDay为空字符串
          setExamDay('');
        } else {
          // 如果当天日期不存在，设置为当天日期
          setExamDay(today);
        }
      } catch (error: unknown) {
        if (error instanceof Error) {
          toast.error(error.message);
        }
      }
    };

    fetchExamDays();
  }, [setExamDay]);

  // 現在のステップのデータを取得
  const handleLoadData = async () => {
    if (!examDay || !currentCategory) return;

    try {
      setLoading(true);

      const response = await healthCheckupAPI.getExamResultData({
        categories: currentCategory,
        examDay,
      });

      // 現在のカテゴリに応じて対応するフォームデータを更新
      if (currentCategory === 'bodyMeasure' && response.bodyMeasureData) {
        updateFormData('bodyMeasureData', response.bodyMeasureData);
      } else if (currentCategory === 'bloodPressure' && response.bloodPressureData) {
        updateFormData('bloodPressureData', response.bloodPressureData);
      } else if (currentCategory === 'bloodLipid' && response.bloodLipidData) {
        updateFormData('bloodLipidData', response.bloodLipidData);
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast.error(err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // 現在のステップのデータを保存
  const handleSaveData = async (data: any) => {
    if (!examDay) {
      toast.error('受診日を選択して下さい', {
        position: 'top-center',
      });
      return;
    }

    try {
      await submitFormData(data);

      // 次のステップに移動
      if (currentStep < totalSteps) {
        nextStep();
        // ページを上部にスクロール
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        // selectedExamDayを設定
        setSelectedExamDay(examDay);
        // 全てのステップを完了後、状態をリセット
        reset();
        // 全てのステップを完了
        router.push('/health-checkup/exam-created');
      }
    } catch (err) {
      console.error('Failed to save exam data:', err);
      toast.error('データの保存に失敗しました');
    } finally {
      if (selectedExamDay !== examDay) {
        setSelectedExamDay(examDay);
      }
    }
  };

  // 現在のステップ情報を取得
  const currentStepInfo = FORM_STEPS.find((step) => step.step === currentStep);
  const progressPercentage = (currentStep / totalSteps) * 100;

  // ページ読み込み時にデータを取得
  useEffect(() => {
    handleLoadData();
  }, [currentStep, currentCategory]);

  const handleBackClick = () => {
    if (currentStep > 1) {
      prevStep();
      // ページを上部にスクロール
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      router.back();
    }
  };

  const renderCurrentForm = () => {
    switch (currentCategory) {
      case 'bodyMeasure':
        return <BodyMeasureForm onSubmit={handleSaveData} initialData={formData.bodyMeasureData} />;
      case 'bloodPressure':
        return (
          <BloodPressureForm onSubmit={handleSaveData} initialData={formData.bloodPressureData} />
        );
      case 'bloodLipid':
        return (
          <BloodLipidForm
            onSubmit={handleSaveData}
            initialData={formData.bloodLipidData}
            examDay={examDay}
          />
        );
      case 'liverFunction':
        return (
          <LiverFunctionForm onSubmit={handleSaveData} initialData={formData.liverFunctionData} />
        );
      case 'bloodGlucose':
        return (
          <BloodGlucoseForm onSubmit={handleSaveData} initialData={formData.bloodGlucoseData} />
        );
      case 'urinalysis':
        return <UrinalysisForm onSubmit={handleSaveData} initialData={formData.urinalysisData} />;
      case 'anemia':
        return <AnemiaForm onSubmit={handleSaveData} initialData={formData.anemiaData} />;
      case 'kidneyFunction':
        return (
          <KidneyFunctionForm onSubmit={handleSaveData} initialData={formData.kidneyFunctionData} />
        );
      case 'others':
        return <OthersForm onSubmit={handleSaveData} initialData={formData.othersData} />;
      case 'question1':
        return <Question1Form onSubmit={handleSaveData} initialData={formData.question1Data} />;
      case 'question2':
        return (
          <Question2Form
            onSubmit={handleSaveData}
            initialData={formData.question2Data}
            examDay={examDay}
          />
        );
      case 'question3':
        return <Question3Form onSubmit={handleSaveData} initialData={formData.question3Data} />;
      case 'question4':
        return (
          <Question4Form
            onSubmit={handleSaveData}
            initialData={formData.question4Data}
            examDay={examDay}
          />
        );
      case 'inquiry':
        return <InquiryForm onSubmit={handleSaveData} initialData={formData.inquiryData} />;
      default:
        return <div className="p-6 text-center text-muted-foreground">準備中...</div>;
    }
  };
  const { isShow, setDialog } = useMessageDialog();
  return (
    <div className="min-h-screen bg-card">
      <TopBar
        title="健康診断の追加"
        enableBack={true}
        enableClose={true}
        onBack={handleBackClick}
        onClose={() => {
          setDialog(true, {
            content: (
              <div className="flex flex-col gap-2 text-center">
                <p>途中までの入力データは保存されません。健康診断情報の入力を終了しますか？</p>
              </div>
            ),
            outSideClickClose: false,
            footer: (
              <div className="flex-col ">
                <Button
                  variant="destructive"
                  className="w-full"
                  onClick={() => {
                    router.push('/health-checkup');
                    setDialog(false);
                  }}
                >
                  終了する
                </Button>
                <DialogClose asChild>
                  <TextButton className="mt-2 w-full" variant="muted">
                    入力を続ける
                  </TextButton>
                </DialogClose>
              </div>
            ),
          });
        }}
      />

      {/* Progress */}
      <div className="px-6 py-4 pb-2  bg-card">
        <div className="flex items-center justify-between gap-2">
          <Progress value={progressPercentage} className="h-2" />
          <span className="text-sm text-muted-foreground">
            {currentStep}/{totalSteps}
          </span>
        </div>
      </div>

      {/* Date Picker */}
      {currentStep === 1 && (
        <div className="px-6  bg-card">
          <div className="flex items-center mb-2">
            <span className="text-sm font-medium">受診日</span>
            {/* 必須 */}
            <span className="text-xs  bg-primary/10 text-primary rounded ml-2 p-1 ">必須</span>
          </div>
          <DateSelect
            value={examDay}
            onChange={(date) => setExamDay(date)}
            disabledDates={examDaysData.map((item) => item.examDay)}
            max={new Date()}
          />
        </div>
      )}

      {/* Form Area */}
      <div className="flex-1">
        {error && (
          <div className="mx-6 mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        {renderCurrentForm()}
      </div>
    </div>
  );
}
