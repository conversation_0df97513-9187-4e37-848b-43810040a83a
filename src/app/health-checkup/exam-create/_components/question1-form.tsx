'use client';
import { But<PERSON> } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';

import { DialogClose } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { Question1Data } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 質問票1フォームのスキーマ
const question1Schema = z.object({
  mPressure: z.string().optional(),
  mSugar: z.string().optional(),
  mFat: z.string().optional(),
  brainDisease: z.string().optional(),
  heartDisease: z.string().optional(),
  kidneyDisease: z.string().optional(),
  anemia: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: Question1Data) => Promise<void>;
  initialData?: Question1Data;
}

export default function Question1Form({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof question1Schema>>({
    resolver: zodResolver(question1Schema),
    defaultValues: {
      mPressure: initialData?.mPressure || '',
      mSugar: initialData?.mSugar || '',
      mFat: initialData?.mFat || '',
      brainDisease: initialData?.brainDisease || '',
      heartDisease: initialData?.heartDisease || '',
      kidneyDisease: initialData?.kidneyDisease || '',
      anemia: initialData?.anemia || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof question1Schema>) => {
    await onSubmit(values);
  };

  const { isShow, setDialog } = useMessageDialog();

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* ヘッダー */}
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">
              質問項目<span className="text-sm text-muted-foreground">（1〜5問目 / 全20問）</span>
            </h2>
          </div>

          {/* 1. 現在、aからcの薬の使用の有無 */}
          <div className="space-y-4">
            <div className="mb-4">
              <p className="text-sm mb-2 font-medium flex">
                <span className="mr-2">1.</span>
                <div>
                  <div>現在、aからcの薬の使用の有無</div>
                  <p className="text-xs text-muted-foreground  my-2">
                    （医師の判断・治療のもとで服薬中の人を指します。）
                  </p>
                </div>
              </p>
            </div>

            <div>
              {/* a. 血圧を下げる薬 */}
              <FormField
                control={form.control}
                name="mPressure"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6">a. 血圧を下げる薬</div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="1" id="mPressure1" />
                          <label htmlFor="mPressure1" className="text-sm">
                            はい
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="2" id="mPressure2" />
                          <label htmlFor="mPressure2" className="text-sm">
                            いいえ
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
              <TextButton
                type="button"
                className="p-0 mt-6 text-primary"
                onClick={() => form.setValue('mPressure', '')}
              >
                選択を解除
              </TextButton>
            </div>

            {/* b. 血糖を下げる薬又はインスリン注射 */}
            <div>
              <FormField
                control={form.control}
                name="mSugar"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6">b. 血糖を下げる薬又はインスリン注射</div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="1" id="mSugar1" />
                          <label htmlFor="mSugar1" className="text-sm">
                            はい
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="2" id="mSugar2" />
                          <label htmlFor="mSugar2" className="text-sm">
                            いいえ
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
              <TextButton
                type="button"
                className="p-0 mt-6 text-primary"
                onClick={() => form.setValue('mSugar', '')}
              >
                選択を解除
              </TextButton>
            </div>
            {/* c. コレステロールや中性脂肪を下げる薬 */}
            <div>
              <FormField
                control={form.control}
                name="mFat"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6">c. コレステロールや中性脂肪を下げる薬</div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="1" id="mFat1" />
                          <label htmlFor="mFat1" className="text-sm">
                            はい
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="2" id="mFat2" />
                          <label htmlFor="mFat2" className="text-sm">
                            いいえ
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <TextButton
              type="button"
              className="p-0 text-primary mt-6"
              onClick={() => form.setValue('mFat', '')}
            >
              選択を解除
            </TextButton>
          </div>
          <hr className="my-6" />

          {/* 2. 脳卒中 */}
          <div>
            <FormField
              control={form.control}
              name="brainDisease"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">2.</span>
                    医師から、脳卒中（脳出血、脳梗塞等）にかかっているといわれたり、治療を受けたことがありますか。
                  </div>

                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="brainDisease1" />
                        <label htmlFor="brainDisease1" className="text-sm">
                          はい
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="brainDisease2" />
                        <label htmlFor="brainDisease2" className="text-sm">
                          いいえ
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 text-primary mt-6"
              onClick={() => form.setValue('brainDisease', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 3. 心臓病 */}
          <div>
            <FormField
              control={form.control}
              name="heartDisease"
              render={({ field }) => (
                <FormItem>
                  <div className="mb-2">
                    <div className="text-sm mb-6 font-medium">
                      <span className="mr-2">3.</span>
                      医師から、心臓病（狭心症、心筋梗塞等）にかかっているといわれたり、治療を受けたことがありますか。
                    </div>
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="heartDisease1" />
                        <label htmlFor="heartDisease1" className="text-sm">
                          はい
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="heartDisease2" />
                        <label htmlFor="heartDisease2" className="text-sm">
                          いいえ
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 text-primary mt-6"
              onClick={() => form.setValue('heartDisease', '')}
            >
              選択を解除
            </TextButton>
          </div>
          <hr className="my-6" />

          {/* 4. 慢性腎臓病 */}
          <div>
            <FormField
              control={form.control}
              name="kidneyDisease"
              render={({ field }) => (
                <FormItem>
                  <div className="mb-2">
                    <div className="text-sm mb-6 font-medium">
                      <span className="mr-2">4.</span>
                      医師から、慢性腎臓病や腎不全にかかっているといわれたり、治療（人工透析など）を受けていますか。
                    </div>
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="kidneyDisease1" />
                        <label htmlFor="kidneyDisease1" className="text-sm">
                          はい
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="kidneyDisease2" />
                        <label htmlFor="kidneyDisease2" className="text-sm">
                          いいえ
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 text-primary mt-6"
              onClick={() => form.setValue('kidneyDisease', '')}
            >
              選択を解除
            </TextButton>
          </div>
          <hr className="my-6" />

          {/* 5. 貧血 */}
          <div>
            <FormField
              control={form.control}
              name="anemia"
              render={({ field }) => (
                <FormItem>
                  <div className="mb-2">
                    <div className="text-sm mb-6 font-medium">
                      <span className="mr-2">5.</span>医師から、貧血といわれたことがありますか。
                    </div>
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="anemia1" />
                        <label htmlFor="anemia1" className="text-sm">
                          はい
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="anemia2" />
                        <label htmlFor="anemia2" className="text-sm">
                          いいえ
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 text-primary mt-6"
              onClick={() => form.setValue('anemia', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
