'use client';
import { But<PERSON> } from '@/components/shared/button';
import { NumberInput } from '@/components/shared/number-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { AnemiaData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 貧血フォームのスキーマ
const anemiaSchema = z.object({
  anRbc: z.string().optional(),
  anHb: z.string().optional(),
  anHematocrit: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: AnemiaData) => Promise<void>;
  initialData?: AnemiaData;
}

export default function AnemiaForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof anemiaSchema>>({
    resolver: zodResolver(anemiaSchema),
    defaultValues: {
      anRbc: initialData?.anRbc || '',
      anHb: initialData?.anHb || '',
      anHematocrit: initialData?.anHematocrit || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof anemiaSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">貧血検査</h2>
        <p className="text-xs text-muted-foreground">
          この項目はすべて半角数字で入力してください。
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="anRbc"
            render={({ field }) => (
              <FormItem>
                <FormLabel>赤血球数</FormLabel>
                <FormControl>
                  <NumberInput
                    name="anRbc"
                    unit="万/mm3"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：450万/μL</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="anHb"
            render={({ field }) => (
              <FormItem>
                <FormLabel>血色素量</FormLabel>
                <FormControl>
                  <NumberInput
                    name="anHb"
                    unit="g/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={1}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：14g/dl</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="anHematocrit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>ヘマトクリット</FormLabel>
                <FormControl>
                  <NumberInput
                    name="anHematocrit"
                    unit="%"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={1}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：41%</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4 "
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
