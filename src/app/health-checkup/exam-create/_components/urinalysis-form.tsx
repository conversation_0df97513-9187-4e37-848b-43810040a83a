'use client';
import { But<PERSON> } from '@/components/shared/button';
import { Select } from '@/components/shared/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { UrinalysisData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 尿検査フォームのスキーマ
const urinalysisSchema = z.object({
  uaSugar: z.string().optional(),
  uaProtein: z.string().optional(),
});

// 尿検査の選択肢
const urinalysisOptions = [
  { value: '(-)', name: '(-)' },
  { value: '(±)', name: '(±)' },
  { value: '(+)', name: '(+)' },
  { value: '(2+)', name: '(2+)' },
  { value: '(3+)', name: '(3+)' },
];

interface FormProps {
  onSubmit: (data: UrinalysisData) => Promise<void>;
  initialData?: UrinalysisData;
}

export default function UrinalysisForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof urinalysisSchema>>({
    resolver: zodResolver(urinalysisSchema),
    defaultValues: {
      uaSugar: initialData?.uaSugar || '',
      uaProtein: initialData?.uaProtein || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof urinalysisSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">尿検査</h2>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="uaSugar"
            render={({ field }) => (
              <FormItem>
                <FormLabel>尿糖</FormLabel>
                <FormControl>
                  <Select
                    defaultValue={field.value || ''}
                    options={urinalysisOptions}
                    title="尿糖"
                    onChange={field.onChange}
                    placeholder="選択する"
                    className="flex items-center justify-between h-12 px-4 border border-input rounded-md w-1/2  text-sm"
                    showClearButton
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="uaProtein"
            render={({ field }) => (
              <FormItem>
                <FormLabel>尿蛋白</FormLabel>
                <FormControl>
                  <Select
                    defaultValue={field.value || ''}
                    options={urinalysisOptions}
                    title="尿蛋白"
                    onChange={field.onChange}
                    placeholder="選択する"
                    className="flex items-center justify-between h-12 px-4 border border-input rounded-md w-1/2 text-sm"
                    showClearButton
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
