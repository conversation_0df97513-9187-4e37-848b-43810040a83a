'use client';
import { But<PERSON> } from '@/components/shared/button';
import { NumberInput } from '@/components/shared/number-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { KidneyFunctionData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// 腎機能フォームのスキーマ
const kidneyFunctionSchema = z.object({
  kfSerumCreatinine: z.string().optional(),
  kfeGFR: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: KidneyFunctionData) => Promise<void>;
  initialData?: KidneyFunctionData;
}

export default function KidneyFunctionForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof kidneyFunctionSchema>>({
    resolver: zod<PERSON>esolver(kidneyFunctionSchema),
    defaultValues: {
      kfSerumCreatinine: initialData?.kfSerumCreatinine || '',
      kfeGFR: initialData?.kfeGFR || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof kidneyFunctionSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">血清クレアチニン検査</h2>
        <p className="text-xs text-muted-foreground">
          この項目はすべて半角数字で入力してください。
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="kfSerumCreatinine"
            render={({ field }) => (
              <FormItem>
                <FormLabel>血清クレアチニン</FormLabel>
                <FormControl>
                  <NumberInput
                    name="kfSerumCreatinine"
                    unit="mg/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={2}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：0.75mg/dl</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="kfeGFR"
            render={({ field }) => (
              <FormItem>
                <FormLabel>eGFR</FormLabel>
                <FormControl>
                  <NumberInput
                    name="kfeGFR"
                    unit="ml/min/1.73㎡"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-2/3"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：100ml/min/1.73㎡</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
