'use client';
import { But<PERSON> } from '@/components/shared/button';
import { NumberInput } from '@/components/shared/number-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { LiverFunctionData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// 肝機能フォームのスキーマ
const liverFunctionSchema = z.object({
  lfGot: z.string().optional(),
  lfGpt: z.string().optional(),
  lfyGtp: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: LiverFunctionData) => Promise<void>;
  initialData?: LiverFunctionData;
}

export default function LiverFunctionForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof liverFunctionSchema>>({
    resolver: zodResolver(liverFunctionSchema),
    defaultValues: {
      lfGot: initialData?.lfGot || '',
      lfGpt: initialData?.lfGpt || '',
      lfyGtp: initialData?.lfyGtp || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof liverFunctionSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">肝機能検査</h2>
        <p className="text-xs text-muted-foreground">
          この項目はすべて半角数字で入力してください。
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="lfGot"
            render={({ field }) => (
              <FormItem>
                <FormLabel>AST (GOT)</FormLabel>
                <FormControl>
                  <NumberInput
                    name="lfGot"
                    unit="U/L"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：31U/L</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lfGpt"
            render={({ field }) => (
              <FormItem>
                <FormLabel>ALT (GPT)</FormLabel>
                <FormControl>
                  <NumberInput
                    name="lfGpt"
                    unit="U/L"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：31U/L</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lfyGtp"
            render={({ field }) => (
              <FormItem>
                <FormLabel>γ-GT (γ-GTP)</FormLabel>
                <FormControl>
                  <NumberInput
                    name="lfyGtp"
                    unit="U/L"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：51U/L</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
