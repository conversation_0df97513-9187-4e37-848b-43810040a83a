'use client';
import { Button } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';

import { DialogClose } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { Question3Data } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// 質問票3フォームのスキーマ
const question3Schema = z.object({
  eatEverything: z.string().optional(),
  eatSpeed: z.string().optional(),
  eatNight3: z.string().optional(),
  eatSuger: z.string().optional(),
  noBreakfast3: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: Question3Data) => Promise<void>;
  initialData?: Question3Data;
}

export default function Question3Form({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof question3Schema>>({
    resolver: zodResolver(question3Schema),
    defaultValues: {
      eatEverything: initialData?.eatEverything || '',
      eatSpeed: initialData?.eatSpeed || '',
      eatNight3: initialData?.eatNight3 || '',
      eatSuger: initialData?.eatSuger || '',
      noBreakfast3: initialData?.noBreakfast3 || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof question3Schema>) => {
    await onSubmit(values);
  };

  const { isShow, setDialog } = useMessageDialog();

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* ヘッダー */}
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">
              質問項目<span className="text-sm text-muted-foreground">（11〜15問目 / 全20問）</span>
            </h2>
          </div>

          {/* 11. 食事をかんで食べる時の状態 */}
          <div>
            <FormField
              control={form.control}
              name="eatEverything"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">11.</span>
                    食事をかんで食べる時の状態はどれにあてはまりますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="eatEverything1" />
                        <label htmlFor="eatEverything1" className="text-sm">
                          何でもかんで食べることができる
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="eatEverything2" />
                        <label htmlFor="eatEverything2" className="text-sm">
                          歯や歯ぐき、かみあわせなど気になる部分があり、かみにくいことがある
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="3" id="eatEverything3" />
                        <label htmlFor="eatEverything3" className="text-sm">
                          ほとんどかめない
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('eatEverything', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 12. 人と比較して食べる速度 */}
          <div>
            <FormField
              control={form.control}
              name="eatSpeed"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">12.</span>人と比較して食べる速度が速いですか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="eatSpeed1" />
                        <label htmlFor="eatSpeed1" className="text-sm">
                          速い
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="eatSpeed2" />
                        <label htmlFor="eatSpeed2" className="text-sm">
                          ふつう
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="3" id="eatSpeed3" />
                        <label htmlFor="eatSpeed3" className="text-sm">
                          遅い
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('eatSpeed', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 13. 就寝前の2時間以内に夕食 */}
          <div>
            <FormField
              control={form.control}
              name="eatNight3"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">13.</span>
                    就寝前の2時間以内に夕食をとることが週に3回以上ありますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="eatNight31" />
                        <label htmlFor="eatNight31" className="text-sm">
                          はい
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="eatNight32" />
                        <label htmlFor="eatNight32" className="text-sm">
                          いいえ
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('eatNight3', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 14. 朝昼夕の3食以外に間食や甘い飲み物 */}
          <div>
            <FormField
              control={form.control}
              name="eatSuger"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">14.</span>
                    朝昼夕の3食以外に間食や甘い飲み物を摂取していますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="eatSuger1" />
                        <label htmlFor="eatSuger1" className="text-sm">
                          毎日
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="eatSuger2" />
                        <label htmlFor="eatSuger2" className="text-sm">
                          時々
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="3" id="eatSuger3" />
                        <label htmlFor="eatSuger3" className="text-sm">
                          ほとんど摂取しない
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('eatSuger', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 15. 朝食を抜くことが週に3回以上 */}
          <div>
            <FormField
              control={form.control}
              name="noBreakfast3"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">15.</span>朝食を抜くことが週に3回以上ありますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="noBreakfast31" />
                        <label htmlFor="noBreakfast31" className="text-sm">
                          はい
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="noBreakfast32" />
                        <label htmlFor="noBreakfast32" className="text-sm">
                          いいえ
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('noBreakfast3', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
