'use client';
import { But<PERSON> } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';

import { DialogClose } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { Question4Data } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 質問票4フォームのスキーマ
const question4Schema = z.object({
  wineFreAfterFY2024: z.string().optional(),
  wineFreBeforFY2024: z.string().optional(),
  wineMountAfterFY2024: z.string().optional(),
  wineMountBeforFY2024: z.string().optional(),
  sleepEnough: z.string().optional(),
  habitImprove: z.string().optional(),
  habitLessonAfterFY2024: z.string().optional(),
  habitLessonBeforeFY2024: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: Question4Data) => Promise<void>;
  initialData?: Question4Data;
  examDay: string;
}

export default function Question4Form({ onSubmit, initialData, examDay }: FormProps) {
  const { bottom } = useSafeArea();
  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');
  const form = useForm<z.infer<typeof question4Schema>>({
    resolver: zodResolver(question4Schema),
    defaultValues: {
      wineFreAfterFY2024: initialData?.wineFreAfterFY2024 || '',
      wineFreBeforFY2024: initialData?.wineFreBeforFY2024 || '',
      wineMountAfterFY2024: initialData?.wineMountAfterFY2024 || '',
      wineMountBeforFY2024: initialData?.wineMountBeforFY2024 || '',
      sleepEnough: initialData?.sleepEnough || '',
      habitImprove: initialData?.habitImprove || '',
      habitLessonAfterFY2024: initialData?.habitLessonAfterFY2024 || '',
      habitLessonBeforeFY2024: initialData?.habitLessonBeforeFY2024 || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof question4Schema>) => {
    await onSubmit(values);
  };

  const { isShow, setDialog } = useMessageDialog();

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* ヘッダー */}
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">
              質問項目<span className="text-sm text-muted-foreground">（16〜20問目 / 全20問）</span>
            </h2>
          </div>

          {/* 16. お酒を飲む頻度 */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="wineFreBeforFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">16.</span>
                        <div>
                          <p>
                            お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度はどれくらいですか。
                          </p>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="1" id="wineFreBeforFY20241" />
                          <label htmlFor="wineFreBeforFY20241" className="text-sm">
                            毎日
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="2" id="wineFreBeforFY20242" />
                          <label htmlFor="wineFreBeforFY20242" className="text-sm">
                            時々
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="3" id="wineFreBeforFY20243" />
                          <label htmlFor="wineFreBeforFY20243" className="text-sm">
                            ほとんど飲まない（飲めない）
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="wineFreAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">16.</span>
                        <div>
                          <p>
                            お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度はどのくらいですか。
                          </p>
                          <p className="text-xs text-muted-foreground my-2">
                            （※「やめた」とは、過去に月1回以上の習慣的な飲酒歴があった者のうち、最近1年以上酒類を摂取していない者）
                          </p>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="4" id="wineFreAfterFY20244" />
                          <label htmlFor="wineFreAfterFY20244" className="text-sm">
                            毎日
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="5" id="wineFreAfterFY20245" />
                          <label htmlFor="wineFreAfterFY20245" className="text-sm">
                            週5〜6日
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="6" id="wineFreAfterFY20246" />
                          <label htmlFor="wineFreAfterFY20246" className="text-sm">
                            週3〜4日
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="7" id="wineFreAfterFY20247" />
                          <label htmlFor="wineFreAfterFY20247" className="text-sm">
                            週1〜2日
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="8" id="wineFreAfterFY20248" />
                          <label htmlFor="wineFreAfterFY20248" className="text-sm">
                            月に1〜3日
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="9" id="wineFreAfterFY20249" />
                          <label htmlFor="wineFreAfterFY20249" className="text-sm">
                            月に1日未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="10" id="wineFreAfterFY202410" />
                          <label htmlFor="wineFreAfterFY202410" className="text-sm">
                            やめた
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="11" id="wineFreAfterFY202411" />
                          <label htmlFor="wineFreAfterFY202411" className="text-sm">
                            飲まない（飲めない）
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => {
                if (isBefore2024) {
                  form.setValue('wineFreBeforFY2024', '');
                } else {
                  form.setValue('wineFreAfterFY2024', '');
                }
              }}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 17. 飲酒日の1日当たりの飲酒量 */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="wineMountBeforFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">17.</span>
                        <div>
                          <p>飲酒日の１日当たりの飲酒量はどれくらいですか。</p>
                          <div className="text-xs text-muted-foreground mb-4 space-y-1 my-2">
                            <p>日本酒1合（180ml）の目安：</p>
                            <p>ビール500ml、</p>
                            <p>焼酎25度（110ml）、</p>
                            <p>ウイスキーダブル1杯（60ml）、</p>
                            <p>ワイン2杯（240ml）</p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="1" id="wineMountBeforFY20241" />
                          <label htmlFor="wineMountBeforFY20241" className="text-sm">
                            1合未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="2" id="wineMountBeforFY20242" />
                          <label htmlFor="wineMountBeforFY20242" className="text-sm">
                            1~2合未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="3" id="wineMountBeforFY20243" />
                          <label htmlFor="wineMountBeforFY20243" className="text-sm">
                            2~3合未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="4" id="wineMountBeforFY20244" />
                          <label htmlFor="wineMountBeforFY20244" className="text-sm">
                            3合以上
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="wineMountAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">17.</span>
                        <div>
                          <p>飲酒日の1日当たりの飲酒量はどれくらいですか。</p>
                          <div className="text-xs text-muted-foreground mb-4 space-y-1 my-2">
                            <p>日本酒1合（アルコール度数15度・180ml）の目安：</p>
                            <p>ビール（同5度・500ml）、</p>
                            <p>焼酎（同25度・約110ml）、</p>
                            <p>ワイン（同14度・約180ml）、</p>
                            <p>ウイスキー（同43度・60ml）、</p>
                            <p>缶チューハイ（同5度・約500ml、同7度・約350ml）</p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="5" id="wineMountAfterFY20245" />
                          <label htmlFor="wineMountAfterFY20245" className="text-sm">
                            1合未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="6" id="wineMountAfterFY20246" />
                          <label htmlFor="wineMountAfterFY20246" className="text-sm">
                            1~2合未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="7" id="wineMountAfterFY20247" />
                          <label htmlFor="wineMountAfterFY20247" className="text-sm">
                            2~3合未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="8" id="wineMountAfterFY20248" />
                          <label htmlFor="wineMountAfterFY20248" className="text-sm">
                            3~5合未満
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="9" id="wineMountAfterFY20249" />
                          <label htmlFor="wineMountAfterFY20249" className="text-sm">
                            5合以上
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => {
                if (isBefore2024) {
                  form.setValue('wineMountBeforFY2024', '');
                } else {
                  form.setValue('wineMountAfterFY2024', '');
                }
              }}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 18. 睡眠で休養が十分とれているか */}
          <div>
            <FormField
              control={form.control}
              name="sleepEnough"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">18.</span>睡眠で休養が十分とれていますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="sleepEnough1" />
                        <label htmlFor="sleepEnough1" className="text-sm">
                          はい
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="sleepEnough2" />
                        <label htmlFor="sleepEnough2" className="text-sm">
                          いいえ
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('sleepEnough', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 19. 運動や食生活等の生活習慣を改善してみようと思いますか */}
          <div>
            <FormField
              control={form.control}
              name="habitImprove"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">19.</span>
                    運動や食生活等の生活習慣を改善してみようと思いますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="habitImprove1" />
                        <label htmlFor="habitImprove1" className="text-sm">
                          改善するつもりはない
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="habitImprove2" />
                        <label htmlFor="habitImprove2" className="text-sm">
                          改善するつもりである（概ね6か月以内）
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="3" id="habitImprove3" />
                        <label htmlFor="habitImprove3" className="text-sm">
                          近いうちに（概ね1か月以内）改善するつもりであり、少しずつ始めている
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="4" id="habitImprove4" />
                        <label htmlFor="habitImprove4" className="text-sm">
                          既に改善に取り組んでいる（6か月未満）
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="5" id="habitImprove5" />
                        <label htmlFor="habitImprove5" className="text-sm">
                          既に改善に取り組んでいる（6か月以上）
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('habitImprove', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 20. 生活習慣の改善について */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="habitLessonBeforeFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6 font-medium">
                      <span className="mr-2">20.</span>
                      生活習慣の改善について保健指導を受ける機会があれば、利用しますか。
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="1" id="habitLessonBeforeFY20241" />
                          <label htmlFor="habitLessonBeforeFY20241" className="text-sm">
                            はい
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="2" id="habitLessonBeforeFY20242" />
                          <label htmlFor="habitLessonBeforeFY20242" className="text-sm">
                            いいえ
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="habitLessonAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6 font-medium">
                      <span className="mr-2">20.</span>
                      生活習慣の改善について、これまでに特定保健指導を受けたことがありますか。
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="1" id="habitLessonAfterFY20241" />
                          <label htmlFor="habitLessonAfterFY20241" className="text-sm">
                            はい
                          </label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="2" id="habitLessonAfterFY20242" />
                          <label htmlFor="habitLessonAfterFY20242" className="text-sm">
                            いいえ
                          </label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => {
                if (isBefore2024) {
                  form.setValue('habitLessonBeforeFY2024', '');
                } else {
                  form.setValue('habitLessonAfterFY2024', '');
                }
              }}
            >
              選択を解除
            </TextButton>
          </div>
          <div className="  mt-6 rounded-md  ">
            <TextButton
              type="button"
              className="text-primary"
              onClick={() => {
                setDialog(true, {
                  title: '利用規約',
                  outSideClickClose: true,
                  content: 'メッセージ',
                });
              }}
            >
              利用規約
            </TextButton>
            に基づき取り扱うことに同意して保存します。
          </div>

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '保存して診察入力へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
