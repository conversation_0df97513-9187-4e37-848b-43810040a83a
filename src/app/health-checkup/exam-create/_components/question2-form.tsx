'use client';
import { <PERSON><PERSON> } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';

import { DialogClose } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  smokingAfterFY2024Options,
  smokingBeforeFY2024Options,
  yesNoOptions,
} from '@/const/health-checkup';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { Question2Data } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// 質問票2フォームのスキーマ
const question2Schema = z.object({
  smokeAfterFY2024: z.string().optional(),
  smokeBeforFY2024: z.string().optional(),
  tenFrom20: z.string().optional(),
  sweatSport: z.string().optional(),
  exercise1hour: z.string().optional(),
  wsf: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: Question2Data) => Promise<void>;
  initialData?: Question2Data;
  examDay: string;
}

export default function Question2Form({ onSubmit, initialData, examDay }: FormProps) {
  const { bottom } = useSafeArea();
  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');
  const form = useForm<z.infer<typeof question2Schema>>({
    resolver: zodResolver(question2Schema),
    defaultValues: {
      smokeAfterFY2024: initialData?.smokeAfterFY2024 || '',
      smokeBeforFY2024: initialData?.smokeBeforFY2024 || '',
      tenFrom20: initialData?.tenFrom20 || '',
      sweatSport: initialData?.sweatSport || '',
      exercise1hour: initialData?.exercise1hour || '',
      wsf: initialData?.wsf || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof question2Schema>) => {
    await onSubmit(values);
  };

  const { isShow, setDialog } = useMessageDialog();

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* ヘッダー */}
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">
              質問項目<span className="text-sm text-muted-foreground">（6〜10問目 / 全20問）</span>
            </h2>
          </div>

          {/* 6. たばこを習慣的に吸っているか */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="smokeBeforFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="mb-2 text-sm font-medium flex">
                        <span className="mr-2">6.</span>
                        <div>
                          <div>現在、たばこを習慣的に吸っていますか。</div>
                          <div className="text-xs text-muted-foreground mb-4 my-2">
                            <p>
                              （※「現在、習慣的に喫煙している人」とは、「合計100本以上、又は6ヶ月以上吸っている人」
                            </p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        {smokingBeforeFY2024Options.map((option) => (
                          <div key={option.value} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={option.value.toString()}
                              id={`smokeBeforFY2024${option.value}`}
                            />
                            <label htmlFor={`smokeBeforFY2024${option.value}`} className="text-sm">
                              {option.label}
                            </label>
                          </div>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="smokeAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="mb-2 text-sm font-medium flex">
                        <span className="mr-2">6.</span>
                        <div>
                          <div>現在、たばこを習慣的に吸っていますか。</div>
                          <div className="text-xs text-muted-foreground mb-4 my-2">
                            <p>
                              ※「現在、習慣的に喫煙している者」とは、条件1と条件2を両方満たす者である。
                            </p>
                            <p>条件1：最近1ヶ月間吸っている</p>
                            <p>条件2：生涯で6ヶ月以上吸っている、又は合計100本以上吸っている</p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="mt-2 flex flex-col gap-6"
                      >
                        {smokingAfterFY2024Options.map((option) => (
                          <div key={option.value} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={option.value.toString()}
                              id={`smokeAfterFY2024${option.value}`}
                            />
                            <label htmlFor={`smokeAfterFY2024${option.value}`} className="text-sm">
                              {option.label}
                            </label>
                          </div>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => {
                if (isBefore2024) {
                  form.setValue('smokeBeforFY2024', '');
                } else {
                  form.setValue('smokeAfterFY2024', '');
                }
              }}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 7. 20歳の時の体重から10kg以上増加 */}
          <div>
            <FormField
              control={form.control}
              name="tenFrom20"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">7.</span>20歳の時の体重から10kg以上増加していますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      {yesNoOptions.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={option.value.toString()}
                            id={`tenFrom20${option.value}`}
                          />
                          <label htmlFor={`tenFrom20${option.value}`} className="text-sm">
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('tenFrom20', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 8. 1回30分以上の軽く汗をかく運動 */}
          <div>
            <FormField
              control={form.control}
              name="sweatSport"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">8.</span>
                    1回30分以上の軽く汗をかく運動を週2日以上、1年以上実施していますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      {yesNoOptions.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={option.value.toString()}
                            id={`sweatSport${option.value}`}
                          />
                          <label htmlFor={`sweatSport${option.value}`} className="text-sm">
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('sweatSport', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 9. 日常生活において歩行又は同等の身体活動 */}
          <div>
            <FormField
              control={form.control}
              name="exercise1hour"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">9.</span>
                    日常生活において歩行又は同等の身体活動を1日1時間以上実施していますか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      {yesNoOptions.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={option.value.toString()}
                            id={`exercise1hour${option.value}`}
                          />
                          <label htmlFor={`exercise1hour${option.value}`} className="text-sm">
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('exercise1hour', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 10. ほぼ同じ年齢の同性と比較して歩く速度 */}
          <div>
            <FormField
              control={form.control}
              name="wsf"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">10.</span>
                    ほぼ同じ年齢の同性と比較して歩く速度が速いですか。
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      {yesNoOptions.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={option.value.toString()}
                            id={`wsf${option.value}`}
                          />
                          <label htmlFor={`wsf${option.value}`} className="text-sm">
                            {option.label}
                          </label>
                        </div>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('wsf', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
