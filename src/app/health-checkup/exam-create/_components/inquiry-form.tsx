'use client';
import { But<PERSON> } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';

import { DialogClose } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { InquiryData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 診察フォームのスキーマ
const inquirySchema = z.object({
  past: z.string().optional(),
  pastDetail: z.string().optional(),
  symptoms: z.string().optional(),
  symptomsDetail: z.string().optional(),
  objective: z.string().optional(),
  objectiveDetail: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: InquiryData) => Promise<void>;
  initialData?: InquiryData;
}

export default function InquiryForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof inquirySchema>>({
    resolver: zodResolver(inquirySchema),
    defaultValues: {
      past: initialData?.past || '',
      pastDetail: initialData?.pastDetail || '',
      symptoms: initialData?.symptoms || '',
      symptomsDetail: initialData?.symptomsDetail || '',
      objective: initialData?.objective || '',
      objectiveDetail: initialData?.objectiveDetail || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof inquirySchema>) => {
    await onSubmit(values);
  };

  const { isShow, setDialog } = useMessageDialog();

  // Watch radio values to control textarea disabled state
  const pastValue = form.watch('past');
  const symptomsValue = form.watch('symptoms');
  const objectiveValue = form.watch('objective');

  // Clear textarea when radio is switched to "特記事項なし"
  useEffect(() => {
    if (pastValue === '2') {
      form.setValue('pastDetail', '');
    }
  }, [pastValue, form]);

  useEffect(() => {
    if (symptomsValue === '2') {
      form.setValue('symptomsDetail', '');
    }
  }, [symptomsValue, form]);

  useEffect(() => {
    if (objectiveValue === '2') {
      form.setValue('objectiveDetail', '');
    }
  }, [objectiveValue, form]);

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* ヘッダー */}
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">診察項目</h2>
          </div>

          {/* 既往歴の有無 */}
          <div>
            <FormField
              control={form.control}
              name="past"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">既往歴の有無</div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="past1" />
                        <label htmlFor="past1" className="text-sm">
                          特記事項あり
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="past2" />
                        <label htmlFor="past2" className="text-sm">
                          特記事項なし
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('past', '')}
            >
              選択を解除
            </TextButton>
          </div>

          {/* 具体的な既往歴 */}
          <div>
            <FormField
              control={form.control}
              name="pastDetail"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-4 font-medium">具体的な既往歴【全角/半角】</div>
                  <FormControl>
                    <Textarea
                      maxLength={256}
                      {...field}
                      disabled={pastValue !== '1'}
                      placeholder=""
                      className="min-h-[120px] resize-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 自覚症状の有無 */}
          <div>
            <FormField
              control={form.control}
              name="symptoms"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">自覚症状の有無</div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="symptoms1" />
                        <label htmlFor="symptoms1" className="text-sm">
                          特記事項あり
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="symptoms2" />
                        <label htmlFor="symptoms2" className="text-sm">
                          特記事項なし
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('symptoms', '')}
            >
              選択を解除
            </TextButton>
          </div>

          {/* 具体的な自覚症状 */}
          <div>
            <FormField
              control={form.control}
              name="symptomsDetail"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-4 font-medium">具体的な自覚症状【全角/半角】</div>
                  <FormControl>
                    <Textarea
                      {...field}
                      maxLength={256}
                      disabled={symptomsValue !== '1'}
                      placeholder=""
                      className="min-h-[120px] resize-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 他覚症状の有無 */}
          <div>
            <FormField
              control={form.control}
              name="objective"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">他覚症状の有無</div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="objective1" />
                        <label htmlFor="objective1" className="text-sm">
                          特記事項あり
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="objective2" />
                        <label htmlFor="objective2" className="text-sm">
                          特記事項なし
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('objective', '')}
            >
              選択を解除
            </TextButton>
          </div>

          {/* 具体的な他覚症状 */}
          <div>
            <FormField
              control={form.control}
              name="objectiveDetail"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-4 font-medium">具体的な他覚症状【全角/半角】</div>
                  <FormControl>
                    <Textarea
                      {...field}
                      maxLength={256}
                      disabled={objectiveValue !== '1'}
                      placeholder=""
                      className="min-h-[120px] resize-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-6 rounded-md">
            <TextButton
              type="button"
              className="text-primary"
              onClick={() => {
                setDialog(true, {
                  title: '利用規約',
                  outSideClickClose: true,
                  content: 'メッセージ',
                });
              }}
            >
              利用規約
            </TextButton>
            に基づき取り扱うことに同意して保存します。
          </div>

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '保存'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
