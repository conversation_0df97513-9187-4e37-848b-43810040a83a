'use client';
import { Button } from '@/components/shared/button';
import { NumberInput } from '@/components/shared/number-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { BodyMeasureData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 身体測定フォームのスキーマ
const bodyMeasureSchema = z.object({
  bodyMeasHeight: z.string().optional(),
  bodyMeasWeight: z.string().optional(),
  bodyMeasWaistCircumference: z.string().optional(),
  bodyMeasBmi: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: BodyMeasureData) => Promise<void>;
  initialData?: BodyMeasureData;
}

export default function BodyMeasureForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof bodyMeasureSchema>>({
    resolver: zodResolver(bodyMeasureSchema),
    defaultValues: {
      bodyMeasHeight: initialData?.bodyMeasHeight || '',
      bodyMeasWeight: initialData?.bodyMeasWeight || '',
      bodyMeasWaistCircumference: initialData?.bodyMeasWaistCircumference || '',
      bodyMeasBmi: initialData?.bodyMeasBmi || '',
    },
  });

  const handleSubmit = async (values: z.infer<typeof bodyMeasureSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <hr className="mb-4" />
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">身体測定</h2>
            <p className="text-xs text-muted-foreground">
              この項目はすべて半角数字で入力してください。
            </p>
          </div>
          <FormField
            control={form.control}
            name="bodyMeasHeight"
            render={({ field }) => (
              <FormItem>
                <FormLabel>身長</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bodyMeasHeight"
                    unit="cm"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={1}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：165.0cm</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bodyMeasWeight"
            render={({ field }) => (
              <FormItem>
                <FormLabel>体重</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bodyMeasWeight"
                    unit="kg"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={1}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：59.9kg</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bodyMeasWaistCircumference"
            render={({ field }) => (
              <FormItem>
                <FormLabel>腹囲</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bodyMeasWaistCircumference"
                    unit="cm"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={1}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：80.0cm</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bodyMeasBmi"
            render={({ field }) => (
              <FormItem>
                <FormLabel>BMI</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bodyMeasBmi"
                    unit=""
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={1}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：22.0</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : '次へ'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
