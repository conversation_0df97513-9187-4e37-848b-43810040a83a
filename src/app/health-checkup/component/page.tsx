'use client';
import { But<PERSON> } from '@/components/shared/button';
import { DatePicker } from '@/components/shared/datetime-picker';
import { NumberInput } from '@/components/shared/number-input';
import { Select } from '@/components/shared/select';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Pencil, Trash } from 'lucide-react';
import { useState } from 'react';
import {
  Assessment,
  AssessmentButtonGroup,
  AssessmentRecordGroup,
} from '../_components/assessment';

const SELECT_OPTIONS = [
  { value: 'all', name: 'すべて' },
  { value: 'score', name: 'ヘルスチェックAI' },
  { value: 'risk', name: 'リスクスコア' },
];

export default function ComponentPage() {
  const [price, setPrice] = useState('1000');

  const [selectedOption, setSelectedOption] = useState('all');
  const handleOptionChange = (value: string) => {
    setSelectedOption(value);
  };
  return (
    <div className="p-4 flex flex-col gap-4">
      <Card>
        <CardTitle className="p-4">Icons</CardTitle>
        <CardContent>
          <div>
            <img src="/images/health-checkup/A.svg" alt="" />
            <img src="/images/health-checkup/B.svg" alt="" />
            <img src="/images/health-checkup/C.svg" alt="" />
            <img src="/images/health-checkup/D.svg" alt="" />
            <img src="/images/health-checkup/icon-mynumber.svg" alt="" />
            <Pencil className="text-primary" />
            <Trash className="text-primary" />

            <img src="/images/health-checkup/logo-ebhs.svg" alt="" />
            <img src="/images/health-checkup/logo-wellcle.svg" alt="" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardTitle className="p-4">Large Button</CardTitle>
        <CardContent>
          <div className="mb-4">
            <Button type="submit" className="w-full">
              はい
            </Button>
          </div>
          <div>
            <Button variant="outline" className="w-full">
              いいえ
            </Button>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardTitle className="p-4">Number Input</CardTitle>
        <CardContent>
          {/* Price Label */}
          <label htmlFor="price">価格</label>
          <NumberInput
            unit="円"
            name="price"
            maxDecLen={0}
            maxIntLen={6}
            value={price}
            onChange={setPrice}
          />
          <div className="text-sm text-gray-400 mt-2">例:1000</div>
        </CardContent>
      </Card>
      <Card>
        <CardTitle className="p-4">Select</CardTitle>
        <CardContent>
          <Select
            className="w-[240px] flex-none"
            defaultValue={selectedOption}
            options={SELECT_OPTIONS}
            title="表示するヘルスチェックAI"
            onChange={(value) => handleOptionChange(value)}
          />
        </CardContent>
      </Card>
      <Card>
        <CardTitle className="p-4">Textarea</CardTitle>
        <CardContent>
          <label htmlFor="textarea">説明</label>
          <Textarea />
          <div className="text-sm text-gray-400 mt-2">例: 1000</div>

          <hr className="my-4" />

          <label htmlFor="textarea">説明</label>
          <Textarea />
          <div className="text-sm text-gray-400 mt-2">例: 1000</div>
        </CardContent>
      </Card>
      {/* Date Picker */}
      <Card>
        <CardTitle className="p-4">DatePicker</CardTitle>
        <CardContent>
          <DatePicker />
        </CardContent>
      </Card>
      {/* Assessment */}
      <Card>
        <CardTitle className="p-4">Assessment</CardTitle>
        <CardContent className="flex flex-col gap-6">
          <div>
            <h3 className="font-bold mb-2">按钮组</h3>
            <AssessmentButtonGroup />
          </div>

          <div>
            <h3 className="font-bold mb-2">记录组</h3>
            <AssessmentRecordGroup />
          </div>

          <div>
            <h3 className="font-bold mb-2">单个等级（按钮模式）</h3>
            <div className="grid grid-cols-5 gap-2">
              <Assessment rank="A" />
              <Assessment rank="B" />
              <Assessment rank="C" />
              <Assessment rank="D" />
              <Assessment rank="其他值" />
            </div>
          </div>

          <div>
            <h3 className="font-bold mb-2">单个等级（记录模式）</h3>
            <div className="flex gap-2">
              <Assessment rank="A" variant="record" />
              <Assessment rank="B" variant="record" />
              <Assessment rank="C" variant="record" />
              <Assessment rank="D" variant="record" />
              <Assessment rank="其他值" variant="record" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
