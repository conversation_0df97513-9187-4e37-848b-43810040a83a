'use client';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { Badge } from '@/components/ui/badge';
import { useRouter } from '@/hooks/use-next-navigation';
import LinkCard from '../_components/link-card';
export default function ExamCreatedPage() {
  const router = useRouter();
  return (
    <div>
      <TopBar
        title="健康診断"
        enableClose={true}
        onClose={() => {
          router.push('/health-checkup');
        }}
      />

      <div className="p-8 pb-24 flex flex-col gap-4">
        <div>
          <p className="font-bold text-lg">健康診断情報の入力が完了しました。</p>
          <p className="my-2">健康診断情報を外部サービスに連携することができます。</p>
        </div>
        {/* <LinkCard
          direction="col"
          onClick={() => router.push('/health-checkup/services/ebhs-gateway')}
        >
          <h3 className="text-primary font-bold text-lg text-center">EBHS Life</h3>
          <p className="text-primary text-center">寿命予測とヘルススコアを確認する</p>
          <Badge variant="secondary" className="text-primary font-medium">
            エムスリー(株)
          </Badge>
        </LinkCard>
        <LinkCard
          direction="col"
          onClick={() => router.push('/health-checkup/services/wellcle-gateway')}
        >
          <h3 className="text-primary font-bold text-lg text-center">生活習慣病チェック</h3>
          <p className="text-primary text-center">生活習慣病の進行度をチェックする</p>
          <Badge variant="secondary" className="text-primary font-medium">
            (株)ウェルクル
          </Badge>
        </LinkCard>
        <LinkCard direction="col" onClick={() => router.push('/health-checkup/health-score')}>
          <h3 className="text-primary font-bold text-lg text-center">ヘルスチェックAI</h3>
          <h3 className="text-primary font-bold text-lg text-center">血糖値・中性脂肪改善習慣</h3>
          <p className="text-primary text-center">あなた専用の改善ミッションに挑戦する</p>
        </LinkCard> */}

        <div className="fixed bottom-0 left-0 right-0  p-6 pb-safe">
          <Button
            onClick={() => router.push('/health-checkup')}
            type="button"
            className="w-full h-12 text-base font-medium"
          >
            健康診断情報画面に戻る
          </Button>
        </div>
      </div>
    </div>
  );
}
