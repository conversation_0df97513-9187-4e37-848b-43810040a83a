'use client';
import TopBar from '@/components/layout/top-bar';
import { But<PERSON> } from '@/components/shared/button';
import {
  useExamResultData,
  useHealthExamData,
  useUpdateHealthExamResult,
} from '@/hooks/use-exam-data';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useHealthCheckupStore } from '@/store/health-checkup';
import type {
  AnemiaData,
  BloodGlucoseData,
  BloodLipidData,
  BloodPressureData,
  BodyMeasureData,
  InquiryData,
  KidneyFunctionData,
  LiverFunctionData,
  OthersData,
  Question1Data,
  Question2Data,
  Question3Data,
  Question4Data,
  UrinalysisData,
} from '@/types/health-checkup-input';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import AnemiaForm from './_components/anemia-form';
import BloodGlucoseForm from './_components/blood-glucose-form';
import BloodLipidForm from './_components/blood-lipid-form';
import BloodPressureForm from './_components/blood-pressure-form';
import BodyMeasureForm from './_components/body-measure-form';
import KidneyFunctionForm from './_components/kidney-function-form';
import LiverFunctionForm from './_components/liver-function-form';
import OthersForm from './_components/others-form';
import QuestionForm from './_components/question-form';
import UrinalysisForm from './_components/urinalysis-form';

export default function ExamEditPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setSelectedExamDay } = useHealthCheckupStore();
  const examDay = searchParams.get('examDay') || '';
  const category = searchParams.get('category') || '';

  // ページ読み込み時に、現在のexamDayを選択された受診日として設定
  useEffect(() => {
    if (examDay) {
      setSelectedExamDay(examDay);
    }
  }, [examDay, setSelectedExamDay]);

  const [initialBodyMeasureData, setInitialBodyMeasureData] = useState<
    BodyMeasureData | undefined
  >();
  const [initialBloodPressureData, setInitialBloodPressureData] = useState<
    BloodPressureData | undefined
  >();
  const [initialBloodLipidData, setInitialBloodLipidData] = useState<BloodLipidData | undefined>();
  const [initialBloodGlucoseData, setInitialBloodGlucoseData] = useState<
    BloodGlucoseData | undefined
  >();
  const [initialUrinalysisData, setInitialUrinalysisData] = useState<UrinalysisData | undefined>();
  const [initialAnemiaData, setInitialAnemiaData] = useState<AnemiaData | undefined>();
  const [initialKidneyFunctionData, setInitialKidneyFunctionData] = useState<
    KidneyFunctionData | undefined
  >();
  const [initialLiverFunctionData, setInitialLiverFunctionData] = useState<
    LiverFunctionData | undefined
  >();
  const [initialOthersData, setInitialOthersData] = useState<
    (OthersData & InquiryData) | undefined
  >();
  const [initialQuestionData, setInitialQuestionData] = useState<
    (Question1Data & Question2Data & Question3Data & Question4Data) | undefined
  >();
  const { getExamResultData } = useExamResultData();
  const { updateHealthExamResultData } = useUpdateHealthExamResult();

  // othersカテゴリ用にuseHealthExamDataでデータを取得
  const { examData: othersExamData, inquiryData: othersInquiryData } = useHealthExamData({
    examDay: examDay || '',
    categories: 'others,inquiry',
  });

  // questionカテゴリ用にuseHealthExamDataでデータを取得
  const {
    question1Data: questionExamData1,
    question2Data: questionExamData2,
    question3Data: questionExamData3,
    question4Data: questionExamData4,
  } = useHealthExamData({
    examDay: examDay || '',
    categories: 'question1,question2,question3,question4',
  });

  // ページ読み込み時に現在のデータを取得
  useEffect(() => {
    const loadData = async () => {
      if (!examDay || !category) return;

      // othersカテゴリの場合、useHealthExamDataのデータを使用
      if (category === 'others') {
        if (othersExamData || othersInquiryData) {
          const combinedData = {
            ...othersExamData,
            ...othersInquiryData,
          };
          setInitialOthersData(combinedData);
        }
        return;
      }

      // questionカテゴリの場合、useHealthExamDataのデータを使用
      if (category === 'question') {
        if (questionExamData1 || questionExamData2 || questionExamData3 || questionExamData4) {
          const combinedQuestionData = {
            ...questionExamData1,
            ...questionExamData2,
            ...questionExamData3,
            ...questionExamData4,
          };
          setInitialQuestionData(combinedQuestionData);
        }
        return;
      }

      try {
        const response = await getExamResultData({
          categories: category,
          examDay,
        });

        if (response && category === 'bodyMeasure' && response.bodyMeasureData) {
          setInitialBodyMeasureData(response.bodyMeasureData);
        } else if (response && category === 'bloodPressure' && response.bloodPressureData) {
          setInitialBloodPressureData(response.bloodPressureData);
        } else if (response && category === 'bloodLipid' && response.bloodLipidData) {
          setInitialBloodLipidData(response.bloodLipidData);
        } else if (response && category === 'bloodGlucose' && response.bloodGlucoseData) {
          setInitialBloodGlucoseData(response.bloodGlucoseData);
        } else if (response && category === 'urinalysis' && response.urinalysisData) {
          setInitialUrinalysisData(response.urinalysisData);
        } else if (response && category === 'anemia' && response.anemiaData) {
          setInitialAnemiaData(response.anemiaData);
        } else if (response && category === 'kidneyFunction' && response.kidneyFunctionData) {
          setInitialKidneyFunctionData(response.kidneyFunctionData);
        } else if (response && category === 'liverFunction' && response.liverFunctionData) {
          setInitialLiverFunctionData(response.liverFunctionData);
        }
      } catch (err) {
        console.log('Failed to load exam data:', err);
        toast.error('データの読み込みに失敗しました');
      }
    };

    loadData();
  }, [
    examDay,
    category,
    getExamResultData,
    othersExamData,
    othersInquiryData,
    questionExamData1,
    questionExamData2,
    questionExamData3,
    questionExamData4,
  ]);

  // 身体測定データを保存
  const handleSaveBodyMeasureData = async (data: BodyMeasureData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['bodyMeasure'],
        autoFlag: 2,
        maxPagesReached: 1,
        bodyMeasureData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(`/health-checkup/exam-detail/body-measure?examDay=${examDay}&_t=${Date.now()}`);
      }, 500);
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast.error(err.message);
        return;
      }
    }
  };

  // 保存血压数据
  const handleSaveBloodPressureData = async (data: BloodPressureData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['bloodPressure'],
        autoFlag: 2,
        maxPagesReached: 1,
        bloodPressureData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(
          `/health-checkup/exam-detail/blood-pressure?examDay=${examDay}&_t=${Date.now()}`,
        );
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存血中脂质数据
  const handleSaveBloodLipidData = async (data: BloodLipidData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['bloodLipid'],
        autoFlag: 2,
        maxPagesReached: 1,
        bloodLipidData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(`/health-checkup/exam-detail/blood-lipid?examDay=${examDay}&_t=${Date.now()}`);
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存血糖数据
  const handleSaveBloodGlucoseData = async (data: BloodGlucoseData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['bloodGlucose'],
        autoFlag: 2,
        maxPagesReached: 1,
        bloodGlucoseData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(
          `/health-checkup/exam-detail/blood-glucose?examDay=${examDay}&_t=${Date.now()}`,
        );
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存尿检查数据
  const handleSaveUrinalysisData = async (data: UrinalysisData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['urinalysis'],
        autoFlag: 2,
        maxPagesReached: 1,
        urinalysisData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(`/health-checkup/exam-detail/urinalysis?examDay=${examDay}&_t=${Date.now()}`);
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存贫血数据
  const handleSaveAnemiaData = async (data: AnemiaData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['anemia'],
        autoFlag: 2,
        maxPagesReached: 1,
        anemiaData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(`/health-checkup/exam-detail/anemia?examDay=${examDay}&_t=${Date.now()}`);
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存肾机能数据
  const handleSaveKidneyFunctionData = async (data: KidneyFunctionData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['kidneyFunction'],
        autoFlag: 2,
        maxPagesReached: 1,
        kidneyFunctionData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(
          `/health-checkup/exam-detail/kidney-function?examDay=${examDay}&_t=${Date.now()}`,
        );
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存肝机能数据
  const handleSaveLiverFunctionData = async (data: LiverFunctionData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['liverFunction'],
        autoFlag: 2,
        maxPagesReached: 1,
        liverFunctionData: data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(
          `/health-checkup/exam-detail/liver-function?examDay=${examDay}&_t=${Date.now()}`,
        );
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存others数据（包含others和inquiry）
  const handleSaveOthersData = async (data: OthersData & InquiryData) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      // 分离others和inquiry数据
      const {
        othersECGComment,
        othersEyegroundComment,
        othersMetabolicRank,
        othersDoctorComment,
        othersUrineUncheckReason,
        othersRemarks,
        ...inquiryFields
      } = data;

      const othersData = {
        othersECGComment,
        othersEyegroundComment,
        othersMetabolicRank,
        othersDoctorComment,
        othersUrineUncheckReason,
        othersRemarks,
      };

      const inquiryData = inquiryFields;

      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['others', 'inquiry'],
        autoFlag: 2,
        maxPagesReached: 1,
        othersData,
        inquiryData,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(`/health-checkup/exam-detail/others?examDay=${examDay}&_t=${Date.now()}`);
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  // 保存question数据（包含question1-4）
  const handleSaveQuestionData = async (
    data: Question1Data & Question2Data & Question3Data & Question4Data,
  ) => {
    if (!examDay) {
      toast.error('受診日が設定されていません');
      return;
    }

    try {
      // 分离question1-4数据
      const {
        mPressure,
        mSugar,
        mFat,
        brainDisease,
        heartDisease,
        kidneyDisease,
        anemia,
        smokeAfterFY2024,
        tenFrom20,
        sweatSport,
        exercise1hour,
        wsf,
        eatEverything,
        eatSpeed,
        eatNight3,
        eatSuger,
        noBreakfast3,
        wineFreAfterFY2024,
        wineMountAfterFY2024,
        sleepEnough,
        habitImprove,
        habitLessonAfterFY2024,
      } = data;

      const question1Data = {
        mPressure,
        mSugar,
        mFat,
        brainDisease,
        heartDisease,
        kidneyDisease,
        anemia,
      };

      const question2Data = {
        smokeAfterFY2024,
        tenFrom20,
        sweatSport,
        exercise1hour,
        wsf,
      };

      const question3Data = {
        eatEverything,
        eatSpeed,
        eatNight3,
        eatSuger,
        noBreakfast3,
      };

      const question4Data = {
        wineFreAfterFY2024,
        wineMountAfterFY2024,
        sleepEnough,
        habitImprove,
        habitLessonAfterFY2024,
      };

      await updateHealthExamResultData({
        sex: '1', // 这里需要根据实际情况设置
        examDay,
        category: ['question1', 'question2', 'question3', 'question4'],
        autoFlag: 2,
        maxPagesReached: 1,
        question1Data,
        question2Data,
        question3Data,
        question4Data,
      });

      toast.success('データを更新しました');

      // 延迟跳转，确保数据库更新完成
      setTimeout(() => {
        router.push(`/health-checkup/exam-detail/question?examDay=${examDay}&_t=${Date.now()}`);
      }, 500);
    } catch (err) {
      console.log('Failed to update exam data:', err);
      toast.error('データの更新に失敗しました');
    }
  };

  const handleCloseClick = () => {
    router.back();
  };

  const getPageTitle = () => {
    switch (category) {
      case 'bodyMeasure':
        return '身体測定結果を編集';
      case 'bloodPressure':
        return '血圧結果を編集';
      case 'bloodLipid':
        return '血中脂質結果を編集';
      case 'bloodGlucose':
        return '血糖結果を編集';
      case 'urinalysis':
        return '尿結果を編集';
      case 'anemia':
        return '貧血結果を編集';
      case 'kidneyFunction':
        return '腎機能結果を編集';
      case 'liverFunction':
        return '肝機能結果を編集';
      case 'others':
        return 'そのほか結果を編集';
      case 'question':
        return '質問票結果を編集';
      default:
        return '健診結果を編集';
    }
  };

  return (
    <div className="min-h-screen bg-card">
      <TopBar
        title={getPageTitle()}
        enableBack={false}
        enableClose={true}
        onClose={handleCloseClick}
      />

      {/* 受診日表示 */}
      <div className="px-6 py-4 pb-0 bg-card">
        <div className="text-sm text-muted-foreground">受診日</div>
        <div className="text-lg font-medium">
          {examDay
            ? new Date(examDay).toLocaleDateString('ja-JP', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'short',
              })
            : ''}
        </div>
        <hr className="mt-4" />
      </div>

      {/* Form Area */}
      <div className="flex-1">
        {category === 'bodyMeasure' ? (
          <BodyMeasureForm
            onSubmit={handleSaveBodyMeasureData}
            initialData={initialBodyMeasureData}
          />
        ) : category === 'bloodPressure' ? (
          <BloodPressureForm
            onSubmit={handleSaveBloodPressureData}
            initialData={initialBloodPressureData}
          />
        ) : category === 'bloodLipid' ? (
          <BloodLipidForm
            onSubmit={handleSaveBloodLipidData}
            initialData={initialBloodLipidData}
            examDay={examDay}
          />
        ) : category === 'bloodGlucose' ? (
          <BloodGlucoseForm
            onSubmit={handleSaveBloodGlucoseData}
            initialData={initialBloodGlucoseData}
          />
        ) : category === 'urinalysis' ? (
          <UrinalysisForm onSubmit={handleSaveUrinalysisData} initialData={initialUrinalysisData} />
        ) : category === 'anemia' ? (
          <AnemiaForm onSubmit={handleSaveAnemiaData} initialData={initialAnemiaData} />
        ) : category === 'kidneyFunction' ? (
          <KidneyFunctionForm
            onSubmit={handleSaveKidneyFunctionData}
            initialData={initialKidneyFunctionData}
          />
        ) : category === 'liverFunction' ? (
          <LiverFunctionForm
            onSubmit={handleSaveLiverFunctionData}
            initialData={initialLiverFunctionData}
          />
        ) : category === 'others' ? (
          <OthersForm onSubmit={handleSaveOthersData} initialData={initialOthersData} />
        ) : category === 'question' ? (
          <QuestionForm
            onSubmit={handleSaveQuestionData}
            initialData={initialQuestionData}
            examDay={examDay}
          />
        ) : null}
      </div>
    </div>
  );
}
