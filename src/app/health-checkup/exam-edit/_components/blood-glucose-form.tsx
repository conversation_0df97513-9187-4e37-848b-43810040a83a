'use client';
import { But<PERSON> } from '@/components/shared/button';
import { NumberInput } from '@/components/shared/number-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { BloodGlucoseData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 血糖フォームのスキーマ
const bloodGlucoseSchema = z.object({
  bgFbg: z.string().optional(),
  bgHba1c: z.string().optional(),
  bgCbg: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: BloodGlucoseData) => Promise<void>;
  initialData?: BloodGlucoseData;
}

export default function BloodGlucoseForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof bloodGlucoseSchema>>({
    resolver: zodResolver(bloodGlucoseSchema),
    defaultValues: {
      bgFbg: initialData?.bgFbg || '',
      bgHba1c: initialData?.bgHba1c || '',
      bgCbg: initialData?.bgCbg || '',
    },
  });

  // 当 initialData 更新时重置表单值
  useEffect(() => {
    if (initialData) {
      form.reset({
        bgFbg: initialData.bgFbg || '',
        bgHba1c: initialData.bgHba1c || '',
        bgCbg: initialData.bgCbg || '',
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: z.infer<typeof bloodGlucoseSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">
          血糖検査 <span className="text-xs text-muted-foreground">(いずれかの項目の実施で可)</span>
        </h2>
        <p className="text-xs text-muted-foreground">
          この項目はすべて半角数字で入力してください。
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="bgFbg"
            render={({ field }) => (
              <FormItem>
                <FormLabel>空腹時血糖</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bgFbg"
                    unit="mg/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：100mg/dl</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bgHba1c"
            render={({ field }) => (
              <FormItem>
                <FormLabel>HbA1c (NGSP)</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bgHba1c"
                    unit="%"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={1}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：5.6%</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bgCbg"
            render={({ field }) => (
              <FormItem>
                <FormLabel>随時血糖</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bgCbg"
                    unit="mg/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：100mg/dl</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : 'この内容で編集を完了'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
