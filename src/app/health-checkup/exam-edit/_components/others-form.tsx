'use client';
import { But<PERSON> } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { InquiryData, OthersData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 合并后的表单数据类型
type CombinedOthersData = OthersData & InquiryData;

// 合并后的表单スキーマ
const combinedOthersSchema = z.object({
  // Others项目
  othersECGComment: z.string().optional(),
  othersEyegroundComment: z.string().optional(),
  othersMetabolicRank: z.string().optional(),
  othersDoctorComment: z.string().optional(),
  othersUrineUncheckReason: z.string().optional(),
  othersRemarks: z.string().optional(),
  // Inquiry项目
  past: z.string().optional(),
  pastDetail: z.string().optional(),
  symptoms: z.string().optional(),
  symptomsDetail: z.string().optional(),
  objective: z.string().optional(),
  objectiveDetail: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: CombinedOthersData) => Promise<void>;
  initialData?: CombinedOthersData;
}

export default function OthersForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof combinedOthersSchema>>({
    resolver: zodResolver(combinedOthersSchema),
    defaultValues: {
      othersECGComment: initialData?.othersECGComment || '',
      othersEyegroundComment: initialData?.othersEyegroundComment || '',
      othersMetabolicRank: initialData?.othersMetabolicRank || '',
      othersDoctorComment: initialData?.othersDoctorComment || '',
      othersUrineUncheckReason: initialData?.othersUrineUncheckReason || '',
      othersRemarks: initialData?.othersRemarks || '',
      past: initialData?.past || '',
      pastDetail: initialData?.pastDetail || '',
      symptoms: initialData?.symptoms || '',
      symptomsDetail: initialData?.symptomsDetail || '',
      objective: initialData?.objective || '',
      objectiveDetail: initialData?.objectiveDetail || '',
    },
  });

  // 当 initialData 更新时重置表单值
  useEffect(() => {
    if (initialData) {
      form.reset({
        othersECGComment: initialData.othersECGComment || '',
        othersEyegroundComment: initialData.othersEyegroundComment || '',
        othersMetabolicRank: initialData.othersMetabolicRank || '',
        othersDoctorComment: initialData.othersDoctorComment || '',
        othersUrineUncheckReason: initialData.othersUrineUncheckReason || '',
        othersRemarks: initialData.othersRemarks || '',
        past: initialData.past || '',
        pastDetail: initialData.pastDetail || '',
        symptoms: initialData.symptoms || '',
        symptomsDetail: initialData.symptomsDetail || '',
        objective: initialData.objective || '',
        objectiveDetail: initialData.objectiveDetail || '',
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: z.infer<typeof combinedOthersSchema>) => {
    await onSubmit(values);
  };

  const { isShow, setDialog } = useMessageDialog();

  // Watch radio values to control textarea disabled state
  const pastValue = form.watch('past');
  const symptomsValue = form.watch('symptoms');
  const objectiveValue = form.watch('objective');

  // Clear textarea when radio is switched to "特記事項なし"
  useEffect(() => {
    if (pastValue === '2') {
      form.setValue('pastDetail', '');
    }
  }, [pastValue, form]);

  useEffect(() => {
    if (symptomsValue === '2') {
      form.setValue('symptomsDetail', '');
    }
  }, [symptomsValue, form]);

  useEffect(() => {
    if (objectiveValue === '2') {
      form.setValue('objectiveDetail', '');
    }
  }, [objectiveValue, form]);

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* 心電図検査 */}
          <FormField
            control={form.control}
            name="othersECGComment"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-lg font-medium">心電図検査</FormLabel>
                <div className="mt-2">
                  <FormLabel className="text-sm">所見</FormLabel>
                  <FormControl>
                    <Textarea
                      maxLength={1024}
                      placeholder=""
                      className="min-h-[80px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <div className="text-xs text-muted-foreground mt-1">例：異常なし</div>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <hr className="my-4" />

          {/* 眼底検査 */}
          <FormField
            control={form.control}
            name="othersEyegroundComment"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-lg font-medium">眼底検査</FormLabel>
                <div className="mt-2">
                  <FormLabel className="text-sm">所見</FormLabel>
                  <FormControl>
                    <Textarea
                      maxLength={1024}
                      placeholder=""
                      className="min-h-[80px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <div className="text-xs text-muted-foreground mt-1">例：H0 S0</div>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <hr className="my-4" />

          {/* メタボリックシンドローム判定 */}
          <FormField
            control={form.control}
            name="othersMetabolicRank"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-lg font-medium">メタボリックシンドローム判定</FormLabel>
                <FormControl>
                  <Input placeholder="" maxLength={10} className="mt-2" {...field} />
                </FormControl>
                <div className="text-xs text-muted-foreground mt-1">例：非該当</div>
                <div className="text-xs text-muted-foreground mt-2">
                  ※「メタボリックシンドローム判定」の欄は、「基準該当／予備軍該当／非該当」を記入すること。
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <hr className="my-4" />

          {/* 医師の判断 */}
          <FormField
            control={form.control}
            name="othersDoctorComment"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-lg font-medium">医師の判断</FormLabel>
                <FormControl>
                  <Textarea
                    maxLength={1024}
                    placeholder=""
                    className="min-h-[100px] resize-none mt-2"
                    {...field}
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground mt-2">
                  ※「医師の判断」の欄は、「①健康診断の結果を踏まえた医師の所見」「②貧血検査、心電図検査、眼底検査及び血清クレアチニン検査を実施した場合の理由」を記入すること。
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <hr className="my-4" />

          {/* 検査未実施の理由 */}
          <FormField
            control={form.control}
            name="othersUrineUncheckReason"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="">検査未実施の理由</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="mt-2 flex flex-col gap-6"
                  >
                    <div className="flex items-center space-x-2 g">
                      <RadioGroupItem value="1" id="reason1" />
                      <label htmlFor="reason1" className="text-sm">
                        生理中
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="2" id="reason2" />
                      <label htmlFor="reason2" className="text-sm">
                        腎疾患等の基礎疾患があるため排尿障害を有する
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="3" id="reason3" />
                      <label htmlFor="reason3" className="text-sm">
                        その他
                      </label>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <TextButton
            type="button"
            className="p-0"
            onClick={() => form.setValue('othersUrineUncheckReason', '')}
          >
            選択を解除
          </TextButton>
          <hr className="my-4" />

          {/* 診察項目ヘッダー */}
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">診察項目</h2>
          </div>

          {/* 既往歴の有無 */}
          <div>
            <FormField
              control={form.control}
              name="past"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">既往歴の有無</div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="past1" />
                        <label htmlFor="past1" className="text-sm">
                          特記事項あり
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="past2" />
                        <label htmlFor="past2" className="text-sm">
                          特記事項なし
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('past', '')}
            >
              選択を解除
            </TextButton>
          </div>

          {/* 具体的な既往歴 */}
          <div>
            <FormField
              control={form.control}
              name="pastDetail"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-4 font-medium">具体的な既往歴【全角/半角】</div>
                  <FormControl>
                    <Textarea
                      {...field}
                      disabled={pastValue !== '1'}
                      placeholder=""
                      className="min-h-[120px] resize-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 自覚症状の有無 */}
          <div>
            <FormField
              control={form.control}
              name="symptoms"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">自覚症状の有無</div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="symptoms1" />
                        <label htmlFor="symptoms1" className="text-sm">
                          特記事項あり
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="symptoms2" />
                        <label htmlFor="symptoms2" className="text-sm">
                          特記事項なし
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('symptoms', '')}
            >
              選択を解除
            </TextButton>
          </div>

          {/* 具体的な自覚症状 */}
          <div>
            <FormField
              control={form.control}
              name="symptomsDetail"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-4 font-medium">具体的な自覚症状【全角/半角】</div>
                  <FormControl>
                    <Textarea
                      {...field}
                      disabled={symptomsValue !== '1'}
                      placeholder=""
                      maxLength={256}
                      className="min-h-[120px] resize-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 他覚症状の有無 */}
          <div>
            <FormField
              control={form.control}
              name="objective"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">他覚症状の有無</div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="mt-2 flex flex-col gap-6"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="1" id="objective1" />
                        <label htmlFor="objective1" className="text-sm">
                          特記事項あり
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="2" id="objective2" />
                        <label htmlFor="objective2" className="text-sm">
                          特記事項なし
                        </label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('objective', '')}
            >
              選択を解除
            </TextButton>
          </div>

          {/* 具体的な他覚症状 */}
          <div>
            <FormField
              control={form.control}
              name="objectiveDetail"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-4 font-medium">具体的な他覚症状【全角/半角】</div>
                  <FormControl>
                    <Textarea
                      {...field}
                      disabled={objectiveValue !== '1'}
                      placeholder=""
                      maxLength={256}
                      className="min-h-[120px] resize-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* その他 */}
          <FormField
            control={form.control}
            name="othersRemarks"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-lg font-medium">その他</FormLabel>
                <div className="mt-2">
                  <FormLabel className="text-sm">備考</FormLabel>
                  <FormControl>
                    <Textarea
                      maxLength={1024}
                      placeholder=""
                      className="min-h-[100px] resize-none"
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="mt-6 rounded-md">
            <TextButton
              type="button"
              className="text-primary"
              onClick={() => {
                setDialog(true, {
                  title: '利用規約',
                  outSideClickClose: true,
                  content: 'メッセージ',
                });
              }}
            >
              利用規約
            </TextButton>
            に基づき取り扱うことに同意して保存します。
          </div>

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : 'この内容で編集を完了'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
