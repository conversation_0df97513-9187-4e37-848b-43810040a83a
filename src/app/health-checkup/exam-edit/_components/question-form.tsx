'use client';
import { But<PERSON> } from '@/components/shared/button';
import { TextButton } from '@/components/shared/text-button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  drinkingAmountAfterFY2024Options,
  drinkingAmountBeforeFY2024Options,
  drinkingFrequencyAfterFY2024Options,
  drinkingFrequencyBeforeFY2024Options,
  eatingSpeedOptions,
  eatingStateOptions,
  habitImprovementOptions,
  smokingAfterFY2024Options,
  smokingBeforeFY2024Options,
  snackFrequencyOptions,
  yesNoOptions,
} from '@/const/health-checkup';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSafeArea } from '@/hooks/use-safe-area';
import type {
  Question1Data,
  Question2Data,
  Question3Data,
  Question4Data,
} from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// 合并后的表单数据类型
type CombinedQuestionData = Question1Data & Question2Data & Question3Data & Question4Data;

// 合并后的表单スキーマ
const combinedQuestionSchema = z.object({
  // Question1项目
  mPressure: z.string().optional(),
  mSugar: z.string().optional(),
  mFat: z.string().optional(),
  brainDisease: z.string().optional(),
  heartDisease: z.string().optional(),
  kidneyDisease: z.string().optional(),
  anemia: z.string().optional(),
  // Question2项目
  smokeAfterFY2024: z.string().optional(),
  smokeBeforFY2024: z.string().optional(),
  tenFrom20: z.string().optional(),
  sweatSport: z.string().optional(),
  exercise1hour: z.string().optional(),
  wsf: z.string().optional(),
  // Question3项目
  eatEverything: z.string().optional(),
  eatSpeed: z.string().optional(),
  eatNight3: z.string().optional(),
  eatSuger: z.string().optional(),
  noBreakfast3: z.string().optional(),
  // Question4项目
  wineFreAfterFY2024: z.string().optional(),
  wineFreBeforFY2024: z.string().optional(),
  wineMountAfterFY2024: z.string().optional(),
  wineMountBeforFY2024: z.string().optional(),
  sleepEnough: z.string().optional(),
  habitImprove: z.string().optional(),
  habitLessonAfterFY2024: z.string().optional(),
  habitLessonBeforeFY2024: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: CombinedQuestionData) => Promise<void>;
  initialData?: CombinedQuestionData;
  examDay: string;
}

export default function QuestionForm({ onSubmit, initialData, examDay }: FormProps) {
  const { bottom } = useSafeArea();
  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');
  const form = useForm<z.infer<typeof combinedQuestionSchema>>({
    resolver: zodResolver(combinedQuestionSchema),
    defaultValues: {
      // Question1 defaults
      mPressure: initialData?.mPressure || '',
      mSugar: initialData?.mSugar || '',
      mFat: initialData?.mFat || '',
      brainDisease: initialData?.brainDisease || '',
      heartDisease: initialData?.heartDisease || '',
      kidneyDisease: initialData?.kidneyDisease || '',
      anemia: initialData?.anemia || '',
      // Question2 defaults
      smokeAfterFY2024: initialData?.smokeAfterFY2024 || '',
      smokeBeforFY2024: initialData?.smokeBeforFY2024 || '',
      tenFrom20: initialData?.tenFrom20 || '',
      sweatSport: initialData?.sweatSport || '',
      exercise1hour: initialData?.exercise1hour || '',
      wsf: initialData?.wsf || '',
      // Question3 defaults
      eatEverything: initialData?.eatEverything || '',
      eatSpeed: initialData?.eatSpeed || '',
      eatNight3: initialData?.eatNight3 || '',
      eatSuger: initialData?.eatSuger || '',
      noBreakfast3: initialData?.noBreakfast3 || '',
      // Question4 defaults
      wineFreAfterFY2024: initialData?.wineFreAfterFY2024 || '',
      wineFreBeforFY2024: initialData?.wineFreBeforFY2024 || '',
      wineMountAfterFY2024: initialData?.wineMountAfterFY2024 || '',
      wineMountBeforFY2024: initialData?.wineMountBeforFY2024 || '',
      sleepEnough: initialData?.sleepEnough || '',
      habitImprove: initialData?.habitImprove || '',
      habitLessonAfterFY2024: initialData?.habitLessonAfterFY2024 || '',
      habitLessonBeforeFY2024: initialData?.habitLessonBeforeFY2024 || '',
    },
  });

  // 当 initialData 更新时重置表单值
  useEffect(() => {
    if (initialData) {
      form.reset({
        // Question1
        mPressure: initialData.mPressure || '',
        mSugar: initialData.mSugar || '',
        mFat: initialData.mFat || '',
        brainDisease: initialData.brainDisease || '',
        heartDisease: initialData.heartDisease || '',
        kidneyDisease: initialData.kidneyDisease || '',
        anemia: initialData.anemia || '',
        // Question2
        smokeAfterFY2024: initialData.smokeAfterFY2024 || '',
        smokeBeforFY2024: initialData.smokeBeforFY2024 || '',
        tenFrom20: initialData.tenFrom20 || '',
        sweatSport: initialData.sweatSport || '',
        exercise1hour: initialData.exercise1hour || '',
        wsf: initialData.wsf || '',
        // Question3
        eatEverything: initialData.eatEverything || '',
        eatSpeed: initialData.eatSpeed || '',
        eatNight3: initialData.eatNight3 || '',
        eatSuger: initialData.eatSuger || '',
        noBreakfast3: initialData.noBreakfast3 || '',
        // Question4
        wineFreAfterFY2024: initialData.wineFreAfterFY2024 || '',
        wineFreBeforFY2024: initialData.wineFreBeforFY2024 || '',
        wineMountAfterFY2024: initialData.wineMountAfterFY2024 || '',
        wineMountBeforFY2024: initialData.wineMountBeforFY2024 || '',
        sleepEnough: initialData.sleepEnough || '',
        habitImprove: initialData.habitImprove || '',
        habitLessonAfterFY2024: initialData.habitLessonAfterFY2024 || '',
        habitLessonBeforeFY2024: initialData.habitLessonBeforeFY2024 || '',
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: z.infer<typeof combinedQuestionSchema>) => {
    await onSubmit(values);
  };

  const { isShow, setDialog } = useMessageDialog();

  // 渲染单选组件的辅助函数
  const renderRadioGroup = (
    field: any,
    options: any[],
    fieldName: string,
    isHorizontal = false,
  ) => (
    <RadioGroup
      onValueChange={field.onChange}
      value={field.value}
      className={`mt-2 flex ${isHorizontal ? 'flex-row gap-8' : 'flex-col gap-6'}`}
    >
      {options.map((option) => (
        <div key={option.value} className="flex items-center space-x-2">
          <RadioGroupItem value={option.value.toString()} id={`${fieldName}${option.value}`} />
          <label htmlFor={`${fieldName}${option.value}`} className="text-sm">
            {option.label}
          </label>
        </div>
      ))}
    </RadioGroup>
  );

  // 渲染はい/いいえ选项和選択を解除按钮的水平布局
  const renderYesNoWithClearButton = (field: any, fieldName: string) => (
    <div className="mt-2 flex items-center gap-8 justify-between">
      <RadioGroup
        onValueChange={field.onChange}
        value={field.value}
        className="flex flex-row gap-8 "
      >
        {yesNoOptions.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem value={option.value.toString()} id={`${fieldName}${option.value}`} />
            <label htmlFor={`${fieldName}${option.value}`} className="text-sm">
              {option.label}
            </label>
          </div>
        ))}
      </RadioGroup>
      <TextButton
        type="button"
        className="p-0 text-primary"
        onClick={() => form.setValue(fieldName as any, '')}
      >
        選択を解除
      </TextButton>
    </div>
  );

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* ヘッダー */}
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">
              質問項目<span className="text-sm text-muted-foreground">（全20問）</span>
            </h2>
          </div>

          {/* 1. 現在、aからcの薬の使用の有無 */}
          <div className="space-y-4">
            <div className="mb-4">
              <p className="text-sm mb-2 font-medium flex">
                <span className="mr-2">1.</span>
                <div>
                  <div>現在、aからcの薬の使用の有無</div>
                  <p className="text-xs text-muted-foreground  my-2">
                    （医師の判断・治療のもとで服薬中の人を指します。）
                  </p>
                </div>
              </p>
            </div>

            {/* a. 血圧を下げる薬 */}
            <div>
              <FormField
                control={form.control}
                name="mPressure"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6">a. 血圧を下げる薬</div>
                    <FormControl>{renderYesNoWithClearButton(field, 'mPressure')}</FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* b. 血糖を下げる薬又はインスリン注射 */}
            <div>
              <FormField
                control={form.control}
                name="mSugar"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6">b. 血糖を下げる薬又はインスリン注射</div>
                    <FormControl>{renderYesNoWithClearButton(field, 'mSugar')}</FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* c. コレステロールや中性脂肪を下げる薬 */}
            <div>
              <FormField
                control={form.control}
                name="mFat"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6">c. コレステロールや中性脂肪を下げる薬</div>
                    <FormControl>{renderYesNoWithClearButton(field, 'mFat')}</FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <hr className="my-6" />

          {/* 2. 脳卒中 */}
          <div>
            <FormField
              control={form.control}
              name="brainDisease"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">2.</span>
                    医師から、脳卒中（脳出血、脳梗塞等）にかかっているといわれたり、治療を受けたことがありますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'brainDisease')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 3. 心臓病 */}
          <div>
            <FormField
              control={form.control}
              name="heartDisease"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">3.</span>
                    医師から、心臓病（狭心症、心筋梗塞等）にかかっているといわれたり、治療を受けたことがありますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'heartDisease')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 4. 慢性腎臓病 */}
          <div>
            <FormField
              control={form.control}
              name="kidneyDisease"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">4.</span>
                    医師から、慢性腎臓病や腎不全にかかっているといわれたり、治療（人工透析など）を受けていますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'kidneyDisease')}</FormControl>
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 5. 貧血 */}
          <div>
            <FormField
              control={form.control}
              name="anemia"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">5.</span>医師から、貧血といわれたことがありますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'anemia')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 6. たばこを習慣的に吸っているか */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="smokeBeforFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="mb-2 text-sm font-medium flex">
                        <span className="mr-2">6.</span>
                        <div>
                          <div>現在、たばこを習慣的に吸っていますか。</div>
                          <div className="text-xs text-muted-foreground mb-4 my-2">
                            <p>
                              （※「現在、習慣的に喫煙している人」とは、「合計100本以上、又は6ヶ月以上吸っている人」
                            </p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      {renderRadioGroup(field, smokingBeforeFY2024Options, 'smokeBeforFY2024')}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="smokeAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="mb-2 text-sm font-medium flex">
                        <span className="mr-2">6.</span>
                        <div>
                          <div>現在、たばこを習慣的に吸っていますか。</div>
                          <div className="text-xs text-muted-foreground mb-4 my-2">
                            <p>
                              ※「現在、習慣的に喫煙している者」とは、条件1と条件2を両方満たす者である。
                            </p>
                            <p>条件1：最近1ヶ月間吸っている</p>
                            <p>条件2：生涯で6ヶ月以上吸っている、又は合計100本以上吸っている</p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      {renderRadioGroup(field, smokingAfterFY2024Options, 'smokeAfterFY2024')}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => {
                if (isBefore2024) {
                  form.setValue('smokeBeforFY2024', '');
                } else {
                  form.setValue('smokeAfterFY2024', '');
                }
              }}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 7. 20歳の時の体重から10kg以上増加 */}
          <div>
            <FormField
              control={form.control}
              name="tenFrom20"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">7.</span>20歳の時の体重から10kg以上増加していますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'tenFrom20')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 8. 1回30分以上の軽く汗をかく運動 */}
          <div>
            <FormField
              control={form.control}
              name="sweatSport"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">8.</span>
                    1回30分以上の軽く汗をかく運動を週2日以上、1年以上実施していますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'sweatSport')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 9. 日常生活において歩行又は同等の身体活動 */}
          <div>
            <FormField
              control={form.control}
              name="exercise1hour"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">9.</span>
                    日常生活において歩行又は同等の身体活動を1日1時間以上実施していますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'exercise1hour')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 10. ほぼ同じ年齢の同性と比較して歩く速度 */}
          <div>
            <FormField
              control={form.control}
              name="wsf"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">10.</span>
                    ほぼ同じ年齢の同性と比較して歩く速度が速いですか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'wsf')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 11. 食事をかんで食べる時の状態 */}
          <div>
            <FormField
              control={form.control}
              name="eatEverything"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">11.</span>
                    食事をかんで食べる時の状態はどれにあてはまりますか。
                  </div>
                  <FormControl>
                    {renderRadioGroup(field, eatingStateOptions, 'eatEverything')}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('eatEverything', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 12. 人と比較して食べる速度 */}
          <div>
            <FormField
              control={form.control}
              name="eatSpeed"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">12.</span>人と比較して食べる速度が速いですか。
                  </div>
                  <FormControl>
                    {renderRadioGroup(field, eatingSpeedOptions, 'eatSpeed')}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('eatSpeed', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 13. 就寝前の2時間以内に夕食 */}
          <div>
            <FormField
              control={form.control}
              name="eatNight3"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">13.</span>
                    就寝前の2時間以内に夕食をとることが週に3回以上ありますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'eatNight3')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 14. 朝昼夕の3食以外に間食や甘い飲み物 */}
          <div>
            <FormField
              control={form.control}
              name="eatSuger"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">14.</span>
                    朝昼夕の3食以外に間食や甘い飲み物を摂取していますか。
                  </div>
                  <FormControl>
                    {renderRadioGroup(field, snackFrequencyOptions, 'eatSuger')}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('eatSuger', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 15. 朝食を抜くことが週に3回以上 */}
          <div>
            <FormField
              control={form.control}
              name="noBreakfast3"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">15.</span>朝食を抜くことが週に3回以上ありますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'noBreakfast3')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 16. お酒を飲む頻度 */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="wineFreBeforFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">16.</span>
                        <div>
                          <p>
                            お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度はどれくらいですか。
                          </p>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      {renderRadioGroup(
                        field,
                        drinkingFrequencyBeforeFY2024Options,
                        'wineFreBeforFY2024',
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="wineFreAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">16.</span>
                        <div>
                          <p>
                            お酒（日本酒、焼酎、ビール、洋酒など）を飲む頻度はどのくらいですか。
                          </p>
                          <p className="text-xs text-muted-foreground my-2">
                            （※「やめた」とは、過去に月1回以上の習慣的な飲酒歴があった者のうち、最近1年以上酒類を摂取していない者）
                          </p>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      {renderRadioGroup(
                        field,
                        drinkingFrequencyAfterFY2024Options,
                        'wineFreAfterFY2024',
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => {
                if (isBefore2024) {
                  form.setValue('wineFreBeforFY2024', '');
                } else {
                  form.setValue('wineFreAfterFY2024', '');
                }
              }}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 17. 飲酒日の1日当たりの飲酒量 */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="wineMountBeforFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">17.</span>
                        <div>
                          <p>飲酒日の１日当たりの飲酒量はどれくらいですか。</p>
                          <div className="text-xs text-muted-foreground mb-4 space-y-1 my-2">
                            <p>日本酒1合（180ml）の目安：</p>
                            <p>ビール500ml、</p>
                            <p>焼酎25度（110ml）、</p>
                            <p>ウイスキーダブル1杯（60ml）、</p>
                            <p>ワイン2杯（240ml）</p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      {renderRadioGroup(
                        field,
                        drinkingAmountBeforeFY2024Options,
                        'wineMountBeforFY2024',
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="wineMountAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-4">
                      <p className="text-sm mb-2 font-medium flex">
                        <span className="mr-2">17.</span>
                        <div>
                          <p>飲酒日の1日当たりの飲酒量はどれくらいですか。</p>
                          <div className="text-xs text-muted-foreground mb-4 space-y-1 my-2">
                            <p>日本酒1合（アルコール度数15度・180ml）の目安：</p>
                            <p>ビール（同5度・500ml）、</p>
                            <p>焼酎（同25度・約110ml）、</p>
                            <p>ワイン（同14度・約180ml）、</p>
                            <p>ウイスキー（同43度・60ml）、</p>
                            <p>缶チューハイ（同5度・約500ml、同7度・約350ml）</p>
                          </div>
                        </div>
                      </p>
                    </div>
                    <FormControl>
                      {renderRadioGroup(
                        field,
                        drinkingAmountAfterFY2024Options,
                        'wineMountAfterFY2024',
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => {
                if (isBefore2024) {
                  form.setValue('wineMountBeforFY2024', '');
                } else {
                  form.setValue('wineMountAfterFY2024', '');
                }
              }}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 18. 睡眠で休養が十分とれているか */}
          <div>
            <FormField
              control={form.control}
              name="sleepEnough"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">18.</span>睡眠で休養が十分とれていますか。
                  </div>
                  <FormControl>{renderYesNoWithClearButton(field, 'sleepEnough')}</FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <hr className="my-6" />

          {/* 19. 運動や食生活等の生活習慣を改善してみようと思いますか */}
          <div>
            <FormField
              control={form.control}
              name="habitImprove"
              render={({ field }) => (
                <FormItem>
                  <div className="text-sm mb-6 font-medium">
                    <span className="mr-2">19.</span>
                    運動や食生活等の生活習慣を改善してみようと思いますか。
                  </div>
                  <FormControl>
                    {renderRadioGroup(field, habitImprovementOptions, 'habitImprove')}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <TextButton
              type="button"
              className="p-0 mt-6 text-primary"
              onClick={() => form.setValue('habitImprove', '')}
            >
              選択を解除
            </TextButton>
          </div>

          <hr className="my-6" />

          {/* 20. 生活習慣の改善について */}
          <div>
            {isBefore2024 ? (
              <FormField
                control={form.control}
                name="habitLessonBeforeFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6 font-medium">
                      <span className="mr-2">20.</span>
                      生活習慣の改善について保健指導を受ける機会があれば、利用しますか。
                    </div>
                    <FormControl>
                      {renderYesNoWithClearButton(field, 'habitLessonBeforeFY2024')}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="habitLessonAfterFY2024"
                render={({ field }) => (
                  <FormItem>
                    <div className="text-sm mb-6 font-medium">
                      <span className="mr-2">20.</span>
                      生活習慣の改善について、これまでに特定保健指導を受けたことがありますか。
                    </div>
                    <FormControl>
                      {renderYesNoWithClearButton(field, 'habitLessonAfterFY2024')}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <div className="mt-6 rounded-md">
            <TextButton
              type="button"
              className="text-primary"
              onClick={() => {
                setDialog(true, {
                  title: '利用規約',
                  outSideClickClose: true,
                  content: 'メッセージ',
                });
              }}
            >
              利用規約
            </TextButton>
            に基づき取り扱うことに同意して保存します。
          </div>

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : 'この内容で編集を完了'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
