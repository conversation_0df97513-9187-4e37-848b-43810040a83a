'use client';
import { But<PERSON> } from '@/components/shared/button';
import { NumberInput } from '@/components/shared/number-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { BloodLipidData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 血中脂質フォームのスキーマ
const bloodLipidSchema = z.object({
  blTriglyceride: z.string().optional(),
  blNormalTriglyceride: z.string().optional(),
  blHdl: z.string().optional(),
  blLdl: z.string().optional(),
  blNonHdl: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: BloodLipidData) => Promise<void>;
  initialData?: BloodLipidData;
  examDay: string;
}

export default function BloodLipidForm({ onSubmit, initialData, examDay }: FormProps) {
  const { bottom } = useSafeArea();

  const isBefore2024 = new Date(examDay) < new Date('2024-04-01');

  const form = useForm<z.infer<typeof bloodLipidSchema>>({
    resolver: zodResolver(bloodLipidSchema),
    defaultValues: {
      blTriglyceride: initialData?.blTriglyceride || '',
      blNormalTriglyceride: initialData?.blNormalTriglyceride || '',
      blHdl: initialData?.blHdl || '',
      blLdl: initialData?.blLdl || '',
      blNonHdl: initialData?.blNonHdl || '',
    },
  });

  // 当 initialData 更新时重置表单值
  useEffect(() => {
    if (initialData) {
      form.reset({
        blTriglyceride: initialData.blTriglyceride || '',
        blNormalTriglyceride: initialData.blNormalTriglyceride || '',
        blHdl: initialData.blHdl || '',
        blLdl: initialData.blLdl || '',
        blNonHdl: initialData.blNonHdl || '',
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: z.infer<typeof bloodLipidSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">血中脂質検査</h2>
        <p className="text-xs text-muted-foreground">
          この項目はすべて半角数字で入力してください。
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="blTriglyceride"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{isBefore2024 ? '中性脂肪' : '空腹時中性脂肪'}</FormLabel>
                <FormControl>
                  <NumberInput
                    name="blTriglyceride"
                    unit="mg/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：150mg/dl</div>
                <FormMessage />
              </FormItem>
            )}
          />

          {!isBefore2024 && (
            <FormField
              control={form.control}
              name="blNormalTriglyceride"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>随時中性脂肪</FormLabel>
                  <FormControl>
                    <NumberInput
                      name="blNormalTriglyceride"
                      unit="mg/dl"
                      value={field.value || ''}
                      onChange={field.onChange}
                      maxDecLen={0}
                      className="w-1/2"
                    />
                  </FormControl>
                  <div className="text-xs text-muted-foreground">例：150mg/dl</div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="blHdl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>HDLコレステロール</FormLabel>
                <FormControl>
                  <NumberInput
                    name="blHdl"
                    unit="mg/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：40mg/dl</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="blLdl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>LDLコレステロール *</FormLabel>
                <FormControl>
                  <NumberInput
                    name="blLdl"
                    unit="mg/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：120mg/dl</div>
                <div className="text-xs text-muted-foreground">
                  ※LDLコレステロールについては、中性脂肪が400mg/dl以上又は食後採血の場合はNon-HDLコレステロールの測定に代えられる
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="blNonHdl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Non-HDLコレステロール *</FormLabel>
                <FormControl>
                  <NumberInput
                    name="blNonHdl"
                    unit="mg/dl"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：150mg/dl</div>
                <div className="text-xs text-muted-foreground">
                  ※LDLコレステロールについては、中性脂肪が400mg/dl以上又は食後採血の場合はNon-HDLコレステロールの測定に代えられる
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : 'この内容で編集を完了'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
