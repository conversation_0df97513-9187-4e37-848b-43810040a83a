'use client';
import { Button } from '@/components/shared/button';
import { NumberInput } from '@/components/shared/number-input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useSafeArea } from '@/hooks/use-safe-area';
import type { BloodPressureData } from '@/types/health-checkup-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
// 血圧フォームのスキーマ
const bloodPressureSchema = z.object({
  bpSystolic: z.string().optional(),
  bpDiastolic: z.string().optional(),
});

interface FormProps {
  onSubmit: (data: BloodPressureData) => Promise<void>;
  initialData?: BloodPressureData;
}

export default function BloodPressureForm({ onSubmit, initialData }: FormProps) {
  const { bottom } = useSafeArea();
  const form = useForm<z.infer<typeof bloodPressureSchema>>({
    resolver: zodResolver(bloodPressureSchema),
    defaultValues: {
      bpSystolic: initialData?.bpSystolic || '',
      bpDiastolic: initialData?.bpDiastolic || '',
    },
  });

  // 当 initialData 更新时重置表单值
  useEffect(() => {
    if (initialData) {
      form.reset({
        bpSystolic: initialData.bpSystolic || '',
        bpDiastolic: initialData.bpDiastolic || '',
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: z.infer<typeof bloodPressureSchema>) => {
    await onSubmit(values);
  };

  return (
    <div className="p-6 pb-24">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="mb-6">
            <h2 className="text-lg font-bold mb-2">血圧</h2>
            <p className="text-xs text-muted-foreground">
              この項目はすべて半角数字で入力してください。
            </p>
          </div>
          <FormField
            control={form.control}
            name="bpSystolic"
            render={({ field }) => (
              <FormItem>
                <FormLabel>収縮期血圧</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bpSystolic"
                    unit="mmHg"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：130mmHg</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bpDiastolic"
            render={({ field }) => (
              <FormItem>
                <FormLabel>拡張期血圧</FormLabel>
                <FormControl>
                  <NumberInput
                    name="bpDiastolic"
                    unit="mmHg"
                    value={field.value || ''}
                    onChange={field.onChange}
                    maxDecLen={0}
                    className="w-1/2"
                  />
                </FormControl>
                <div className="text-xs text-muted-foreground">例：85mmHg</div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div
            style={{ paddingBottom: bottom + 24 }}
            className="fixed bottom-0 left-0 right-0 bg-white p-4"
          >
            <Button
              type="submit"
              className="w-full h-12 text-base font-medium"
              disabled={form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? '保存中...' : 'この内容で編集を完了'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
