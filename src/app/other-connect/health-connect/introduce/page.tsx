// src/components/SetupGuide.tsx

import TopBar from '@/components/layout/top-bar';
import { APP_TEXT } from '@/const/text/app';
import Step from '../../_components/fit-props';

const SetupGuide = () => {
  return (
    <>
      <TopBar title={APP_TEXT.CONNECT.TITLE_SETTING} enableBack={true} />
      <div className="">
        <Step
          number={1}
          title={APP_TEXT.CONNECT.CONNECT_GUIDE_TITLE1}
          content={
            <p style={{ whiteSpace: 'pre-line' }}>{APP_TEXT.CONNECT.CONNECT_GUIDE_CONTEXT1}</p>
          }
        />

        <Step
          number={2}
          title={APP_TEXT.CONNECT.CONNECT_GUIDE_TITLE2}
          content={
            <p style={{ whiteSpace: 'pre-line' }}>{APP_TEXT.CONNECT.CONNECT_GUIDE_CONTEXT2}</p>
          }
        />

        <Step
          number={3}
          title={APP_TEXT.CONNECT.CONNECT_GUIDE_TITLE3}
          content={
            <p style={{ whiteSpace: 'pre-line' }}>{APP_TEXT.CONNECT.CONNECT_GUIDE_CONTEXT3}</p>
          }
        />
      </div>
    </>
  );
};

export default SetupGuide;
