'use client';

import { ServiceSettingRequest, dataConnectAPI } from '@/api/modules/data-connect';
import TopBar from '@/components/layout/top-bar';
import {
  DataSourceDeviceType,
  FitbitScope,
  VitalTypeLabels,
  fitbit,
  initialData,
} from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter } from '@/hooks/use-next-navigation';
import { isMobile } from '@/utils/device-detect';
import { sendMessageToNative } from '@/utils/native-bridge';
import React, { useState, useEffect, useRef } from 'react';
import { FitbitCard } from '../_components/fitbit-card-props';

export default function FitbitPage() {
  const [vitalTypes, setVitalTypes] = useState(initialData);
  const { setLoading } = useLoading();
  const userPermissions = useRef('');
  const scopeStr = useRef('');
  const router = useRouter();

  useEffect(() => {
    getVitaType();
  }, []);

  //必要な権限を取得する
  const getVitaType = async () => {
    const request = new ServiceSettingRequest();
    request.deviceType = DataSourceDeviceType.Fitbit;
    setLoading(true);
    dataConnectAPI
      .getVitaType(request)
      .then((response) => {
        setLoading(false);
        //戻り値に基づいてデータを組み立てる
        const typeList = response.vitalTypeList.map((id) => ({
          id,
          label: VitalTypeLabels[id],
        }));
        const idString = typeList.map((item) => item.id).join(',');
        const scopes = typeList.map((item) => FitbitScope[item.id]).join(' ');
        userPermissions.current = idString;
        scopeStr.current = scopes;
        setVitalTypes(typeList);
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };

  const handleSubmit = () => {
    const redirectUri = fitbit.callback;
    const scope = encodeURIComponent(scopeStr.current);
    const client_id =
      process.env.NEXT_PUBLIC_APP_ENV === 'dev' ? fitbit.client_id_dev : fitbit.client_id_stg;
    const url = `${fitbit.auth_url}&client_id=${client_id}&redirect_uri=${redirectUri}&scope=${scope}`;
    //Fitbitを連携する
    // if (isMobile()) {
    console.log('mobile');
    sendMessageToNative({
      type: 'start-other-link',
      data: {
        link: url,
        callbackLink: redirectUri,
        returnLink: '',
        enableClose: '1',
        enableBack: '0',
        title: `${APP_TEXT.FITBIT.TITLE_BAR}`,
      },
      callback: (data) => {
        window.location.href = data?.url;
      },
    });
    // } else {
    console.log('web');
    // window.location.href = url;
    // }
  };

  const close = () => {
    router.back();
  };

  return (
    <div className="flex flex-col">
      <TopBar
        title={APP_TEXT.FITBIT.TITLE_BAR}
        enableBack={false}
        enableClose={true}
        onClose={close}
      />
      <FitbitCard
        title={APP_TEXT.FITBIT.CONTENT}
        dataItems={vitalTypes}
        onSubmit={() => handleSubmit()}
      />
    </div>
  );
}
