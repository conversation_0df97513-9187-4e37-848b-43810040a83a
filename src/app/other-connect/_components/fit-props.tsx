interface StepProps {
  number: number;
  title: string;
  content: JSX.Element | string;
}
const Step: React.FC<StepProps> = ({ number, title, content }) => {
  return (
    <div className="mb-9">
      <div className="flex items-center justify-center">
        <div className="font-bold text-primary mr-2 flex flex-col items-center">
          <span className="text-xs">STEP</span>
          <span className="text-[42px] leading-[42px]"> {number}</span>
        </div>
        <span className="text-lg font-bold text-primary ml-2">{title}</span>
      </div>
      <hr className="mb-4 mt-2" />
      <div className="mt-2 text-base font-normal text-black">{content}</div>
    </div>
  );
};

export default Step;
