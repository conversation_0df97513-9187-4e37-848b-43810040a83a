'use client';

import { But<PERSON> } from '@/components/shared/button';
import { APP_TEXT } from '@/const/text/app';
import fitbitIcon from '../../../images/fitbit.png';

type HealthDataItem = {
  id: number;
  label: string;
};

type FitbitCardProps = {
  title: string;
  icon?: React.ReactNode;

  dataItems: HealthDataItem[];

  description?: string;
  onSubmit?: () => void;
  note?: string;
  className?: string;
};

export const FitbitCard = ({
  title,
  icon = <img className="h-12 w-12" alt="desc" src={fitbitIcon.src} />,
  dataItems,
  description = APP_TEXT.FITBIT.CONNECTDESCRIPTION,
  note = APP_TEXT.FITBIT.DATA_NOTE,
  onSubmit,
  className = '',
}: FitbitCardProps) => {
  return (
    <div className="bg-white flex flex-col min-h-[calc(100vh-36px)]">
      <div className="my-4 text-base font-bold text-black">{APP_TEXT.FITBIT.TITLE}</div>
      <div className={`flex flex-col items-center  ${className}`}>
        <div className="w-full max-w-md rounded-2xl p-6 bg-primary-5">
          <div className="flex items-center mb-4">
            {icon}
            <h2 className="ml-4 text-lg font-bold text-black">{title}</h2>
          </div>
          <hr className="my-4 bg-gray-30" />
          <div className="mb-2">
            <p className="text-base font-normal">{APP_TEXT.FITBIT.DATA_TITLE}</p>
            <ul className="list-disc pl-6 mt-2">
              {dataItems.map((item) => (
                <li key={item.id} className="text-base text-primary font-bold">
                  {item.label}
                </li>
              ))}
            </ul>
          </div>
          {note && <p className="text-[12px] text-gray-800 font-normal">{note}</p>}
        </div>
        <p className="text-base text-black font-normal mt-6 ">{description}</p>
      </div>
      <div className="mt-6  w-full max-w-md pt-4">
        <Button type="submit" className="w-full" onClick={onSubmit}>
          {APP_TEXT.FITBIT.AGREE_BUTTON}
        </Button>
      </div>
    </div>
  );
};
