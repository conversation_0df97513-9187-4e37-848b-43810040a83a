'use client';

import TopBar from '@/components/layout/top-bar';
import { But<PERSON> } from '@/components/shared/button';
import ListRadio from '@/components/shared/list-radio-new';
import { TextButton } from '@/components/shared/text-button';
import { DialogClose } from '@/components/ui/dialog';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { sendMessageToNative } from '@/utils/native-bridge';

import {
  formatDateTime,
  formatDateYMD,
  generateDateRange,
  getJapanTimeRange,
  getYesterdayNoonByDate,
} from '../../../utils/date-format';

import {
  ServiceSettingRequest,
  SleepData,
  SyncVitalDataRequest,
  VitalSettingRequest,
  WeightData,
  dataConnectAPI,
} from '@/api/modules/data-connect';
import { DataSourceDeviceType, DeviceType, VitalType, fitbit } from '@/const/app';
import { RESPONSE_CODE } from '@/const/response-code';
import { COMMON_TEXT } from '@/const/text/common';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useAuthStore } from '@/store/auth';
import type {
  FitbitSleepLog,
  FitbitWeightEntry,
  OptionInfo,
  VitalFromResponse,
} from '@/types/data-connect';
import { isIOS } from '@/utils/device-detect';
import { nlog } from '@/utils/logger';
import axios from 'axios';
import { UndoIcon } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import bikuri_icon from '../../../images/bikuri.png';
import fail_icon from '../../../images/fail.png';

function DataSrcSet() {
  interface Option {
    value: string;
    title: string;
    tip: string;
    help?: string;
    icon?: string;
  }
  const { isShow, setDialog } = useMessageDialog();
  const router = useRouter();
  const { setLoading } = useLoading();
  const [currentValue, setCurrentValue] = useState('0');
  const [vitalType, setVitalType] = useState(0);
  const [title, setTitle] = useState('');
  const [dateList, setDateList] = useState<string[]>([]);
  const [weightList, setWeightList] = useState<WeightData[]>([]);
  const [sleepList, setSleepList] = useState<SleepData[]>([]);
  const goToDataConnect = async () => {
    if (window.location.pathname.indexOf('other-connect/datasrc') > -1) {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
        router.back();
      }, 2000);
    }
  };
  const searchParams = useSearchParams();
  const [radioList, setRadioList] = useState<Option[]>([]);
  const { user } = useAuthStore.getState();

  useEffect(() => {
    if (searchParams != null) {
      const encodedData = searchParams.get('data') || '';
      if (encodedData.length !== 0) {
        const params = JSON.parse(atob(encodedData));
        const item = params.item;
        const value = params.value;
        const options = params.options as OptionInfo[];
        if (value !== undefined) {
          setSelectedOption(String(value));
          setCurrentValue(String(value));
        }
        if (item !== undefined) {
          if (isIOS()) {
            if (item === 'weight') {
              setTitle(APP_TEXT.DATA_SRCSET.TITLE_WEIGHT);
              setVitalType(2);
            } else if (item === 'sleep') {
              setTitle(APP_TEXT.DATA_SRCSET.TITLE_SLEEP);
              setVitalType(5);
            } else if (item === 'press') {
              setTitle(APP_TEXT.DATA_SRCSET.TITLE_PRESS);
              setVitalType(3);
            }
          } else {
            if (item === 'weight') {
              setTitle(APP_TEXT.DATA_SRCSET.TITLE_WEIGHT);
              setVitalType(2);
            } else if (item === 'sleep') {
              setTitle(APP_TEXT.DATA_SRCSET.TITLE_SLEEP);
              setVitalType(5);
            } else if (item === 'press') {
              setTitle(APP_TEXT.DATA_SRCSET.TITLE_PRESS);
              setVitalType(3);
            }
          }
        }

        if (options !== undefined) {
          const radioList = options.map((item) => ({
            title:
              APP_TEXT.DATA_SRCSET.SRCITEMS[item.deviceType === undefined ? 0 : item.deviceType],
            value: String(item.deviceType === undefined ? 0 : item.deviceType),
            icon: item.iconUrl,
            tip: item.deviceType === 1 ? 'iPhone' : item.deviceType === 2 ? 'Apple Watch' : '',
          }));
          setRadioList(radioList as Option[]);
        }
      }
    }
  }, [searchParams]);

  //「変更する」動作
  const decideSrc = async () => {
    if (selectedOption === '0') {
      sendToServer(Number(selectedOption));
    } else {
      setDialog(true, {
        content: (
          <div className="flex flex-col gap-[8px]]">
            <div className="font-bold text-[20px] text-center">
              {APP_TEXT.DATA_SRCSET.CONFIRM_TITLE}
            </div>
            <div className="font-bold text-[16px] mb-1">{APP_TEXT.DATA_SRCSET.CONFIRM_SUB1}</div>
            <div className="text-[16px]">{APP_TEXT.DATA_SRCSET.CONFIRM_TXT1}</div>
            <div className="text-[14px] text-gray-600">{APP_TEXT.DATA_SRCSET.CONFIRM_SMTXT1}</div>
            <div className="font-bold text-[16px] mb-1 mt-1">
              {APP_TEXT.DATA_SRCSET.CONFIRM_SUB2}
            </div>
            <div className="text-[16px]">{APP_TEXT.DATA_SRCSET.CONFIRM_TXT2}</div>
          </div>
        ),
        outSideClickClose: false,
        footer: (
          <div className="flex-col ">
            <Button
              variant="default"
              className="w-full bg-primary text-white mb-2"
              onClick={() => {
                setDialog(false);
                if (Number(selectedOption) === DataSourceDeviceType.HealthConnect) {
                  //ヘルスコネクトが選択された場合、権限をチェックする
                  checkHealthPermission(String(vitalType), selectedOption);
                } else {
                  //データ取得元を変更
                  sendToServer(Number(selectedOption));
                }
              }}
            >
              {APP_TEXT.DATA_SRCSET.CONFIRM_BTN}
            </Button>
            <DialogClose asChild>
              <TextButton className="mt-2 w-full" variant="muted">
                {COMMON_TEXT.BUTTON.CANCEL}
              </TextButton>
            </DialogClose>
          </div>
        ),
      });
    }
  };

  const sendToServer = async (value: number) => {
    //バイタルデータの取得先変更API
    const request = new VitalSettingRequest();
    request.vitalPostChange = Number(selectedOption);
    request.vitalType = vitalType;
    setLoading(true);
    dataConnectAPI
      .vitalSetting(request)
      .then((response) => {
        nlog('vitalSetting response');
        getVitalFrom(String(vitalType), String(value));
      })
      .catch((response) => {
        setLoading(false);
      });
  };

  const checkHealthPermission = (vitalType: string, deviceType: string) => {
    sendMessageToNative({
      type: 'check-health-permission',
      //vitalType: weight:2 sleep:5  press:3
      //deviceType: ヘルスコネクト:4  OMRON:7 ヘルスケア
      data: { vitalType: vitalType, deviceType: deviceType },
      callback: (data) => {
        if (data) {
          if (data.isNotPermitted === '1') {
            //許可未済
            showPermissionError();
          } else {
            //許可済
            //データ取得元を変更
            sendToServer(Number(deviceType));
          }
        }
      },
    });
  };

  const syncHealthToNative = (vitalType: string, deviceType: string, date: string) => {
    const timer = setTimeout(() => {
      setLoading(false);
      goToDataConnect();
    }, 20000);
    sendMessageToNative({
      type: 'sync-health',
      //vitalType: weight:2 sleep:5  press:3
      //deviceType: ヘルスコネクト:4  OMRON:7 ヘルスケア　iPhone:1 ヘルスケア　AppleWatch:2
      data: { vitalType: vitalType, deviceType: deviceType, date: date, single: '1' },
      callback: (data) => {
        nlog('sync-health callback');
        clearTimeout(timer);
        if (data) {
          //該当権限あり、直接サーバへ送信する
          //data：{weightData:[],sleepData:[],bloodPressureData:[]}
          const dataStr = JSON.stringify(data);
          console.log(dataStr);
          //nullでない場合はアップロードしてください
          const request = new SyncVitalDataRequest();
          // 必要なすべてのプロパティに値を割り当て
          if (data.weightData || data.sleepData || data.bloodPressureData) {
            request.weightData = data.weightData || [];
            request.sleepData = data.sleepData || [];
            request.bloodPressureData = data.bloodPressureData || [];

            setLoading(true);
            nlog('syncVitalData');
            dataConnectAPI
              .syncVitalData(request)
              .then((response) => {
                nlog('syncVitalData response');
                setLoading(false);
                goToDataConnect();
              })
              .catch((response) => {
                setLoading(false);
                goToDataConnect();
              });
          } else {
            setLoading(false);
            goToDataConnect();
          }
        } else {
          setLoading(false);
          goToDataConnect();
        }
      },
    });
  };

  //Fitbit AccessTokenを取得
  const updateFitbitToken = async (vitalType: string, date: string) => {
    setLoading(true);
    dataConnectAPI
      .getFitbitToken()
      .then((response) => {
        //Fitbit情報取得し、サーバへ送信する Nativeで送信
        if (response.accessToken !== undefined) {
          //Fitbitデータ取得し、サーバ送信する
          if (vitalType === '2') {
            //体重
            const addDateList = generateDateRange(date);
            setDateList([...dateList, ...addDateList]);
            const start = addDateList[0];
            const end = addDateList[addDateList.length - 1];
            getFitbitWeight(response.accessToken, start, end, addDateList);
          } else if (vitalType === '5') {
            //睡眠
            const addDateList = generateDateRange(date);
            setDateList([...dateList, ...addDateList]);
            let start = addDateList[0];
            const end = addDateList[addDateList.length - 1];
            start = formatDateYMD(getYesterdayNoonByDate(start));
            getFitbitSleep(response.accessToken, start, end, addDateList);
          } else {
            setLoading(false);
            goToDataConnect();
          }
        } else {
          setLoading(false);
          goToDataConnect();
        }
      })
      .catch((response) => {
        if (response.data.code === 'EHBF00013') {
          //Token更新失敗の場合
          //FitBit連携をOFFにする
          if (user?.id) {
            setDiviceOff(DeviceType.Fitbit, user?.id);
          } else {
            setLoading(false);
            goToDataConnect();
          }
          return Promise.reject();
        }
      });
  };
  //バイタルデータ連携設定取得API
  const getVitalFrom = async (vitalType: string, deviceType: string) => {
    dataConnectAPI
      .getVitalFrom()
      .then((response) => {
        if (response != null) {
          nlog('getVitalFrom response not null');
          //Fitbitへ変更された時　体重、睡眠
          if (deviceType === '6') {
            //まず、Fitbit Tokenを取得する
            if (vitalType === VitalType.Weight || vitalType === VitalType.Sleep) {
              if (vitalType === VitalType.Weight && response.queryWeightStartDate !== undefined) {
                updateFitbitToken(vitalType, response.queryWeightStartDate);
              } else if (
                vitalType === VitalType.Sleep &&
                response.querySleepStartDate !== undefined
              ) {
                updateFitbitToken(vitalType, response.querySleepStartDate);
              } else {
                goToDataConnect();
                setLoading(false);
              }
            } else {
              setLoading(false);
              goToDataConnect();
            }
          } else if (deviceType === '7') {
            //OMRONへ変更された時　　体重、血圧
            if (vitalType === VitalType.Weight || vitalType === VitalType.Blood) {
              handleVitalSync(vitalType, deviceType, response);
            } else {
              setLoading(false);
              goToDataConnect();
            }
          } else if (deviceType === '4') {
            //ヘルスコネクトへ変更された時 Androidのみ　　体重、睡眠、血圧
            handleVitalSync(vitalType, deviceType, response);
          } else if (deviceType === '1' || deviceType === '2') {
            //ヘルスケアへ変更された時　iOSのみ　　体重、睡眠、血圧
            handleVitalSync(vitalType, deviceType, response);
          } else {
            setLoading(false);
            goToDataConnect();
          }
        } else {
          nlog('getVitalFrom response  null');
          setLoading(false);
          goToDataConnect();
        }
      })
      .catch((response) => {
        setLoading(false);
      });
  };
  //vitalTypeに基づいて最後のクエリの時間を生成
  function handleVitalSync(vitalType: string, deviceType: string, response: VitalFromResponse) {
    const fieldMap: Partial<Record<string, string>> = {
      [VitalType.Weight]: response.queryWeightStartDate,
      [VitalType.Sleep]: response.querySleepStartDate,
      [VitalType.Blood]: response.queryBPStartDate,
    };

    const field = fieldMap[vitalType];
    if (field !== null && field !== undefined) {
      syncHealthToNative(String(vitalType), deviceType, field);
    } else {
      setLoading(false);
      goToDataConnect();
    }
  }
  //date:yyyy-MM-dd
  const getFitbitWeight = async (token: string, start: string, end: string, dateList: string[]) => {
    try {
      const res = await axios.get(
        `${fitbit.query_url}body/log/weight/date/${start}/${end}.json?timezone=Asia/Tokyo`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (res.data.weight.length > 0) {
        dateList.forEach((date, index) => {
          const weights = res.data.weight.filter(
            (weight: FitbitWeightEntry) => weight.date === date,
          );
          if (weights !== undefined && weights.length > 0) {
            const latest = res.data.weight
              .filter((weight: FitbitWeightEntry) => weight.date === date)
              .reduce((prev: FitbitWeightEntry, current: FitbitWeightEntry) =>
                new Date(prev.date) > new Date(current.date) ? prev : current,
              );
            if (latest !== undefined) {
              const weightData = new WeightData();
              weightData.bodyFrom = 6;
              weightData.measureDate = date.replaceAll('-', '');
              weightData.weight = `${latest.weight}`;
              if (latest.fat || latest.fat !== undefined) {
                weightData.fatPercentage = `${latest.fat}`;
              }
              weightList.push(weightData);
              setWeightList(weightList);
            }
          }
        });

        //全部データ取得済の場合、体重情報をサーバへ送信する
        if (weightList.length > 0) {
          const data = new SyncVitalDataRequest();
          data.weightData = weightList;
          dataConnectAPI
            .syncVitalData(data)
            .then((response) => {
              setLoading(false);
              goToDataConnect();
            })
            .catch((response) => {
              setLoading(false);
              goToDataConnect();
              return Promise.reject();
            });
        } else {
          setLoading(false);
          goToDataConnect();
        }
      } else {
        setLoading(false);
        goToDataConnect();
      }
    } catch (err) {
      setLoading(false);
      goToDataConnect();
      console.error('体重取得失敗:', err);
    } finally {
      setLoading(false);
    }
  };

  const getFitbitSleep = async (token: string, start: string, end: string, dateList: string[]) => {
    try {
      setLoading(true);
      const url = `${fitbit.query_url}sleep/date/${start}/${end}.json`;
      const res = await axios.get(url, { headers: { Authorization: `Bearer ${token}` } });
      // dateList.forEach((date, index) => {
      //   const { yesterday, today, yesterdayNoon, todayNoon } = getJapanTimeRange(date);
      //   const sleepList = res.data.sleep.filter(
      //     (log: FitbitSleepLog) =>
      //       log.startTime !== undefined &&
      //       log.startTime.replace('T', ' ').split('.')[0] >= yesterdayNoon &&
      //       log.startTime.replace('T', ' ').split('.')[0] <= todayNoon &&
      //       log.endTime !== undefined &&
      //       log.endTime.replace('T', ' ').split('.')[0] >= yesterdayNoon &&
      //       log.endTime.replace('T', ' ').split('.')[0] <= todayNoon,
      //   );

      //   const sleepData = new SleepData();
      //   sleepList.map((log: FitbitSleepLog) => {
      //     if (
      //       log.startTime !== undefined &&
      //       log.startTime < (sleepData.sleepAt === undefined ? '99' : sleepData.sleepAt)
      //     ) {
      //       sleepData.sleepAt = log.startTime;
      //     }

      //     if (
      //       log.endTime !== undefined &&
      //       log.endTime > (sleepData.wakeAt === undefined ? '00' : sleepData.wakeAt)
      //     ) {
      //       sleepData.wakeAt = log.endTime;
      //     }
      //   });
      //   if (sleepData.sleepAt !== undefined && sleepData.wakeAt !== undefined) {
      //     // sleepData.sleepTime = minuDiffDate(sleepData.sleepAt, sleepData.wakeAt);
      //     sleepData.sleepAt = sleepData.sleepAt.replace('T', ' ').split('.')[0];
      //     sleepData.wakeAt = sleepData.wakeAt.replace('T', ' ').split('.')[0];
      //     sleepData.measureDate = date.replaceAll('-', '');
      //     sleepData.sleepFrom = 6;
      //     resultList.push(sleepData);
      //   }
      // });
      const resultList = res.data.sleep
        ?.map((item: FitbitSleepLog) => {
          const measureDate = item.dateOfSleep;
          if (!measureDate) {
            return null;
          }
          const { yesterdayNoon, todayNoon } = getJapanTimeRange(measureDate);

          if (!item.startTime || !item.endTime) return false;
          const startTime = formatDateTime(item.startTime);
          const endTime = formatDateTime(item.endTime);
          //睡眠時間が昨日の１８時〜今日の１２時のデータのみ有効
          if (
            startTime >= yesterdayNoon &&
            startTime <= todayNoon &&
            endTime >= yesterdayNoon &&
            endTime <= todayNoon
          ) {
            const sleepData = new SleepData();
            let minStartTime = '';
            let maxEndTime = '';
            const startTime = formatDateTime(item.startTime);
            const endTime = formatDateTime(item.endTime);

            if (!minStartTime || startTime < minStartTime) {
              minStartTime = startTime;
            }

            if (!maxEndTime || endTime > maxEndTime) {
              maxEndTime = endTime;
            }
            if (minStartTime && maxEndTime) {
              sleepData.sleepAt = minStartTime;
              sleepData.wakeAt = maxEndTime;
              sleepData.measureDate = measureDate.replaceAll('-', '');
              sleepData.sleepFrom = 6;
              return sleepData;
            }
          } else {
            return null;
          }
        })
        .filter(Boolean) as SleepData[];
      //全部データ取得済の場合、睡眠情報をサーバへ送信する
      if (resultList.length > 0) {
        const data = new SyncVitalDataRequest();
        data.sleepData = resultList;
        dataConnectAPI
          .syncVitalData(data)
          .then((response) => {
            setLoading(false);
            goToDataConnect();
          })
          .catch((response) => {
            setLoading(false);
            goToDataConnect();
            return Promise.reject();
          });
      } else {
        setLoading(false);
        goToDataConnect();
      }
    } catch (err) {
      setLoading(false);
      goToDataConnect();
    }
  };

  //date1 date2:yyyy-MM-ddTHH:mm:ss.000
  function minuDiffDate(date1: string, date2: string) {
    const parseDate = (dateStr: string) => new Date(dateStr).getTime();
    return Math.abs(parseDate(date2) - parseDate(date1)) / (1000 * 60);
  }

  const setDiviceOff = async (deviceType: number, userId: string) => {
    const request = new ServiceSettingRequest();
    request.deviceType = deviceType;
    request.connectFlg = 0;
    request.changeUserId = userId;
    setLoading(true);
    dataConnectAPI
      .serviceSetting(request)
      .then((response) => {
        setLoading(false);
        //「連携失敗」Dialogをポップアップする。
        if (deviceType === DeviceType.Fitbit) {
          showOffDialog(APP_TEXT.DATA_SRCSET.FITBIT_OFF_TITLE);
        }
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };

  const showPermissionError = () => {
    setDialog(true, {
      content: (
        <div>
          <div className="flex justify-center items-center mb-4">
            <img className="h-16 w-16" alt="desc" src={bikuri_icon.src} />
          </div>
          <div className="text-[16px] font-bold mb-2">{APP_TEXT.DATA_SRCSET.PERMISSION_TITLE}</div>
          <div className="text-[14px] text-gray-400">
            {APP_TEXT.DATA_SRCSET.PERMISSION_CONTETNT}
          </div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="default"
            className="w-full font-bold shadow-primary! bg-primary text-white"
            onClick={() => {
              setDialog(false);
            }}
          >
            {APP_TEXT.DATA_SRCSET.PERMISSION_OK}
          </Button>
        </div>
      ),
    });
  };

  const showOffDialog = (title: string) => {
    setDialog(true, {
      content: (
        <div>
          <img className="h-16 w-16 mx-auto mb-2" alt="desc" src={fail_icon.src} />
          <div className="font-bold text-[15px] text-center">{title}</div>
          <div className="text-[13px]">{APP_TEXT.DATA_SRCSET.OFF_CONTENT}</div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="destructive"
            className="w-full bg-primary text-white"
            onClick={() => {
              setDialog(false);
              //データ連携設定画面へ遷移する
              goToDataConnect();
            }}
          >
            {APP_TEXT.DATA_SRCSET.CONFIRM_CLOSE}
          </Button>
        </div>
      ),
    });
  };

  const [selectedOption, setSelectedOption] = useState('0');
  const handleOptionChange = (value: string) => {
    setSelectedOption(value);
  };
  return (
    <div>
      <TopBar title={title} enableBack={false} enableClose={true} onClose={goToDataConnect} />
      <div className=" ">
        <div className="flex gap-4 flex-wrap">
          <ListRadio
            value={selectedOption}
            onChange={(value) => handleOptionChange(value)}
            options={radioList}
            className="gap-4 w-full mt-2"
          />
        </div>
      </div>
      <div className="fixed bottom-3 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box] mb-10">
        <Button
          className="bg-primary text-white disabled:bg-primary-light  font-bold h-12 ml-[20px] mr-[20px] rounded-3xl w-[88vw]"
          type="button"
          disabled={selectedOption === currentValue}
          onClick={decideSrc}
        >
          {APP_TEXT.DATA_SRCSET.BTN_DATA_CONNECT}
        </Button>
      </div>
    </div>
  );
}

export default DataSrcSet;
