// device-data-sync.service.ts
import {
  SleepData,
  SyncVitalDataRequest,
  WeightData,
  dataConnectAPI,
} from '@/api/modules/data-connect';
import { DataSourceDeviceType, VitalType, fitbit } from '@/const/app';
import { APP_TEXT } from '@/const/text/app';
import type { FitbitSleepLog, FitbitWeightEntry, VitalFromResponse } from '@/types/data-connect';
import {
  formatDateTime,
  formatDateYMD,
  generateDateRange,
  getJapanTimeRange,
  getYesterdayNoonByDate,
} from '@/utils/date-format';
import { isAndroid } from '@/utils/device-detect';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import axios from 'axios';

export class DeviceDataSyncService {
  private weightList: WeightData[] = [];
  private sleepList: SleepData[] = [];

  timer = null;

  /**
   * 同じ量の健康特性
   * @param isSyncStep
   * @returns Promise<void>
   */
  public async syncAllData(isSyncStep: number): Promise<void> {
    try {
      const response = await dataConnectAPI.getVitalFrom();
      if (isSyncStep === 1) {
        //承認が完了し、アップロード手順が完了しました
        await new Promise<void>((resolve) => {
          sendMessageToNative({
            type: 'sync-steps',
            callback: (data) => {
              // コールバックの実行完了を待っています
              resolve();
            },
          });
        });
      }
      nlog(`sync-step ${JSON.stringify(response)}`);
      await this.fetchAndCombineData(response);
    } catch (error) {
      console.log('Data sync failed:', error);
      throw error;
    }
  }

  private async fetchAndCombineData(response: VitalFromResponse): Promise<void> {
    try {
      const queryPermissions: string[] = [];
      const dateMap: string[] = [];
      const deviceTypeMap: string[] = [];

      await Promise.all([
        //weight
        this.processVitalData(
          response.weightFrom,
          VitalType.Weight,
          response.queryWeightStartDate || '',
          queryPermissions,
          dateMap,
          deviceTypeMap,
        ),
        //sleep
        this.processVitalData(
          response.sleepFrom,
          VitalType.Sleep,
          response.querySleepStartDate || '',
          queryPermissions,
          dateMap,
          deviceTypeMap,
        ),
        //bloodpress
        this.processVitalData(
          response.bloodPressureFrom,
          VitalType.Blood,
          response.queryBPStartDate || '',
          queryPermissions,
          dateMap,
          deviceTypeMap,
        ),
      ]);

      if (queryPermissions.length > 0) {
        if (isAndroid()) {
          await this.syncHealthToNativeSingle(
            queryPermissions.join(','),
            DataSourceDeviceType.HealthConnect,
            dateMap.join(','),
          );
        } else {
          await this.syncHealthToNativeMulti(
            queryPermissions.join(','),
            deviceTypeMap.join(','),
            dateMap.join(','),
          );
        }
      }
    } catch (error) {
      console.log('Failed to combine data:', error);
      throw error;
    }
  }

  private async processVitalData(
    source: number | undefined,
    vitalType: string,
    date: string,
    queryPermissions: string[],
    dateMap: string[],
    deviceTypeMap: string[],
  ): Promise<void> {
    //手動入力状況は直接返されます
    if (source === DataSourceDeviceType.input) {
      return;
    }
    if (
      source === DataSourceDeviceType.HealthCare ||
      source === DataSourceDeviceType.AppleWatch ||
      source === DataSourceDeviceType.HealthConnect
    ) {
      queryPermissions.push(vitalType);
      dateMap.push(date);
      deviceTypeMap.push(String(source));
    } else {
      if (source !== undefined) {
        this.getDeviceData(vitalType, source, date);
      }
    }
  }

  private async getDeviceData(vitalType: string, deviceType: number, date: string): Promise<void> {
    console.log(date);
    switch (deviceType) {
      case DataSourceDeviceType.Fitbit:
        await this.updateFitbitToken(vitalType, date);
        break;
      case DataSourceDeviceType.Omron:
        if (vitalType === VitalType.Weight || vitalType === VitalType.Blood) {
          await this.syncHealthToNativeSingle(vitalType, deviceType, date);
        }
        break;
      default:
        console.warn(`Unsupported device type: ${deviceType}`);
    }
  }

  private async syncHealthToNativeSingle(
    vitalType: string,
    deviceType: number,
    date?: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      sendMessageToNative({
        type: 'sync-health',
        data: { vitalType: vitalType, deviceType: deviceType, date: date },
        callback: (data) => {
          if (!data) {
            resolve();
            return;
          }
          try {
            const request = new SyncVitalDataRequest();
            if (data.weightData || data.sleepData || data.bloodPressureData) {
              request.weightData = data.weightData || [];
              request.sleepData = data.sleepData || [];
              request.bloodPressureData = data.bloodPressureData || [];
              dataConnectAPI
                .syncVitalData(request) //
                .then((response) => {
                  resolve();
                })
                .catch((response) => {
                  resolve();
                });
            } else {
              resolve();
            }
          } catch (error) {
            reject(error);
          }
        },
      });
    });
  }

  private async syncHealthToNativeMulti(
    vitalType: string,
    deviceType: string,
    date?: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(APP_TEXT.HEALTH_RECORD_PAGE.SYNC_TIMEOUT);
        return;
      }, 20000);

      sendMessageToNative({
        type: 'sync-health',
        data: { vitalType: vitalType, deviceType: deviceType, date: date },
        callback: (data) => {
          clearTimeout(timer);
          if (!data) {
            resolve();
            return;
          }
          try {
            const request = new SyncVitalDataRequest();
            if (data.weightData || data.sleepData || data.bloodPressureData) {
              request.weightData = data.weightData || [];
              request.sleepData = data.sleepData || [];
              request.bloodPressureData = data.bloodPressureData || [];
              dataConnectAPI
                .syncVitalData(request) //
                .then((response) => {
                  resolve();
                })
                .catch((response) => {
                  resolve();
                });
            } else {
              resolve();
            }
          } catch (error) {
            reject(error);
          }
        },
      });
    });
  }

  private async updateFitbitToken(vitalType: string, date: string): Promise<void> {
    try {
      const response = await dataConnectAPI.getFitbitToken();
      if (!response.accessToken) {
        throw new Error('No access token received');
      }

      if (vitalType === VitalType.Weight) {
        await this.getFitbitWeight(response.accessToken, date);
      } else if (vitalType === VitalType.Sleep) {
        await this.getFitbitSleep(response.accessToken, date);
      }
    } catch (error) {
      console.log('Fitbit token update failed:', error);
      throw error;
    }
  }

  private async getFitbitWeight(token: string, date: string): Promise<void> {
    const dateList = generateDateRange(date);
    const start = dateList[0];
    const end = dateList[dateList.length - 1];

    try {
      const res = await axios.get(
        `${fitbit.query_url}body/log/weight/date/${start}/${end}.json?timezone=Asia/Tokyo`,
        { headers: { Authorization: `Bearer ${token}` } },
      );
      console.log(res.data.weight.length);
      nlog(`getFitbitWeight ${JSON.stringify(res.data.weight)}`);
      if (res.data.weight?.length > 0) {
        const weights = dateList
          .map((date) => {
            const entries = res.data.weight.filter((w: FitbitWeightEntry) => w.date === date);
            if (entries.length === 0) return null;

            const latest = entries.reduce((prev: FitbitWeightEntry, current: FitbitWeightEntry) =>
              new Date(prev.date) > new Date(current.date) ? prev : current,
            );

            const data = new WeightData();
            data.bodyFrom = DataSourceDeviceType.Fitbit;
            data.measureDate = date.replaceAll('-', '');
            data.weight = `${latest.weight}`;
            if (latest.fat || latest.fat !== undefined) {
              data.fatPercentage = `${latest.fat}`;
            }
            return data;
          })
          .filter(Boolean) as WeightData[];

        if (weights.length > 0) {
          this.weightList = [...this.weightList, ...weights];
          const request = new SyncVitalDataRequest();
          request.weightData = this.weightList;
          await dataConnectAPI.syncVitalData(request, false);
        }
      }
    } catch (error) {
      console.log('Failed to get Fitbit weight:', error);
      throw error;
    }
  }

  private async getFitbitSleep(token: string, date: string): Promise<void> {
    const dateList = generateDateRange(date);
    let start = dateList[0];
    const end = dateList[dateList.length - 1];
    start = formatDateYMD(getYesterdayNoonByDate(start));

    try {
      //fitbitは睡眠データを検索します
      const res = await axios.get(`${fitbit.query_url}sleep/date/${start}/${end}.json`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      nlog(`getFitbitSleep ${JSON.stringify(res.data.sleep)}`);
      const resultList = res.data.sleep
        ?.map((item: FitbitSleepLog) => {
          const measureDate = item.dateOfSleep;
          if (!measureDate) {
            return null;
          }
          const { yesterdayNoon, todayNoon } = getJapanTimeRange(measureDate);

          if (!item.startTime || !item.endTime) return false;
          const startTime = formatDateTime(item.startTime);
          const endTime = formatDateTime(item.endTime);
          //睡眠時間が昨日の１８時〜今日の１２時のデータのみ有効
          if (
            startTime >= yesterdayNoon &&
            startTime <= todayNoon &&
            endTime >= yesterdayNoon &&
            endTime <= todayNoon
          ) {
            const sleepData = new SleepData();
            let minStartTime = '';
            let maxEndTime = '';
            const startTime = formatDateTime(item.startTime);
            const endTime = formatDateTime(item.endTime);
            //整理睡眠時間をオーバーします
            if (!minStartTime || startTime < minStartTime) {
              minStartTime = startTime;
            }

            if (!maxEndTime || endTime > maxEndTime) {
              maxEndTime = endTime;
            }
            if (minStartTime && maxEndTime) {
              sleepData.sleepAt = minStartTime;
              sleepData.wakeAt = maxEndTime;
              sleepData.measureDate = measureDate.replaceAll('-', '');
              //6: fitbit
              sleepData.sleepFrom = DataSourceDeviceType.Fitbit;
              return sleepData;
            }
          } else {
            return null;
          }
        })
        .filter(Boolean) as SleepData[];
      if (resultList.length > 0) {
        this.sleepList = [...this.sleepList, ...resultList];
        const request = new SyncVitalDataRequest();
        request.sleepData = this.sleepList;
        await dataConnectAPI.syncVitalData(request, false);
      }
    } catch (error) {
      console.log('Failed to get Fitbit sleep:', error);
      throw error;
    }
  }
}
