'use client';

import TopBar from '@/components/layout/top-bar';
import { omron } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useRouter } from '@/hooks/use-next-navigation';
import { OmronCard } from '../_components/omron-card-props';
export default function OmronPage() {
  const initialData = [
    { id: 1, label: '体重' },
    { id: 2, label: '睡眠時間' },
  ];
  const router = useRouter();
  const close = () => {
    router.back();
  };
  const handleSubmit = () => {
    // const clientId = '23QJLN';
    //const YOUR_CLIENT_SECRET = '79dcd0836d157df745499a2917f839ae';
    const redirectUri = encodeURIComponent(
      `http://localhost:4399${ROUTES.DATA_CONNECT.OMRON_FINISH}`,
    );
    ('https://data-stg-jp-sp.omronconnect.mobi/app/oauth2-frontend/login-dsp/bdf72f34?response_type=code&client_id=7fa4hp1e1s0b34d8habmropio9&redirect_uri=https:%2F%2Fdata-stg-jp-sp.omronconnect.mobi%2Fapi%2Fapps%2Fb2bgroup%2Fintegration%2Fwebauth%2Fcallback&state=ixqqya3qluw2eatf9hqoayyzq&scope=openid%20scope_for_b2b_groups&master_app_key=dummy%23%2Fsignin&result_url=https:%2F%2Fstg5-api.kenko-mileage.com%2Fomron&group_name=HANYO-STG5&group_id=2de9aec01f15');
    const oauthUrl = `${omron.login.domain}/${omron.group.id}/integration/webauth/connect?id=openidconnect.simple&scope=openid&state=display_back&result_url=${omron.login.call_back_uri}`;

    //Omron·を連携する
    window.location.href = oauthUrl;
  };
  return (
    <div className="flex flex-col">
      <TopBar
        title={APP_TEXT.OMRON.TITLE_BAR}
        enableBack={true}
        enableClose={true}
        onClose={close}
      />
      <OmronCard
        title={APP_TEXT.OMRON.CONTENT}
        dataItems={initialData}
        onSubmit={() => handleSubmit()}
      />
    </div>
  );
}
