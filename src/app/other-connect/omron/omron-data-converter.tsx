import { useEffect, useState } from 'react';

// データ型の定義
interface WeightData {
  measureDate: string;
  weight: number;
  fatPercentage: number;
  bodyFrom: number;
}

interface BloodPressureData {
  measureDate: string;
  bpAmMeasureAt: string;
  highBpAm: number;
  lowBpAm: number;
  bpPmMeasureAt: string;
  highBpPm: number;
  lowBpPm: number;
  bloodPressureFrom: number;
}

interface HealthData {
  weightData: WeightData[];
  bloodPressureData: BloodPressureData[];
}

const OmronDataConverter = () => {
  const [healthData, setHealthData] = useState<HealthData>({
    weightData: [],
    bloodPressureData: [],
  });

  // 元データ
  const rawData = {
    NextSequenceNo: 1,
    PartnerList: [
      {
        PartnerId: 'yyyyyyyy-yyyy-yyyy-yyyy-yyyyyyyyyyyy',
        PartnerName: 'myパートナ001',
        GroupList: [
          {
            GroupId: 'zzzzzzzzzzzz',
            GroupName: 'myグループ001',
            UserList: [
              {
                UserId: '5b6feb5cac80-04c9-7e11-03fd-0b5c9aff',
                deviceCategoryList: [
                  {
                    deviceCategory: '0',
                    deviceModelList: [
                      {
                        deviceModel: 'HEM-7280T-AP',
                        deviceSerialIDList: [
                          {
                            deviceSerialID: '0001009400040e0b1b39300054aa',
                            userNumberInDevice: 1,
                            measureList: [
                              {
                                bodyIndexList: {
                                  '1': ['98', '20496', '0', '4'],
                                  '10': ['0', '0', '0', '4'],
                                },
                                deleteFlag: 0,
                                measureDateFrom: 1500272810004,
                                measureDateTo: 1500272810000,
                                measureDeviceDateFrom: '20170717142650000',
                                measureDeviceDateTo: '20170717142650000',
                                timeZone: 'Asia/Shanghai',
                                transferDate: 1500272823000,
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  };

  // データ解析
  useEffect(() => {
    const parseData = () => {
      const parsedData: HealthData = {
        weightData: [],
        bloodPressureData: [],
      };

      // ユーザーデータの抽出
      const userData = rawData.PartnerList[0].GroupList[0].UserList[0];
      const deviceData = userData.deviceCategoryList[0];

      // デバイス情報の解析
      const deviceCategory = deviceData.deviceCategory;
      const measureList = deviceData.deviceModelList[0].deviceSerialIDList[0].measureList;

      for (const measure of measureList) {
        if (deviceCategory === '0') {
          // 血圧データ
          const bpData = parseBloodPressure(measure);
          if (bpData) parsedData.bloodPressureData.push(bpData);
        } else if (deviceCategory === '1') {
          // 体重データ
          const weightData = parseWeightData(measure);
          if (weightData) parsedData.weightData.push(weightData);
        }
      }

      setHealthData(parsedData);
    };

    parseData();
  }, []);

  // 血圧データの解析
  const parseBloodPressure = (measure: any): BloodPressureData | null => {
    const bodyIndex = measure.bodyIndexList['1'];
    if (!bodyIndex) return null;

    const timestamp = measure.measureDateFrom;
    const date = new Date(timestamp);
    const measureDate = formatDateToYYYYMMDD(timestamp);
    const measureTime = formatTimeToHHMM(date);

    return {
      measureDate,
      bpAmMeasureAt: measureTime,
      highBpAm: Number.parseInt(bodyIndex[0]),
      lowBpAm: Number.parseInt(bodyIndex[1]),
      highBpPm: 0,
      lowBpPm: 0,
      bpPmMeasureAt: '15:00', // 午後の測定時間を15:00と仮定
      bloodPressureFrom: measure.deivceType, //TODO 送信方式
    };
  };
  const parseWeightData = (measure: any): WeightData | null => {
    const bodyIndex = measure.bodyIndexList['2'] || measure.bodyIndexList['3'];
    if (!bodyIndex) return null;

    const timestamp = measure.measureDateFrom;
    const measureDate = formatDateToYYYYMMDD(timestamp);

    return {
      measureDate,
      weight: Number.parseFloat(bodyIndex[0]), // weight（kg）
      fatPercentage: Number.parseFloat(bodyIndex[1]), // fat（%）
      bodyFrom: 1, // データソース（1=Omronデバイス） //
    };
  };
  const formatDateToYYYYMMDD = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // start 0，need+1
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}${month}${day}`;
  };
  const formatTimeToHHMM = (date: Date): string => {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${hours}:${minutes}`;
  };
};
