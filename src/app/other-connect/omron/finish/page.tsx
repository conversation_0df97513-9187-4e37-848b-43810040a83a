'use client';
import {
  OmronRequest,
  ServiceSettingRequest,
  dataConnectAPI,
  getOneUserVitalData,
} from '@/api/modules/data-connect';
import TopBar from '@/components/layout/top-bar';
import { DeviceType } from '@/const/app';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useCallback, useEffect, useState } from 'react';
import fail_icon from '../../../../images/fail.png';
import success_icon from '../../../../images/success.png';

function FitbitFinish() {
  const router = useRouter();
  const { setLoading } = useLoading();

  const [success, setSuccess] = useState('');

  const goToDataConnect = async () => {
    router.push(ROUTES.DATA_CONNECT.MAIN);
  };
  const sendToServer = async (deviceType: number) => {
    const request = new ServiceSettingRequest();
    request.deviceType = deviceType;
    request.connectFlg = 1;
    setLoading(true);
    dataConnectAPI
      .serviceSetting(request)
      .then((response) => {
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };
  const getOmronToken = async () => {
    setLoading(true);
    dataConnectAPI
      .getOmronToken()
      .then((response) => {
        setLoading(false);
        if (response?.accessToken && response?.accountId) {
          getOneUserVitalData(response.accessToken, response.accountId);
        }
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };
  //callback url: https://stg5-portal.kenko-mileage.com/omron?provider_user_id=53ed980e-6e9b-4c54-a62c-5b753e73f43f&kii_succeeded=true&code=d0751e65-b43a-4eac-811a-7ca52d6e1d31
  useEffect(() => {
    const userId = new URLSearchParams(window.location.search).get('provider_user_id');
    const scope = encodeURIComponent('activity weight sleep');
    if (userId) {
      setLoading(true, { text: '連携認証中...' });
      const request = new OmronRequest();
      request.accountId = userId;
      // request.scope = scope;
      dataConnectAPI
        .omronRedirect(request)
        .then((response) => {
          setSuccess('1');
          setLoading(false);
          sendToServer(DeviceType.Omron);
          getOmronToken();
        })
        .catch((response) => {
          setLoading(false);
          setSuccess('0');
          return Promise.reject();
        });
    } else {
      setLoading(false);
    }
  }, [setSuccess]);
  return (
    <div>
      <TopBar
        title={APP_TEXT.OMRON_FINISH.TITLE}
        enableBack={false}
        enableClose={true}
        onClose={goToDataConnect}
      />
      <div className="min-h-screen flex justify-center items-center h-screen pb-32">
        {success === '1' && (
          <div className="p-4 rounded">
            <img className="h-16 w-16 mx-auto mb-2" alt="desc" src={success_icon.src} />
            <div className="text-[20px] text-center font-bold mb-2">
              {APP_TEXT.OMRON_FINISH.SUCCESS}
            </div>
            <div className="text-[15px]">{APP_TEXT.OMRON_FINISH.SUCCESS_DESC}</div>
          </div>
        )}

        {success === '0' && (
          <div className="p-4 rounded">
            <img className="h-16 w-16 mx-auto mb-2" alt="desc" src={fail_icon.src} />
            <div className="text-[20px] text-center font-bold mb-2">
              {APP_TEXT.OMRON_FINISH.FAIL}
            </div>
            <div className="text-[15px]">{APP_TEXT.OMRON_FINISH.FAIL_DESC}</div>
          </div>
        )}
      </div>
      {/* <div className="fixed bottom-3 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]">
        <button
          className="bg-primary disabled:bg-primary-light text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
          type="button"
          onClick={goToDataConnect}
        >
          {APP_TEXT.OMRON_FINISH.BTN_DATA_CONNECT}
        </button>
      </div> */}
      {success === '' && (
        <div className="fixed bottom-3 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]  mb-10">
          <button
            className="bg-primary disabled:bg-primary-light text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
            type="button"
            onClick={goToDataConnect}
          >
            {APP_TEXT.OMRON_FINISH.BTN_DATA_CONNECT}
          </button>
        </div>
      )}
    </div>
  );
}

export default FitbitFinish;
