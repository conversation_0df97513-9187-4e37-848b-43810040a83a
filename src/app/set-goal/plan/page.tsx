'use client';

import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { DialogClose } from '@/components/shared/dialog';
import { TextButton } from '@/components/shared/text-button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ROUTES } from '@/const/routes';
import { COMMON_TEXT } from '@/const/text/common';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useEffect, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';
import image from '../../../images/hatena.png';

function SetGoalPlan() {
  const [plan, setPlan] = useState<string | undefined>('');
  const [originPlan, setOriginPlan] = useState<string | undefined>('');
  const { isShow, setDialog } = useMessageDialog();
  const searchParams = useSearchParams();
  useEffect(() => {
    if (searchParams != null) {
      const encodedData = searchParams.get('data') || '';
      if (encodedData.length !== 0) {
        const params = JSON.parse(atob(encodedData));
        const item = params.item;
        if (item != null) {
          setPlan(String(item));
          setOriginPlan(String(item));
        }
      }
    }
  }, [searchParams]);

  const router = useRouter();

  // const [openDesc, setOpenDesc] = useState(false);

  const showPopUp = async () => {
    setDialog(true, {
      content: (
        <div className="flex flex-col gap-[8px]]">
          <div className="font-bold text-[22px] text-center">
            {APP_TEXT.SET_GOAL_PLAN.POPUP_TITLE}
          </div>
          <div className="mb-2">{APP_TEXT.SET_GOAL_PLAN.POPUP_CONTENT}</div>
        </div>
      ),
      outSideClickClose: false,
      footer: (
        <div className="flex-col ">
          <Button
            variant="default"
            className="w-full mb-2"
            onClick={() => {
              setDialog(false);
            }}
          >
            {COMMON_TEXT.BUTTON.CLOSE}
          </Button>
        </div>
      ),
    });
  };

  const goNext = async () => {
    const params = {
      item: originPlan,
    };
    const paramsStr = btoa(JSON.stringify(params));
    if (plan === '1') {
      router.push(`${ROUTES.GOAL.RECMMEND}?data=${paramsStr}`);
    } else if (plan === '2') {
      router.push(`${ROUTES.GOAL.PLUSTEN}?data=${paramsStr}`);
    } else if (plan === '3') {
      router.push(`${ROUTES.GOAL.CUSTOM}?data=${paramsStr}`);
    }
  };

  const goBack = async () => {
    router.back();
  };

  const handleValueChange = (value: string) => {
    setPlan(value);
  };

  return (
    <div>
      <TopBar
        title={APP_TEXT.SET_GOAL_PLAN.TITLE}
        onBack={goBack}
        enableBack={true}
        enableClose={true}
        onClose={goBack}
      />
      <div className="flex flex-col items-start min-h-[calc(100vh-10rem)]">
        <div>
          <RadioGroup value={plan} onValueChange={handleValueChange} className="mb-24">
            <div
              className={`w-full mb-[10px] p-[15px] flex items-center rounded-[16px] border ${plan === '1' ? 'border-primary border-2' : 'border-gray-400'}`}
            >
              <div key="rec" className="checkbox-option mr-1">
                <RadioGroupItem value="1" id="rec" />
              </div>
              <div className="flex-1 ml-[10px]">
                <label className="text-[20px] font-bold" htmlFor="rec">
                  {APP_TEXT.SET_GOAL_PLAN.RECOMMEND_TITLE}
                </label>
                <label className="text-[16px] text-gray-700" htmlFor="rec">
                  <br />
                  {APP_TEXT.SET_GOAL_PLAN.RECOMMEND_CONTENT}
                </label>
              </div>
            </div>
            <div
              className={`w-full mb-[10px] p-[15px] flex items-center rounded-[16px] border ${plan === '2' ? 'border-primary  border-2' : 'border-gray-400'}`}
            >
              <div key="plusten" className="checkbox-option mr-1">
                <RadioGroupItem value="2" id="plusten" />
              </div>
              <div className="flex-1 ml-[10px]">
                <label className="text-[20px] font-bold" htmlFor="plusten">
                  {APP_TEXT.SET_GOAL_PLAN.PLUSTEN_TITLE}
                  {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                  <img
                    alt="desc"
                    className="w-6 h-6 inline-block mb-2"
                    src={image.src}
                    onClick={showPopUp}
                  />
                </label>
                <label className="text-[16px] text-gray-700" htmlFor="plusten">
                  <br />
                  {APP_TEXT.SET_GOAL_PLAN.PLUSTEN_CONTENT}
                </label>
              </div>
            </div>
            <div
              className={`w-full mb-[10px] p-[15px] flex items-center rounded-[16px] border ${plan === '3' ? 'border-primary border-2' : 'border-gray-400'}`}
            >
              <div key="custom" className="checkbox-option mr-1">
                <RadioGroupItem value="3" id="custom" />
              </div>
              <div className="flex-1 ml-[10px]">
                <label className="text-[20px] font-bold" htmlFor="custom">
                  {APP_TEXT.SET_GOAL_PLAN.CUSTERM_TITLE}
                </label>
                <label className="text-[16px] text-gray-700" htmlFor="custom">
                  <br />
                  {APP_TEXT.SET_GOAL_PLAN.CUSTERM_CONTENT}
                </label>
              </div>
            </div>
          </RadioGroup>
          <div className="fixed bottom-12 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]">
            <Button
              className="bg-primary text-white disabled:bg-primary-light  font-bold h-12 ml-[20px] mr-[20px] rounded-3xl w-[88vw]"
              type="button"
              disabled={plan === ''}
              onClick={goNext}
            >
              {APP_TEXT.SET_GOAL_PLAN.NEXT}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SetGoalPlan;
