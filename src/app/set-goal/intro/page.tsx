'use client';

import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { useRouter } from '@/hooks/use-next-navigation';
import { APP_TEXT } from '../../../const/text/app';
import image from '../../../images/intro.png';
function SetGoalIntro() {
  const router = useRouter();

  const goNext = async () => {
    router.push(ROUTES.GOAL.PLAN);
  };

  const goBack = async () => {
    if (router.includePathInHistory(ROUTES.GOAL.HEALTH_RECORD)) {
      router.backTo(ROUTES.GOAL.HEALTH_RECORD);
      return;
    }
    if (router.includePathInHistory(ROUTES.MENU.SETTINGS)) {
      router.backTo(ROUTES.MENU.SETTINGS);
      return;
    }
    if (router.includePathInHistory(ROUTES.MENU.MENU)) {
      router.backTo(ROUTES.MENU.MENU);
      return;
    }
    router.replace(ROUTES.HOME);
  };

  return (
    <div>
      <TopBar
        title={APP_TEXT.SET_GOAL_INTRO.TITLE}
        enableBack={false}
        enableClose={true}
        onClose={goBack}
      />
      <div className="flex flex-col items-start min-h-[calc(100vh-10rem)]">
        <div>
          <div className="ml-5 mr-5">
            <img alt="desc" src={image.src} />
          </div>
          <div className="mt-[25px] text-[22px] font-bold text-center mb-2">
            {APP_TEXT.SET_GOAL_INTRO.SUBTITLE}
          </div>
          <div className="text-[18px] mb-24">{APP_TEXT.SET_GOAL_INTRO.TEXT}</div>
          <div className="fixed bottom-12 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]">
            <Button
              className="bg-primary text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
              type="button"
              onClick={goNext}
            >
              {APP_TEXT.SET_GOAL_INTRO.NEXT}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SetGoalIntro;
