'use client';

import { StepTargetRequest, stepGoalAPI } from '@/api/modules/step-goal';
import { userAPI } from '@/api/modules/user';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { Select } from '@/components/shared/select';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { APP_TEXT } from '../../../const/text/app';
function SetGoalRec() {
  const { setLoading } = useLoading();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [plan, setPlan] = useState<string | undefined>('');
  const [originStepTarget, setOriginStepTarget] = useState<number | undefined>(0);
  const [stepTarget, setStepTarget] = useState<number>(0);
  const [sex, setSex] = useState<number>(0);
  const [age, setAge] = useState<number>(0);

  const [sexEnable, setSexEnable] = useState<boolean>(true);
  const [ageEnable, setAgeEnable] = useState<boolean>(true);

  const [sexChoice, setSexChoice] = useState<string>('');
  const [ageChoice, setAgeChoice] = useState<string>('');

  const [isBack, setIsBack] = useState<boolean>(false);
  const goNext = async () => {
    //歩数目標登録API を呼び出す
    registSteps(stepTarget);
  };

  const goBack = async () => {
    router.back();
  };

  const calcAge = (birthAt: string | undefined) => {
    let age = 0;
    if (birthAt !== undefined && birthAt !== '') {
      const birthDay = new Date(birthAt === undefined ? '' : birthAt);
      const today = new Date();

      // 精确年龄计算
      age = today.getFullYear() - birthDay.getFullYear();
      const monthDiff = today.getMonth() - birthDay.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDay.getDate())) {
        age--;
      }
    }
    age = Math.floor(age / 10) * 10;
    return age;
  };

  const calcSteps = (birthAt: string | undefined, sex: number | undefined) => {
    const age = calcAge(birthAt);
    if (age > 0) {
      setAgeEnable(false);
    }
    setAge(age);
    setAgeChoice(String(age));

    return calcStepByAge(age, sex);
  };

  const goBackMain = async () => {
    if (plan === null || plan === '') {
      router.push(ROUTES.GOAL.INTRO);
    } else {
      router.push(ROUTES.GOAL.GOAL);
    }
  };

  const registSteps = useCallback(
    (stepTarget: number) => {
      setLoading(true, { text: 'データを通信中...' });
      const request = new StepTargetRequest();
      request.stepTarget = stepTarget;
      request.targetPlan = 1;
      stepGoalAPI
        .setStepTargetInfo(request)
        .then((response) => {
          setLoading(false);
          toast.success('目標を設定しました');
          //歩数目標へ遷移する
          router.push(ROUTES.GOAL.GOAL);
        })
        .catch((error) => {
          console.log(error);
          setLoading(false);
          return Promise.reject();
        });
    },
    [router, setLoading],
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const fetchUser = useCallback(() => {
    setLoading(true, { text: 'データを通信中...' });
    userAPI
      .getUser()
      .then((response) => {
        setLoading(false);
        if (response !== null) {
          if (response.sex !== null && response.sex !== undefined) {
            setSex(response.sex);
            setSexChoice(String(response.sex));
            setSexEnable(false);
          }

          const steps = calcSteps(response?.birthDt, response.sex);
          if (steps !== undefined && steps > 0) {
            setOriginStepTarget(steps);
            setStepTarget(steps);
          }
        }
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        return Promise.reject();
      });
  }, [setLoading]);

  useEffect(() => {
    fetchUser();
    if (searchParams != null) {
      const isBack = searchParams.get('isBack') || '';
      // ボタンの文言
      if (isBack) {
        setIsBack(true);
      }
      const encodedData = searchParams.get('data') || '';
      if (encodedData.length > 0) {
        const params = JSON.parse(atob(encodedData));
        const item = params.item;
        if (item != null) {
          setPlan(item);
        }
      }
    }
  }, [fetchUser, searchParams]);

  const sexs = [
    { value: '1', name: APP_TEXT.SET_GOAL_REC.MAN },
    { value: '2', name: APP_TEXT.SET_GOAL_REC.WOMAN },
  ];

  const ages = [
    { value: '10', name: APP_TEXT.SET_GOAL_REC.AGE10 },
    { value: '20', name: APP_TEXT.SET_GOAL_REC.AGE20 },
    { value: '30', name: APP_TEXT.SET_GOAL_REC.AGE30 },
    { value: '40', name: APP_TEXT.SET_GOAL_REC.AGE40 },
    { value: '50', name: APP_TEXT.SET_GOAL_REC.AGE50 },
    { value: '60', name: APP_TEXT.SET_GOAL_REC.AGE60 },
    { value: '70', name: APP_TEXT.SET_GOAL_REC.AGE70 },
    { value: '80', name: APP_TEXT.SET_GOAL_REC.AGE80 },
  ];

  function calcStepByAge(age: number, sex: number | undefined) {
    let steps = 0;
    if (age === 0 || sex === 0) {
      return steps;
    }
    if (age < 60) {
      //男性
      if (sex === 1) {
        steps = 8000;
      } else if (sex === 2) {
        //女性
        steps = 7000;
      }
    } else if (age < 70) {
      //男性
      if (sex === 1) {
        steps = 7000;
      } else if (sex === 2) {
        //女性
        steps = 6000;
      }
    } else {
      //男性
      if (sex === 1) {
        steps = 5000;
      } else if (sex === 2) {
        //女性
        steps = 4500;
      }
    }
    return steps;
  }

  const onSelectSex = async (value: string) => {
    setSex(Number(value));
    setSexChoice(value);
    const tempSex = Number(value);
    const steps = calcStepByAge(age, tempSex);
    setStepTarget(steps);
  };
  const onSelectAge = async (value: string) => {
    setAge(Number(value));
    setAgeChoice(value);
    const tempAge = Number(value);
    const steps = calcStepByAge(tempAge, sex);
    setStepTarget(steps);
  };

  return (
    <div>
      <TopBar
        title={APP_TEXT.SET_GOAL_REC.TITLE}
        onBack={goBack}
        enableBack={true}
        enableClose={true}
        onClose={goBackMain}
      />
      <div className="flex flex-col items-start min-h-[calc(100vh-10rem)]">
        <div>
          <div className="text-[17px]">{APP_TEXT.SET_GOAL_REC.SUBTITLE}</div>
          <div className="flex gap-4 mt-4 mb-6">
            <span className="w-[45%] ">
              <div>{APP_TEXT.SET_GOAL_REC.SEX}</div>
              <Select
                defaultValue={sexChoice}
                options={sexs}
                disabled={!sexEnable}
                title={APP_TEXT.SET_GOAL_REC.SEX}
                onSelect={(value) => {
                  onSelectSex(value);
                }}
              />
            </span>
            <span className="w-[45%]">
              <div>{APP_TEXT.SET_GOAL_REC.AGE}</div>
              <Select
                defaultValue={ageChoice}
                options={ages}
                disabled={!ageEnable}
                title={APP_TEXT.SET_GOAL_REC.AGE}
                onSelect={(value) => {
                  onSelectAge(value);
                }}
              />
            </span>
          </div>
          <div className="w-full mb-[18px] p-[15px] flex items-center rounded-[16px] bg-primary-5">
            <div className="flex-1 ml-[10px]">
              <div className="text-[17px]">{APP_TEXT.SET_GOAL_REC.DATA_TITLE}</div>
              <div className="text-[16px] text-gray-700 text-end">
                <span className="mr-2 font-bold text-primary text-[26px]">
                  {stepTarget === 0
                    ? '-'
                    : stepTarget.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                </span>
                {APP_TEXT.SET_GOAL_REC.UNIT}
              </div>
            </div>
          </div>
          <div className="text-[15px] text-gray-500">{APP_TEXT.SET_GOAL_REC.TEXT}</div>
          <div className="text-[15px] text-gray-500">
            {APP_TEXT.SET_GOAL_REC.DATASRC}
            <span>〜〜〜〜〜〜</span>
          </div>
          <div className="fixed bottom-12 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]">
            <Button
              className="bg-primary  text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
              type="button"
              disabled={stepTarget === 0}
              onClick={goNext}
            >
              {isBack ? APP_TEXT.SET_GOAL_REC.NEXT_CHANGE : APP_TEXT.SET_GOAL_REC.NEXT}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SetGoalRec;
