'use client';

import { StepTargetRequest, stepGoalAPI } from '@/api/modules/step-goal';
import TopBar from '@/components/layout/top-bar';
import { Button } from '@/components/shared/button';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useCallback, useEffect, useState } from 'react';
import { APP_TEXT } from '../../../const/text/app';
function SetGoalPlusTen() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const searchParams = useSearchParams();
  const [plan, setPlan] = useState<string | undefined>('');
  const [originStepTarget, setOriginStepTarget] = useState<number | undefined>(0);
  const [stepTarget, setStepTarget] = useState<number>(0);

  const registSteps = useCallback(
    (stepTarget: number) => {
      setLoading(true, { text: 'データを通信中...' });
      const request = new StepTargetRequest();
      request.stepTarget = stepTarget;
      request.targetPlan = 2;
      stepGoalAPI
        .setStepTargetInfo(request)
        .then((response) => {
          setLoading(false);
          //歩数目標へ遷移する
          router.push(ROUTES.GOAL.GOAL);
        })
        .catch((error) => {
          console.log(error);
          setLoading(false);
          return Promise.reject();
        });
    },
    [router, setLoading],
  );

  const goNext = async () => {
    //歩数目標登録API を呼び出す
    if (stepTarget !== undefined) {
      registSteps(stepTarget);
    }
  };

  const goBack = async () => {
    router.back();
  };

  const goBackMain = async () => {
    if (plan === null || plan === '') {
      router.push(ROUTES.GOAL.INTRO);
    } else {
      router.push(ROUTES.GOAL.GOAL);
    }
  };

  const fetchPeriodStep = useCallback(() => {
    setLoading(true, { text: 'データを通信中...' });
    stepGoalAPI
      .getPeriodStep()
      .then((response) => {
        setLoading(false);
        if (response !== null) {
          setOriginStepTarget(response?.stepTarget);
          setStepTarget(response?.stepTarget === undefined ? 0 : response?.stepTarget);
        }
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        return Promise.reject();
      });
  }, [setLoading]);

  useEffect(() => {
    if (searchParams != null) {
      const encodedData = searchParams.get('data') || '';
      if (encodedData.length > 0) {
        const params = JSON.parse(atob(encodedData));
        const item = params.item;
        if (item != null) {
          setPlan(item);
        }
      }
    }
    fetchPeriodStep();
  }, [fetchPeriodStep, searchParams]);

  return (
    <div>
      <TopBar
        title={APP_TEXT.SET_GOAL_PLUSTEN.TITLE}
        onBack={goBack}
        enableBack={true}
        enableClose={true}
        onClose={goBackMain}
      />
      <div className="flex flex-col items-start min-h-[calc(100vh-10rem)]">
        <div>
          <div className="text-[17px] mb-4">{APP_TEXT.SET_GOAL_PLUSTEN.SUBTITLE}</div>
          <div className="w-full mb-[18px] p-[15px] flex items-center rounded-[16px]  bg-primary-5">
            <div className="flex-1 ml-[10px]">
              <div className="text-[17px]">{APP_TEXT.SET_GOAL_PLUSTEN.DATA_TITLE}</div>
              <div className="text-[16px] text-gray-700 text-end">
                <span className="mr-2 font-bold text-primary text-[26px]">
                  {stepTarget === 0
                    ? '-'
                    : stepTarget.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                </span>
                {APP_TEXT.SET_GOAL_PLUSTEN.UNIT}
              </div>
            </div>
          </div>
          <div className="text-[15px] text-gray-500">{APP_TEXT.SET_GOAL_PLUSTEN.TEXT}</div>
          <div className="fixed bottom-12 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]">
            <Button
              className="bg-primary  text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
              type="button"
              onClick={goNext}
            >
              {APP_TEXT.SET_GOAL_PLUSTEN.NEXT}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SetGoalPlusTen;
