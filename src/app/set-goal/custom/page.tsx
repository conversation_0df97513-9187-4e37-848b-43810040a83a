'use client';

import { StepTargetRequest, stepGoalAPI } from '@/api/modules/step-goal';
import TopBar from '@/components/layout/top-bar';
import { NumberInput } from '@/components/shared/number-input';
import { ROUTES } from '@/const/routes';
import { useLoading } from '@/hooks/use-loading';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { useCallback, useEffect, useState } from 'react';

import { Button } from '@/components/shared/button';
import { APP_TEXT } from '../../../const/text/app';
function SetGoalCus() {
  const router = useRouter();
  const { setLoading } = useLoading();
  const searchParams = useSearchParams();
  const [plan, setPlan] = useState<string | undefined>('');
  const [originStepTarget, setOriginStepTarget] = useState<number | undefined>(0);
  const [stepTarget, setStepTarget] = useState<string>('');

  const registSteps = useCallback(
    (stepTarget: number) => {
      setLoading(true, { text: 'データを通信中...' });
      const request = new StepTargetRequest();
      request.stepTarget = stepTarget;
      request.targetPlan = 3;
      stepGoalAPI
        .setStepTargetInfo(request)
        .then((response) => {
          setLoading(false);
          //歩数目標へ遷移する
          router.push(ROUTES.GOAL.GOAL);
        })
        .catch((error) => {
          console.log(error);
          setLoading(false);
          return Promise.reject();
        });
    },
    [router, setLoading],
  );

  useEffect(() => {
    if (searchParams != null) {
      const encodedData = searchParams.get('data') || '';
      if (encodedData.length > 0) {
        const params = JSON.parse(atob(encodedData));
        const item = params.item;
        if (item != null) {
          setPlan(item);
        }
      }
    }
  }, [searchParams]);

  const goNext = async () => {
    //歩数目標登録API を呼び出す
    if (stepTarget !== undefined) {
      registSteps(Number(stepTarget));
    }
  };

  const goBack = async () => {
    router.back();
  };

  const goBackMain = async () => {
    //状態により、戻る画面が違う
    //元画面が歩数目標の場合は歩数目標画面へ戻る
    if (plan === null || plan === '') {
      router.push(ROUTES.GOAL.INTRO);
    } else {
      router.push(ROUTES.GOAL.GOAL);
    }
    //それ以外の場合は歩数目標初期画面へ遷移
  };

  const isStepValid = (step: string) => {
    if (typeof step !== 'string' || step.trim() === '') return false;
    const isNum = /^[-+]?(\d+\.?\d*|\.\d+)(e[-+]?\d+)?$/i.test(step.trim());

    if (!isNum) {
      return isNum;
    }

    if (Number(step) < 1000) {
      return false;
    }
    return true;
  };

  return (
    <div>
      <TopBar
        title={APP_TEXT.SET_GOAL_CUS.TITLE}
        onBack={goBack}
        enableBack={true}
        enableClose={true}
        onClose={goBackMain}
      />
      <div className="flex flex-col items-start min-h-[calc(100vh-10rem)]">
        <div>
          <div className="text-[17px]">{APP_TEXT.SET_GOAL_CUS.SUBTITLE}</div>
          <div className="flex gap-1 mt-4 mb-6">
            <span className="w-[130px]">
              <NumberInput
                unit={APP_TEXT.SET_GOAL_CUS.UNIT}
                name="step"
                value={stepTarget}
                maxIntLen={5}
                onChange={(value) => setStepTarget(value)}
              />
            </span>
          </div>
          <div className="text-[15px] text-gray-500">{APP_TEXT.SET_GOAL_CUS.TEXT}</div>
          <div className="fixed bottom-12 left-0 right-0   z-[2] items-center w-full font-bold text-center box-[border-box]">
            <Button
              className="bg-primary disabled:bg-primary-light text-white  font-bold h-12 ml-[20px] mr-[20px] rounded-[25px] w-[88vw]"
              type="button"
              onClick={goNext}
              disabled={!isStepValid(stepTarget)}
            >
              {APP_TEXT.SET_GOAL_CUS.NEXT}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SetGoalCus;
