'use client';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { ERROR_CODES } from '@/const/error-codes';
import { useRegisterState } from '@/store/register';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '../../_components/button';
import { Select } from '../../_components/select';
// 全角文字（漢字、仮名、全角記号など）であることを確認します
const isZenkaku = (value: string) => {
  return !/[^\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\uFF00-\uFFEF\u4E00-\u9FAF]/.test(value);
};

// 全角文字長の計算
const countZenkakuChars = (value: string) => {
  return value.replace(/[^\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\uFF00-\uFFEF\u4E00-\u9FAF]/g, '')
    .length;
};
const FormSchema = z.object({
  groupId: z
    .string({
      required_error: '所属団体を選択してください',
    })
    .min(1, '所属団体を選択してください'),
  nickName: z
    .string()
    .min(1, 'ニックネームを入力してください')
    .refine((value) => isZenkaku(value), {
      message: '全角文字のみ入力してください',
    })
    .refine((value) => countZenkakuChars(value) <= 10, {
      message: '最大10文字まで入力できます',
    }),
});
export default function BaseInfo({
  nextStep,
  initialData,
}: {
  nextStep: (data: z.infer<typeof FormSchema>) => void;
  initialData?: z.infer<typeof FormSchema>;
}) {
  const { litaUserInfo, organizerInfo, groupList } = useRegisterState();
  const [dataFirst, setDataFirst] = useState<{ groupId: string; nickName: string }>();
  const [groupFormList, setGroupFormList] = useState<{ value: string; name: string }[]>([]);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: initialData || {
      groupId: '',
      nickName: '',
    },
    mode: 'onChange',
  });

  const [selectedOption, setSelectedOption] = useState('option-one');

  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);

  useEffect(() => {
    if (groupList) {
      // const groupList = organizerInfo.organizerList[0]?.groupList;
      const groupListAll: { value: string; name: string }[] = [];
      if (groupList) {
        groupList.map((item) => {
          groupListAll.push({
            value: String(item.groupId),
            name: item.groupNm ? item.groupNm : '',
          });
        });
        if (groupListAll) {
          setGroupFormList(groupListAll);
        }
      }
    }
  }, [groupList]);

  const onSubmit = (data: z.infer<typeof FormSchema>) => {
    nextStep(data);
    setDataFirst({ ...data });
    return data;
  };
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, field: any) => {
    const input = e.target.value;
    // 非全角文字のフィルタリング
    const filtered = input.replace(
      /[^\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\uFF00-\uFFEF\u4E00-\u9FAF]/g,
      '',
    );
    // 最初の10文字を切り取る
    const truncated = filtered.slice(0, 10);

    field.onChange(truncated);
  };

  return (
    <div className="w-full ">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className=" w-full  flex flex-col  items-center content-between h-[calc(100vh-156px)]"
        >
          <div className="space-y-6 flex-1">
            <FormField
              control={form.control}
              name="groupId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="font-bold text-lg">所属団体</span>
                    <span className="text-primary bg-primary/10 px-1 rounded-sm py-[2px] ml-1">
                      必須 {field.value}
                    </span>
                  </FormLabel>
                  {/* [{ value: '5001', name: '所属団体A-1' }] */}

                  <Select
                    placeholder="選択する"
                    defaultValue={field.value}
                    options={groupFormList}
                    title="所属団体"
                    onSelect={field.onChange}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="border-border w-full h-[1px] border-b" />
            <FormField
              control={form.control}
              name="nickName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="font-bold text-lg">ニックネーム</span>
                    <span className="text-primary bg-primary/10 px-1 rounded-sm py-[2px] ml-1">
                      必須
                    </span>
                  </FormLabel>
                  <FormDescription>全角最大10文字で入力してください</FormDescription>
                  <FormControl>
                    <Input className="text-black text-base  h-[48px]" {...field} />
                  </FormControl>
                  <FormDescription>例：健康ウォーキングマン</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex mt-2 text-xs text-muted-foreground mt-6">
              <p className="mr-1">※</p>
              <p>
                本名、または本名酷似する個人を特定できるニックネームは使用できません。公序良俗に反する表記等、事務局が不適切と判断した場合は、使用をお断りする場合がありますのでご了承ください。
              </p>
            </div>
            <div className="flex mt-2 text-xs text-muted-foreground mb-6">
              <p className="mr-1">※</p>
              <p>ニックネームは、全角の漢字・ひらがな・カタカナ・英数・記号でご記入ください。</p>
            </div>
          </div>
          <div className=" w-full py-4">
            {/*  onClick={nextInfo} */}
            <Button className="mb-4 w-full" type="submit">
              次へ
            </Button>
            <Button variant="outline">戻る</Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
