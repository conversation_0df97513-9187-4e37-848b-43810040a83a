'use client';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { ERROR_CODES } from '@/const/error-codes';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '../../_components/button';
import { Select } from '../../_components/select';

const FormSchema = z.object({
  email: z.string().regex(
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // 基本邮箱格式检查
    'ドメインのメールアドレスのみ利用可能です',
  ),
  phoneNo: z
    .string()
    .min(1, '電話番号は必須です') // 必填
    .regex(/^[0-9]+$/, '半角数字のみ入力してください') // 半角数字验证
    .length(11, '11桁の電話番号を入力してください'), // 固定长度验证,
});

export default function ContactInfo({
  nextStep,
  previousStep,
  initialData,
}: {
  nextStep: (data: z.infer<typeof FormSchema>) => void;
  previousStep: (data: number) => void;
  initialData?: z.infer<typeof FormSchema>;
}) {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: initialData || {
      email: '',
      phoneNo: '',
    },
    mode: 'onChange',
  });
  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
    }
  }, [initialData, form]);
  const onSubmit = (data: z.infer<typeof FormSchema>) => {
    nextStep(data);
    return data;
  };
  // 前へ
  const previousStepClick = () => {
    previousStep(1);
  };

  return (
    <div className="w-full ">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className=" w-full  flex flex-col  items-center content-between h-[calc(100vh-156px)]"
        >
          <div className="flex-1">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="font-bold text-lg">メールアドレス</span>
                    <span className="text-primary bg-primary/10 px-1 rounded-sm py-[2px] ml-1">
                      必須
                    </span>
                  </FormLabel>
                  <FormDescription>半角英数字で入力してください</FormDescription>
                  <Input className="text-black text-base  h-[48px]" {...field} />
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormDescription>例：<EMAIL></FormDescription>
            <div className="border-border w-full h-[1px] border-b my-6" />
            <FormField
              control={form.control}
              name="phoneNo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="font-bold text-lg">電話番号</span>
                    <span className="text-primary bg-primary/10 px-1 rounded-sm py-[2px] ml-1">
                      必須
                    </span>
                  </FormLabel>
                  <FormDescription>半角数字で入力してください</FormDescription>
                  <Input className="text-black text-base  h-[48px]" {...field} />
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormDescription>例：09012345678</FormDescription>

            {/* <Button type="submit">Submit</Button> */}
            <div className="flex mt-2 text-xs text-muted-foreground mt-6">
              <p className="mr-1">※</p>
              <p>
                電話番号を変更された場合、自動的にログアウトされ、再度ログインする必要があります。ご注意ください。
              </p>
            </div>
          </div>
          <div className=" w-full py-4">
            {/*  onClick={nextInfo} */}
            <Button className="mb-4 w-full" type="submit">
              次へ
            </Button>
            <Button variant="outline" type="button" onClick={previousStepClick}>
              戻る
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
