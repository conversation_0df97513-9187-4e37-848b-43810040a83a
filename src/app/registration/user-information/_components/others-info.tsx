'use client';

import { Form, FormDescription } from '@/components/ui/form';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useRegisterState } from '@/store/register';
import type { LableType, RemarkType } from '@/types/register';
import { zodResolver } from '@hookform/resolvers/zod';
import { CircleHelp } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '../../_components/button';
import { FormInput } from '../../_components/form-input';
// const remarks = [
//   { index: 1, key: 'aaa', value: '12345' },
//   { index: 2, key: 'bbb', value: 'xxxxx' },
//   { index: 3, key: 'cccc', value: 'abc-xyz-123' },
// ];

const createNumberValidator = (options: {
  fieldName: string;
  min: number;
  max: number;
  decimalPlaces?: number;
}) => {
  return z
    .string()
    .trim()
    .refine(
      (value) => {
        if (value === '') return true;
        const regex =
          options.decimalPlaces === 0
            ? /^\d+$/
            : new RegExp(`^\\d+(\\.\\d{1,${options.decimalPlaces ?? 1}})?$`);
        if (!regex.test(value)) {
          return false;
        }
        const numValue = Number.parseFloat(value);
        return numValue >= options.min && numValue <= options.max;
      },
      {
        message: `半角数字、小数点${options.decimalPlaces ?? 1}桁まで、${options.min}-${options.max}${
          options.fieldName === 'height' ? 'cm' : options.fieldName === 'weight' ? 'kg' : ''
        }`,
      },
    );
};

// 固定フィールドのschema
const fixedSchema = z.object({
  height: createNumberValidator({
    fieldName: 'height',
    min: 100.0,
    max: 230.0,
    decimalPlaces: 1,
  }),
  weight: createNumberValidator({
    fieldName: 'weight',
    min: 30.0,
    max: 135.0,
    decimalPlaces: 1,
  }),
  pace: createNumberValidator({
    fieldName: 'pace',
    min: 30.0,
    max: 120.0,
    decimalPlaces: 1,
  }),
});

// 動的フィールドを生成するschema
const generateDynamicSchema = (dynamicFields: Record<string, string>) => {
  const dynamicSchema: Record<string, any> = {};

  for (const [fieldName, fieldValue] of Object.entries(dynamicFields)) {
    // 固定フィールドをスキップ（fixedSchemaで定義）
    if (['height', 'weight', 'pace'].includes(fieldName)) continue;
    // 動的フィールドごとの基本検証ルールの作成
    dynamicSchema[fieldName] = z.string().trim().optional();
    // 必要に応じて特定のフィールドを追加できる特別な検証
    if (fieldName === 'insuranceCardNo') {
      dynamicSchema[fieldName] = z
        .string()
        .trim()
        .regex(/^\d+$/, { message: '保険証番号は数字のみで入力してください' })
        .min(1, {
          message: '保険証番号は必須です',
        });
    }
    if (fieldName === 'insuredSymbol') {
      dynamicSchema[fieldName] = z
        .string()
        .trim()
        .regex(/^\d+$/, { message: '保被保険者記号は数字のみで入力してください' });
    }
    if (fieldName === 'insuredNumber') {
      dynamicSchema[fieldName] = z
        .string()
        .trim()
        .regex(/^\d+$/, { message: '被保険者番号は数字のみで入力してください' })
        .min(1, {
          message: '被保険者番号は必須です',
        });
    }
    if (fieldName === 'insuredBranchNo') {
      dynamicSchema[fieldName] = z
        .string()
        .trim()
        .regex(/^\d+$/, { message: '枝番は数字のみで入力してください' })
        .min(1, {
          message: '枝番は必須です',
        });
    }
  }

  return z.object(dynamicSchema);
};

// 固定および動的schemaのマージ
const generateCompleteSchema = (dynamicFields: Record<string, string>) => {
  const dynamicSchema = generateDynamicSchema(dynamicFields);
  return fixedSchema.merge(dynamicSchema);
};

export default function OthersInfo({
  nextStep,
  previousStep,
  initialData,
  isRemarkFlg,
  isEntryInsured,
}: {
  nextStep: (data: any) => void;
  previousStep: <T>(data: T) => void;
  initialData?: any;
  isRemarkFlg?: boolean;
  isEntryInsured?: boolean;
}) {
  const { setDialog } = useMessageDialog();
  const { litaUserInfo, organizerSetupDetail } = useRegisterState();

  const formSchema = generateCompleteSchema(initialData ? initialData : []);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues:
      Object.keys(initialData).length !== 0
        ? initialData
        : {
            height: '',
            weight: '',
            pace: '',
          },
    mode: 'onChange',
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: 'remark',
  });
  const [remarks, setRemark] = useState<RemarkType[]>([]);
  useEffect(() => {
    // if (litaUserInfo?.remark) {
    //   setRemark(litaUserInfo.remark);
    // }
    if (organizerSetupDetail) {
      const configurations: LableType[] | undefined = organizerSetupDetail.baseSetting?.find(
        (item) => item.childFcnId === 1101,
      )?.configurations;
      const remarkList: RemarkType[] = [];
      configurations?.map((item, i) => {
        if (item.key && /^remark\d+$/.test(item.key)) {
          remarkList.push({
            index: i + 1,
            key: item.key,
            value: item.value,
          });
        }
      });
      setRemark(remarkList);
    }
  }, [litaUserInfo, organizerSetupDetail]);
  useEffect(() => {
    console.log(Object.keys(initialData).length);
    if (Object.keys(initialData).length !== 0) {
      form.reset(initialData);
    }
  }, [initialData, form]);
  // const remarks = [
  //   { index: 1, key: 'aaa', value: '12345' },
  //   { index: 2, key: 'bbb', value: 'xxxxx' },
  //   { index: 3, key: 'cccc', value: 'abc-xyz-123' },
  // ];

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    nextStep(data);
    return data;
  };
  // 前へ
  const previousStepClick = () => {
    previousStep(form.getValues());
  };
  return (
    <div className="bg-white  flex-1">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="">
          {/* からだの情報 */}
          <div className="text-lg font-bold">{APP_TEXT.REGISTRATION_CREATE.BODY_INFO}</div>
          <div className="text-xs text-muted-foreground font-normal !m-0 ">
            {APP_TEXT.REGISTRATION_CREATE.COMMON_HALF_NUMBER}
          </div>
          <div className="grid grid-cols-2 gap-x-4">
            <FormInput
              name="height"
              subLabel={APP_TEXT.REGISTRATION_CREATE.HEIGHT}
              unit={COMMON_TEXT.UNIT.CM}
              maxIntLen={5}
              maxDecLen={1}
              description={`160.5${COMMON_TEXT.UNIT.CM}`}
            />
            <FormInput
              name="weight"
              subLabel={APP_TEXT.REGISTRATION_CREATE.WEIGHT}
              unit={COMMON_TEXT.UNIT.KG}
              maxIntLen={5}
              maxDecLen={1}
              description={`50.5${COMMON_TEXT.UNIT.KG}`}
            />
          </div>
          <FormDescription className="text-[11px] font-normal mt-2 whitespace-pre-wrap ">
            {APP_TEXT.REGISTRATION_CREATE.BODY_INFO_DESC}
          </FormDescription>
          <div className="grid grid-cols-2 gap-4 ">
            <FormInput
              name="pace"
              subLabel={APP_TEXT.REGISTRATION_CREATE.PACE}
              unit={COMMON_TEXT.UNIT.CM}
              maxIntLen={3}
              maxDecLen={0}
              description={`45${COMMON_TEXT.UNIT.CM}`}
            />
          </div>
          <FormDescription className="text-[11px] font-normal mt-2 whitespace-pre-wrap ">
            {APP_TEXT.REGISTRATION_CREATE.PACE_DESC}
          </FormDescription>
          <div className="border-b border-solid border-border my-6" />
          {/* 保険証情報 */}
          {isEntryInsured && (
            <div>
              <div className="text-lg font-bold flex items-center gap-x-2">
                {APP_TEXT.REGISTRATION_CREATE.INSURER_INFO}
                <CircleHelp
                  className="text-muted-foreground "
                  onClick={() =>
                    setDialog(true, {
                      title: APP_TEXT.REGISTRATION_CREATE.INSPECT_EXAMPLE,
                      content: (
                        <div className="text-base font-normal">
                          <img src="/images/create/insterted.svg" alt="complete" className="mb-2" />
                          {APP_TEXT.REGISTRATION_CREATE.INSPECT_DESC}
                        </div>
                      ),
                    })
                  }
                />
              </div>
              <FormInput
                name="insuranceCardNo"
                subRequired
                subLabel={APP_TEXT.REGISTRATION_CREATE.INSURER_NO}
                rule={APP_TEXT.REGISTRATION_CREATE.HALF_NUMBER}
                description="345678"
              />
              <FormInput
                name="insuredSymbol"
                subLabel={APP_TEXT.REGISTRATION_CREATE.INSURED1}
                rule={APP_TEXT.REGISTRATION_CREATE.HALF_NUMBER}
                maxDecLen={0}
                description="12"
                hint={APP_TEXT.REGISTRATION_CREATE.INSURED1_MAX_BYTES}
              />
              <FormInput
                name="insuredNumber"
                subRequired
                subLabel={APP_TEXT.REGISTRATION_CREATE.INSURED2}
                rule={APP_TEXT.REGISTRATION_CREATE.HALF_NUMBER}
                description="345678"
                hint={APP_TEXT.REGISTRATION_CREATE.INSURED2_MAX_BYTES}
              />
              <FormInput
                name="insuredBranchNo"
                subRequired
                subLabel={APP_TEXT.REGISTRATION_CREATE.INSURED3}
                rule={APP_TEXT.REGISTRATION_CREATE.HALF_NUMBER}
                description="01"
              />
              <div className="border-b border-solid border-border my-6" />
            </div>
          )}

          {/* その他 */}
          {isRemarkFlg && (
            <div>
              {remarks.map((item: any, i: number) => (
                <FormInput
                  key={i}
                  type="string"
                  label={i === 0 ? APP_TEXT.REGISTRATION_CREATE.OTHER : ''}
                  name={item.key}
                  subLabel={item.key}
                  maxIntLen={2}
                  rule={APP_TEXT.REGISTRATION_CREATE.HALF_NUMBER}
                  description="0123456"
                />
              ))}
            </div>
          )}
          <div className="w-full py-4">
            {/*  onClick={nextInfo} */}
            <Button className="mb-4 w-full" type="submit">
              {APP_TEXT.REGISTRATION_CREATE.CONFIRM}
            </Button>
            <Button variant="outline" type="button" onClick={previousStepClick}>
              戻る
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
