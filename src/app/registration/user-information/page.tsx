'use client';

import { registerAPI } from '@/api/modules/register';
import TopBar from '@/components/layout/top-bar';
import { ScrollArea } from '@/components/shared/scroll-area';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import { cn } from '@/lib/utils';
import { useRegisterState } from '@/store/register';
import type {
  LableType,
  LitaUserInfoResponse,
  LooseObject,
  LooseObjectItem,
  RemarkType,
} from '@/types/register';
import { useEffect, useRef, useState } from 'react';
import { map } from 'zod';
import { Button } from '../_components/button';
import Step from '../_components/step';
import { getBaseSetting } from '../_utils/data-convert';
import { USER_INFO_FOUR, USER_INFO_ONE, USER_INFO_THREE, USER_INFO_TWO } from '../_utils/enums';
import BaseInfo from './_components/base-info';
import ContactInfo from './_components/contact-info';
import OthersInfo from './_components/others-info';
import UserInfoConfirm from './_components/user-info-confirm';
export default function Mission() {
  const router = useRouter();
  const searchParams = useSearchParams().get('loginToken');

  const [isShowConfirm, setIsShowConfirm] = useState<boolean>(true);
  const [step, setStep] = useState<number>(1);
  // 用户是否已存在 true 存在
  const [isUserAlready, setIsUserAlready] = useState<boolean>(false);
  const [firstFormData, setFirstFormData] = useState<{ groupId: string; nickName: string }>();
  const [secondFormData, setSecondFormData] = useState<{ email: string; phoneNo: string }>();
  const [threeFormData, setThreeFormData] = useState<any>();
  const [authMode, setAuthMode] = useState<LableType>();
  const [entryInsuredFlg, setEntryInsuredFlg] = useState<boolean>(false);
  const [remarkFlg, setRemarkFlg] = useState<boolean>(false);
  const lastDataRefs = useRef<LooseObject[]>();
  const {
    litaUserInfo,
    organizerSetupDetail,
    businesstoken,
    newLoginFlag,
    organizerInfo,
    groupList,
  } = useRegisterState();

  useEffect(() => {
    const base = getBaseSetting('baseSetting', 'authMode');
    const entryInsured = getBaseSetting('baseSetting', 'entryInsuredFlg');
    const remarkKey = getBaseSetting('baseSetting', 'remarkFlg');
    if (base) {
      setAuthMode(base);
    }
    // ログインモード_業務トークンあり
    // TODO:  && businesstoken
    if (searchParams && searchParams === '1' && !newLoginFlag) {
      setIsShowConfirm(true);
      const lastData: LooseObject[] = [];
      if (litaUserInfo) {
        const allKeyObj = [
          { title: '基本情况', step: 1, data: USER_INFO_ONE },
          { title: '連絡先', step: 2, data: USER_INFO_TWO },
          { title: 'からだの情報', step: 3, data: USER_INFO_THREE },
        ];
        const litaOrganizers = litaUserInfo.organizers?.find((item) => item.isDefault === 1);
        const litaIinfo = { groupId: String(litaOrganizers?.groupId), ...litaUserInfo };
        // 被保険者番号・記号 0：利用しない 1：利用する
        if (entryInsured && entryInsured.value === '1') {
          setEntryInsuredFlg(true);
          allKeyObj.push({ title: '保険証情報', step: 3, data: USER_INFO_FOUR });
        }
        allKeyObj.map((item) => {
          lastData.push(formSubData(litaIinfo, item.data, item.title, item.step));
        });
        // 備考欄機能利用 0：利用しない 1：利用する
        if (remarkKey && remarkKey.value === '1') {
          setRemarkFlg(true);
          const remarkInfo: { key?: string | number | symbol; value?: string; lable?: string }[] =
            [];
          litaUserInfo?.remark?.map((item) => {
            remarkInfo.push({
              key: item.key,
              lable: item.key,
              value: item.value ? item.value : '',
            });
          });
          lastData.push({
            info: remarkInfo,
            step: 3,
            title: 'その他',
          });
        }
        lastDataRefs.current = lastData;
        getInitData(litaUserInfo);
      }
    } else {
      setIsShowConfirm(false);
      if (litaUserInfo) {
        getInitData(litaUserInfo);
      }
    }
  }, [searchParams, litaUserInfo, organizerSetupDetail]);

  const getInitData = (litaInfo: LitaUserInfoResponse) => {
    const initFirstForm = initLitaUserInfo(USER_INFO_ONE, litaInfo);
    const initSecondForm = initLitaUserInfo(USER_INFO_TWO, litaInfo);
    const inithreeForm = initLitaUserInfo([...USER_INFO_THREE, ...USER_INFO_FOUR], litaInfo);
    if (initSecondForm) {
      setSecondFormData({
        email: typeof initSecondForm.email === 'string' ? initSecondForm.email : '',
        phoneNo: typeof initSecondForm.phoneNo === 'string' ? initSecondForm.phoneNo : '',
      });
    }
    if (initFirstForm) {
      setFirstFormData({
        groupId: litaInfo.organizers ? String(litaInfo.organizers[0].groupId) : '',
        nickName: typeof initFirstForm.nickName === 'string' ? initFirstForm.nickName : '',
      });
    }
    if (inithreeForm) {
      let remarksObj = {};
      if (litaInfo.remark && litaInfo.remark.length > 0) {
        remarksObj = litaInfo.remark.reduce((acc: RemarkType, item) => {
          if (item.key !== undefined) {
            (acc as Record<string, string>)[item.key] = item.value || '';
          }
          return acc;
        }, {});
      }
      setThreeFormData({ ...remarksObj, ...inithreeForm });
    }
  };

  const initLitaUserInfo = (KeyList: Record<string, unknown>[], litaUser: LitaUserInfoResponse) => {
    const initObj = {} as Record<string, unknown>;
    Object.keys(litaUser).map((key: string) => {
      const keyName = KeyList.find((item) => item.id === key);
      if (keyName) {
        initObj[key] = String(litaUser[key as keyof typeof litaUser]);
      }
    });
    return initObj;
  };

  const formSubData = (formData: any, keyArr: any[], title: string, step: number) => {
    const remarks = litaUserInfo.remark;

    const objInfo: {
      key?: string | number | symbol;
      value?: string;
      lable?: string;
      index?: string;
    }[] = [];
    (Object.keys(formData) as Array<keyof typeof formData>).map((key) => {
      if (keyArr.length === 0) {
        let result: RemarkType | undefined = {};
        if (remarks && remarks?.length > 0) {
          result = remarks.find((item) => item.key === key);
          if (result) {
            objInfo.push({
              key: result.key,
              lable: String(key),
              value: formData[key] ? formData[key] : '',
            });
          }
        }
      } else {
        const resultLast = keyArr.find((item: { id: string | number | symbol }) => item.id === key);
        if (resultLast) {
          let valueName = formData[key] ? formData[key] : '';
          if (resultLast.id === 'groupId' && groupList) {
            // const groupList = organizerInfo.organizerList[0]?.groupList;
            groupList?.map((item) => {
              if (String(item.groupId) === formData[key]) {
                valueName = item.groupNm;
              }
            });
          }
          objInfo.push({
            key: key,
            lable: resultLast.label,
            value: valueName,
            index: formData[key] ? formData[key] : '',
          });
        }
      }
    });
    return {
      title: title,
      step: step,
      info: objInfo,
    };
  };

  const nextStep = (data: any) => {
    if (step === 1) {
      setFirstFormData({ ...data });
    }
    if (step === 2) {
      setSecondFormData({ ...data });
    }

    if (step !== 3) {
      setStep(step + 1);
    }
    if (step === 3) {
      const lastData: LooseObject[] = [];
      if (firstFormData) {
        const objOne = formSubData(firstFormData, USER_INFO_ONE, '基本情况', 1);
        lastData.push(objOne);
      }
      if (secondFormData) {
        const objTwo = formSubData(secondFormData, USER_INFO_TWO, '連絡先', 2);
        lastData.push(objTwo);
      }
      if (data) {
        const objThree = formSubData(data, USER_INFO_THREE, 'からだの情報', 3);
        lastData.push(objThree);
        if (entryInsuredFlg) {
          const objFour = formSubData(data, USER_INFO_FOUR, '保険証情報', 3);
          lastData.push(objFour);
        }

        if (remarkFlg && litaUserInfo.remark && litaUserInfo.remark?.length > 0) {
          const objFive = formSubData(data, [], 'その他', 3);
          lastData.push(objFive);
        }
      }
      lastDataRefs.current = lastData;
      setIsShowConfirm(true);
    }
  };

  const previousStep = (val: number) => {
    setStep(val);
  };

  const previousStepLast = <T,>(data: T) => {
    setStep(2);
    setThreeFormData(data);
  };
  const editFromClick = (data: any) => {
    if (data?.step) {
      setIsShowConfirm(false);
      setStep(Number(data.step));
      if (data.step === 1) {
        setFirstFormData({ ...data.info });
      } else if (data.step === 2) {
        setSecondFormData({ ...data.info });
      } else {
        setThreeFormData({ ...data.info });
      }
    }
  };
  // flex flex-col items-center justify-center w-full h-screen
  return (
    <div className="w-full h-screen relative flex-1 bg-background">
      <TopBar title="ユーザー情報の入力" enableBack={true} />
      <ScrollArea className="h-[calc(100vh-48px)] w-full  flex flex-col" type="hover">
        {isShowConfirm ? (
          <UserInfoConfirm dataConfirm={lastDataRefs.current} editFromClick={editFromClick} />
        ) : (
          <div className="px-6 bg-white h-[calc(100vh-48px)] overflow-auto">
            <div className="py-6 mb-2">
              <Step stepArr={['基本情報', '連絡先', 'その他']} step={step} />
            </div>
            {step === 1 && <BaseInfo nextStep={nextStep} initialData={firstFormData} />}
            {step === 2 && (
              <ContactInfo
                nextStep={nextStep}
                previousStep={previousStep}
                initialData={secondFormData}
              />
            )}
            {step === 3 && (
              <div className="bg-white flex-1" style={{ height: 'calc(100vh - 156px)' }}>
                <OthersInfo
                  nextStep={nextStep}
                  previousStep={previousStepLast}
                  initialData={threeFormData}
                  isRemarkFlg={remarkFlg}
                  isEntryInsured={entryInsuredFlg}
                />
              </div>
            )}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
