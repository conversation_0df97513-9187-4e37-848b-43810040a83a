'use client';

import TopBar from '@/components/layout/top-bar';
import { Checkbox } from '@/components/shared/checkbox';
import { Button } from '@/components/ui/button';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { cn } from '@/lib/utils';
import type { MissionListResponse, MissionPopupDetailResponse } from '@/types/mission';
import { Check } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
export default function Step({
  step,
  stepArr,
}: {
  stepArr: string[];
  step: number;
}) {
  //   const [step, setStep] = useState<number>(1);

  useEffect(() => {}, []);

  const login = () => {};

  return (
    <div className="w-full flex justify-center">
      <div className="flex items-center">
        {stepArr?.map((item: string | undefined, i: number) => (
          <div key={i} className="flex items-center">
            <div className="flex flex-col  items-center justify-center">
              <div className="text-xs pb-1">{item}</div>
              <div
                className={cn(
                  'size-8 font-bold rounded-full flex justify-center items-center border border-primary ',
                  step < i + 1 ? 'text-primary' : 'bg-primary text-white',
                )}
              >
                {step - 1 >= i + 1 ? <Check className="h-4 w-4 text-white" /> : <>{i + 1}</>}
              </div>
            </div>
            {i + 1 !== stepArr.length && (
              <div className="w-8 mt-6 boder-border border-b h-[1px] mx-4" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
