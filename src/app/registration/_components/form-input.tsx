import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import TopBar from '@/components/layout/top-bar';
import { FormError } from '@/components/shared/form-error';
import { NumberInput } from '@/components/shared/number-input';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { APP_TEXT } from '@/const/text/app';
import { rule } from 'postcss';

interface FormInputProps {
  type?: string; //type
  className?: string; //style
  name: string; //from name
  placeholder?: string; //placeholder
  label?: string; //label
  labelDescription?: string; // label description
  required?: boolean; //label required
  subLabel?: string; //sub label
  subRequired?: boolean; //sub required
  disabled?: boolean; //input disabled
  rule?: string; //rule
  unit?: string; //単位
  maxIntLen?: number; // 最大整数位数
  maxDecLen?: number; // 最大小数位数
  description?: string; //例xxx
  hint?: string; //※xxx
}

export const FormInput = (props: FormInputProps) => {
  const {
    type = 'number',
    className = '',
    name,
    placeholder = '',
    label = '',
    labelDescription = '',
    required = false,
    subLabel = '',
    subRequired = false,
    disabled = false,
    rule = '',
    unit = '',
    maxIntLen,
    maxDecLen,
    description = '',
    hint = '',
  } = props;
  return (
    <FormField
      name={name}
      render={({ field }) => (
        <div>
          {label && (
            <div className={className}>
              <div className="flex items-center gap-x-2">
                <div className="text-lg font-bold">{label}</div>
                <div className="bg-[#F6F8FF] text-xs text-button w-8 py-[2px] text-center">
                  {required ? APP_TEXT.REGISTRATION_CREATE.REQUIRED : ''}
                </div>
              </div>
              {labelDescription && (
                <FormDescription className="text-xs font-normal">
                  {labelDescription}
                </FormDescription>
              )}
            </div>
          )}
          <div className="flex flex-col gap-1 w-full">
            {subLabel && (
              <div className="flex items-center gap-x-2 mt-6">
                <div className="text-sm font-normal">{subLabel}</div>
                <div className="bg-[#F6F8FF] text-xs text-button w-8 py-[2px] text-center">
                  {subRequired ? APP_TEXT.REGISTRATION_CREATE.REQUIRED : ''}
                </div>
              </div>
            )}
            {rule && <FormDescription className="text-xs font-normal">{rule}</FormDescription>}
            <FormControl>
              {type === 'number' ? (
                <NumberInput
                  className="w-full"
                  unit={unit}
                  maxIntLen={maxIntLen}
                  maxDecLen={maxDecLen}
                  disabled={disabled}
                  {...field}
                />
              ) : (
                <Input
                  placeholder={placeholder}
                  className="w-full h-12 "
                  disabled={disabled}
                  {...field}
                />
              )}
            </FormControl>
            <FormError className="mt-2" />
            {description && (
              <FormDescription className="text-[11px] font-normal">
                {APP_TEXT.REGISTRATION_CREATE.DESC + description}
              </FormDescription>
            )}
            {hint && (
              <FormDescription className="text-[11px] font-normal mt-1">{hint}</FormDescription>
            )}
          </div>
        </div>
      )}
    />
  );
};
