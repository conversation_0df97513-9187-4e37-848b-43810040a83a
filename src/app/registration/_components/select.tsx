import {
  <PERSON><PERSON><PERSON>er,
  SelectDrawerClose,
  SelectDrawer<PERSON>ontent,
  SelectDrawerHeader,
  SelectDrawerTrigger,
} from '@/components/shared/select-drawer';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useRef, useState } from 'react';

export function Select({
  className,
  defaultValue,
  options,
  title,
  placeholder,
  onSelect,
  onChange,
}: {
  className?: string;
  defaultValue: string;
  options: { value: string; name: string }[];
  title: string;
  placeholder: string;
  onSelect?: (value: string) => void;
  onChange?: (value: string) => void;
}) {
  const defaultItem = options.find((option) => option.value === defaultValue);
  // const [selectedItem, setSelectedItem] = useState(defaultItem);
  const selectedItem = useRef<{ value: string; name: string }>();
  selectedItem.current = defaultItem;

  const handleSelect = (item: { value: string; name: string }) => {
    // setSelectedItem(item);
    selectedItem.current = item;
    onSelect?.(item.value);
    onChange?.(item.value);
  };
  return (
    <SelectDrawer>
      <SelectDrawerTrigger className={className}>
        <span>
          {/* {options.find((option) => option.value === selectedItem?.value)?.name || placeholder} */}
          {selectedItem.current ? selectedItem.current.name : placeholder}
        </span>
      </SelectDrawerTrigger>
      <SelectDrawerContent className="bg-card">
        <SelectDrawerHeader>{title}</SelectDrawerHeader>
        <RadioGroup defaultValue={selectedItem.current?.value}>
          {options.map((item) => (
            <SelectDrawerClose
              key={item.value}
              asChild
              onClick={() => {
                handleSelect(item);
              }}
            >
              <div className="flex items-center h-14 px-6">
                <RadioGroupItem value={item.value} id={`select-${item.value}`} />
                <Label htmlFor={`select-${item.value}`} className="text-base ml-4">
                  {item.name}
                </Label>
              </div>
            </SelectDrawerClose>
          ))}
        </RadioGroup>
      </SelectDrawerContent>
    </SelectDrawer>
  );
}
