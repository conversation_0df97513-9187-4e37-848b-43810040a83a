'use client';

import { registerAPI } from '@/api/modules/register';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useRouter } from '@/hooks/use-next-navigation';
import { Sex, useAuthStore } from '@/store/auth';
import { useRegisterState } from '@/store/register';
import type { LableType, LitaTokenResponse, LitaUserInfoResponse } from '@/types/register';
import { nlog } from '@/utils/logger';
import { getMessageToNative, sendMessageToNative } from '@/utils/native-bridge';
import { useEffect, useRef, useState } from 'react';
import { generate, getBaseSetting } from '../_utils/data-convert';
import { AUTH_MODE_TYPE, LOGIN_FLG_TYPE, LOGIN_METHOD_TYPE } from '../_utils/enums';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from './alert-dialog';
export default function LitaDialog({
  popupContent,
  isAlreadyToken,
  trigger,
}: {
  trigger: boolean;
  isAlreadyToken: boolean;
  popupContent?: string;
}) {
  const router = useRouter();
  const {
    organizerInfo,
    accountProfile,
    setLitaUserInfo,
    organizerSetupDetail,
    deviceId,
    newLoginFlag,
    useOrganizerId,
    setHomeDialog,
    setLitaOrganizerIds,
  } = useRegisterState();
  const litaUserInfo = useRef<LitaUserInfoResponse>();
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  // const [organizerIds, setOrganizerIds] = useState<number[]>([]);
  const [groupId, setGroupId] = useState<number>();
  const { user, setUser, setToken } = useAuthStore();
  // const [authMode, setAuthMode] = useState<LableType>();
  const litaUserOrganizerIds = useRef<string>();
  const authMode = useRef<LableType>();
  const organizerIds = useRef<number[]>();
  const [generateCode, setGenerateCode] = useState<string>(generate());

  useEffect(() => {
    if (organizerInfo) {
      const ids: number[] = [];
      organizerInfo.organizerList?.map((item) => {
        if (item.organizerId) {
          ids.push(item.organizerId);
        }
      });
      organizerIds.current = ids;
    }
  }, [organizerInfo]);

  useEffect(() => {
    if (trigger) {
      haveAnAccount();
    }
    if (organizerSetupDetail) {
      const base = getBaseSetting('baseSetting', 'authMode');
      if (base) {
        authMode.current = base;
        // setAuthMode(base);
      }
    }
  }, [trigger, organizerSetupDetail]);

  const handleCancel = () => {
    // 第三者にジャンプして画面に登ります
    if (organizerInfo.organizerList) {
      sendMessageToNative({
        type: 'start-other-link',
        data: {
          link: organizerInfo.organizerList[0].loginUrl,
          // link: 'https://www.baidu.com/',
          callbackLink: 'http://tg3-portal.kenko-mileage.com/',
          title: 'registration',
        },
        callback: (data) => {
          console.log(data, 'sendMessageToNative');
          // window.location.href = data?.url;
          setOpenDialog(false);
        },
      });
    }
  };
  const handleConfirm = () => {
    console.log('handleConfirm');
    setOpenDialog(false);
    authLogin();
  };

  const haveAnAccount = () => {
    // 団体認証方式＝2.Smart-LiTA認証_SMS認証なし または 3.Smart-LiTA認証_SMS認証ありの場合
    if (
      authMode.current?.value === AUTH_MODE_TYPE.LOGIN_SMS_NO ||
      authMode.current?.value === AUTH_MODE_TYPE.LOGIN_SMS
    ) {
      // 第三者にジャンプして画面に登ります
      if (organizerInfo.organizerList) {
        if (newLoginFlag) {
          sendMessageToNative({
            type: 'start-other-link',
            data: {
              link: organizerInfo.organizerList[0].registerUrl,
              // link: 'https://stg3-portal.kenko-mileage.com/?memo=xxx&userId=e501627b-ce88-3963-ae56-2850b22f2db2',
              callbackLink: 'https://stg3-portal.kenko-mileage.com',
              title: 'registration',
            },
            callback: (data) => {
              // window.location.href = data?.url;
              router.push('/registration/agreement');
            },
          });
        }
        // const code = generate();
        // setGenerateCode(code);
        // generate

        sendMessageToNative({
          type: 'start-other-link',
          data: {
            link: `${organizerInfo.organizerList[0].loginUrl}&state=${generateCode}`,
            // link: 'https://www.baidu.com/',
            callbackLink: 'https://stg3-portal.kenko-mileage.com',
            title: 'registration',
          },
          callback: (data) => {
            // window.location.href = data?.url;
            getLitaToken(data?.url);
          },
        });
      }
    }
  };
  const areArraysEqual = (a: number[], b: number[]) => {
    return b.every((item) => a.includes(item));
  };

  const getLitaToken = (urlString: string) => {
    const url = new URL(urlString);
    const searchParams = url.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    if (state !== generateCode) return;
    if (code) {
      registerAPI.litaToken({ code: code }).then(async (res) => {
        if (res) {
          litaUserInfo.current = res;
          setLitaUserInfo(res);

          if (accountProfile.externalUserId && res.litaId !== accountProfile.externalUserId) {
            setOpenDialog(true);
            return;
          }
          if (newLoginFlag) {
            setLitaOrganizerIds(useOrganizerId);
            router.push('/registration/user-information');
            return;
          }
          const organizerIDs: number[] = [];
          if (res?.organizers) {
            const organizerIDArr = res.organizers;
            organizerIDArr?.map((item) => {
              if (item.organizerId) {
                organizerIDs.push(Number(item.organizerId));
              }
            });
          }
          litaUserOrganizerIds.current = organizerIDs.join(',');
          setLitaOrganizerIds(organizerIDs.join(','));
          let idsArrFalse = false;
          // 団体一致かどうかは「Lita認証トークン取得API」のレスポンスと「1.3」の団体情報で判定する
          if (organizerIds.current) {
            const userOrganizerIds: number[] = [];
            res.organizers?.map((item) => {
              if (item.organizerId) {
                userOrganizerIds.push(item.organizerId);
              }
            });
            idsArrFalse = await areArraysEqual(userOrganizerIds, organizerIds.current);
            // 団体一致の場合：
            // 「ユーザーログイン API」を呼び出し、業務トークンを取得する。
            if (idsArrFalse) {
              authLogin();
            } else {
              // 団体不一致の場合：
              // 「A002_利用規約画面」に遷移する(ログインモード_業務トークンなし)。
              router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken}`);
            }
          }
        }
      });
    }
  };
  const authLogin = () => {
    if (!litaUserInfo.current) return;
    const groupId = litaUserInfo.current.organizers
      ? litaUserInfo.current.organizers[0].groupId
      : [];
    const params = {
      ...litaUserInfo.current,
      deviceId,
      // organizerId: organizerInfo.organizerList[0].organizerId,
      loginMethod: authMode.current?.value,
      litaId: litaUserInfo.current.litaId,
      litaAccessToken: litaUserInfo.current.litaAccessToken,
      litaRefreshToken: litaUserInfo.current.litaRefreshToken,
      groupId: groupId ? Number(groupId) : 0,
      organizerId: organizerIds.current ? Number(organizerIds.current[0]) : 0,
      sex: litaUserInfo.current.sex ? Number(litaUserInfo.current.sex) : 0,
      height: Number.parseFloat(String(litaUserInfo.current.height)),
      weight: Number.parseFloat(String(litaUserInfo.current?.weight)),
      pace: Number.parseFloat(String(litaUserInfo.current?.pace)),
      level: Number(litaUserInfo.current?.level),
      insuranceCardNo: Number(litaUserInfo.current?.insuranceCardNo),
      insuredSymbol: Number(litaUserInfo.current?.insuredSymbol),
      insuredNumber: Number(litaUserInfo.current?.insuredNumber),
      insuredBranchNo: Number(litaUserInfo.current?.insuredBranchNo),
    };
    if (litaUserInfo.current) {
      registerAPI
        .authLogin({
          ...params,
        })
        .then((res) => {
          if (res) {
            if (litaUserInfo.current) {
              setToken(res.accessToken ? res.accessToken : '');
              setUser({
                ...(user || { lat: 0, lng: 0 }),
                id: res.userId ? String(res.userId) : '',
                name: litaUserInfo.current.nickName ? litaUserInfo.current.nickName : '',
                email: litaUserInfo.current.email ? litaUserInfo.current.email : '',
                birthday: litaUserInfo.current.birthDt ? litaUserInfo.current.birthDt : '',
                sex: Sex[String(litaUserInfo.current.sex) as keyof typeof Sex],
                organizerID: litaUserOrganizerIds.current ? litaUserOrganizerIds.current : '',
                useOrganizerID: organizerIds.current ? String(organizerIds.current[0]) : '',
              });
              sendMessageToNative({
                type: 'user-info',
                data: {
                  userName: litaUserInfo.current.nickName ? litaUserInfo.current.nickName : '',
                  userID: String(res?.userId ?? 0),
                  token: res?.accessToken ?? '',
                  sex: Sex[String(litaUserInfo.current.sex) as keyof typeof Sex],
                  organizerID: litaUserOrganizerIds.current,
                  userOrganizerID: organizerIds.current ? String(organizerIds.current[0]) : '',
                },
                callback: (data) => {
                  nlog('callbacklogin');
                },
              });
            }
            setHomeDialog(true);
            router.push('/home');
            // router.push(`/registration/agreement?isAlreadyToken=${isAlreadyToken}`);
          }
        });
    }
  };

  return (
    <AlertDialog open={openDialog}>
      <AlertDialogContent className="rounded-2xl shadow-none border-0 bg-white px-0 gap-0">
        <AlertDialogTitle>
          <div className="flex flex-col items-center mb-2 font-bold " />
        </AlertDialogTitle>
        <AlertDialogDescription className="hidden" />
        <div className="px-6 mb-4">{popupContent}</div>
        <AlertDialogFooter>
          <AlertDialogCancel
            className="rounded-3xl ml-4 mr-4 border-0  bg-primary font-bold h-12 text-white text-[16px]"
            onClick={handleCancel}
          >
            {COMMON_TEXT.BUTTON.CANCEL}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="rounded-3xl ml-4 mr-4 border border-primary  bg-transparent font-bold h-12 text-primary text-[16px]"
          >
            {COMMON_TEXT.BUTTON.KEEP_ON}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
