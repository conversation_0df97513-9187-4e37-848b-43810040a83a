'use client';
import { TextButton } from '@/components/shared/text-button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { LooseObject, LooseObjectItem } from '@/types/register';
import {
  ChevronDown,
  ChevronRight,
  Folder,
  Pencil,
  Search,
  SquareChevronDown,
  SquareChevronRight,
  SquareMinus,
  SquarePlus,
} from 'lucide-react';
export interface UserInfoItemType {
  id: string;
  lable: string;
  value: string;
  value2?: string;
}

interface UserInfoType {
  title: string;
  step: number;
  info: UserInfoItemType[];
}

interface CardInfoPageProps {
  editClick: (item: LooseObject) => void;
  useInfo: LooseObject;
}

export const CardInfoPage = (props: CardInfoPageProps) => {
  const { useInfo } = props;

  const editClick = () => {
    props.editClick(useInfo);
  };

  return (
    <Card className="w-full bg-white p-4 mb-4">
      <CardHeader className="p-0">
        <CardTitle className="text-lg font-bold pb-4 border-b border-solid border-border">
          <div className="flex items-center justify-between">
            <div className="text-lg font-bold">{useInfo.title}</div>
            <TextButton
              onClick={() => {
                editClick();
              }}
            >
              <Pencil />
            </TextButton>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 pt-4">
        {useInfo?.info?.map((item, index) => (
          <div key={index} className="mb-6 last:mb-0">
            <div className="text-sm font-normal mb-2">{item.lable}</div>
            <div className="text-base font-bold mr-2">{item.value}</div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

// TODO: Api type
interface CardInfoPage2Props {
  title: string;
  nameList: any;
  valueList: any;
  editClick: () => void;
}

export const CardInfoPage2 = (props: CardInfoPage2Props) => {
  const { title, nameList, valueList } = props;

  return (
    <Card className="w-full bg-white p-4 mb-4">
      <CardHeader className="p-0">
        <CardTitle className="text-lg font-bold pb-4 border-b border-solid border-border">
          <div className="flex items-center justify-between">
            <div className="text-lg font-bold">{title}</div>
            <TextButton
              onClick={() => {
                props.editClick();
              }}
            >
              <img src="/images/create/edit.svg" alt="edit" />
            </TextButton>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 pt-4">
        {Object.keys(nameList)
          .filter((key) => key in valueList)
          .map((key) => (
            <div key={key} className="mb-6 last:mb-0">
              <div className="text-sm font-normal mb-2">
                {nameList[key as keyof typeof nameList]}
              </div>
              <div className="text-base font-bold mr-2">
                {typeof valueList[key] === 'string' && <div>{valueList[key]}</div>}
                {Array.isArray(valueList[key]) &&
                  valueList[key]?.map((item: string, index: number) => (
                    <div key={index}>{item}</div>
                  ))}
              </div>
            </div>
          ))}
      </CardContent>
    </Card>
  );
};
