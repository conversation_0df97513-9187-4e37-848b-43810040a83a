'use client';

import { missionAPI } from '@/api/modules/mission';
import TopBar from '@/components/layout/top-bar';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useRouter } from '@/hooks/use-next-navigation';
import type { MissionListResponse, MissionPopupDetailResponse } from '@/types/mission';
import { useEffect, useRef, useState } from 'react';
import { Button } from '../_components/button';
export default function Mission() {
  const router = useRouter();
  const [isShow, setIsShow] = useState<boolean>(false);
  // 用户是否已存在 true 存在
  const [isUserAlready, setIsUserAlready] = useState<boolean>(false);
  const popupContentRef = useRef<MissionPopupDetailResponse>();

  useEffect(() => {
    setIsShow(true);
    setIsUserAlready(false);
  }, []);

  // 一時トークン取得API
  const getTempToken = () => {
    // deviceId デバイスID
    return { tempToken: 'eyJhbGciOi' };
  };
  // 「A002_利用規約画面」に遷移する(モード：新規モード)。
  const firstTimeLogin = () => {};

  //「団体認証情報取得 API」を呼び出し、団体認証情報を取得する
  const getOrganizerInfo = () => {
    // deviceId デバイスID
    return { tempToken: 'eyJhbGciOi' };
  };

  const login = () => {
    router.push('/registration/agreement');
  };
  return (
    <div className="flex h-lvh w-full flex-col items-center justify-between">
      <TopBar title={COMMON_TEXT.AUTH.TERMS} enableBack={true} />
      {isShow && (
        <div className="flex flex-col items-center">
          <img className="w-24" src="/images/registration/confirmed.svg" alt="" />
          <div className="text-center mt-4">
            ご利用いただくには、 <br />
            マイナンバーカードによる <br /> 本人確認が必要です。
          </div>
        </div>
      )}
      <Button className="mb-6">本人確認に進む</Button>
      <Button className="mb-6" variant="outline">
        本人確認に進む
      </Button>
    </div>
  );
}
