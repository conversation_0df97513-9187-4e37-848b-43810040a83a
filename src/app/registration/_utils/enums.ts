// 認証モード
export enum AUTH_MODE_TYPE {
  LOGIN_JPKI = '1', // 1：JPKI認証
  LOGIN_SMS_NO = '2', // 2：Smart-LiTA認証_SMS認証なし
  LOGIN_SMS = '3', // 3：Smart-LiTA認証_SMS認証あり
}

// ログイン方式
export enum LOGIN_METHOD_TYPE {
  LOGIN_NO = 0, // 0:なし
  LOGIN_JPKI = 1, // 1:JPKI,
  LOGIN_IDP = 2, // 2:外部IDP,
  LOGIN_SMS = 3, // 3:外部IDP(SMSあり)
}

// 登録済みフラグ
export enum LOGIN_FLG_TYPE {
  LOGIN_NO = 0, //  0:未登録,
  LOGIN = 1, // 1:登録済み
}

export enum YES_NO_TYPE {
  NO = '0', //  0:あり,
  YES = '1', // 1:なし
}

export const USER_INFO_ONE = [
  { id: 'groupId', label: '所属団体' },
  { id: 'nickName', label: 'ニックネーム' },
];

export const USER_INFO_TWO = [
  { id: 'email', label: 'メールアドレス' },
  { id: 'phoneNo', label: '電話番号' },
];

export const USER_INFO_THREE = [
  { id: 'height', label: '身長' },
  { id: 'weight', label: '体重' },
  { id: 'pace', label: '歩幅' },
];

export const USER_INFO_FOUR = [
  { id: 'insuranceCardNo', label: '保険者番号' },
  { id: 'insuredSymbol', label: '被保険者記号' },
  { id: 'insuredNumber', label: '被保険者番号' },
  { id: 'insuredBranchNo', label: '枝番' },
];
