import type {
  BaseSettingType,
  LableType,
  LitaUserInfoResponse,
  OrganizerInfoResponse,
  OrganizerSetupResponse,
} from '@/types/register';
import { useRegisterState } from '../../../store/register';

// コミュニティ構成情報の取得
export const getBaseSetting = (key: string, value: string, childFcnId = 1101) => {
  // const childFcnId = 1101;
  const currentState: OrganizerSetupResponse = useRegisterState.getState().organizerSetupDetail;
  let configType: LableType | undefined = {};

  if (key && currentState) {
    const baseArr: BaseSettingType[] | undefined =
      key === 'baseSetting' ? currentState.baseSetting : currentState.optionSetting;

    if (baseArr) {
      const config = baseArr.find((item) => item.childFcnId === childFcnId);
      if (config?.configurations) {
        configType = config.configurations.find((item) => item.key === value);
      }
    }
  }
  return configType;
};

// 乱数6～10桁の英数字を生成
export const generate = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const length = Math.floor(Math.random() * 5) + 6; // 6-10位
  return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
};
