'use client';
import TopBar from '@/components/layout/top-bar';
import { TextButton } from '@/components/shared/text-button';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { APP_TEXT } from '@/const/text/app';
import { useMessageDialog } from '@/hooks/use-message-dialog';

import { useRouter, useSearchParams } from '@/hooks/use-next-navigation';
import type { LooseObject, LooseObjectItem } from '@/types/register';
import { useEffect, useRef, useState } from 'react';
import { CardInfoPage, CardInfoPage2, type UserInfoItemType } from '../_components/card-info';

export default function PointHintPage() {
  const router = useRouter();
  const searchParams = useSearchParams().get('data');

  const { setDialog } = useMessageDialog();
  const [isOpen, setIsOpen] = useState(true);
  const pointRefs = useRef<{ [key: string]: HTMLElement | null }>({});
  // const useListInfo = useRef();
  useEffect(() => {
    // useListInfo.current = searchParams ? JSON.parse(searchParams) : [];
    setUseListInfo(searchParams ? JSON.parse(searchParams) : []);
  }, [searchParams]);

  // TODO: mock data
  const [useListInfo, setUseListInfo] = useState([]);

  const [agreeMentList, setAgreeMentList] = useState([
    'AAAの利用規約',
    'BBBの利用規約',
    'CCCの利用規約',
  ]);

  const transformData = (data: LooseObject[]) => {
    return data.reduce<Record<string, string | number>>((acc, item) => {
      item.info?.map((infoItem) => {
        if (infoItem.key !== undefined && typeof infoItem.key !== 'symbol') {
          // 确保 key 不是 undefined 且不是 symbol
          acc[infoItem.key as string | number] = infoItem.value !== undefined ? infoItem.value : ''; // 处理可能的 undefined value
        }
      });
      return acc;
    }, {});
  };

  const editClick = (item: LooseObject) => {
    const infoArr: LooseObject[] = [];
    useListInfo.map((its: LooseObject) => {
      if (item.step === 3 && its.step === 3) {
        infoArr.push(its);
      }
    });

    if (infoArr.length === 0) {
      infoArr.push(item);
    }
    console.log(infoArr, 'infoArr');
    router.push(
      `/registration/user-information?data=${encodeURIComponent(JSON.stringify({ step: item.step, info: transformData(infoArr) }))}`,
    );
  };

  const linkClick = () => {};

  const confirmClick = () => {
    setDialog(true, {
      title: APP_TEXT.REGISTRATION_CREATE.CONFIRM_TITLE,
      content: (
        <div className="text-base font-normal">
          <img src="/images/create/finish.svg" alt="complete" className="mb-2" />
          {APP_TEXT.REGISTRATION_CREATE.CONFIRM_CONTENT}
        </div>
      ),
    });
  };

  useEffect(() => {}, []);

  return (
    <div className="bg-background">
      <TopBar title={APP_TEXT.REGISTRATION_CREATE.TITLE} />
      <div className="px-6 pt-4">
        {useListInfo.map((item, index) => (
          <CardInfoPage key={index} useInfo={item} editClick={editClick} />
        ))}

        {/* 利用規約 */}
        <div className="text-base font-normal ">
          {APP_TEXT.REGISTRATION_CREATE.CONFIRM_AGREEMENT}
        </div>
        <div className="py-4">
          {agreeMentList?.map((item, index) => (
            <div className="text-base font-normal mb-1" key={index}>
              {index + 1}.<TextButton onClick={linkClick}>{item}</TextButton>
            </div>
          ))}
        </div>
        <Button className="w-full rounded-3xl h-12 mb-2" onClick={confirmClick}>
          登録する
        </Button>
      </div>
    </div>
  );
}
