'use client';

import { registerAPI } from '@/api/modules/register';
import TopBar from '@/components/layout/top-bar';
import { Checkbox } from '@/components/shared/checkbox';
import { ROUTES } from '@/const/routes';
import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import { useDeviceDetect, useDeviceDetectReliable } from '@/hooks/use-device-detect';
import { useGlobalInit } from '@/hooks/use-global-init';
import { useLoading } from '@/hooks/use-loading';
import { useMessageDialog } from '@/hooks/use-message-dialog';
import { useSearchParams } from '@/hooks/use-next-navigation';
import { useRouter } from '@/hooks/use-next-navigation';
import { Sex, useAuthStore } from '@/store/auth';
import { useGlobalStore } from '@/store/global';
import { useRegisterState } from '@/store/register';
import type { FormData, LableType, organizerTermsResponse } from '@/types/register';
import { nlog } from '@/utils/logger';
import { sendMessageToNative } from '@/utils/native-bridge';
import { format } from 'date-fns';
import { useEffect, useRef, useState } from 'react';
import { number } from 'zod';
import { Button } from '../_components/button';
import LitaDialog from '../_components/lita-dialog';
import { getBaseSetting } from '../_utils/data-convert';
import { AUTH_MODE_TYPE, LOGIN_FLG_TYPE, LOGIN_METHOD_TYPE, YES_NO_TYPE } from '../_utils/enums';
import AgreeAlertDialog from './_components/alert-dialog-mission';
import UserInfoForm from './_components/user-info';
// import { MOCK_AGREEMENT, MOCK_AGREEMENT_2 } from './_mock';

export default function Mission() {
  const { isMobile } = useDeviceDetectReliable();
  const router = useRouter();
  const searchParams = useSearchParams().get('isAlreadyToken');
  const isAlreadyToken: boolean = !!(searchParams && searchParams === 'true');
  const { checkRiskForAddOrg } = useGlobalInit();
  const { isRiskInited, setIsRiskInited } = useGlobalStore();
  const {
    setGroupAdditionFlag,
    setOrganizerInfo,
    setFriendInfo,
    setAccountProfile,
    setHomeDialog,
    deviceId,
    accountProfile,
    organizerInfo,
    organizerSetupDetail,
    litaUserInfo,
    newLoginFlag,
    businesstoken,
    friendInfo,
    useOrganizerId,
    litaOrganizerIds,
  } = useRegisterState();
  const [isShow, setIsShow] = useState<boolean>(false);
  const [personalInfoCollection, setPersonalInfoCollection] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [trigger, setTrigger] = useState<boolean>(false);
  const [agreementContent, setAgreementContent] = useState<organizerTermsResponse>();
  const [termsType, setTermsType] = useState<string>('');
  const formRef = useRef<{ submitForm: () => Promise<FormData> }>(null);

  const userFromData = useRef<FormData>();
  const [authMode, setAuthMode] = useState<LableType>();
  const [organizerId, setOrganizerId] = useState<number>();
  const [organizerCode, setOrganizerCode] = useState<string>();
  const registerUrl = useRef<string | undefined>('');
  const [isOrganizerTerm, setIsOrganizerTerm] = useState<boolean>(false);
  const [individualTerm1Content, setIndividualTerm1Content] = useState<boolean>(false);
  const [individualTerm2Content, setIndividualTerm2Content] = useState<boolean>(false);
  const [individualTerm3Content, setIndividualTerm3Content] = useState<boolean>(false);

  const { setUser, setToken, user, token } = useAuthStore();
  const { setLoading, isLoading } = useLoading();
  useEffect(() => {
    if (organizerSetupDetail) {
      const base = getBaseSetting('baseSetting', 'personalInfoCollection');
      if (base) {
        setPersonalInfoCollection(base.value === YES_NO_TYPE.YES);
      }
      const baseAuthMode = getBaseSetting('baseSetting', 'authMode');
      if (baseAuthMode) {
        setAuthMode(baseAuthMode);
      }
    }
    if (organizerInfo?.organizerList?.[0]?.organizerId) {
      setOrganizerId(organizerInfo.organizerList[0].organizerId);
      setOrganizerCode(organizerInfo.organizerList[0].organizerCode);
      getAgreement(organizerInfo.organizerList[0].organizerId);
    }
  }, [organizerSetupDetail, organizerInfo]);

  // 利用規約取得API
  const getAgreement = (id: number) => {
    setLoading(true);
    registerAPI.organizerSetup({ organizerId: id, fcnId: 11 }).then((res) => {
      setLoading(false);
      if (res) {
        const config = res.baseSetting?.find((item) => item.childFcnId === 1102);
        if (config) {
          const termsType = config.configurations?.find((item) => item.key === 'termsType');
          if (termsType?.value) {
            setTermsType(termsType.value);
          }
        }
      }
    });
    registerAPI.organizerTerms({ organizerId: id }).then((res) => {
      setLoading(false);
      if (res) {
        setAgreementContent(res);
      }
    });
  };

  const disagreeFuc = () => {
    setOpen(true);
  };
  const onCancel = () => {
    setOpen(false);
  };

  const handleParentTriggerSubmit = async () => {
    nlog('zzz13 handleParentTriggerSubmit');
    // 団体規約を選択するかどうか
    if (!isOrganizerTerm) {
      nlog('zzz13 isOrganizerTerm');
      setDialog(true, {
        content: (
          <div className="text-base font-normal">
            <div className="max-h-64 break-all">
              主催団体に対応する規約のチェックボックスに必ずチェックを入れなければ、次のステップに進むことができません。
            </div>
          </div>
        ),
      });
      return;
    }

    let errorVerify: any = null;

    nlog(`zzz13 personalInfoCollection ${personalInfoCollection} newLoginFlag ${newLoginFlag}`);
    // 匿名ユーザー
    if (!personalInfoCollection && formRef.current) {
      errorVerify = await formRef.current.submitForm();
      if (errorVerify && newLoginFlag) {
        // 「本人確認」機能から遷移する以外　かつ　団体の個人情報登録必要なしの場合
        // 匿名ユーザー新規作成API
        setLoading(true);
        nlog('zzz13 userInfoLoginNo 1');
        userInfoLoginNo();
      }
    }
    // if (errorVerify !== null) return;
    nlog(`zzz13 newLoginFlag ${newLoginFlag}`);
    // 新規モード
    if (newLoginFlag) {
      if (personalInfoCollection) {
        // 団体認証方式＝2.Smart-LiTA認証_SMS認証なし または 3.Smart-LiTA認証_SMS認証ありの場合：
        if (
          authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS_NO ||
          authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS
        ) {
          // 詳細はA001の1.4.3.2.へ参照
          setTrigger(true);
          nlog('zzz13 setTrigger');
        }
      }
    } else {
      // ログインモード
      // 業務トークンあり
      nlog(`zzz13 businesstoken ${businesstoken}`);
      if (businesstoken !== '') {
        // 個人情報登録必要なしの場合
        if (!personalInfoCollection) {
          nlog('zzz13 personalInfoCollection');
          // 「ユーザー所属団体追加API」
          userOrganizersAdd();
        } else {
          nlog('zzz13 personalInfoCollection');
          // 業務トークンなし
          // 個人情報登録必要ありかつ(団体認証方式＝2.Smart-LiTA認証_SMS認証なし または 3.Smart-LiTA認証_SMS認証あり)の場合
          if (
            authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS_NO ||
            authMode?.value === AUTH_MODE_TYPE.LOGIN_SMS
          ) {
            if (accountProfile.externalLoginFlg === LOGIN_FLG_TYPE.LOGIN) {
              nlog('zzz13 accountProfile.externalLoginFlg 1');
              getUserInfoLoginFlg();
            } else {
              nlog('zzz13 accountProfile.externalLoginFlg 2');
              // ソーシャル未登録の場合、新規モードの「1.5」処理を行う、団体追加フラグ＝１。
              setGroupAdditionFlag(true); // 団体追加フラグ＝１
              setTrigger(true);
            }
          }
        }
      } else {
        // ログインモード_業務トークンなし
        nlog('zzz13 businesstoken 3');
        getUserInfoLoginFlg();
      }
    }
  };

  const getUserInfoLoginFlg = () => {
    if (accountProfile.userInfoLoginFlg === LOGIN_FLG_TYPE.LOGIN) {
      // 個人情報が補足する必要があるの場合、「A006_個人情報登録画面」に遷移する。(ログインモード_業務トークンあり)
      router.push('/registration/user-information');
      // router.push('/registration/user-information');
    } else {
      // 個人情報が補足する必要がないの場合、「A007_個人情報登録確認画面」に遷移する。(ログインモード_業務トークンあり)
      router.push('/registration/user-information?loginToken=1');
    }
  };
  const addFriend = () => {
    registerAPI
      .addFriend({ uuid: String(friendInfo.friendUserId) })
      .then((res) => {})
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };

  // 匿名ユーザー新規作成API
  const userInfoLoginNo = () => {
    if (userFromData.current) {
      registerAPI
        .registerAnonymous({
          deviceId: deviceId,
          organizerId: organizerId,
          groupId: Number(userFromData.current.groupId),
          sex: Number(userFromData.current.sex),
          birthDt: format(String(userFromData.current.birthDt), 'yyyyMMdd'),
          nickName: userFromData.current.nickName,
        })
        .then((res) => {
          setLoading(false);
          if (res) {
            if (friendInfo.friendUserId) {
              addFriend();
            }
            const resStr = JSON.stringify(res);
            setToken(res.accessToken ? res.accessToken : '');
            if (userFromData.current) {
              setUser({
                ...(user || { lat: 0, lng: 0 }),
                id: String(res?.userId) ?? '',
                name: userFromData.current.nickName ? userFromData.current.nickName : '',
                email: '',
                birthday: userFromData.current
                  ? format(String(userFromData.current.birthDt), 'yyyyMMdd')
                  : '',
                sex: Sex[String(userFromData.current.sex) as keyof typeof Sex],
                organizerID: String(organizerId),
                organizerCode: String(organizerCode),
                useOrganizerID: String(organizerId),
              });
            }
            if (!isMobile) {
              gotoHome();
            }
            sendMessageToNative({
              type: 'user-info',
              data: {
                userName: userFromData.current?.nickName,
                userID: String(res?.userId) ?? '0',
                token: res?.accessToken ?? '',
                sex: Sex[String(litaUserInfo.sex) as keyof typeof Sex],
                organizerID: String(organizerId),
                organizerCode: String(organizerCode),
                userOrganizerID: String(organizerId),
              },
              callback: (data) => {},
            });
            // sendMessageToNative({
            //   type: 'user-info',
            //   data: {
            //     userID: res?.userId ?? 0,
            //     token: res?.accessToken ?? '',
            //   },
            //   callback: (data) => {},
            // });
            // A003_アクセス許可画面
            sendMessageToNative({
              type: 'show-location-permission',
              data: {},
              callback: (data) => {
                setHomeDialog(true);
                gotoHome();
              },
            });
          }
        })
        .catch((response) => {
          setLoading(false);
          return Promise.reject();
        });
    }
  };

  const gotoHome = () => {
    if (isRiskInited) {
      nlog('agreement: checkRiskForAddOrg');
      checkRiskForAddOrg();
    } else {
      nlog('agreement: home');
      router.push('/home');
    }
  };
  // ユーザー所属団体追加API
  const userOrganizersAdd = () => {
    nlog(`zzz13 userOrganizersAdd organizerInfo.organizerList ${organizerInfo.organizerList}`);
    nlog(`zzz13 userOrganizersAdd userFromData.current ${userFromData.current}`);
    if (!organizerInfo.organizerList || !userFromData.current) return;
    // organizerList
    nlog(
      `zzz13 userOrganizersAdd organizerInfo.organizerList[0].organizerId ${organizerInfo.organizerList[0].organizerId}`,
    );
    nlog(`zzz13 userOrganizersAdd userFromData.current.groupId ${userFromData.current.groupId}`);
    registerAPI
      .userOrganizers({
        organizerId: Number(organizerInfo.organizerList[0].organizerId),
        groupId: Number(userFromData.current.groupId),
      })
      .then(async (res) => {
        nlog(`zzz13 userOrganizersAdd res ${res}`);
        if (res) {
          // ユーザー情報更新API
          // await userProfilePATCH();
          const allOrganizerIds = res.organizers.map((org) => org.organizerId).join(',');
          const allOrganizerCodes = res.organizers.map((org) => org.organizerCode).join(',');
          const defaultOrganizer = res.organizers.find((org) => org.isDefault === 1);
          const defaultOrganizerId = defaultOrganizer?.organizerId;
          if (userFromData.current) {
            setUser({
              ...(user || { lat: 0, lng: 0 }),
              id: String(user?.id) ?? '',
              name: userFromData.current.nickName ? userFromData.current.nickName : '',
              email: '',
              userAgent: navigator.userAgent,
              birthday: userFromData.current
                ? format(String(userFromData.current.birthDt), 'yyyyMMdd')
                : '',
              sex: Sex[String(userFromData.current.sex) as keyof typeof Sex],
              organizerID: String(allOrganizerIds),
              organizerCode: String(allOrganizerCodes),
              useOrganizerID: String(defaultOrganizerId),
            });
          }

          if (!isMobile) {
            gotoHome();
          }
          sendMessageToNative({
            type: 'user-info',
            data: {
              userID: String(user?.id) ?? '0',
              userName: userFromData.current?.nickName,
              sex: Sex[String(litaUserInfo.sex) as keyof typeof Sex],
              organizerCode: String(allOrganizerCodes),
              organizerID: String(allOrganizerIds),
              userOrganizerID: String(defaultOrganizerId),
              token: token,
            },
          });
          setHomeDialog(true);
          gotoHome();

          // // A003_アクセス許可画面
          // sendMessageToNative({
          //   type: 'show-location-permission',
          //   data: {},
          //   callback: (data) => {
          //     setHomeDialog(true);
          //     router.push('/home');
          //   },
          // });
          console.log('registerAnonymous', res);
        }
      })
      .catch((error) => {
        // 安全なエラー情報の出力
        nlog(`zzz13 userOrganizersAdd error 全体: ${JSON.stringify(error)}`);

        // error.response の存在確認
        if (error && typeof error === 'object' && 'response' in error) {
          nlog(`zzz13 userOrganizersAdd error.response: ${JSON.stringify(error.response)}`);

          // error.response.data の存在確認
          if (error.response && typeof error.response === 'object' && 'data' in error.response) {
            nlog(
              `zzz13 userOrganizersAdd error.response.data: ${JSON.stringify(error.response.data)}`,
            );

            // error.response.data.message の存在確認
            if (
              error.response.data &&
              typeof error.response.data === 'object' &&
              'message' in error.response.data
            ) {
              nlog(
                `zzz13 userOrganizersAdd error.response.data.message: ${error.response.data.message}`,
              );
            } else {
              nlog('zzz13 userOrganizersAdd error.response.data.message は存在しません');
            }
          } else {
            nlog('zzz13 userOrganizersAdd error.response.data は存在しません');
          }
        } else {
          nlog('zzz13 userOrganizersAdd error.response は存在しません');
        }

        // エラーの型と構造を確認
        nlog(`zzz13 userOrganizersAdd error の型: ${typeof error}`);
        nlog(
          `zzz13 userOrganizersAdd error のキー: ${error && typeof error === 'object' ? Object.keys(error).join(', ') : 'N/A'}`,
        );
      });
  };

  // ユーザー情報更新API
  const userProfilePATCH = () => {
    if (!organizerInfo.organizerList || !userFromData.current) return;
    // organizerList
    registerAPI
      .userProfile({
        litaAccessToken: litaUserInfo.litaAccessToken,
        litaRefreshToken: litaUserInfo.litaRefreshToken,
        nickName: userFromData.current.nickName,
        sex: Number(userFromData.current.sex),
        birthDt: String(format(userFromData.current.birthDt, 'yyyyMMdd')),
        // String(format(defaultValue, formatDate))
      })
      .then((res) => {
        if (res) {
          gotoHome();
          // A003_アクセス許可画面
          // sendMessageToNative({
          //   type: 'show-location-permission',
          //   data: {},
          //   callback: (data) => {
          //     setHomeDialog(true);
          //     router.push('/home');
          //   },
          // });
        }
      });
  };

  const handleSubmit = (data: FormData) => {
    format(data.birthDt, 'yyyy/MM/dd');
    const formattedDate: string = format(new Date(data.birthDt), 'yyyy/MM/dd');
    userFromData.current = { ...data, birthDt: formattedDate };
  };
  const { setDialog } = useMessageDialog();
  const individualFileClick = (File: any | undefined) => {
    if (!File) return;
    setDialog(true, {
      content: (
        <div className="text-base font-normal">
          <div className="max-h-64 break-all overflow-auto max-h-[70vh]">
            <AgreementContent html={File} />
          </div>
        </div>
      ),
    });
  };

  return (
    // grid grid-rows-[auto_1fr] h-screen
    <div>
      {!isLoading && (
        <div className="">
          <TopBar
            title={COMMON_TEXT.AUTH.TERMS}
            enableBack={true}
            onBack={() => {
              if (Object.keys(accountProfile).length !== 0) {
                router.push('/home');
              }
            }}
          />
          <div className="flex justify-between flex-col px-6 overflow-auto">
            <div>
              <div className="mb-6">
                {termsType === '1' && agreementContent?.commonTerm && (
                  <AgreementContent html={agreementContent?.commonTerm} />
                )}
                {termsType === '2' && agreementContent?.organizerTerms?.organizerTerm && (
                  <AgreementContent html={agreementContent?.organizerTerms?.organizerTerm} />
                )}
                {/* <AgreementContent html={MOCK_AGREEMENT_2} /> */}
              </div>
              <div className="text-base mb-4">
                以下の内容に同意する場合、チェックボックスを選択してください。
              </div>
              <div className="border-border w-full  border-b">
                {organizerInfo.organizerList && (
                  <div className="flex text-primary  items-center mb-4">
                    <Checkbox
                      checked={isOrganizerTerm}
                      onCheckedChange={() => {
                        setIsOrganizerTerm(!isOrganizerTerm);
                      }}
                    />
                    <span className="w-full justify-start pl-2 py-0 h-6 text-black">
                      {organizerInfo.organizerList[0]?.organizerName}利用規約
                    </span>
                  </div>
                )}

                {agreementContent?.organizerTerms && (
                  <>
                    {agreementContent.organizerTerms.individualTerm1Content && (
                      <div className="text-primary flex items-center mb-4">
                        <Checkbox
                          checked={individualTerm1Content}
                          onCheckedChange={() => setIndividualTerm1Content(!individualTerm1Content)}
                        />
                        <Button
                          type="button"
                          variant="link"
                          className="w-full justify-start pl-2 py-0 h-6"
                          onClick={() => {
                            individualFileClick(
                              agreementContent?.organizerTerms?.individualTerm1Content,
                            );
                          }}
                        >
                          主催団体の個別同意事項 A
                        </Button>
                      </div>
                    )}
                    {agreementContent.organizerTerms.individualTerm2Content && (
                      <div className="text-primary flex items-center mb-4">
                        <Checkbox
                          checked={individualTerm2Content}
                          onCheckedChange={() => setIndividualTerm2Content(!individualTerm2Content)}
                        />
                        <Button
                          type="button"
                          variant="link"
                          className="w-full justify-start pl-2 py-0 h-6"
                          onClick={() => {
                            individualFileClick(
                              agreementContent?.organizerTerms?.individualTerm2Content,
                            );
                          }}
                        >
                          主催団体の個別同意事項 B
                        </Button>
                      </div>
                    )}
                    {agreementContent.organizerTerms.individualTerm3Content && (
                      <div className="text-primary flex items-center mb-4">
                        <Checkbox
                          checked={individualTerm3Content}
                          onCheckedChange={() => setIndividualTerm3Content(!individualTerm3Content)}
                        />
                        <Button
                          type="button"
                          variant="link"
                          className="w-full justify-start pl-2 py-0 h-6"
                          onClick={() => {
                            individualFileClick(
                              agreementContent?.organizerTerms?.individualTerm3Content,
                            );
                          }}
                        >
                          主催団体の個別同意事項 C
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </div>
              <div>
                {!personalInfoCollection && <UserInfoForm ref={formRef} onSubmit={handleSubmit} />}
              </div>
            </div>
            <div className="my-6">
              <Button type="submit" className="mb-2" onClick={handleParentTriggerSubmit}>
                {COMMON_TEXT.DIALOG.AGREE}
              </Button>
              <Button type="button" variant="outline" className="pb-6" onClick={disagreeFuc}>
                {COMMON_TEXT.DIALOG.DISAGREE}
              </Button>
            </div>
          </div>
          {open && organizerInfo.organizerList && (
            <AgreeAlertDialog
              open={open}
              onCancel={onCancel}
              popupContent={`${organizerInfo.organizerList[0]?.organizerName} ${APP_TEXT.REGISTRATION_CREATE.CONFIRM_DISAGREE}`}
            />
          )}

          {trigger && (
            <LitaDialog
              trigger={trigger}
              isAlreadyToken={isAlreadyToken}
              popupContent={
                'このアカウントは他のデバイスでログイン実績があります。ログインを続けてもよろしいでしょうか。'
              }
            />
          )}
        </div>
      )}
    </div>
  );
}

function AgreementContent({ html }: { html: string }) {
  return (
    <>
      <style>
        {`
        #agreement-content {
          font-size: 14px;
        }
          #agreement-content h1 {
          font-weight: bold;
          font-size: 20px;
          text-align: center;
          padding-top: 10px;
        }
        #agreement-content h5 {
          font-weight: bold;
        }
        #agreement-content p {
          padding-top: 8px;
          padding-bottom: 8px;
          margin: 0;
          line-height: 26px;
        }
        #agreement-content blockquote {
          display: block;
          overflow: auto;
          overflow-scrolling: touch;
          border-left: 3px solid rgba(0, 0, 0, 0.4);
          background: rgba(0, 0, 0, 0.05);
          color: #6a737d;
          padding-top: 10px;
          padding-bottom: 10px;
          padding-left: 20px;
          padding-right: 10px;
          margin-bottom: 20px;
          margin-top: 20px;
        }
        #agreement-content ol {
          margin-top: 8px;
          margin-bottom: 8px;
          padding-left: 25px;
          list-style-type: decimal;
        }
        #agreement-content li {
          font-size: 14px;
        }
        `}
      </style>
      {/* biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>*/}
      {/* <div id="agreement-content" dangerouslySetInnerHTML={{ __html: MOCK_AGREEMENT }} /> */}
      {/* biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>*/}
      <div id="agreement-content" dangerouslySetInnerHTML={{ __html: html }} />
    </>
  );
}
