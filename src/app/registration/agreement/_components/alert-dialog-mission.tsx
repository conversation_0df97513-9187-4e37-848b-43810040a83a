'use client';

import { APP_TEXT } from '@/const/text/app';
import { COMMON_TEXT } from '@/const/text/common';
import type { ExtendedQuizUpdateResponse, MissionPopupDetailResponse } from '@/types/mission';
import { useEffect, useState } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from '../../_components/alert-dialog';

export default function AgreeAlertDialog({
  open,
  popupContent,
  onCancel,
}: {
  open: boolean;
  popupContent: string;
  onCancel: () => void;
}) {
  const handleCancel = () => {
    onCancel();
  };
  return (
    <AlertDialog open={open}>
      <AlertDialogContent className="rounded-2xl shadow-none border-0 bg-white px-0 gap-0">
        <AlertDialogTitle />

        <AlertDialogDescription className="hidden" />
        {popupContent && <div className="px-8">{popupContent}</div>}
        <AlertDialogFooter>
          <AlertDialogCancel
            className="rounded-3xl ml-4 mr-4 border-0  bg-primary font-bold h-12 text-white text-[16px]"
            onClick={handleCancel}
          >
            {COMMON_TEXT.BUTTON.CLOSE}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
