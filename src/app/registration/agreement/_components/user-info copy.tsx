// 'use client';

// import TopBar from '@/components/layout/top-bar';
// import { Checkbox } from '@/components/shared/checkbox';
// import { DateRangeSelect } from '@/components/shared/date-range-select';
// import { Select } from '@/components/shared/select';
// import { Button } from '@/components/ui/button';
// import {
//   Form,
//   FormControl,
//   FormDescription,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from '@/components/ui/form';
// import { APP_TEXT } from '@/const/text/app';
// import { COMMON_TEXT } from '@/const/text/common';
// import { cn } from '@/lib/utils';
// import type { MissionListResponse, MissionPopupDetailResponse } from '@/types/mission';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { Popover, PopoverContent, PopoverTrigger } from '@radix-ui/react-popover';
// import { format } from 'date-fns';
// import { Calendar, CalendarIcon } from 'lucide-react';
// import { useEffect, useRef, useState } from 'react';
// import { useForm } from 'react-hook-form';
// import { z } from 'zod';
// import { DatePicker } from '../../_components/date-picker';
// // const formSchema = z.object({
// //   id: z.string().min(1, { message: 'ログインIDを入力してください' }),
// //   password: z.string().min(0, { message: 'パスワードを入力してください' }),
// // });

// const formSchema = z.object({
//   dob: z.date({
//     required_error: 'A date of birth is required.',
//   }),
// });

// export default function Mission() {
//   const [isShow, setIsShow] = useState<boolean>(false);

//   useEffect(() => {
//     setIsShow(true);
//   }, []);
//   const form = useForm<z.infer<typeof formSchema>>({
//     resolver: zodResolver(formSchema),
//   });

//   //   const form = useForm<z.infer<typeof formSchema>>({
//   //     resolver: zodResolver(formSchema),
//   //     defaultValues: {
//   //       dob: '',
//   //       //   selected: '',
//   //       //   capacity: 0,
//   //       //   select: '',
//   //       //   dateRange: undefined,
//   //       //   dateTime: undefined,
//   //       //   dateTime2: undefined,
//   //       //   radio: 'all',
//   //       //   dateStart: undefined,
//   //       //   dateEnd: undefined,
//   //       //   date: undefined,
//   //       //   checkbox: ['recents', 'home'],
//   //     },
//   //   });

//   const onSubmit = () => {};

//   const onChangeDatePicker = () => {};

//   return (
//     <div>
//       <div className="my-6">
//         本サービスをより楽しく利用するため、以下の項目を入力してください。
//         ご利用開始後も変更可能です。
//       </div>
//       <div className="mb-4">
//         <Form {...form}>
//           <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
//             <Select
//               defaultValue="apple"
//               options={[
//                 { value: 'apple', name: 'Apple' },
//                 { value: 'banana', name: 'Banana' },
//                 { value: 'blueberry', name: 'Blueberry' },
//                 { value: 'grapes', name: 'Grapes' },
//                 { value: 'pineapple', name: 'Pineapple' },
//               ]}
//               title="23456789"
//               //   onSelect={(value) => {
//               //     onSelectSex(value);
//               //   }}
//             />
//             {/* biome-ignore lint/style/useSelfClosingElements: <explanation> */}
//             {/* <DatePicker value="" onChange={() => onChangeDatePicker}></DatePicker> */}

//             <FormField
//               control={form.control}
//               name="dob"
//               render={({ field }) => (
//                 <FormItem className="flex flex-col">
//                   <FormLabel>Date of birth</FormLabel>
//                   <Popover>
//                     <PopoverTrigger asChild>
//                       <FormControl>
//                         <Button
//                           variant={'outline'}
//                           className={cn(
//                             'w-[240px] pl-3 text-left font-normal',
//                             !field.value && 'text-muted-foreground',
//                           )}
//                         >
//                           {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
//                           <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
//                         </Button>
//                       </FormControl>
//                     </PopoverTrigger>
//                     <PopoverContent className="w-auto p-0" align="start">
//                       <Calendar
//                         mode="single"
//                         selected={field.value}
//                         onSelect={field.onChange}
//                         disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
//                         initialFocus
//                       />
//                     </PopoverContent>
//                   </Popover>
//                   <FormDescription>
//                     Your date of birth is used to calculate your age.
//                   </FormDescription>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />
//           </form>
//         </Form>
//       </div>
//     </div>
//   );
// }
