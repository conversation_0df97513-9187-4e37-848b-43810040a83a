'use client';

import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ERROR_CODES } from '@/const/error-codes';
import { COMMON_TEXT } from '@/const/text/common';
import { cn } from '@/lib/utils';
import { useRegisterState } from '@/store/register';
import type { FormData, OptionsType } from '@/types/register';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  differenceInYears,
  format,
  isBefore,
  isFuture,
  isValid,
  parseISO,
  subYears,
} from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { map, string, z } from 'zod';
import { Button } from '../../_components/button';

import { Select } from '@/components/shared/select';
import { nlog } from '@/utils/logger';
// import { Select } from '../../_components/select';
import { WheelDatePicker } from '../../_components/wheel-date-picker';
import { getBaseSetting } from '../../_utils/data-convert';
// 创建Zod校验模式（使用闭包接收动态minimumAge）
// const createBirthDateSchema = (minimumAge: number) => z
const createFormSchema = (minimumAge: number, isEntryAge: boolean) =>
  z.object({
    groupId: z
      .string({
        required_error: ERROR_CODES.EHFT0009,
      })
      .min(1, ERROR_CODES.EHFT0009),
    sex: z.string().optional(),
    nickName: z.string().max(20, '20文字以内入力してください。'),
    birthDt: z
      .string({
        required_error: ERROR_CODES.EHFT0009,
      })
      .min(1, { message: '誕生日に空白をしないようにしてください。' })
      .refine(
        (val) => {
          const date = new Date(val);
          return !isFuture(date);
        },
        {
          message: '誕生日に未来の日付を指定しないようにしてください。',
        },
      )
      .refine(
        (val) => {
          if (isEntryAge) return true;
          if (minimumAge === 0) return true;
          const date = new Date(val);
          const age = differenceInYears(new Date(), date);
          return age >= minimumAge;
        },
        {
          message: `${minimumAge}歳に満たすようにしてください。`,
        },
      ),
    // birthDt: z.date({
    //   required_error: ERROR_CODES.EHFT0009,
    // }),
  });

interface UserInfoFormHandles {
  submitForm: () => Promise<FormData>;
}

interface UserInfoFormProps {
  onSubmit: (data: FormData) => void;
}

const UserInfoForm = forwardRef<UserInfoFormHandles, UserInfoFormProps>(({ onSubmit }, ref) => {
  const { organizerInfo, organizerSetupDetail, accountProfile, QRGroupId, friendInfo } =
    useRegisterState();
  const entryAgeRef = useRef<number>(0);
  const groupOptionsList = useRef<OptionsType[]>();
  const [accountProfileData, setAccountProfileData] = useState<any>({});
  const [groupDisabled, setGroupDisabled] = useState<boolean>(false);
  const friendEntryAgeRef = useRef<boolean>(false);

  useEffect(() => {
    setAccountProfileData(accountProfile);
    console.log(accountProfile);
  }, [accountProfile]);

  const FormSchema = createFormSchema(entryAgeRef.current, friendEntryAgeRef.current);
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      sex: accountProfile.sex ? String(accountProfile.sex) : '1',
      nickName: accountProfile.nickName ? accountProfile.nickName : '',
      groupId: '',
      birthDt: accountProfile.birthDt ? accountProfile.birthDt : '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (organizerInfo.organizerList) {
      // 申込年齢 0:年齢制限なし
      const entryAge = getBaseSetting('baseSetting', 'entryAge');
      if (entryAge) {
        entryAgeRef.current = Number(entryAge.value);
      }
      const groupList = organizerInfo.organizerList[0]?.groupList;
      let friendGroupId = '';
      if (groupList) {
        const list: OptionsType[] = [];
        const friendList: OptionsType[] = [];
        groupList.map((item) => {
          if (item.isPublic === 1) {
            list.push({
              value: String(item.groupId),
              name: item.groupNm ? item.groupNm : '',
            });
          }
          friendList.push({
            value: String(item.groupId),
            name: item.groupNm ? item.groupNm : '',
          });
          if (item.groupType === 3) {
            friendGroupId = String(item.groupId);
          }
        });
        groupOptionsList.current = list;
        if (list.length === 1) {
          setGroupDisabled(true);
          form.setValue('groupId', list[0].value);
        } else if (QRGroupId) {
          setGroupDisabled(true);
          form.setValue('groupId', String(QRGroupId));
        } else if (friendInfo.friendUserId && accountProfile.birthDt) {
          const date = new Date(accountProfile.birthDt);
          const age = differenceInYears(new Date(), date);
          if (age < entryAgeRef.current && friendGroupId) {
            groupOptionsList.current = friendList;
            friendEntryAgeRef.current = true;
            setGroupDisabled(true);
            form.setValue('groupId', friendGroupId);
          }
        } else {
          friendEntryAgeRef.current = false;
          setGroupDisabled(false);
        }
      }
    }
  }, [organizerInfo, organizerSetupDetail]);

  // フォーム送信の処理
  const handleSubmit = form.handleSubmit((data) => {
    onSubmit(data);
    return data;
  });

  // 親コンポーネントへのコミット方法の暴露
  useImperativeHandle(ref, () => ({
    submitForm: async () => {
      // フォーム検証を手動でトリガーする
      const isValid = await form.trigger();
      if (!isValid) {
        return false as unknown as FormData;
      }
      // フォームデータの取得
      const formData = form.getValues();
      onSubmit(formData);
      return formData;
    },
  }));

  const calculateExactAge = (birthDate: Date) => {
    const today = new Date();
    const age = differenceInYears(today, birthDate);

    // 检查今年是否已经过了生日
    const birthDateThisYear = new Date(
      today.getFullYear(),
      birthDate.getMonth(),
      birthDate.getDate(),
    );

    return isBefore(today, birthDateThisYear) ? age - 1 : age;
  };

  return (
    <div className="pb-6">
      <div className="my-6">
        <p> 本サービスをより楽しく利用するため、以下の項目を入力してください。</p>
        <p> ご利用開始後も変更可能です。</p>
      </div>

      <Form {...form}>
        <form onSubmit={handleSubmit} className="space-y-6">
          <FormField
            control={form.control}
            name="groupId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  所属団体
                  <span className="text-primary bg-primary/10 px-1 rounded-sm py-[2px] ml-1">
                    必須
                  </span>
                </FormLabel>
                <Select
                  className="text-black"
                  placeholder="選択する"
                  defaultValue={field.value}
                  disabled={groupDisabled}
                  options={groupOptionsList.current ? groupOptionsList.current : []}
                  title="所属団体"
                  onSelect={(value) => {
                    field.onChange(value);
                  }}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="birthDt"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  生年月日
                  <span className="text-primary bg-primary/10 px-1 rounded-sm py-[2px] ml-1">
                    必須
                  </span>
                </FormLabel>
                <FormControl>
                  <WheelDatePicker
                    value={field.value ? new Date(field.value) : undefined}
                    defaultValue={field.value ? field.value : undefined}
                    onChange={(date: Date | undefined) => {
                      if (date) {
                        field.onChange(date ? String(format(date, 'yyyy/MM/dd')) : '');
                      }
                    }}
                  />
                </FormControl>
                {/* <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'text-black text-base h-[48px] border-border  border rounded-lg py-3 bg-white h-18 pl-4 w-full ',
                        )}
                      >
                        {field.value ? format(field.value, 'yyyy/MM/dd') : <span>選択する</span>}
                        <CalendarIcon className="ml-auto h-6 w-6  text-black" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Calendar
                      className="w-full"
                      mode="single"
                      initialFocus
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date('1900-01-01')}
                    />
                  </PopoverContent>
                </Popover> */}
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="sex"
            render={({ field }) => (
              <FormItem className="">
                <FormLabel>性別</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex justify-start  items-center"
                    disabled={!!accountProfile.sex}
                  >
                    <FormItem className="flex justify-start items-center space-y-0 mr-4">
                      <FormControl>
                        <RadioGroupItem value="1" />
                      </FormControl>
                      <FormLabel className="font-normal ml-1">男性</FormLabel>
                    </FormItem>
                    <FormItem className="flex justify-start items-center space-y-0 mr-4">
                      <FormControl>
                        <RadioGroupItem value="2" />
                      </FormControl>
                      <FormLabel className="font-normal ml-1">女性</FormLabel>
                    </FormItem>
                    <FormItem className="flex justify-start items-center space-y-0 mr-4">
                      <FormControl>
                        <RadioGroupItem value="0" />
                      </FormControl>
                      <FormLabel className="font-normal ml-1">無回答</FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="nickName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>ニックネーム</FormLabel>
                <FormControl>
                  <Input
                    disabled={!!accountProfile.nickName}
                    className="text-black text-base  h-[48px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
});

UserInfoForm.displayName = 'UserInfoForm';

export default UserInfoForm;
